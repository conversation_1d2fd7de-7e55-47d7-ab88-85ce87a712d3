---
title: Campaign Definition JSON Structure
classification: Confidential
created: 2024-08-28
updated: 2024-10-16
authors:
  - "[[<PERSON>]]"
version: 2.4.0
next-review: 2024-10-20
tags:
  - campaign
  - definition
  - json
  - structure
  - ruleforge
---

# Campaign Definition JSON Structure

## Document Specification

### Purpose

This document defines the JSON structure for campaign definitions in the RuleForge Campaign Rules Engine (CRE). It specifies the format, content, and constraints of the campaign definition data used by the Rules Engine.

### Scope

This document covers:

- The top-level JSON structure for campaign definitions
- Definitions of essential components (campaign metadata, persistent variables, local variables, entities, transaction contexts, rules)
- Data types and constraints for each field
- Naming conventions and size limits

This document does not cover:

- The internal processing of campaign definitions by the Rules Engine
- GUI representations of campaigns
- The process of updating or deleting campaigns (handled separately by the API)

### Target Audience

- Developers implementing campaign logic in RuleForge CRE
- System architects designing campaign structures
- QA engineers validating campaign definitions
- Technical writers creating campaign-related documentation

## Table of Contents

- [[#1. Top-Level Structure]]
- [[#2. Component Definitions]]
- [[#3. Constraints and Validation]]
- [[#4. Usage Guidelines]]
- [[#5. Example]]

## 1. Top-Level Structure

A campaign definition is represented by a JSON object with the following top-level structure:

```json
{
  "schemaVersion": "string",
  "campaignId": "string",
  "name": "string",
  "description": "string",
  "campaignVersion": "integer",
  "status": "string",
  "startDateTime": "string (ISO 8601 format)",
  "endDateTime": "string (ISO 8601 format)",
  "lastModifiedDateTime": "string (ISO 8601 format)",
  "collectionMappings": [],
  "persistentVariableDefinitions": [],
  "localVariableDefinitions": [],
  "entities": []
}
```

## 2. Component Definitions

### Schema Version

- `schemaVersion`: Version of the JSON schema structure (string, semantic versioning format)
  - Example: "2.2.0"
  - This version represents changes to the JSON structure itself, not the campaign content
  - MAJOR version for backwards-incompatible changes
  - MINOR version for backwards-compatible additions
  - PATCH version for backwards-compatible fixes

### Campaign Metadata

- `campaignId`: Unique identifier for the campaign (string, max 50 characters)
- `name`: Human-readable name for the campaign (string, max 100 characters)
- `description`: Description of the campaign's purpose (string, max 500 characters)
- `campaignVersion`: Version of the campaign content (integer)
- `status`: Current status of the campaign (string, enum: "DRAFT", "ACTIVE", "PAUSED", "COMPLETED")
- `startDateTime`: Start date and time of the campaign (string, ISO 8601 format)
- `endDateTime`: End date and time of the campaign (string, ISO 8601 format)
- `lastModifiedDateTime`: Date and time of the last modification to the campaign (string, ISO 8601 format)
- 
### Collection Mappings

The `collectionMappings` array defines how collections relate to specific properties across different entities and transaction contexts:

```json
{
  "collectionMappings": [
    {
      "collectionName": "string",
      "keyMappings": [
        {
          "entityId": "string",
          "contextId": "string",
          "propertyName": "string"
        }
      ]
    }
  ]
}
```

Fields:

- `collectionName`: Name of the collection (string, max 50 characters)
- `keyMappings`: Array of mappings defining which property to use as the key for each entity and context
  - `entityId`: Identifier of the entity (string, max 50 characters)
  - `contextId`: Identifier of the transaction context (string, max 50 characters)
  - `propertyName`: Name of the property to use as the key (string, max 100 characters)

### Persistent Variable Definitions

The `persistentVariableDefinitions` array defines variables that maintain state across multiple transactions:

```json
{
  "persistentVariableDefinitions": [
    {
      "variableId": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "defaultValue": "any",
      "collection": "string"
    }
  ]
}
```

Fields:

- `variableId`: Unique identifier for the variable (string, max 50 characters)
- `name`: Human-readable name for the variable (string, max 100 characters)
- `description`: Description of the variable's purpose (string, max 250 characters)
- `type`: Data type of the variable (string, enum: "number", "string", "boolean", "date", "enum")
- `defaultValue`: Initial value for the variable (type must match the specified `type`)
- `collection`: Name of the collection this variable is associated with (string, must match a collectionName in collectionMappings)

### Local Variable Definitions

The `localVariableDefinitions` array defines variables that are local to a single rule execution:

```json
{
  "localVariableDefinitions": [
    {
      "variableId": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "defaultValue": "any"
    }
  ]
}
```

Fields:

- `variableId`: Unique identifier for the variable (string, max 50 characters)
- `name`: Human-readable name for the variable (string, max 100 characters)
- `description`: Description of the variable's purpose (string, max 250 characters)
- `type`: Data type of the variable (string, enum: "number", "string", "boolean", "date", "enum")
- `defaultValue`: Initial value for the variable (type must match the specified `type`)

### Entities

The `entities` array contains the definitions for each entity involved in the campaign:

```json
{
  "entities": [
    {
      "entityId": "string",
      "entityName": "string",
      "transactionContexts": []
    }
  ]
}
```

Fields:

- `entityId`: Unique identifier for the entity (string, max 50 characters)
- `entityName`: Human-readable name for the entity (string, max 100 characters)
- `transactionContexts`: Array of transaction contexts for this entity

### Transaction Contexts

Each entity can have multiple transaction contexts:

```json
{
  "transactionContexts": [
    {
      "contextId": "string",
      "contextName": "string",
      "rules": []
    }
  ]
}
```

Fields:

- `contextId`: Unique identifier for the transaction context (string, max 50 characters)
- `contextName`: Human-readable name for the transaction context (string, max 100 characters)
- `rules`: Array of rule definitions for this context



### Rule Definitions

The `rules` array contains the individual rule definitions for each transaction context:

```json
{
  "rules": [
    {
      "ruleId": "string",
      "name": "string",
      "description": "string",
      "priority": "integer",
      "condition": {},
      "actions": [],
      "variableOperations": []
    }
  ]
}
```

Fields:

- `ruleId`: Unique identifier for the rule (string, max 50 characters)
- `name`: Human-readable name for the rule (string, max 100 characters)
- `description`: Description of the rule's purpose (string, max 500 characters)
- `priority`: Integer indicating the priority of rule execution (lower numbers indicate higher priority)
- `conditions`: Array of condition objects (see Condition Structure)
- `actions`: Array of action objects (see Action Structure)
- `variableOperations`: Array of operations on variables (see Variable Operation Structure)

#### Condition Structure

Each condition is represented by an object with the following structure:

```json
{
  "type": "string",
  "operator": "string",
  "parameters": {}
}
```

- `type`: Type of condition (e.g., "COMPARISON", "LOGICAL")
- `operator`: The specific operation to perform (e.g., "==", ">", "AND", "OR")
- `parameters`: Object containing key-value pairs for the condition parameters

Example of a comparison condition:

```json
{
  "type": "COMPARISON",
  "operator": "==",
  "parameters": {
    "leftOperand": "{customerType}",
    "rightOperand": "GOLD"
  }
}
```

Example of a logical condition:

```json
{
  "type": "LOGICAL",
  "operator": "AND",
  "parameters": {
    "conditions": [
      {
        "type": "COMPARISON",
        "operator": ">",
        "parameters": {
          "leftOperand": "{amount}",
          "rightOperand": 100
        }
      },
      {
        "type": "COMPARISON",
        "operator": "==",
        "parameters": {
          "leftOperand": "{customerType}",
          "rightOperand": "GOLD"
        }
      }
    ]
  }
}
```

This structure allows for flexible and nested conditions while keeping the operator separate from the parameters.


#### Action Structure

```json
{
  "type": "string",
  "parameters": {}
}
```

- `type`: Action type as defined in the Rules Engine (PascalCase)
- `parameters`: Object containing key-value pairs for the action parameters

#### Variable Operation Structure

```json
{
  "variableId": "string",
  "operation": "string",
  "value": "any"
}
```

- `variableId`: Identifier of the variable to operate on
- `operation`: Type of operation (string, enum: "SET", "ADD", "SUBTRACT", "MULTIPLY", "DIVIDE")
- `value`: Value to use in the operation (should be a number or a reference to a numeric variable)

### Variable References

Throughout the campaign definition, variables and properties can be referenced using curly braces {}. 

Important rules:
1. Only single property names, persistent variables, or temporary variables are allowed inside {}.
2. No complex expressions, calculations, or operations are permitted within {}.
3. References must point to valid properties or variables defined within the campaign or provided in the transaction context.

Allowed examples:
- "{amount}"
- "{customerType}"
- "{totalPurchases}"

Disallowed examples:
- "{NOW - 30 DAYS}"  // No date calculations allowed
- "{amount * 0.1}"   // No arithmetic operations allowed
- "{Math.max(score, 100)}"  // No functions allowed

## 3. Constraints and Validation

### Size Limits

- Maximum campaign JSON size: 1MB
- Maximum number of entities per campaign: 10
- Maximum number of transaction contexts per entity: 20
- Maximum number of rules per transaction context: 50
- Maximum number of conditions per rule: 10
- Maximum number of actions per rule: 5
- Maximum number of variable operations per rule: 10

### Naming Conventions

- Use camelCase for all property names and variable identifiers
- Use PascalCase for action types
- Use UPPER_SNAKE_CASE for enumeration values
- All identifiers must:
  - Start with a letter
  - Contain only alphanumeric characters and underscores
  - Be case-sensitive

### Date Formats

All dates must be in ISO 8601 format: YYYY-MM-DDTHH:mm:ss.sssZ

## 4. Usage Guidelines

### Variable Definitions

1. Persistent Variables:
   - Use `persistentVariableDefinitions` for data that needs to be maintained across multiple transactions or campaign executions.
   - Always specify a `collection` that corresponds to a valid collection defined in the entity registration.
   - Choose appropriate data types and default values to ensure consistency.

2. Local Variables:
   - Use `localVariableDefinitions` for temporary data needed only during a single rule evaluation.
   - Initialize local variables with suitable default values.

### Rules

1. Structure:
   - Ensure each rule has a unique `ruleId` within the campaign.
   - Set appropriate `priority` values to control the order of rule evaluation.

2. Conditions:
   - Use the condition structure to create logical expressions with AND/OR operators.
   - Leverage both persistent and local variables in conditions for dynamic decision-making.

3. Actions:
   - Define actions to be executed when rule conditions are met.
   - Use actions for operations that affect external systems or trigger responses.

4. Variable Operations:
   - Use `variableOperations` array for updating both persistent and local variables.
   - Perform all variable updates within `variableOperations` to ensure atomic execution.

### Best Practices

1. Naming Conventions:
   - Use clear, descriptive names for variables, rules, and other components.
   - Follow the specified naming conventions consistently throughout the campaign definition.

2. Documentation:
   - Provide clear descriptions for variables, rules, and other components to aid in understanding and maintenance.

3. Modularization:
   - Group related rules and variables logically within the campaign structure.
   - Consider breaking very complex campaigns into multiple, interrelated campaigns if possible.

4. Performance:
   - Minimize the use of complex conditions and variable operations in high-volume scenarios.
   - Use local variables for intermediate calculations to reduce persistent storage operations.

5. Error Handling:
   - Implement appropriate error handling within rules, especially when dealing with external data or complex calculations.
   - Use default values and fallback logic to handle unexpected scenarios.

6. Testing:
   - Thoroughly test campaigns with various input scenarios to ensure correct behavior.
   - Pay special attention to edge cases and potential variable value extremes.

7. Version Control:
   - Maintain version control for campaign definitions, especially when making significant changes.
   - Document major changes in campaign logic or structure in the version field and changelog.

## 5. Example

Here's an example of a valid campaign definition JSON:

```json
{
  "schemaVersion": "2.4.0",
  "campaignId": "summerPromo2024",
  "name": "Summer Promotion 2024",
  "description": "Special offers for the summer season",
  "campaignVersion": 1,
  "status": "ACTIVE",
  "startDateTime": "2024-06-01T00:00:00Z",
  "endDateTime": "2024-08-31T23:59:59Z",
  "lastModifiedDateTime": "2024-10-16T14:30:00Z",
  "collectionMappings": [
    {
      "collectionName": "customers",
      "keyMappings": [
        {
          "entityId": "RETAIL_POS",
          "contextId": "PURCHASE",
          "propertyName": "customerId"
        },
        {
          "entityId": "ONLINE_STORE",
          "contextId": "WEB_PURCHASE",
          "propertyName": "userId"
        }
      ]
    }
  ],
  "persistentVariableDefinitions": [
    {
      "variableId": "totalPurchases",
      "name": "Total Purchases",
      "description": "The total amount of purchases made by a customer",
      "type": "number",
      "defaultValue": 0,
      "collection": "customers"
    }
  ],
  "localVariableDefinitions": [
    {
      "variableId": "discountAmount",
      "name": "Discount Amount",
      "description": "Calculated discount amount for the current transaction",
      "type": "number",
      "defaultValue": 0
    },
    {
      "variableId": "purchaseAmount",
      "name": "Purchase Amount",
      "description": "The amount of the current purchase",
      "type": "number",
      "defaultValue": 0
    }
  ],
  "entities": [
    {
      "entityId": "RETAIL_POS",
      "entityName": "Retail Point of Sale",
      "transactionContexts": [
        {
          "contextId": "PURCHASE",
          "contextName": "Product Purchase", 
          "rules": [
            {
              "ruleId": "summerDiscount",
              "name": "Summer Discount Rule",
              "description": "Applies a discount for purchases over $100 by loyal customers",
              "priority": 1,
              "condition": {
                "type": "LOGICAL",// 
                "operator": "AND",// AND Or OR
                "parameters": {
                  "conditions": [
                    {
                      "type": "COMPARISON",
                      "operator": ">",
                      "parameters": {
                        "leftOperand": "{purchaseAmount}",
                        "rightOperand": 100
                      }
                    },
                    {
                      "type": "COMPARISON",
                      "operator": "==",
                      "parameters": {
                        "leftOperand": "{customerType}",
                        "rightOperand": "GOLD"
                      }
                    }
                  ] 
                }
              },
              "actions": [
                {
                  "type": "ApplyDiscount",
                  "parameters": {
                    "discountAmount": "{discountAmount}"
                  }
                }
              ],
              "variableOperations": [
                {
                  "variableId": "discountAmount",
                  "operation": "SET",
                  "value": 10
                },
                {
                  "variableId": "totalPurchases",
                  "operation": "ADD",
                  "value": "{purchaseAmount}"
                }
              ]
            }
          ]
        }
      ]
    }
  ]
}
```

This example demonstrates a "Summer Promotion" campaign with one persistent variable, one local variable, one entity (Retail POS), one transaction context (Purchase), and one rule that applies a discount for purchases over $100 by loyal customers.

## Related Documents

- [[RuleForge Interface Overview]]
- [[Entity Integration Schema]]
- [[JSON Formatting Standards for Documentation]]

## Approvals

| Role/Department       | Name | Date | Signature |
| --------------------- | ---- | ---- | --------- |
| Lead Architect        |      |      |           |
| API Team Lead         |      |      |           |
| Documentation Manager |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| ------- | -------------- | --------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 2.4.0   | [[2024-10-16]] | [[Wayne Smith]] | - Replaced all numeric types ("integer", "amount", "float") with a single "number" type<br>- Updated all relevant sections and examples to reflect this change<br>- Simplified numeric value handling throughout the document                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            |
| 2.3.0    | [[2024-10-15]] | [[Wayne Smith]] | - Added restrictions on variable references to only allow simple properties and variables<br>- Updated main example to reflect new limitations on variable references <br>- Added "Variable References" section explaining allowed and disallowed usage                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  |
| 2.2.0   | [[2024-09-20]] | [[Wayne Smith]] | - Added `schemaVersion` field to represent JSON structure version<br>- Changed campaign version to `campaignVersion` as a simple integer<br>- Added `status` field to indicate campaign state<br>- Added `lastModifiedDateTime` field to track modifications<br>- Updated condition structure: moved `operator` out of `parameters` object<br>- Renamed `startDate` to `startDateTime` and `endDate` to `endDateTime` for clarity<br>- Implemented nested structure for logical operators (AND/OR) in rule conditions<br>- Updated condition structure to use a single top-level condition object representing an AST<br>- Updated naming conventions to align with company standards:<br>  - camelCase for property names and variable identifiers<br>  - PascalCase for action types<br>  - UPPER_SNAKE_CASE for enumeration values<br>- Clarified that all fields are required for complete campaign definition<br>- Improved overall document structure and readability<br>- Updated examples to reflect all changes |
| 2.1.0   | [[2024-09-19]] | [[Wayne Smith]] | Updated to incorporate nested logical conditions and align with latest naming conventions.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               |
| 2.0.0   | [[2024-08-29]] | [[Wayne Smith]] | Major revision to incorporate collection key mappings and update persistent variable definitions.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        |
| 1.0.0   | [[2024-08-28]] | [[Wayne Smith]] | Initial version of the Campaign Definition JSON Structure document.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      |

---
© 2024 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.0.1. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->