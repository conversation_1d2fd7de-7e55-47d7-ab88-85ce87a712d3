// JsDoc components

// <========== Global jsDoc (jsDocs) ==========>

/**
 * @typedef {Object} InputField
 * @property {string} value - The input value
 * @property {boolean} isValid - Whether the input is valid
 * @property {string} error - Error message if any
 * @property {boolean} isShowError - Whether to show the error
 */

/**
 * @typedef {Object} ColumnHeader
 * @property {string} label - The display label for the column header
 * @property {number} gridSize - The size of the column in the grid system (out of 12)
 * @property {Object} sx - Additional styles to apply to the column header
 */

/**
 * @typedef {Object} Column
 * @property {string} content - The content to display in the column
 * @property {number} gridSize - The size of the column in the grid system
 */

/**
 * @typedef {Object} ReusablePropsForInputsData
 * @property {string} id - Unique identifier for the mapping
 * @property {string} mode - Current mode of the mapping ("viewing" or "editing")
 * @property {boolean} isEditable - Whether the mapping can be edited
 * @property {boolean} isDeletable - Whether the mapping can be deleted
 * @property {Column[]} columns - Array of column configurations
 * @property {ValidationRules} validations - Validation rules for the mapping
 */

// <========== File path: src\components\pages\dashboard\templates\campaigns\create\sections\Details.jsx (jsDocs) ==========>

// ____ InputField ___

/**
 * @typedef {Object} DetailsInputsSection
 * @property {InputField} name - Campaign name field
 * @property {InputField} description - Campaign description field
 * @property {InputField} startDate - Campaign start date field
 * @property {InputField} endDate - Campaign end date field
 */

// <========== File path: src\components\pages\dashboard\templates\campaigns\create\sections\ColMapping.jsx (jsDocs) ==========>

// ____ InputField ___

/**
 * @typedef {Object} ValidationRules
 * @property {Object.<string, boolean|string>} [collectionName] - Validation rules for collection name
 * @property {Object.<string, boolean|string>} [keyMappings] - Validation rules for key mappings
 * @property {Object.<string, boolean|string>} [other] - Other validation rules
 */

/**
 * @typedef {Object} KeyMapping
 * @property {string} sourceKey - The key from the source data
 * @property {string} targetKey - The mapped key for the target collection
 * @property {string} [dataType] - The data type of the mapping
 */

/**
 * @typedef {Object} CollectionMappingInputData
 * @property {string} collectionName - Name of the collection
 * @property {KeyMapping[]} keyMappings - Array of key mapping configurations
 */

// <========== File path: src\components\pages\dashboard\templates\campaigns\create\sections\LocalVar.jsx(jsDocs) ==========>

// ____ InputField ___
/**
 * @typedef {Object} VariablesInputDataInputData
 * @property {ValidationRules} validations - Validation rules for the mapping
 * @property {string} collectionName - Name of the collection
 * @property {KeyMapping[]} keyMappings - Array of key mapping configurations
 */

// <========== File path: src\components\pages\dashboard\organisms\campaigns\create\colMappings\CollectionMappingContent.jsx(jsDocs) ==========>

// Inputs
/**
 * @typedef {Object} Inputs
 * @property {string} id - Unique identifier for the collection mapping
 *
 * */

// ____ InputField ___
/**
 *   ReusablePropsForInputsData
 *  {ReusablePropsForInputsData} ...
 * @property {string} collectionName - Name of the collection
 */

// <========== File path: src\components\pages\dashboard\organisms\campaigns\create\colMappings\KeysMappingContent.jsx(jsDocs) ==========>

// ____ InputField ___

/**
 * @typedef {Object} KeyMappingContentInputsData
 * @property {InputField} entityId - The entity ID field
 * @property {InputField} contextId - The context ID field
 * @property {InputField} propertyName - The property name field
 */

// <========== File path: src\components\pages\dashboard\organisms\campaigns\create\LocalVar\VariablesContent.jsx(jsDocs) ==========>

// ____ InputField ___

/**
 * @typedef {Object} VariablesContentInputsData
 * @property {InputField} name  - The name of the variable
 * @property {InputField} description - The description of the variable
 * @property {InputField} type - The type of the of the variable e.g. string, number, boolean etc.
 * @property {InputField} defaultValue - The default value of the variable
 * @property {InputField} collectionName - The name of the collection the variable belongs to note: this is only for persistent variables
 */

export {};
