
import { initialState as AlertInitialState } from "redux/slices/global/alert";
import { initialState as DashboardInitialState } from "redux/slices/global/dashboard";
import { initialState as AuthInitialState } from "redux/slices/global/auth";
import { initialState as ConfirmationInitialState } from "redux/slices/global/confirmation";
import { initialState as CampaignsInitialState } from "redux/slices/tabs/campaigns/campaigns";
import { initialState as CampaignInitialState } from "redux/slices/tabs/campaigns/campaign";


/** 
 * @typedef {Object} Store The redux store
 * 
 * @property {AlertInitialState} alert - Alert state
 * @property {DashboardInitialState} dashboard - Dashboard state
 * @property {AuthInitialState} auth - Auth state
 * @property {ConfirmationInitialState} confirmation - Confirmation state
 * @property {CampaignsInitialState} campaigns - Campaigns state
 * @property {CampaignInitialState} campaign - Campaign state
 * 
 */
export {}