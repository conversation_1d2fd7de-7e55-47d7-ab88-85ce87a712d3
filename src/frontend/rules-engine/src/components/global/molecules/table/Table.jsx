import PropTypes from "prop-types";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Typography,
} from "@mui/material";

/**
 * Global Table Component for rendering dynamic tables
 *
 * @component
 * @param {Array} columns - An array of objects defining table headers (with keys: `id`, `label`, `align`, and `style`).
 * @param {Array} rows - An array of objects representing table data.
 * @param {object} tableBodySX - Custom MUI styles for the table body.
 * @param {object} tableHeadSX - Custom MUI styles for the table head.
 * @param {object} tableRowSX - Custom MUI styles for the table row.
 * @param {object} tableCellSX - Custom MUI styles for the table cell.
 * @param {object} sx - Custom MUI styles for the table container.
 * @returns {JSX.Element} The rendered table component.
 */
const TableMolecule = ({
  columns,
  rows,
  sx,
  tableBodySX,
  tableHeadSX,
  tableRowSX,
  tableCellSX,
}) => {
  return (
    <TableContainer
      component={Paper}
      sx={{
        ...sx,
        padding: 2,
        boxShadow: "0px 4px 10px rgba(0, 0, 0, 0.1)",
        borderRadius: 2,
      }}
    >
      <Table>
        {/* Table Head */}
        <TableHead sx={{ ...tableHeadSX }}>
          <TableRow sx={{ ...tableRowSX }}>
            {columns.map((column) => (
              <TableCell
                key={column.id}
                align={column.align || "left"}
                sx={{ fontWeight: "bold", ...column.style }} // Support custom styles for each column
              >
                {column.label}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>

        {/* Table Body */}
        <TableBody sx={{ ...tableBodySX }}>
          {rows.length > 0 ? (
            rows.map((row, rowIndex) => (
              <TableRow key={rowIndex} hover>
                {columns.map((column) => (
                  <TableCell
                    key={column.id}
                    align={column.align || "left"}
                    sx={{ ...column.cellStyle }}
                  >
                    {row[column.id]}
                  </TableCell>
                ))}
              </TableRow>
            ))
          ) : (
            <TableRow sx={{ ...tableRowSX }}>
              <TableCell sx={{ ...tableCellSX }} colSpan={columns.length}>
                <Box sx={{ textAlign: "center", py: 2 }}>
                  <Typography variant="body1">No Data Available</Typography>
                </Box>
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

TableMolecule.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired, // Identifier to match the data in rows
      label: PropTypes.string.isRequired, // Header label
      align: PropTypes.oneOf(["left", "right", "center"]), // Alignment of the column
      style: PropTypes.object, // Custom styling for the header cell
      cellStyle: PropTypes.object, // Custom styling for the body cells
    })
  ).isRequired,
  tableBodySX: PropTypes.object, // Custom styles for the table body
  tableHeadSX: PropTypes.object, // Custom styles for the table head
  tableRowSX: PropTypes.object, // Custom styles for the table row
  tableCellSX: PropTypes.object, // Custom styles for the table cell
  rows: PropTypes.arrayOf(PropTypes.object).isRequired, // Array of row objects matching the column IDs
  sx: PropTypes.object, // Custom styles for the table container
};

export default TableMolecule;
