import React, { useState, useEffect } from "react";
import { TextField } from "@mui/material";
import SmartValueModal from "../../atoms/smartValue/modal/SmartValueModal";
import SmartValue from "../smartValue/SmartValue";
import { Alert } from "@mui/material";
import Box from "@mui/material/Box";
import { Alert as AlertUtils } from "../../../../utils/alert/alertUtils";
import { useSelector } from "react-redux";
import { Helpers } from "../../../../utils/generalFunctions/index";

/**
 * SmartValueHoc component that wraps a TextField and a SmartValueModal.
 *
 * @param {Object} props - The component props
 * @param {string} props.label - The label for the text field
 * @param {Function} props.onSave - Callback function when a new value is saved from the modal
 * @param {Object} props.smartValueProps - Props to be passed to the SmartValue component
 * @param {Object} props.smartValueProps.enumValues - The enum values to be passed to the SmartValue component, if it's "auto" then the enum values will be fetched from the receivedData
 * @returns {React.JSX.Element} The rendered component
 */
const SmartValueHoc = ({ label, onSave, smartValueProps }) => {
  const { data, emptySaveAllowed, errorDetails, disabled, onDisabledClick, enumValues } =
    smartValueProps;
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [value, setValue] = useState(data);
  const [modalOpenCount, setModalOpenCount] = useState(0);
  const [error, setError] = useState("");
  const { receivedData } = useSelector((state) => state.campaign);
  const { functions } = receivedData; 

  // handle the input click

  const handleInputClick = () => {
    if (disabled) {
      onDisabledClick();
      return;
    }
    // Reset value before opening modal
    setValue(data);

    // Reset error state
    setError("");
    setModalOpenCount((prev) => prev + 1);
    setIsModalOpen(true);
  };

  // handle the modal close
  const handleModalClose = () => {
    setIsModalOpen(false);
  };
  // handle the save
  const handleSave = () => {
    // validate the value
    const validationResult = Helpers.validateSmartValue(
      value,
      emptySaveAllowed,
      functions
    );

    if (!validationResult.isValid) {
      setError(validationResult.errorMessage);
      setTimeout(() => {
        setError("");
      }, 2000);
      return;
    }
    
    onSave(value);
    handleModalClose();
  };
 
  return (
    <>
      <TextField
        label={label}
        // format the data value to be displayed according to expected format
        value={Helpers.formatDataValue(data)}
        onClick={handleInputClick}
        fullWidth
        required={emptySaveAllowed ? false : true}
        autoComplete="off"
        size="small"
        // Apply styles to make it look disabled but remain clickable
        sx={{
          ...(!disabled
            ? {}
            : {
                "& .MuiInputBase-root": {
                  backgroundColor: "rgba(0, 0, 0, 0.05)",
                  color: "rgba(0, 0, 0, 0.38)",
                },
                "& .MuiInputBase-input": {
                  cursor: "pointer", // Keep cursor as pointer to indicate clickability
                },
                "& .MuiInputLabel-root": {
                  color: "rgba(0, 0, 0, 0.38)",
                },
                "& .MuiOutlinedInput-notchedOutline": {
                  borderColor: "rgba(0, 0, 0, 0.12)",
                },
              }),
        }}
      />
      {errorDetails?.isShowError && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {errorDetails.error ? errorDetails.error : "Please Select a value"}
        </Alert>
      )}
      <SmartValueModal
        open={isModalOpen}
        onClose={handleModalClose}
        onSave={handleSave}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            gap: 2,
            width: "100%",
          }}
        >
          {/* Error message at the top */}
          {error && (
            <Alert
              severity="error"
              sx={{
                width: "200px",
                mb: 2,
                top: "50px",
                left: 0,
                position: "absolute",
                zIndex: 1000,
              }}
            >
              {error}
            </Alert>
          )}
 
          {/* SmartValue component */}
          <SmartValue
            {...smartValueProps}
            setValue={setValue}
            value={value}
            handleEnterKey={handleSave}
            key={`modal-instance-${modalOpenCount}`}
          />
        </Box> 
      </SmartValueModal>
    </>
  );
};

export default SmartValueHoc;
 