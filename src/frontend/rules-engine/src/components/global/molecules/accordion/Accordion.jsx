import PropTypes from 'prop-types';
import MUIAccordion from '@mui/material/Accordion';
import AccordionActions from '@mui/material/AccordionActions';
import AccordionSummary from '@mui/material/AccordionSummary';
import AccordionDetails from '@mui/material/AccordionDetails';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import React from 'react';

/**
 * Global Accordion component for rendering a list of accordion items.
 * 
 * @component
 * @example
 * const accordionItems = [
 *   {
 *     summaryComponent: <div>Accordion 1</div>,
 *     detailsComponent: <div>Lorem ipsum dolor sit amet.</div>
 *   },
 *   {
 *     summaryComponent: <div>Accordion 2</div>,
 *     detailsComponent: <div>Details for Accordion 2</div>,
 *     actionsComponent: (
 *       <>
 *         <Button>Cancel</Button>
 *         <Button>Agree</Button>
 *       </>
 *     ),
 *   },
 * ];
 * return <Accordion accordionItems={accordionItems} />;
 *
 * @param {Object[]} accordionItems - Array of accordion items to render.
 * @param {React.ReactNode} accordionItems[].summaryComponent - The component to render as the accordion summary.
 * @param {React.ReactNode} accordionItems[].detailsComponent - The component to render inside the accordion's details section.
 * @param {React.ReactNode} [accordionItems[].actionsComponent] - Optional component to render in the accordion's action section (e.g., buttons).
 * 
 * @returns {JSX.Element} Rendered Accordion component.
 */
export const Accordion = ({ accordionItems = [] }) => {
  return (
    <div>
      {accordionItems.map((item, index) => (
        <MUIAccordion key={index}>
          <AccordionSummary
            expandIcon={<ExpandMoreIcon />}
            aria-controls={`panel-${index}-content`}
            id={`panel-${index}-header`}
          >
            {item.summaryComponent}
          </AccordionSummary>
          <AccordionDetails>
            {item.detailsComponent}
          </AccordionDetails>
          {item.actionsComponent && (
            <AccordionActions>
              {item.actionsComponent}
            </AccordionActions>
          )}
        </MUIAccordion>
      ))}
    </div>
  );
};

// PropTypes for type-checking the props
Accordion.propTypes = {
  /**
   * Array of accordion items, each containing a summary, details, and optional actions.
   */
  accordionItems: PropTypes.arrayOf(
    PropTypes.shape({
      /**
       * The component to display in the summary (header) section of the accordion.
       */
      summaryComponent: PropTypes.node.isRequired,

      /**
       * The component to display in the details (content) section of the accordion.
       */
      detailsComponent: PropTypes.node.isRequired,

      /**
       * Optional component to display in the actions section (e.g., buttons).
       */
      actionsComponent: PropTypes.node,
    })
  ).isRequired,
};
