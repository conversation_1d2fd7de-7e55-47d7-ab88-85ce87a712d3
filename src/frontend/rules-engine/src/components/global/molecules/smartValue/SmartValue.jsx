import DropdownAtom from "../../atoms/dropdown/Dropdown";
import { Box, Button, Tab, Tabs } from "@mui/material";
import { useState, useEffect, useRef } from "react";
import { Btn } from "../../atoms/buttons/Button";
import Typography from "@mui/material/Typography";
import InputFormField from "../../atoms/smartValue/inputField/InputFormField";
import { useVariableOptions } from "../../../../hooks/useVariableOptions";
import { useSelector } from "react-redux";
import FunctionsVal from "../../atoms/smartValue/functions/FunctionsVal";
import { Helpers } from "../../../../utils/generalFunctions";
import PropTypes from "prop-types";
import MembershipValue from "../../atoms/smartValue/membership/MembershipValue";
import EnumManager from "../../atoms/enum/Index";
/**
 * SmartValue component that displays a smart value.
 *
 * @param {Object} smartValueProps - The props for the SmartValue component
 * @param {Array<string>} smartValueProps.types - The allowed data types for the smart value
 * @param {Array<string>} smartValueProps.allowedRef - The allowed reference types for the smart value
 * @param {boolean} smartValueProps.customValAllowed - Flag indicating if custom values are allowed
 * @param {string} smartValueProps.entityId - The ID of the entity
 * @param {string} smartValueProps.contextId - The ID of the context
 * @param {*} smartValueProps.data - The current data value
 * @param {boolean} smartValueProps.emptySaveAllowed - Flag indicating if saving an empty value is allowed
 * @returns {React.JSX.Element} The rendered SmartValue component
 */
const SmartValue = ({ ...smartValueProps }) => {
  let {
    types,
    functionTypes,
    allowedRef,
    customValAllowed,
    entityId,
    contextId,
    data,
    emptySaveAllowed,
    setValue,
    value,
    ruleType,
    handleEnterKey,
    enumValues,
  } = smartValueProps;


  const [selectedType, setSelectedType] = useState(() => {
    // If value is a function object, set type to "functions"
    if (value && typeof value === "object" && value.functionId) {
      return "functions";
    }
    // Otherwise use the first available type
    return types[0] || "";
  });

  const [activeButton, setActiveButton] = useState("custom");
  const [selectedOption, setSelectedOption] = useState({
    category: "",
    label: "",
  });
  const { receivedData } = useSelector((state) => state.campaign);
  const { functions } = receivedData;
  const [typesState, setTypesState] = useState([...types]);

  // const [value, setValue] = useState(data || "");
  // Keep track of the first render to avoid setting value to an empty string

  const { generateVariablesOptions } = useVariableOptions(
    entityId,
    contextId,
    ruleType
  );


  if(!functionTypes){
   functionTypes = types;
  }

  /**
   * Handles the change of data type selection
   * Resets the current value and selected option when type changes
   *
   * @param {string} id - The identifier of the dropdown (not used)
   * @param {string} value - The newly selected data type
   */
  const handleTypeChange = (id, value) => {
    setSelectedType(value);
    setValue("");
    // if the type is boolean, set the value to false
    if (value == "boolean" && activeButton == "custom") {
      setValue(false);
    }
    setSelectedOption({ category: "", label: "" });

    // You can add additional logic here if needed
  };
  /**
   * Handles the click event for the reference button
   * Resets the current value and selected option when the reference button is clicked
   *
   * @param {string} buttonType - The type of button clicked ("reference" or "custom")
   */
  const handleButtonClick = (buttonType) => {
    if (activeButton != buttonType) {
      setActiveButton(buttonType);
      setValue("");
      setSelectedOption({ category: "", label: "" });
      // if the type is boolean, set the value to false
      if (selectedType == "boolean" && buttonType == "custom") {
        setValue(false);
      }
    }
  };
  /**
   * Handles the input change event
   * Updates the value state with the new input value
   *
   * @param {any} newValue - The new value from the input field
   */
  const handleInputChange = (newValue) => {
    setValue(newValue);
  };
  /**
   * Handles the click event for an option
   * Updates the value state with the selected option value
   *
   * @param {string} category - The category of the selected option
   * @param {Object} option - The selected option object
   */
  const handleOptionClick = (category, option) => {
    setValue(option.value);
    setSelectedOption({ category, label: option.value });
  };

  /**
   * Effect hook that runs when the component mounts
   * Initializes the variable options and list options
   * Determines the type and source of the current value
   * Updates the selected type and active button based on the determined type and source
   *  for the functions types we need to check if the functionTypes are available in the types array
   *  if not we need to add it to the types array
   */
  useEffect(() => {
    // Check if any of the types are available in functionTypes
    // Returns true if at least one type from the types array matches any functionType
    let isFunctionExists = 
      functionTypes.length > 0 &&
      types.some((type) => functionTypes.some((funcType) => funcType === type));

      // If type contains "enum" and we provided enumValues then set the isFunctionExists to false, as there is not functions for enum and we only want to render the enum values dropdown
      if(types.includes("enum") && enumValues){
        isFunctionExists = false;
      }

    // If functions exist for the current types, add "functions" to the types array
    if (isFunctionExists && !types.includes("functions")) {
      // Update the state version of types instead of modifying the prop directly
      setTypesState([...types, "functions"]);
    }
  
    // Get all variable options for checking references
    const allVariableOptions = allowedRef
      .map((ref) => generateVariablesOptions(ref))
      .flat();
    
    // get the list options
    const listOptions =
      receivedData.lists?.lists?.map((item) => ({
        value: item.name,
        label: item.name,
        description: item.elemsCount ? item.elemsCount + " rows" : "",
      })) || [];
    // Determine type and source of the current value
    const { type, source, category, label } =
      Helpers.determineValueTypeAndSource(
        value,
        allVariableOptions,
        functions,
        {
          isMembershipOperator:
            typesState.includes("list") && typesState.includes("array"),
          listOptions,
        }
      );

    // Only update if we have a determined type
    if ((type && typesState.includes(type)) || type === "functions") {
      setSelectedType(type);

      // Set appropriate source button
      if (source === "reference") {
        setActiveButton("reference");
        setSelectedOption({ category, label });
      } else if (source === "custom") {
        setActiveButton("custom");
      }
    }
  }, []);

  // Generate and filter options based on allowedRef and selectedType
  const filteredOptions = allowedRef
    .map((ref) => generateVariablesOptions(ref))
    .flat()
    .map((group) => ({
      ...group,
      options: group.options.filter((option) => option.type === selectedType),
    }))
    .filter((group) => group.options.length > 0);


 
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          // padding: 2,
          width: "100%",
        }}
      >
        {typesState.length > 1 &&
          !(typesState.includes("reference") && typesState.includes("array")) && (
            <DropdownAtom
              fullWidth={false}
              sx={{ width: "100%", marginTop: 2, marginBottom: 3 }}
              label="Select Type"
              selectedItem={selectedType}
              onChange={handleTypeChange}
              options={typesState.map((type) => ({
                label: type.charAt(0).toUpperCase() + type.slice(1),
                value: type,
              }))}
              required={true}
              validation={{ isShowError: false, error: null }}
            />
          )}

        {/* {typesState.length === 1 &&  !typesState.includes("reference") && typesState.includes("array") && (
          <>
           
            <EnumManager
              values={Array.isArray(value) ? value : []}
              onAddNewValue={(newValue) => setValue([...value, newValue])}
              onDeleteValue={(indexToDelete) =>
                setValue(value.filter((_, index) => index !== indexToDelete))
              }
            />
          </>
        )} */}
       
      </Box>

      {/* Render FunctionsVal component if selectedType is "functions" */}
      {selectedType === "functions" ? (
        <Box sx={{ width: "100%" }}>
          <FunctionsVal
            functions={
              functions.filter((func) =>
                functionTypes.includes(func.returnType)
              ) || []
            }
            value={value && typeof value === "object" ? value : {}}
            onChange={setValue}
            ruleType={ruleType}
          />
        </Box>
      ) 
      : typesState.includes("list") && typesState.includes("array") ? (
        <Box sx={{ width: "100%" }}>
          <MembershipValue
            listOptions={
              receivedData.lists?.lists?.map((item) => ({
                value: item.name,
                label: item.name,
                description: item.elemsCount ? item.elemsCount + " rows" : "",
              })) || []
            }
            transactionVariables={receivedData.transactionVariables}
            value={value}
            onChange={setValue}
          />
        </Box>
      )
       : typesState.length !== 1 && typesState.includes("reference") && typesState.includes("array") ? (
        <Box sx={{ width: "100%" }}>
         
          <MembershipValue
            ruleType={ruleType}
            entityId={entityId}
            contextId={contextId}
            listOptions={
              receivedData.lists?.lists?.map((item) => ({
                value: item.name,
                label: item.name,
                description: item.elemsCount ? item.elemsCount + " rows" : "",
              })) || []
            }
            allowedRef={allowedRef}
            value={value}
            onChange={setValue}
          />
        </Box>
      ) 
      :  selectedType == "enum" && !enumValues ? (
        <Box component="ul" sx={{ listStyleType: "none", padding: 0 }}>
        {filteredOptions.map((group) => (
          <Box
            component="li"
            key={group.category}
            sx={{ marginBottom: 2 }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                color: "white.main",
                fontWeight: "bold",
                fontSize: "14px",
                backgroundColor: "black",
                padding: "0.5px 1px",
                textAlign: "center",
              }}
            >
              {group.category}:
            </Typography>
            <Box
              component="ul"
              sx={{ listStyleType: "none", padding: 0 }}
            >
              {group.options.map((option) => (
                <Box
                  component="li"
                  key={option.value}
                  onClick={() =>
                    handleOptionClick(group.category, option)
                  }
                  sx={{
                    cursor: "pointer",
                    padding: "8px 12px",
                    margin: "4px 0",
                    borderRadius: 1,
                    // transition: "background-color 0.3s, color 0.3s",
                    bgcolor: "grey.100",
                    // color: "text.primary",
                    fontSize: "0.9em",
                    px: 2,
                    py: 1,
                    borderLeft:
                      selectedOption?.label === option?.value
                        ? "4px solid"
                        : "none",
                    borderColor:
                      selectedOption?.label === option?.value
                        ? "primary.main"
                        : "transparent",
                    backgroundColor:
                      selectedOption?.label === option?.value
                        ? "primary.main"
                        : "grey.100",
                    color:
                      selectedOption?.label === option?.value
                        ? "white.main"
                        : "text.primary",
                    transition: "all 0.2s ease-in-out",
                    "&:hover": {
                      backgroundColor: "primary.main",
                      color: "white.main",
                    },

                    // ...(selectedOption?.label === option?.value && {
                    //   bgcolor: "#E0F7FA", // cyan.50 equivalent
                    //   color: "#006064", // cyan.800 equivalent
                    // }),
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      fontSize: "16px",
                      fontWeight: 800,
                      letterSpacing: "0.3px", // Slightly spaced letters
                      textTransform: "capitalize",
                    }}
                  >
                    {option.label}
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ fontSize: "12px", }}
                  >
                    {option.description}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Box>
        ))}
        {filteredOptions.length === 0 && (
          <Typography variant="subtitle1" sx={{ color: "grey.500",textAlign:"center" }}>
            No reference
          </Typography>
        )}
      </Box>
      )
      :  selectedType == "enum" && enumValues ? (
        <SimpleValuesListUl key={"enum-values-list"} values={enumValues} currentValue={value} setValue={setValue} />
      )
      
      
      : (
        // Only render buttons and inputs for non-function types
        <>
          {/* Don't show buttons for list or enum types, and set activeButton to custom */}
          {selectedType == "list" || selectedType == "array" ? (
            // Hidden component to ensure activeButton is set to custom
            <Box sx={{ display: "none" }}>
              
              {activeButton !== "custom" && setActiveButton("custom")}
            </Box>
          ) : (
            // Show buttons for all other types and if the allowedRef is empty than don't show the custom button
            <Box sx={{ display: "flex", gap: 2 }}>
              {allowedRef.length > 0 && (
                <Tabs
                  value={activeButton}
                  onChange={(event, newValue) => {
                    handleButtonClick(newValue);
                  }}
                  aria-label="basic tabs example"
                  sx={{ mb: 2 }}
                >
                  <Tab
                    label="Custom"
                    value="custom"
                    onClick={() => handleButtonClick("custom")}
                  />
                  <Tab
                    label="Reference"
                    value="reference"
                    onClick={() => handleButtonClick("reference")}
                  />
                </Tabs>
              )}
            </Box>
          )}
          {activeButton === "custom" && customValAllowed && (
            <InputFormField
              selectedType={selectedType}
              value={value}
              onChange={handleInputChange}
              handleEnterKey={handleEnterKey}
            />
          )}
          {activeButton === "reference" && (
            <Box sx={{ minWidth: 300, padding: 2 }}>
              {selectedOption.label && filteredOptions.length > 0 && (
                <Box
                  sx={{
                    marginBottom: 2,
                    fontWeight: "bold",
                    color: "success.main",
                    fontSize: "1.2em",
                  }}
                >
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontWeight: "bold",
                      textAlign: "center",
                      borderBottom: "1px solid",
                      paddingBottom: 3,
                      borderColor: "#2e7d324d",
                    }}
                  >
                    Selected:{" "}
                    <span style={{ fontWeight: "normal", color: "black" }}>
                      {selectedOption.category.replace(" Variables", "")}:{" "}
                      <span style={{ fontWeight: "bold", color: "black" }}>
                        {selectedOption.label}
                      </span>
                    </span>
                  </Typography>
                  {/* {selectedOption.category}: {selectedOption.label} */}
                </Box>
              )}
              <Box component="ul" sx={{ listStyleType: "none", padding: 0 }}>
                {filteredOptions.map((group) => (
                  <Box
                    component="li"
                    key={group.category}
                    sx={{ marginBottom: 2 }}
                  >
                    <Typography
                      variant="subtitle1"
                      sx={{
                        color: "white.main",
                        fontWeight: "bold",
                        fontSize: "14px",
                        backgroundColor: "black",
                        padding: "0.5px 1px",
                        textAlign: "center",
                      }}
                    >
                      {group.category}:
                    </Typography>
                    <Box
                      component="ul"
                      sx={{ listStyleType: "none", padding: 0 }}
                    >
                      {group.options.map((option) => (
                        <Box
                          component="li"
                          key={option.value}
                          onClick={() =>
                            handleOptionClick(group.category, option)
                          }
                          sx={{
                            cursor: "pointer",
                            padding: "8px 12px",
                            margin: "4px 0",
                            borderRadius: 1,
                            // transition: "background-color 0.3s, color 0.3s",
                            bgcolor: "grey.100",
                            // color: "text.primary",
                            fontSize: "0.9em",
                            px: 2,
                            py: 1,
                            borderLeft:
                              selectedOption?.label === option?.value
                                ? "4px solid"
                                : "none",
                            borderColor:
                              selectedOption?.label === option?.value
                                ? "primary.main"
                                : "transparent",
                            backgroundColor:
                              selectedOption?.label === option?.value
                                ? "primary.main"
                                : "grey.100",
                            color:
                              selectedOption?.label === option?.value
                                ? "white.main"
                                : "text.primary",
                            transition: "all 0.2s ease-in-out",
                            "&:hover": {
                              backgroundColor: "primary.main",
                              color: "white.main",
                            },

                            // ...(selectedOption?.label === option?.value && {
                            //   bgcolor: "#E0F7FA", // cyan.50 equivalent
                            //   color: "#006064", // cyan.800 equivalent
                            // }),
                          }}
                        >
                          <Typography
                            variant="body2"
                            sx={{
                              fontSize: "16px",
                              fontWeight: 800,
                              letterSpacing: "0.3px", // Slightly spaced letters
                              textTransform: "capitalize",
                            }}
                          >
                            {option.label}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ fontSize: "12px", }}
                          >
                            {option.description}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </Box>
                ))}
                {filteredOptions.length === 0 && (
                  <Typography variant="subtitle1" sx={{ color: "grey.500",textAlign:"center" }}>
                    No reference
                  </Typography>
                )}
              </Box>
            </Box>
          )}
        </>
      )}
    </Box>
  );
};



/**
 * Function to render a list of values which on click will set the value to the current value
 * @param {object} param0 
 * @param {array} param0.values - The values to render
 * @param {string} param0.currentValue - The current value
 * @param {function} param0.setValue - The function to set the value
 * @returns 
 */
function SimpleValuesListUl({values, currentValue, setValue}) {
  return (
    <Box component="ul" sx={{ listStyleType: "none", padding: 0, width: "100%" }}>
      {values.map((value) => (
        <Box
          component="li"
          key={value}
          onClick={() => setValue(value)}
          sx={{
            cursor: "pointer",
            padding: "8px 12px",
            margin: "4px 0",
            borderRadius: 1,
            bgcolor: "grey.100",
            fontSize: "0.9em",
            px: 2,
            py: 1,
            borderLeft: currentValue === value ? "4px solid" : "none",
            borderColor: currentValue === value ? "primary.main" : "transparent",
            backgroundColor: currentValue === value ? "primary.main" : "grey.100",
            color: currentValue === value ? "white.main" : "text.primary",
            transition: "all 0.2s ease-in-out",
            "&:hover": {
              backgroundColor: "primary.main",
              color: "white.main",
            },
          }}
        >
          <Typography
            variant="body2"
            sx={{
              fontSize: "16px",
              fontWeight: 800,
              letterSpacing: "0.3px",
              textTransform: "capitalize",
            }}
          >
            {value}
          </Typography>
        </Box>
      ))}
    </Box>
  );
}

SimpleValuesListUl.propTypes = {
  values: PropTypes.arrayOf(PropTypes.string).isRequired,
  currentValue: PropTypes.string,
  setValue: PropTypes.func.isRequired,
};



export default SmartValue;
SmartValue.propTypes = {
  types: PropTypes.arrayOf(PropTypes.string).isRequired,
  allowedRef: PropTypes.arrayOf(PropTypes.string).isRequired,
  customValAllowed: PropTypes.bool.isRequired,
  entityId: PropTypes.string.isRequired,
  contextId: PropTypes.string.isRequired,
  data: PropTypes.any,
  emptySaveAllowed: PropTypes.bool.isRequired,
  setValue: PropTypes.func.isRequired,
  value: PropTypes.any,
  ruleType: PropTypes.string.isRequired,
};
