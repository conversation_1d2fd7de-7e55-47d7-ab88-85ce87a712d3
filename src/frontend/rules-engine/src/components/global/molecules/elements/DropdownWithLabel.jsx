import PropTypes from "prop-types";
import { Box } from "@mui/material";
import Label from "../../atoms/label/Label";
import DropdownAtom from "../../atoms/dropdown/Dropdown";
import React from "react";

/**
 * A molecule that combines a label and a dropdown into a single component.
 * 
 * @param {object} props - The component props.
 * @param {string} props.label - The label text for the dropdown.
 * @param {string | number} props.value - The selected value for the dropdown.
 * @param {function} props.onChange - Callback function for when the dropdown value changes.
 * @param {Array<{label: string, value: string | number}>} props.options - Array of options to display in the dropdown.
 * @param {boolean} props.required - If the dropdown field is required.
 * @param {boolean} props.disabled - If the dropdown field is disabled.
 * @param {boolean} props.fullWidth - If the dropdown should be full width.
 * @param {string} props.size - Size of the dropdown ('small', 'medium', 'large').
 * @param {object} props.sx - Additional styles for the component.
 * @returns {JSX.Element} The combined dropdown and label component.
 */
const DropdownWithLabel = ({
  label,
  value,
  onChange,
  options,
  required = false,
  disabled = false,
  fullWidth = true,
  size = "small",
  sx = {},
}) => {
  return (
    <Box sx={{ display: "flex", flexDirection: "column", ...sx }}>
      <Label text={label} required={required} sx={{ mb: 1 }} />
      <DropdownAtom
        value={value}
        onChange={onChange}
        options={options}
        required={required}
        disabled={disabled}
        fullWidth={fullWidth}
        size={size}
      />
    </Box>
  );
};

DropdownWithLabel.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  onChange: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
    })
  ).isRequired,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  fullWidth: PropTypes.bool,
  size: PropTypes.oneOf(["small", "medium", "large"]),
  sx: PropTypes.object,
};

export default DropdownWithLabel;
