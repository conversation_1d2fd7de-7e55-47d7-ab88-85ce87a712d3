// @ts-nocheck
import * as React from "react";
import PropTypes from "prop-types";
import Tabs from "@mui/material/Tabs";
import Tab from "@mui/material/Tab";
import Box from "@mui/material/Box";

// Reusable TabPanel component

/**
 * A functional component that renders the content of a single tab panel.
 *
 * @component
 * @param {Object} props - Component props.
 * @param {React.ReactNode} props.children - The content to be rendered inside the TabPanel.
 * @param {number} props.value - The current selected tab index.
 * @param {number} props.index - The index of the TabPanel.
 * @param {Object} [props.other] - Additional props passed to the Box component.
 * @returns {JSX.Element|null} - The rendered TabPanel component or null if not visible.
 */
function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`tabpanel-${index}`}
      aria-labelledby={`tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </Box>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

// Utility function for accessibility props
function a11yProps(index) {
  return {
    id: `tab-${index}`,
    "aria-controls": `tabpanel-${index}`,
  };
}
/**
 * A functional component that renders a tabbed interface with multiple tabs.
 * @param {Object} props - Component props.
 * @param {number} [props.selectedTab=0] - The selected tab index.
 * @param {Function} [props.onTabChange] - Callback when tab is changed.
 * @param {Object[]} props.tabs - Array of tabs.
 * @param {string} [props.orientation='horizontal'] - Tab orientation.
 * @param {string} [props.variant='scrollable'] - Tab variant.
 * @param {string} [props.indicatorColor='primary'] - Indicator color.
 * @param {string} [props.textColor='primary'] - Tab text color.
 * @param {Object} [props.tabContentSx={}] - Styles for the TabPanel content.
 * @param {Object} [props.tabSx={}] - Styles for the Tabs.
 * @returns {JSX.Element} - The rendered TabMolecule component.
 */
// Tab Molecule Component
const TabMolecule = ({
  selectedTab = 0,
  onTabChange,
  tabs,
  orientation = "horizontal",
  variant = "scrollable",
  indicatorColor = "primary",
  textColor = "primary",
  tabContentSx = {},
  tabSx = {},
}) => {
  const [value, setValue] = React.useState(selectedTab);

  const handleChange = (event, newValue) => {
    setValue(newValue);
    if (onTabChange) onTabChange(newValue);
  };
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: orientation === "vertical" ? "row" : "column",
      }}
    >
      <Tabs
        orientation={orientation}
        variant={variant}
        value={value}
        onChange={handleChange}
        indicatorColor={indicatorColor}
        textColor={textColor}
        sx={{
          borderRight: orientation === "vertical" ? 1 : 0,
          borderColor: "divider",
          ...tabSx,
        }}
      >
        {tabs.map((tab, index) => (
          <Tab
            key={index}
            disabled={tab.tab.disabled ? true : false}
            label={tab.tab.label}
            {...a11yProps(index)}
          />
        ))}
      </Tabs>

      {tabs.map((tab, index) => (
        <TabPanel key={index} value={value} index={index} sx={tabContentSx}>
          {tab.component}
        </TabPanel>
      ))}
    </Box>
  );
};

TabMolecule.propTypes = {
  selectedTab: PropTypes.number, // The selected tab index
  onTabChange: PropTypes.func, // Callback when tab is changed
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string, // Tab label
      component: PropTypes.element, // Optional: Component to render in TabPanel
      content: PropTypes.node, // Optional: Content to render if component is not provided
    })
  ).isRequired, // Array of tabs
  orientation: PropTypes.oneOf(["horizontal", "vertical"]), // Tab orientation
  variant: PropTypes.oneOf(["standard", "scrollable", "fullWidth"]), // Tab variant
  indicatorColor: PropTypes.string, // Indicator color
  textColor: PropTypes.string, // Tab text color
  tabContentSx: PropTypes.object, // Styles for the TabPanel content
  tabSx: PropTypes.object, // Styles for the Tabs
};

export default TabMolecule;
