import PropTypes from "prop-types";
import { FormGroup } from "@mui/material";
import { Checkbox } from "../atoms/Checkbox"; // Reusing the atom component
import React from "react";

/**
 * A molecule component that renders a group of checkboxes by reusing the GlobalCheckbox component.
 *
 * This component takes an array of checkbox options where each checkbox can have its own
 * label, checked state, and change handler. It supports dynamic rendering of multiple
 * checkboxes and is flexible for use in various forms and components.
 *
 * @component
 * @param {Object} props - The component props.
 * @param {Object[]} props.options - The array of checkbox options.
 * @param {function} props.onChange - Function to handle change events for each checkbox.
 * @param {string} props.color - The color of the checkboxes (default: "primary").
 * @param {string} props.size - The size of the checkboxes (default: "medium").
 * @param {object} props.sx - MUI sx style object for custom styling.
 * @param {boolean} props.row - Whether to display the checkboxes in a row (inline).
 * @returns {JSX.Element} - The rendered group of checkboxes.
 */
const CheckboxGroup = ({ options, onChange, color = "primary", size = "medium", sx = {}, row = false }) => (
  <FormGroup row={row}>
    {options.map((option, index) => (
      <Checkbox
        key={index}
        checked={option.checked}
        onChange={(event) => onChange(event, option.value)}
        label={option.label}
        color={color}
        size={size}
        sx={sx}
      />
    ))}
  </FormGroup>
);

CheckboxGroup.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired,
      checked: PropTypes.bool.isRequired,
    })
  ).isRequired,
  onChange: PropTypes.func.isRequired,
  color: PropTypes.oneOf(["primary", "secondary", "default"]),
  size: PropTypes.oneOf(["small", "medium"]),
  sx: PropTypes.object,
  row: PropTypes.bool, // New prop to control inline rendering
};

export { CheckboxGroup };
