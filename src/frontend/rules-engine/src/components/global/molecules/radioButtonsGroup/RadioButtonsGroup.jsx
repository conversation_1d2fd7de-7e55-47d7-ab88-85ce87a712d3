import PropTypes from "prop-types";
import { FormGroup, FormLabel } from "@mui/material";
import { RadioButton } from "../atoms/RadioButton"; // Reusing the atom component
import React from "react";

/**
 * A reusable radio button group component that can be used globally.
 *
 * This component allows you to render a group of radio buttons with dynamic options
 * and supports various customization options. It helps in managing the selected state
 * of radio buttons and provides a clean interface for use in forms.
 *
 * @component
 * @param {object} props - The component props.
 * @param {string} props.label - The label to display for the radio group (optional).
 * @param {string} props.value - The current selected value.
 * @param {function} props.onChange - Function to handle changes to the selected option.
 * @param {Array<{label: string, value: string}>} props.options - The array of options to display in the radio group.
 * @param {object} props.sx - Material-UI sx style object for custom styling.
 * @returns {JSX.Element} - The rendered group of radio buttons.
 */
const RadioButtonsGroup = ({
  label = "",
  value,
  onChange,
  options = [],
  sx = {},
}) => (
  <FormGroup sx={sx}>
    {label && <FormLabel>{label}</FormLabel>}
    {options.map((option) => (
      <RadioButton
        key={option.value}
        label={option.label}
        checked={value === option.value}
        onChange={onChange}
        value={option.value}
      />
    ))}
  </FormGroup>
);

RadioButtonsGroup.propTypes = {
  label: PropTypes.string,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired,
    })
  ).isRequired,
  sx: PropTypes.object,
};

export { RadioButtonsGroup };
