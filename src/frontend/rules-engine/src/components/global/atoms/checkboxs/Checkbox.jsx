import PropTypes from "prop-types";
import { Checkbox as Ck<PERSON>ox, FormControlLabel } from "@mui/material";
import React from "react";

/**
 * A globally reusable and configurable Checkbox component.
 *
 * This component renders a Material-UI Checkbox with optional label and
 * supports various customization options, including checked state,
 * change handler, label, size, and color.
 *
 * @component
 * @param {object} props - The component props.
 * @param {boolean} props.checked - The checked state of the checkbox.
 * @param {function} props.onChange - The function to handle the change event.
 * @param {string} props.label - The label to display next to the checkbox.
 * @param {string} props.color - The color of the checkbox (default: "primary").
 * @param {string} props.size - The size of the checkbox (default: "medium").
 * @param {object} props.sx - Custom styles using MUI's sx prop.
 * @returns {JSX.Element} - The rendered checkbox component.
 */
const Checkbox = ({
  checked = false,
  onChange,
  label = "",
  color = "primary",
  size = "medium",
  sx = {},
  
}) => (
  <FormControlLabel
    control={
      <CkBox
        checked={checked}
        onChange={onChange}
        color={color}
        size={size}
        sx={sx}
        inputProps={{ "aria-label": label }}
      />
    }
    label={label}
  />
);

Checkbox.propTypes = {
  checked: PropTypes.bool,
  onChange: PropTypes.func.isRequired,
  label: PropTypes.string,
  color: PropTypes.oneOf(["primary", "secondary", "default"]),
  size: PropTypes.oneOf(["small", "medium"]),
  sx: PropTypes.object,
};

export { Checkbox };
