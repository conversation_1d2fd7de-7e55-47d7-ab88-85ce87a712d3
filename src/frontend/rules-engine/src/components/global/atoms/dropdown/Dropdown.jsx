import PropTypes from "prop-types";
import {
  Select,
  FormControl,
  InputLabel,
  Box,
  MenuItem,
  FormHelperText,
} from "@mui/material";
import { useEffect } from "react";
import React from "react";
import { useDispatch } from "react-redux";
import { registerConfirmation } from "../dialog/Index";
import { openDialog } from "../../../../redux/slices/global/confirmation";

/**
 * A reusable dropdown (select) component that can be used globally.
 *
 * @component
 * @param {object} props - The component props.
 * @param {string} [props.label = ""] - The label to display for the dropdown (optional).
 * @param {object} [props.selectedItem = null] - The current selected item (with both label and value).
 * @param {function} props.onChange - Function to handle changes to the selected option.
 * @param {Array<{label: string, value: string | number}>} props.options - The array of options to display in the dropdown.
 * @param {boolean} [props.required = false] - Whether the dropdown is required.
 * @param {boolean} [props.disabled = false] - Whether the dropdown is disabled.
 * @param {object} props.validation - The validation object for the dropdown.
 * @param {boolean} [props.validation.isShowError = false] - Flag to show error message.
 * @param {string} [props.validation.error = null] - The error message to display.
 * @param {string} [props.name = ""] - The name of the dropdown.
 * @param {boolean} [props.fullWidth = true] - Whether the dropdown should take the full width of its container.
 * @param {string} [props.size = "small"] - The size of the dropdown: small, medium, or large.
 * @param {object} [props.sx = {}] - Material-UI sx style object for custom styling.
 * @param {function} [props.onDisabledClick = null] - Function to handle clicks when the dropdown is disabled.
 * @param {boolean} [props.isDialogShowBeforeChange = false] - Flag to show dialog before change.
 * @param {object} [props.dialog = {title: "Change Item", message: "Do you want to change the item?"}] - The dialog object.
 * @returns {JSX.Element} - The rendered dropdown component.
 */
export const DropdownAtom = ({
  id,
  label = "",
  selectedItem = null,
  onChange,
  options,
  required = false,
  disabled = false,
  fullWidth = true,
  size = "small",
  name = "",
  onDisabledClick,
  sx = {},
  validation = { isShowError: false, error: null },
  isDialogShowBeforeChange = false,
  dialog = {
    title: "Change Item",
    message: "Do you want to change the item?",
  },
}) => {
  const labelId = `${label.replace(/\s+/g, "-").toLowerCase()}-label`;
  selectedItem = selectedItem?.value ? selectedItem.value : selectedItem;
  const dispatch = useDispatch();

  // Handle selection changes
  const handleDropdownChange = (event) => {
    const selectedValue = event.target.value;
    if (isDialogShowBeforeChange) {
      const confirmationId = registerConfirmation((confirmed) => {
        if (confirmed) {
          onChange(id || name, selectedValue);
        } else {
        }
      });
      dispatch(
        openDialog({
          ...dialog,
          confirmationId,
        })
      );
      return;
    }

    onChange(id || name, selectedValue);
  };
  // Add a wrapper handler for clicks
  const handleClick = (event) => {
    if (disabled && onDisabledClick) {
      onDisabledClick();
      event.preventDefault();
      return;
    }
  };
  // On options change then validate the dropdown field
  useEffect(() => {
    const selectedOption = options.find(
      (option) => option.value === selectedItem
    );
    // if (!selectedOption) {
    //   onChange(name, null, "Invalid selection");
    // }
  }, [options, selectedItem]);

  return (
    <Box
      sx={{ minWidth: 120, width: "100%", backgroundColor: "bgPrimary.main" }}
      onClick={handleClick}
    >
      <FormControl
        fullWidth={fullWidth}
        size={size}
        sx={sx}
        error={validation.isShowError}
      >
        {label && <InputLabel id={labelId}>{label}</InputLabel>}
        <Select
          labelId={labelId}
          id={`${labelId}-select`}
          value={selectedItem}
          onChange={handleDropdownChange}
          required={required}
          disabled={disabled}
          label={label}
          displayEmpty
        >
          {options.map((option) => (
            <MenuItem key={option.value} value={option.value}>
              {`${option.label} 
              
             
              `}
              {/* ${ use this description part above with option.lable if needed
                option.description ? `(${option.description})` : ""
              } */}
            </MenuItem>
          ))}
        </Select>
        {validation.isShowError && (
          <FormHelperText error>{validation.error}</FormHelperText>
        )}
      </FormControl>
    </Box>
  );
};

DropdownAtom.propTypes = {
  label: PropTypes.string,
  selectedItem: PropTypes.shape({
    label: PropTypes.string.isRequired,
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  }).isRequired,
  onChange: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
        .isRequired,
    })
  ).isRequired,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  fullWidth: PropTypes.bool,
  size: PropTypes.oneOf(["small", "medium", "large"]),
  sx: PropTypes.object,
  name: PropTypes.string,
};

export default DropdownAtom;
