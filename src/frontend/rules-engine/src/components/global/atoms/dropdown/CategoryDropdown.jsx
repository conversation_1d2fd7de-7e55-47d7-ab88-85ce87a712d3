import React, { useEffect, useRef, useState } from "react";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import ListSubheader from "@mui/material/ListSubheader";
import FormControl from "@mui/material/FormControl";
import Select from "@mui/material/Select";
import TextField from "@mui/material/TextField";
import { Menu, Box, IconButton, Paper, Divider } from "@mui/material";
import ArrowDropDownIcon from "@mui/icons-material/ArrowDropDown";
import { TypographyAtom } from "../typography/Typography"; // Assuming this is your custom typography component
import DynamicIcon from "../icons/Index";
import { InputField } from "../inputFields/InputField";
import { campaignValidators, Helpers } from "../../../../utils";

/**
 *
 * @param {Object} props
 * @param {import("../../../../../jsDocs/global/global").ValidationObj} props.value - The value of the dropdown.
 * @param { (name: string, value: string)=> void} props.onChange - Callback with the value data, including name and value object.
 * @param {(name: string, value: {value: string | number,isValid: boolean, error: string, isShowError: boolean })=> void} props.onChangeFormated - Callback with the formated value object
 * @param {string} props.label - The label for the dropdown.
 * @param {boolean} [props.showBooleanAndNulls=false] - Flag to show boolean and null value options at the top of the dropdown.
 * @param {string} [props.value] - The current value of the dropdown.
 * @param {string} props.name - The name of the dropdown.
 * @param {string} props.key - The parameter id of the dropdown.
 * @param {"number" | "string"} [props.type] - The field type. It will validate accordingly and restrict the input to the type.
 *
 * @param {string} [props.value] - The current value of the dropdown.
 * @param {"string" | "number"} [props.valueType] - The value type of the field
 * @param {Array<{label: string, value: string}>} props.categories - Array of categories to display in the dropdown.
 * @param {boolean} [props.isCustomInputDisabled] - Flag to indicate if the custom input is disabled.
 * @param {() => void} [props.onClick] - Optional click handler for the dropdown.
 * @returns {JSX.Element} - The rendered dropdown component.
 */
const CategoryDropdown = ({
  id,
  categories,
  label,
  onChange,
  onChangeFormated,
  showBooleanAndNulls = false,
  value = "",
  required = false,
  category = "custom",
  name,
  isCustomInputDisabled = false,
  valueType,
  onClick,
  onFocus,
}) => {
  const [inputValue, setInputValue] = useState({});
  const [openMenu, setOpenMenu] = useState(null);

  const handleInputChange = (event) => {
    let value = event.target.value;

    const newResult = getFieldResult(value);
    setInputValue((prev) => ({ ...prev, ...newResult }));
    callCallBacks(newResult);
  };

  const getFieldResult = (value) => {
    // if (validator && validateOn === "change") {
    //   const validationResult = validator(value);

    //   return {
    //     value,
    //     isValid: validationResult.success,
    //     error: validationResult.message,
    //     isShowError: !validationResult.success,
    //   };
    // }
    const fieldValidated = value !== "";
    if (value == null) {
      return {
        value: value,
        isValid: true,
        error: "",
      };
    }
    if (value == true) {
      return {
        value: value,
        isValid: true,
        error: "",
      };
    }
    if (value == false) {
      return {
        value: value,
        isValid: true,
        error: "",
      };
    }
    if (value == "") {
      return {
        value: value,
        isValid: true,
        error: "",
      };
    }

    // If the value type is the string then return as it is because it is string
    if (valueType && valueType.toString().toLowerCase() == "string") {
      value = value.toString();
    } else {
      // Auto cast it

      // Right cast the value
      value = Helpers.getNumberOrString(value);
    }

    // if the value

    return {
      value,
      isValid: fieldValidated,
      error: fieldValidated ? "" : `${name} field is required`,
    };
  };

  const callCallBacks = (value) => {
    // Call on change with the value
    onChange && onChange(id || name, value.value);

    // Call if the onChangeFormated is provided
    onChangeFormated && onChangeFormated(id || name, value);
  };

  const handleInputBlur = () => {
    // Call the call backs
    // callCallBacks(inputValue);

    setTimeout(() => {
      setOpenMenu(false);
    }, [200]);
  };

  const handleOptionClick = (value, category) => {
    // Call the handle input change
    handleInputChange({ target: { value } });

    // Get the result of the field
    const newResult = getFieldResult(value);

    // Call the call backs
    callCallBacks(newResult);

    // Close the menu
    setOpenMenu(false);
  };
  // Handle clicks outside the dropdown to close the menu

  // On value change then update the input value. Usually the value changes when we have the error and we want to show
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  return (
    <Box fullWidth>
      <TextField
        autoComplete="off"
        label={label}
        value={inputValue.value}
        onClick={onClick ? onClick : null}
        onChange={isCustomInputDisabled ? null : handleInputChange}
        onBlur={handleInputBlur}
        onFocus={() => {
          setOpenMenu(true);
          onFocus && onFocus();
        }}
        error={inputValue.isShowError}
        helperText={inputValue.isShowError ? inputValue.error : ""}
        fullWidth
        size="small"
        variant="outlined"
        required={required}
        slotProps={{
          // Show the label if the value is empty, undefined or null
          inputLabel: {
            shrink:
              inputValue.value !== "" &&
              // inputValue.value !== null &&
              inputValue.value !== undefined,
          },
        }}
      />
      {openMenu && (
        <Box
          sx={{
            // width: "100%",
            position: "absolute",
            zIndex: 100,
            backgroundColor: "white",
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "4px",
            boxShadow:
              "0px 2px 5px rgba(0, 0, 0, 0.1), 0px 3px 8px rgba(0, 0, 0, 0.2)",
            mt: 1,
            overflowY: "auto",
          }}
        >
          {showBooleanAndNulls && (
            <Box sx={{ backgroundColor: "white !important" }}>
              <Divider />
              <ListSubheader sx={{ textAlign: "left", fontWeight: "bold" }}>
                Boolean Values
              </ListSubheader>
              <Divider />
              {[
                { label: "True", value: true },
                { label: "False", value: false },
              ].map((option) => (
                <Box
                  onClick={() =>
                    handleOptionClick(option.value, "Boolean Values")
                  }
                  onMouseDown={(e) =>
                    handleOptionClick(option.value, "Boolean Values")
                  }
                  key={`boolean-null-${option.label}`}
                  sx={{
                    padding: "10px",
                    cursor: "pointer",
                    textAlign: "left",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    zIndex: 1000,
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)",
                    },
                  }}
                >
                  <TypographyAtom
                    sx={{ textTransform: "capitalize" }}
                    text={`\u00A0 \u00A0 - ${option.label}`}
                  />
                  {String(option.value) === String(value) && (
                    <DynamicIcon iconName={"Check"} />
                  )}
                </Box>
              ))}
            </Box>
          )}
          {categories.map((categoryItem) => (
            <Box
              sx={{ backgroundColor: "white !important" }}
              key={categoryItem.category}
            >
              <Divider />
              <ListSubheader sx={{ textAlign: "left", fontWeight: "bold" }}>
                {categoryItem.category}
              </ListSubheader>
              <Divider />
              {categoryItem.options.map((option) => (
                <Box
                  onClick={() => {
                    handleOptionClick(option.value, categoryItem.category);
                  }}
                  onMouseDown={(e) => {
                    // Prevents the TextField from blurring
                    // e.preventDefault();
                    handleOptionClick(option.value, categoryItem.category);
                  }}
                  key={`${option.value}-${categoryItem.category}`}
                  sx={{
                    padding: "10px",
                    cursor: "pointer",
                    textAlign: "left",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    zIndex: 1000,
                    "&:hover": {
                      backgroundColor: "rgba(0, 0, 0, 0.1)", // Add hover effect
                    },
                  }}
                >
                  <TypographyAtom
                    sx={{ textTransform: "capitalize" }}
                    text={`\u00A0 \u00A0 - ${option.label}`}
                  />

                  {categoryItem.category === category &&
                    option.value === value && (
                      <DynamicIcon iconName={"Check"} />
                    )}
                </Box>
              ))}
            </Box>
          ))}
        </Box>
      )}
    </Box>
  );
};

export default CategoryDropdown;
