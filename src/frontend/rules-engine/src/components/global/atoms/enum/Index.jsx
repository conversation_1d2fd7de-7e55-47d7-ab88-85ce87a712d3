import React, { useState } from "react";
import PropTypes from "prop-types";
import {
  Box,
  Button,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import { Btn as Btn<PERSON>tom } from "../buttons/Button";
import DynamicIcon from "../icons/Index";
import { InputField } from "../inputFields/InputField";
import { TypographyAtom } from "../typography/Typography";
/**
 * EnumManager (Array) component for managing and displaying a list of enum values.
 * Provides the ability to add, view, and delete values from the list.
 *
 * @component
 * @param {Object} props - The component props.
 * @param {Array<string>} props.values - Initial list of enum values to display.
 * @param {function} props.onAddNewValue - Callback to handle the addition of a new enum value.
 * @param {function} props.onDeleteValue - Callback to handle the deletion of an enum value.
 * @param {string} [props.mainHeading="Array Values"] - The heading text displayed at the top of the component.
 * @param {Object} [props.sx={}] - Custom styles for different parts of the component.
 * @returns {JSX.Element} The rendered EnumManager component.
 */
const EnumManager = ({
  values = [],
  onAddNewValue,
  onDeleteValue,
  mainHeading = "Array Values",
  sx = {
    mainContainer: {},
  },
}) => {
  const [enumValues, setEnumValues] = useState(values);
  const [isAdding, setIsAdding] = useState(false);
  const [newValue, setNewValue] = useState("");
  /**
   * Converts a string input to the appropriate data type (number, boolean, or string)
   * @param {string} value - The string value to convert
   * @returns {any} - The converted value with appropriate type
   */
  const convertToProperType = (value) => {
    const trimmedValue = value.trim();

    // Check if it's a boolean
    if (trimmedValue.toLowerCase() === "true") return true;
    if (trimmedValue.toLowerCase() === "false") return false;

    // Check if it's a number
    const numberValue = Number(trimmedValue);
    if (!isNaN(numberValue) && trimmedValue !== "") return numberValue;

    // Otherwise, return as string
    return trimmedValue;
  };
  /**
   * Handles adding a new value to the list.
   */
  const handleAddNew = () => {
    if (newValue.trim()) {
      const typedValue = convertToProperType(newValue);
      onAddNewValue(typedValue);
      setNewValue("");
      setIsAdding(false);
    }
  };

  const handleDelete = (indexToDelete) => {
    onDeleteValue(indexToDelete);
    // setEnumValues(enumValues.filter((_, index) => index !== indexToDelete));
  };

  return (
    <Box
      sx={{
        width: "100%",
        // maxWidth: "800px",
        mx: "auto",
        // p: 2,
        spaceY: 2,
        ...sx.mainContainer,
      }}
    >
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={2}
      
      >
        <TypographyAtom text={mainHeading} variant="h6" fontWeight="bold" />
        {!isAdding && (
          <BtnAtom text="Add" onClick={() => setIsAdding(true)} />
        )}
      </Box>

      <TableContainer
        component={Paper}
        sx={{ borderRadius: 1, overflow: "hidden", width: "100%" }}
      >
        <Table>
          <TableHead sx={{ bgcolor: "grey.100", px: 1 }}>
            <TableRow>
              <TableCell sx={{ fontWeight: "bold", padding: "0px 5px" }}>
                Value
              </TableCell>
              <TableCell
                align="right"
                sx={{ fontWeight: "bold", width: "80px", padding: "10px 5px" }}
              >
                Actions
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {isAdding && (
              <TableRow sx={{ padding: "0px", px: 1 }}>
                <TableCell sx={{ padding: 0 }}>
                  <InputField
                    fullWidth
                    value={newValue}
                    onChange={(e) => setNewValue(e.target.value)}
                    sx={{ padding: 0 }}
                    placeholder="Enter new value"
                    onKeyPress={(e) => e.key === "Enter" && handleAddNew()}
                    variant="outlined"
                  />
                </TableCell>
                <TableCell sx={{ padding: "0px 10px" }} align="right">
                  <Box display="flex" justifyContent="flex-end" gap={1}>
                    <IconButton
                      onClick={handleAddNew}
                      color="success"
                      disabled={!newValue.trim()}
                    >
                      <DynamicIcon iconName="Save" />
                    </IconButton>
                    <IconButton
                      onClick={() => {
                        setIsAdding(false);
                        setNewValue("");
                      }}
                      color="error"
                    >
                      <DynamicIcon iconName="Close" />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            )}
            {values.map((value, index) => (
              <TableRow
                sx={{ padding: "0px", maxHeight: "20px" }}
                key={index}
                hover
              >
                <TableCell
                  sx={{ padding: "0px", pl: 1 }}
                >{`${value}`}</TableCell>
                <TableCell sx={{ padding: "0px" }} align="right">
                  <IconButton onClick={() => handleDelete(index)} color="error">
                    <DynamicIcon iconName="Delete" />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {values.length === 0 && !isAdding && (
        <Typography align="center" sx={{ py: 4, color: "grey.500" }}>
          No Array values added yet. Click "Add" to start.
        </Typography>
      )}
    </Box>
  );
};

// Define PropTypes for the EnumManager component
EnumManager.propTypes = {
  values: PropTypes.arrayOf(PropTypes.string),
  onAddNewValue: PropTypes.func.isRequired,
  onDeleteValue: PropTypes.func.isRequired,
  mainHeading: PropTypes.string,
  sx: PropTypes.shape({
    mainContainer: PropTypes.object,
  }),
};

export default EnumManager;
