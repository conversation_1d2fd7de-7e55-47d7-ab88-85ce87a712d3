import { useSelector } from "react-redux";
import Stack from "@mui/material/Stack";
import { Alert } from "@mui/material";
import React from "react";
/**
 * AlertStack - Displays a list of alerts from the Redux store.
 */
/**
 * AlertStack component renders a stack of alert messages.
 *
 * This component uses the `useSelector` hook to access the alerts from the Redux store.
 * It displays each alert in a fixed position at the top center of the screen.
 *
 * @component
 * @example
 * // Example usage:
 * // Assuming the Redux store has been set up and contains alerts in the state.
 * <AlertStack />
 *
 * @returns {JSX.Element} A stack of alert messages.
 */
const AlertStack = () => {
  /**
   * Redux store state object.
   * @type {import("../../../../../jsDocs/redux/store.js").Store["alert"]["alerts"]}
   */
  const alerts = useSelector((state) => state.alerts.alerts);

  return (
    <Stack
      sx={{
        maxWidth: "400px",
        // width: "100%",
        minWidth: "250px",
        position: "fixed", // Fix position to top of the screen
        top: "10px", // Adjust to your preferred spacing from the top
        left: "50%", // Center horizontally
        transform: "translateX(-50%)", // Offset to truly center
        zIndex: 9999999, // Make sure it appears above other content
      }}
      spacing={2}
    >
      {alerts.map((alert) => (
        <Alert
          key={alert.id}
          style={{ fontSize: "16px" }}
          severity={alert.severity}
        >
          {alert.message}
        </Alert>
      ))}
    </Stack>
  );
};
export default AlertStack;
