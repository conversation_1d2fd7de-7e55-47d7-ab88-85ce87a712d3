import React from 'react';
import PropTypes from 'prop-types';
import Backdrop from '@mui/material/Backdrop';
import CircularProgress from '@mui/material/CircularProgress';
import Typography from '@mui/material/Typography';

/**
 * A Material-UI Backdrop component that is used to block the user
 * interaction and display a loading animation.
 *
 * @component
 * @param {object} props - The component props.
 * @param {boolean} props.open - Whether to show the backdrop.
 * @param {function} [props.onClose] - The function to call when the backdrop is clicked.
 * @param {string} [props.color='#fff'] - The color of the CircularProgress and message.
 * @param {string} [props.message] - The message to show in the backdrop.
 * @returns {JSX.Element} - The rendered backdrop.
 */
const LoadingBackdropAtom = ({ open, onClose, color = '#fff', message }) => {
  return (
    <Backdrop
      sx={(theme) => ({ color, zIndex: theme.zIndex.drawer + 1, display: 'flex', flexDirection: 'column' })}
      open={open}
      onClick={onClose || null}
    >
      <CircularProgress color="inherit" />
      {message && (
        <Typography variant="h6" sx={{ mt: 2, color: color }}>
          {message}
        </Typography>
      )}
    </Backdrop>
  );
};

LoadingBackdropAtom.propTypes = {
  open: PropTypes.bool.isRequired,        // Controls the visibility of the backdrop
  onClose: PropTypes.func,                // Function to handle backdrop click (optional)
  color: PropTypes.string,                // Color of the CircularProgress and message
  message: PropTypes.string               // Optional message displayed below the loader
};

export default LoadingBackdropAtom;
