import { Menu, MenuItem, ListItemIcon, Typography } from "@mui/material";
import PropTypes from "prop-types";
import DynamicIcon from "../icons/Index"; // Assume this dynamically renders icons based on name
import React from "react";

/**
 * Reusable ActionMenu component
 * @param {object} props - Component props
 * @param {Array} props.menuItems - Array of menu items with optional icon and label.
 * @param {object} props.anchorEl - The element to anchor the menu to.
 * @param {boolean} props.open - Whether the menu is open.
 * @param {function} props.onClose - Function to close the menu.
 * @param {function} props.onMenuItemClick - Callback function for menu item click event.
 * @param {object} props.sxMenu - Custom MUI sx styles for the menu.
 * @param {object} props.sxMenuItem - Custom MUI sx styles for the menu item.
 * @param {object} props.sxIcon - Custom MUI sx styles for the menu item icon.
 * @param {object} props.sxLabel - Custom MUI sx styles for the menu item label.
 * @returns {JSX.Element} Rendered ActionMenu component.
 */
const ActionMenuAtom = ({
  menuItems,
  anchorEl,
  open,
  onClose,
  onMenuItemClick,
  sxMenu = {},
  sxMenuItem = {},
  sxIcon = {},
  sxLabel = {}
}) => {
  
  // Handle menu item click and pass the clicked item to the callback function
  const handleMenuItemClick = (value) => {
    onMenuItemClick(value);
    onClose(); // Close the menu after click
  };

  return (
    <Menu
      id="action-menu"
      anchorEl={anchorEl}
      sx={{ width: "100%", ...sxMenu }}
      open={open}
      onClose={onClose}
      MenuListProps={{
        "aria-labelledby": "action-button",
      }}
    >
      {menuItems.map((item, index) => (
        <MenuItem
          key={index}
          sx={{ ...sxMenuItem }}
          onClick={() => handleMenuItemClick(item.value)}
        >
          {item.icon && (
            <ListItemIcon>
              <DynamicIcon sx={{ ...sxIcon }} iconName={item.icon} />
            </ListItemIcon>
          )}
          <Typography sx={{ ...sxLabel }} variant="inherit">
            {item.label}
          </Typography>
        </MenuItem>
      ))}
    </Menu>
  );
};

ActionMenuAtom.propTypes = {
  menuItems: PropTypes.arrayOf(
    PropTypes.shape({
      icon: PropTypes.string, // Icon name as a string for dynamic rendering
      label: PropTypes.string.isRequired, // Text for the menu item
      value: PropTypes.string.isRequired, // Value for the menu item
    })
  ).isRequired,
  anchorEl: PropTypes.object, // The anchor element to which the menu is attached
  open: PropTypes.bool.isRequired, // Whether the menu is open or not
  onClose: PropTypes.func.isRequired, // Function to close the menu
  onMenuItemClick: PropTypes.func.isRequired, // Function to call on menu item click
  sxMenu: PropTypes.object, // Custom styles for the menu
  sxMenuItem: PropTypes.object, // Custom styles for the menu item
  sxIcon: PropTypes.object, // Custom styles for the menu item icon
  sxLabel: PropTypes.object, // Custom styles for the menu item label
};

export default ActionMenuAtom;
