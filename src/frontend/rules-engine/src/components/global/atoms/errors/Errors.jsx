import React from 'react';
import { Typo<PERSON><PERSON><PERSON> } from "../typography/Typography";
import { Box } from "@mui/material";
import { Btn } from '../buttons/Button';
/**
 * This component displays an error message in a box.
 * It uses the TypographyAtom component to display the error message.
 * It also uses the Btn component to display a button that refreshes the page.
 * 
 * @param {Object} props - The component props.
 * @param {string} props.errorMessage - The error message to display.
 * @returns {React.ReactElement} The ErrorMessage component.
 */
export const ErrorMessage = ({ errorMessage }) => {
  return (
    <Box
    sx={{
      position:"absolute",
      // top:"30%",
      left:"50%",
      transform:"translate(-50%,-50%)",
      width:"300px",
      height:"100px",
      mt:"70px",
      
    }}
    >
      <TypographyAtom 
        variant="h6" 
        text={errorMessage} 
        sx={{
          textAlign: 'center',
          padding: '10px',
          margin: '0 auto',
          width:"300px",
          height:"100px",
          display:"flex",
          justifyContent:"center",
          alignItems:"center",
          backgroundColor: 'background.paper',
          borderRadius: '4px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          color: 'error.main'
        }}
      />
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
        <Btn
          text="Refresh Page"
          onClick={() => window.location.reload(true)}
         
        sx={{ backgroundColor: "primary.secondary" }}
        />
      </Box>
      </Box>
  );
};


