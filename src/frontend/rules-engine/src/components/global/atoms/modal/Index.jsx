import * as React from "react";
import PropTypes from "prop-types";
import Box from "@mui/material/Box";
import Modal from "@mui/material/Modal";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import { TypographyAtom } from "../typography/Typography";

// Define styles for the modal
const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 400,
  bgcolor: "background.paper",
  boxShadow: 24,
  p: 4,
};

/**
 * A reusable MUI Modal component that renders a modal with a title, content, and
 * a close button (replaced with a cross icon). It accepts the following props:
 *
 * - title (string): The title of the modal.
 * - content (JSX.Element): The content of the modal.
 * - open (boolean): Whether the modal is open (defaults to true).
 * - onClose (function): The callback to call when the modal is closed.
 * - sxModal (object): Additional styles for the Modal component.
 * - sxMainBox (object): Additional styles for the main Box component.
 * - sxContentBox (object): Additional styles for the Box component that wraps
 *   the content.
 *
 * @param {Object} props - An object of props.
 * @param {string} props.title - The title of the modal.
 * @param {JSX.Element} props.content - The content of the modal.
 * @param {boolean} props.open - Whether the modal is open. Defaults to true.
 * @param {function} props.onClose - The callback to call when the modal is closed.
 * @param {object} props.sxModal - Additional styles for the Modal component.
 * @param {object} props.sxMainBox - Additional styles for the main Box component.
 * @param {object} props.sxContentBox - Additional styles for the Box component that wraps the content.
 * @returns {JSX.Element} The rendered Modal component.
 */
const ModalAtom = ({
  title,
  content,
  open = true,
  onClose,
  sxModal = {},
  sxMainBox = {},
  sxContentBox = {},
}) => {
  const handleClose = () => {
    if (onClose) {
      onClose(); // Call the onClose callback if provided
    }
  };

  return (
    <Modal
      open={open}
      onClose={handleClose}
      aria-labelledby="modal-title"
      aria-describedby="modal-description"
      sx={{
        position: "absolute",
        width: "100%",
        height: "auto",
        ...sxModal,
      }}
    >
      <Box sx={{ ...style, ...sxMainBox, position: "absolute" }}>
        {/* Close Icon at Top Right */}
        <IconButton
          onClick={handleClose}
          sx={{
            position: "absolute",
            top: 8,
            right: 8,
          }}
        >
          <CloseIcon />
        </IconButton>

        <TypographyAtom
          id="modal-title"
          variant="h6"
          component="h2"
          text={title}
        />

        <Box sx={{ ...sxContentBox }}>{content}</Box>
      </Box>
    </Modal>
  );
};

// PropTypes for type checking
ModalAtom.propTypes = {
  title: PropTypes.string.isRequired,
  content: PropTypes.element.isRequired,
  open: PropTypes.bool,
  onClose: PropTypes.func,
  sxModal: PropTypes.object,
  sxMainBox: PropTypes.object,
  sxContentBox: PropTypes.object,
};

export default ModalAtom;
