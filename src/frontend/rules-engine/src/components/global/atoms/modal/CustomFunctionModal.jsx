import React from "react";
import { Modal, Box, Typography, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

// Basic styling for the modal content box
const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  minWidth: 500, // Ensure minimum width like before
  bgcolor: "background.paper", // Use theme's background color
  border: "1px solid #ccc",
  borderRadius: "8px", // Add some rounded corners
  boxShadow: 24, // Standard MUI shadow
  p: 0, // Remove padding here, apply to inner Box
  outline: "none", // Remove focus outline
  maxHeight: "90vh", // Limit height
  display: "flex",
  flexDirection: "column",
};

const headerStyle = {
  display: "flex",
  justifyContent: "space-between",
  alignItems: "center",
  p: 2, // Padding for header
  borderBottom: "1px solid",
  borderColor: "divider", // Use theme divider color
};

const contentStyle = {
  p: 2, // Padding for the main content area
  overflowY: "auto", // Allow scrolling for tall content
};

/**
 * A custom modal component specifically for function configuration.
 *
 * @param {Object} props - Component props.
 * @param {boolean} props.open - Controls if the modal is open.
 * @param {Function} props.onClose - Function to call when the modal should close.
 * @param {string} props.title - The title to display in the modal header.
 * @param {React.ReactNode} props.children - The content to render inside the modal.
 * @returns {React.JSX.Element}
 */
const CustomFunctionModal = ({ open, onClose, title, children }) => {
  return (
    <Modal
      open={open}
      onClose={onClose} // Allows closing by clicking backdrop or pressing Esc
      aria-labelledby="function-modal-title"
      aria-describedby="function-modal-description" // For accessibility
    >
      <Box sx={style}>
        {/* Modal Header */}
        <Box sx={headerStyle}>
          <Typography id="function-modal-title" variant="h6" component="h2">
            {title}
          </Typography>
          <IconButton aria-label="close" onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>

        {/* Modal Content */}
        <Box sx={contentStyle} id="function-modal-description">
          {children}
        </Box>
      </Box>
    </Modal>
  );
};

export default CustomFunctionModal;
