// GlobalDatePicker.js
import * as React from "react";
import PropTypes from "prop-types";
import dayjs, { Dayjs } from "dayjs";
import utc from "dayjs/plugin/utc";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { TextField } from "@mui/material";


dayjs.extend(utc);

/**
 * GlobalDatePicker component for reusable date picking across the application.
 * Can be used in both controlled and uncontrolled modes.
 *
 * @component
 * @param {object} param0 - Component props.
 * @param {string} param0.label - The label for the date picker.
 * @param {boolean} param0.controlled - Flag to indicate if the picker is controlled.
 * @param {any} param0.value - The current value of the date (used in controlled mode).
 * @param {function} param0.onChange - Function to handle date changes (used in controlled mode).
 * @param {any} param0.defaultValue - The default date (used in uncontrolled mode).
 * @param {boolean} param0.disableFuture - Option to disable future dates.
 * @param {boolean} param0.disablePast - Option to disable past dates.
 * @param {string} param0.name - The name of the input field.
 * @param {string} param0.time - Set the time of the day
 * @param {string} param0.format - The format of the date string.
 * @param {object} param0.validation - The validation object for the input field.
 * @param {boolean} param0.validation.isShowError - Flag to show error message.
 * @param {string} param0.validation.error - The error message to display.
 * @param {boolean} param0.required - Flag to indicate if the input is required.
 * @param {"date" | "dateTime"} param0.component - The component to render.
 * @returns {JSX.Element} props.The rendered date picker component.
 */

const DatePickerAtom = ({
  label,
  controlled = true,
  value = dayjs(),
  onChange,
  defaultValue = null,
  disableFuture = false,
  disablePast = true,
  name = null,
  format = "YYYY-MM-DD",
  time = null,
  validation = { isShowError: false, error: null },
  required = false,
  component = "dateTime", 
  //sx = {},
}) => {
  // If not controlled, manage internal state

  const handleDateChange = (newValue) => {
    // Get the value from the date picker in utc format
   const customDateString = newValue?  newValue.format("YYYY-MM-DDTHH:mm:ss") + "Z"  : null;;
   


    // Call the onChange function with the custom date string
    onChange(customDateString, name);

  };
 /**
  * Parse the value to ensure it's a valid dayjs object
  * @param {any} val - The value to parse
  * @returns {Dayjs} The parsed value
  */
const parseValue = (val) => {
  if (!val) return null;
  
  // If it's already a dayjs object, return it as-is
  if (dayjs.isDayjs(val)) return val;
  
  // If it's a string with Z (UTC format), remove Z and parse as local time
  if (typeof val === 'string' && val.endsWith('Z')) {
    const cleanValue = val.slice(0, -1); // Remove the 'Z'
    return dayjs(cleanValue);
  }
  
  // For any other string, parse normally
  return dayjs(val);
};
  

  const isRequired = label.includes("*") || required;
  const formatLabel = label.replace("*", "") + " (UTC)" + (isRequired ? " *" : "");

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      {component === "date" && (
        <DatePicker
          label={formatLabel}
          value={parseValue(value)}
          onChange={handleDateChange}
          disableFuture={disableFuture}
          disablePast={disablePast}
          required={required}
          format={format}
          // views={["year", "month", "day"]}
          renderInput={(params) => {
            return (
              <TextField
                name="datepicker"
                value={value}
                {...params}
                sx={{ width: "500px" }}
                error={false} // Show error state if validation fails
                helperText={true ? "validation.error" : ""} // Display custom error message
              />
            );
          }}
        />
      )}

      {component === "dateTime" && (
        <DateTimePicker
          label={formatLabel}
          value={parseValue(value)}
          onChange={handleDateChange}
          ampm={false}
          views={["year", "month", "day", "hours", "minutes"]}
          disableFuture={disableFuture}
          disablePast={disablePast}
          required={required}
          format={"YYYY/MM/DD HH:mm:ss"}
          renderInput={(params) => {
            return (
              <TextField
                name="datepicker"
                value={value}
                {...params}
                sx={{ width: "500px" }}
                error={false} // Show error state if validation fails
                helperText={true ? "validation.error" : ""} // Display custom error message
              />
            );
          }}
        />
      )}
    </LocalizationProvider>
  );
};

// PropTypes for validation
DatePickerAtom.propTypes = {
  label: PropTypes.string.isRequired,
  controlled: PropTypes.bool,
  value: PropTypes.any,
  onChange: PropTypes.func,
  defaultValue: PropTypes.any,
  disableFuture: PropTypes.bool,
  disablePast: PropTypes.bool,
};

export default DatePickerAtom;
