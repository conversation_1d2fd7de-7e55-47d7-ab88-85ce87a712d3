import React from "react";
import { Dialog, DialogActions, <PERSON>alogContent, DialogContentText, DialogTitle, But<PERSON> } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { closeDialog } from "../../../../redux/slices/global/confirmation";


let status = {
  id:"",
  isDeletedConfirmed: false
};

// Store for callbacks since we can't store functions in Redux
const confirmationCallbacks = new Map();

export const registerConfirmation = (callback) => {
  const id = Math.random().toString(36).substr(2, 9);
  confirmationCallbacks.set(id, callback);
  return id;
};
export const openConfirmationDialog = (params) => (dispatch) => {
  dispatch({
    type: "OPEN_DIALOG",
    payload: params
  });
};


/**
 * A reusable confirmation dialog component that displays a title, content, and two buttons.
 * The component will call the onClose callback when the dialog is closed and the onConfirm callback
 * when the confirm button is clicked.
 * 
 * @param {object} props - Component props.
 * @param {boolean} props.open - Whether the dialog is open or not.
 * @param {function} props.onClose - Callback when the dialog is closed.
 * @param {function} props.onConfirm - Callback when the confirm button is clicked.
 * @param {string} props.title - The title of the dialog.
 * @param {string} props.content - The content of the dialog.
 * @param {string} props.confirmText - The text of the confirm button.
 * @param {string} props.cancelText - The text of the cancel button.
 * @param {object} props.buttonStyles - Custom styles for the buttons.
 */
const ConfirmationDialog = ({ 
  // open, 
  // onClose, 
  // onConfirm, 
  // title = "Confirm Deletion", 
  // content = "Do you want to delete this?", 
  // confirmText = "Yes", 
  // cancelText = "No", 
  // buttonStyles = {} 
}) => {

  const dispatch = useDispatch();
  const { isOpen, title, message, confirmationId } = useSelector(
    (state) => state.confirmDialog
  );

  const handleConfirm = () => {
    if (confirmationId && confirmationCallbacks.has(confirmationId)) {
      confirmationCallbacks.get(confirmationId)(true);
      confirmationCallbacks.delete(confirmationId);
    }
    dispatch(closeDialog());
  };

  const handleCancel = () => {
    if (confirmationId && confirmationCallbacks.has(confirmationId)) {
      confirmationCallbacks.get(confirmationId)(false);
      confirmationCallbacks.delete(confirmationId);
    }
    dispatch(closeDialog());
  };

  return (
    <Dialog open={isOpen} onClose={handleCancel}>
      <DialogTitle>{title}</DialogTitle>
      <DialogContent>
        <DialogContentText>
          {message}
        </DialogContentText>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleCancel} color="primary" sx={{}}>
          {"Cancel"}
        </Button>
        <Button onClick={handleConfirm} color="secondary" sx={{}}>
          {"Yes"}
        </Button>
      </DialogActions>
    </Dialog>
  );
}; 

export default ConfirmationDialog;
