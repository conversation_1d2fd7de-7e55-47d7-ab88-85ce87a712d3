// src/components/global/atoms/smartValue/membership/MembershipValue.jsx
import React, { useState } from "react";
import { Box, Tabs, Tab } from "@mui/material";
import Typography from "@mui/material/Typography";
import EnumManager from "../../enum/Index";
import PropTypes from "prop-types";
import { useVariableOptions } from "../../../../../hooks/useVariableOptions";

/**
 * MembershipValue component for handling "list" and "enum" type selections
 *
 * @param {Object} props - Component props
 * @param {Array} props.listOptions - List options available for selection
 * @param {Function} props.onChange - Function to call when value changes
 * @param {Object|Array} props.value - Current value (listId object or enum array)
 * @returns {React.JSX.Element} The rendered component
 */
const MembershipValue = ({
  listOptions,
  ruleType,
  onChange,
  value,
  entityId,
  contextId, 
}) => {
  // Default to "list" tab if value has listId, otherwise default to "enum"
  const [activeTab, setActiveTab] = useState(() => {
    if (Array.isArray(value)) return "array";
    if (value && typeof value === "object") return "reference";

    return "reference";
  } );
 



  const { generateVariablesOptions } = useVariableOptions(
    entityId,
    contextId,
    ruleType
  );

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    // Initialize appropriate value type when switching tabs
    if (newValue === "list") {
      onChange({ listId: "" });
    } else if (newValue === "array") {
      onChange([]);
    }
  };

  const handleListOptionClick = (option) => {
    onChange({ listId: option.value });
  };

  const handleOptionClick = (option) => {
    // onChange({ transactionVariable: option.value });
    onChange(option.value);
  };

  const filteredOptions = generateVariablesOptions('all')
  .flat()
  .map((group) => ({
    ...group,
    options: group.options.filter((option) => (option.type === "array")),
  })) 
  .filter((group) => group.options.length > 0);

  return (
    <Box sx={{ width: "100%" }}>
      <Box sx={{ borderBottom: 1, borderColor: "divider", mb: 1 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          
          sx={{
            "& .MuiTab-root": {
              textTransform: "none",
              fontWeight: "medium",
              fontSize: "1.2rem",
            },
            "& .Mui-selected": {
              color: "secondary.main",
              fontWeight: "bold",
            },
            "& .MuiTabs-indicator": {
              backgroundColor: "secondary.main",
            },
          }}
        >
          <Tab label="Reference" value="reference" />
          <Tab label="Array" value="array" />
        </Tabs>
      </Box>

      {activeTab === "reference" && (
        <Box sx={{ minWidth: 300, padding: 2 }}>
          {value?.listId && (
            <Box
              sx={{
                marginBottom: 2,
                fontWeight: "bold",
                color: "error.main",
                fontSize: "1.2em",
              }}
            >
              List Id: {value.listId}
            </Box>
          )}
          {value && typeof value === "string"&& (
            <Box
              sx={{
                marginBottom: 2,
                fontWeight: "bold",
                color: "error.main",
                fontSize: "1.2em",
              }}
            >
              Transaction Variable: {value}
            </Box>
          )}
          <Box component="ul" sx={{ listStyleType: "none", padding: 0 }}>
            <Box component="li" sx={{ marginBottom: 2 }}>
              <Typography
                variant="subtitle1"
                sx={{
                  color: "white.main",
                  fontWeight: "bold",
                  fontSize: "14px",
                  backgroundColor: "black",
                  padding: "0.5px 1px",
                  textAlign: "center",
                }}
              >
                 Lists
              </Typography>
              <Box component="ul" sx={{ listStyleType: "none", padding: 0 }}>
                {listOptions.map((option) => (
                  <Box
                    component="li"
                    key={option.value}
                    onClick={() => handleListOptionClick(option)}
                    // sx={{
                    //   cursor: "pointer",
                    //   padding: "8px 12px",
                    //   margin: "4px 0",
                    //   borderRadius: 1,
                    //   transition: "background-color 0.3s, color 0.3s",
                    //   bgcolor: "grey.100",
                    //   color: "text.primary",
                    //   fontSize: "0.9em",
                    //   "&:hover": {
                    //     bgcolor: "grey.200",
                    //   },
                    //   ...(value?.listId === option.value && {
                    //     bgcolor: "#E0F7FA", // cyan.50 equivalent
                    //     color: "#006064", // cyan.800 equivalent
                    //   }),
                    // }}
                    sx={{
                      cursor: "pointer",
                      padding: "8px 12px",
                      margin: "4px 0",
                      borderRadius: 1,
                      // transition: "background-color 0.3s, color 0.3s",
                      bgcolor: "grey.100",
                      // color: "text.primary",
                      fontSize: "0.9em",
                      px: 2,
                      py: 1,
                      borderLeft:
                        value?.listId === option?.value
                          ? "4px solid"
                          : "none",
                      borderColor:
                        value?.listId === option?.value
                          ? "primary.main"
                          : "transparent",
                      backgroundColor:
                          value?.listId === option?.value
                          ? "primary.main"
                          : "grey.100",
                      color:
                        value?.listId === option?.value
                          ? "white.main"
                          : "text.primary",
                      transition: "all 0.2s ease-in-out",
                      "&:hover": {
                        backgroundColor: "primary.main",
                        color: "white.main",
                      },
  
                      // ...(selectedOption?.label === option?.value && {
                      //   bgcolor: "#E0F7FA", // cyan.50 equivalent
                      //   color: "#006064", // cyan.800 equivalent
                      // }),
                    }}
                  >
                    <Typography variant="body2"  sx={{
                      fontSize: "16px",
                      fontWeight: 800,
                      letterSpacing: "0.3px", // Slightly spaced letters
                      textTransform: "capitalize",
                    }} >{option.label}</Typography>
                  </Box>
                ))}
              </Box>
            </Box>
          </Box>


          {filteredOptions.map((group) => (
                  <Box
                    component="li"
                    key={group.category}
                    sx={{ marginBottom: 2 }}
                  >
                    <Typography
                      variant="subtitle1"
                      sx={{
                        color: "white.main",
                        fontWeight: "bold",
                        fontSize: "14px",
                        backgroundColor: "black",
                        padding: "0.5px 1px",
                        textAlign: "center",
                      }}
                    >
                      {group.category}:
                    </Typography>
                    <Box
                      component="ul"
                      sx={{ listStyleType: "none", padding: 0 }}
                    >
                      {group.options.map((option) => (
                        <Box
                          component="li"
                          key={option.value}
                          onClick={() => handleOptionClick(option)}
                          sx={{
                            cursor: "pointer",
                            padding: "8px 12px",
                            margin: "4px 0",
                            borderRadius: 1,
                            // transition: "background-color 0.3s, color 0.3s",
                            bgcolor: "grey.100",
                            // color: "text.primary",
                            fontSize: "0.9em",
                            px: 2,
                            py: 1,
                            borderLeft:
                              value === option?.value
                                ? "4px solid"
                                : "none",
                            borderColor:
                              value === option?.value
                                ? "primary.main"
                                : "transparent",
                            backgroundColor:
                              value === option?.value
                                ? "primary.main"
                                : "grey.100",
                            color:
                              value === option?.value
                                ? "white.main"
                                : "text.primary",
                            transition: "all 0.2s ease-in-out",
                            "&:hover": {
                              backgroundColor: "primary.main",
                              color: "white.main",
                            },

                            // ...(selectedOption?.label === option?.value && {
                            //   bgcolor: "#E0F7FA", // cyan.50 equivalent
                            //   color: "#006064", // cyan.800 equivalent
                            // }),
                          }}
                        >
                          <Typography
                            variant="body2"
                            sx={{
                              fontSize: "16px",
                              fontWeight: 800,
                              letterSpacing: "0.3px", // Slightly spaced letters
                              textTransform: "capitalize",
                            }}
                          >
                            {option.label}
                          </Typography>
                          <Typography
                            variant="body2"
                            sx={{ fontSize: "12px", }}
                          >
                            {option.description}
                          </Typography>
                        </Box>
                      ))}
                    </Box>
                  </Box>
                ))}
        </Box>
      )}

      {activeTab === "array" && (
        <Box sx={{ minWidth: 300, padding: 2 }}>
          <EnumManager
            values={Array.isArray(value) ? value : []}
            onAddNewValue={(newValue) => onChange([...value, newValue])}
            onDeleteValue={(index) =>
              onChange(value.filter((_, i) => i !== index))
            }
          />
        </Box>
      )}
    </Box>
  );
};

MembershipValue.propTypes = {
  listOptions: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      description: PropTypes.string,
    })
  ).isRequired,
  onChange: PropTypes.func.isRequired,
  value: PropTypes.oneOfType([
    PropTypes.array,
    PropTypes.shape({
      listId: PropTypes.string,
    }),
  ]),
};

export default MembershipValue;
