import React from "react";
import { Dialog, DialogContent, IconButton, Button, Box, Divider } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { Btn as BtnAtom } from "../../buttons/Button";
/**
 * SmartValueModal component that wraps content in a modal dialog.
 *
 * @param {Object} props - The component props
 * @param {boolean} props.open - Whether the modal is open
 * @param {Function} props.onClose - Callback function to close the modal
 * @param {Function} props.onSave - Callback function to save changes
 * @param {React.ReactNode} props.children - The content to render inside the modal
 * @returns {React.JSX.Element} The rendered modal component
 */
const SmartValueModal = ({ open, onClose, onSave, children }) => {
  return (
    <Dialog
      open={open}
      // onClose={onClose}
      fullWidth
      onClose={(event, reason) => {
        if (reason !== "backdropClick" && reason !== "escapeKeyDown") {
          onClose();
        }
      }}
      // maxWidth="md"
      PaperProps={{
        sx: {
          // p: 0,
          // minHeight: "500px", // Set minimum height
          // maxHeight: "80vh",
          px: 1,
          py: 1,
        },
      }}
    >
      {/* Header actions */}
      {/* <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-end",
          bgcolor: "background.paper",
          //   borderBottom: 1,
          //   borderColor: "divider",
          p: 0,
        }}
      >
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </Box>
      <Divider /> */}
      <DialogContent
        // dividers
        sx={{
          p: 1,
          display: "flex",
          flexDirection: "column",
          gap: 1,
          flex: 1, // Take up remaining space
          overflow: "auto", // Enable scrolling if content overflows
         
          borderColor: "primary.main",
        }}
      >
        {children}

        {/* You can pass any children or content to the modal here */}
      </DialogContent>
      <Divider />
      <Box
        sx={{
          width: "100%",
          display: "flex",
          flexDirection: "row",
          gap: 1,
          pt: 1,
          justifyContent: "center",
        }}
      >
        <BtnAtom
          sx={{ backgroundColor: "primary.secondary" }}
          onClick={onSave}
          text="Save"
        />
        <BtnAtom
          sx={{ backgroundColor: "danger.main" }}
          onClick={onClose}
          text=" Cancel"
        />
      </Box>
    </Dialog>
  );
};

export default SmartValueModal;
