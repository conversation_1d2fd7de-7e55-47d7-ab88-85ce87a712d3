// src/components/global/atoms/smartValue/functions/FunctionsVal.jsx

import React, { useState, useEffect } from "react";
import { Box } from "@mui/material";
import DropdownAtom from "../../../atoms/dropdown/Dropdown";
import Input<PERSON><PERSON><PERSON>ield from "../inputField/InputFormField";
import { useSelector } from "react-redux";
import SmartValueHoc from "../../../molecules/smartValueHoc/SmartValueHoc";

/**
 * FunctionsVal component that handles function selection and parameters
 *
 * @param {Object} props - Component props
 * @param {Array} props.functions - Array of available functions
 * @param {Function} props.onChange - Function to call when value changes
 * @param {*} props.value - Current function value
 * @returns {React.JSX.Element} The rendered component
 */
const FunctionsVal = ({ functions, onChange, value, ruleType }) => {
  const [selectedFunction, setSelectedFunction] = useState(null);
  const [parametersData, setParametersData] = useState({});
  const { receivedData, campaign } = useSelector((state) => state.campaign);
  const { dateFormatePatterns } = receivedData;
  const { entityId, contextId } = campaign;

  // Format function options for dropdown
  const functionOptions = functions.map((func) => ({
    label: func.label,
    value: func.value,
  }));

  // Find selected function when value changes
  useEffect(() => {
    if (value?.functionId) {
      const functionData = functions.find(
        (func) => func.value === value.functionId
      );
      if (functionData) {
        setSelectedFunction(functionData);

        // Map args to parameters
        if (functionData.parameters && value.args) {
          const paramData = {};
          functionData.parameters.forEach((param, index) => {
            if (index < value.args.length) {
              paramData[param.parameterId] = value.args[index];
            }
          });
          setParametersData(paramData);
        }
      }
    }
  }, [functions, value]);

  // Handle function selection change
  const handleFunctionChange = (id, selectedValue) => {
    const functionData = functions.find((func) => func.value === selectedValue);
    setSelectedFunction(functionData);
    setParametersData({});

    // Create empty parameters data
    const initialParams = {};
    if (functionData?.parameters) {
      functionData.parameters.forEach((param) => {
        initialParams[param.parameterId] = "";
      });
    }

    onChange({
      functionId: selectedValue,
      args: functionData?.parameters
        ? functionData.parameters.map(() => "")
        : [],
    });
  };

  // Handle parameter value changes
  const handleParameterChange = (paramId, paramValue) => {
    setParametersData((prev) => ({
      ...prev,
      [paramId]: paramValue,
    }));

    // Update the full function value with new parameter
    if (selectedFunction) {
      const newArgs = selectedFunction.parameters.map((param) =>
        param.parameterId === paramId
          ? paramValue
          : parametersData[param.parameterId] || ""
      );

      onChange({
        functionId: selectedFunction.value,
        args: newArgs,
      });
    }
  };

  // Render specific parameter input based on parameterId and type
  const renderParameterInput = (param) => {
    // Special case for pattern parameter
    if (param.parameterId === "pattern" && param.type === "string") {
      return (
        <DropdownAtom
          key={`${param.parameterId}-dropdown`}
          id={param.parameterId}
          label={param.name}
          options={dateFormatePatterns}
          onChange={(id, value) => handleParameterChange(id, value)}
          required={true}
          selectedItem={
            parametersData[param.parameterId] !== undefined &&
            parametersData[param.parameterId] !== null
              ? parametersData[param.parameterId]
              : ""
          }
          validation={{ isShowError: false, error: null }}
        />
      );
    }
    const props = {
      types: ["date"],
      functionTypes: [],
      allowedRef: ["all"],
      customValAllowed: true,
      entityId: entityId,
      contextId: contextId,
      data: parametersData[param.parameterId] || "",
      emptySaveAllowed: false,
      ruleType: ruleType,
      errorDetails: {
        error: "",
        isShowError: false,
      },
    };
    // Special case for date parameter
    if (param.parameterId === "date" && param.type === "date") {
      return (
        <SmartValueHoc
          key={`${param.parameterId}-smartValue`}
          label={param.name}
          onSave={(newValue) => {
            handleParameterChange(param.parameterId, newValue);
          }}
          smartValueProps={props}
        />
      );
    }

    // Default case for all other parameters
    return (
      <InputFormField
        key={param.parameterId}
        selectedType={param.type}
        label={param.name}
        value={parametersData[param.parameterId] || ""}
        onChange={(value) => handleParameterChange(param.parameterId, value)}
      />
    );
  };

  return (
    <>
      <Box
        sx={{ display: "flex", flexDirection: "column", gap: 2, width: "100%" }}
      >
        <DropdownAtom
          label="Select Function"
          options={functionOptions}
          selectedItem={selectedFunction?.value || ""}
          onChange={handleFunctionChange}
          required={true}
          validation={{ isShowError: false, error: null }}
        />
        {selectedFunction?.parameters && (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
            {selectedFunction.parameters.length === 2 ? (
              <Box sx={{ display: "flex", flexDirection: "row", gap: 2 }}>
                {selectedFunction.parameters.map((param) => (
                  <Box key={param.parameterId} sx={{ flex: 1 }}>
                    {renderParameterInput(param)}
                  </Box>
                ))}
              </Box>
            ) : (
              // For any other number of parameters, use column layout
              selectedFunction.parameters.map((param) => (
                <Box key={param.parameterId}>{renderParameterInput(param)}</Box>
              ))
            )}
          </Box>
        )}
      </Box>
    </>
  );
};

export default FunctionsVal;
