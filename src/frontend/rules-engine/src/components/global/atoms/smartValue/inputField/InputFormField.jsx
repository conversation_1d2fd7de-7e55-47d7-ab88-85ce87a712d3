// src/components/global/atoms/smartValue/inputField/inputFormField.jsx

import React from "react";
import PropTypes from "prop-types";
import { TextField } from "@mui/material";
import DatePickerAtom from "../../datePicker/Index";
import EnumManager from "../../enum/Index";
import Switch from "@mui/material/Switch";
import Box from "@mui/material/Box";
import FormControlLabel from "@mui/material/FormControlLabel";
import { useEffect } from "react";
/**
 * InputFormField component that renders an input field based on the selected type.
 *
 * @param {Object} props - The props for the InputFormField component
 * @param {string} props.selectedType - The currently selected type
 * @param {string|number} props.value - The current value of the input field
 * @param {Function} props.onChange - Callback function to handle input changes
 * @param {Function} props.handleEnterKey - Callback function to handle enter key
 * @returns {React.JSX.Element|null} The rendered input field or null if type is not supported
 */
const InputFormField = ({ selectedType, value, onChange,handleEnterKey }) => {
  const handleTextFieldChange = (e) => {
    const inputValue = e.target.value;
    const parsedValue =
      selectedType === "number" ? parseFloat(inputValue) : inputValue;
    onChange(parsedValue);
  };

  useEffect(() => {
    if (selectedType == "string" && typeof value == "object") {
      onChange("");
    }
    if (selectedType == "boolean" && value == "") {
      onChange(false);
    }
  }, [selectedType]);

  switch (selectedType) {
    case "string":
    case "number":
      return (
        <TextField
          type={selectedType === "number" ? "number" : "text"}
          value={selectedType === "number" ? value : value}
          onChange={handleTextFieldChange}
          fullWidth
          variant="outlined"
          label="Enter Value"
          size="small"
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              handleEnterKey(); // 🔁 Replace with your actual function
            }
          }}
        />
      );
    case "date":
      return (
        <DatePickerAtom
          value={value}
          onChange={onChange}
          label="Select Date"
          disableFuture={false}
          disablePast={false}
        />
      );
    case "dateTime":
      return (
        <DatePickerAtom
          value={value}
          onChange={onChange}
          label="Select Date Time"
          disableFuture={false}
            disablePast={false}
            component="dateTime"
          />
        );
    case "array":
      return (
        <EnumManager
          values={Array.isArray(value) ? value : value ? [value] : []}
          onAddNewValue={(newValue) => onChange([...value, newValue])}
          onDeleteValue={(index) =>
            onChange(value.filter((_, i) => i !== index))
          }
        />
      );
    case "boolean":
      return (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <FormControlLabel
            control={
              <Switch
                checked={Boolean(value)}
                onChange={(e) => onChange(e.target.checked)}
              />
            }
            label={`${value ? "True" : "False"}`}
            labelPlacement="end"
          />
        </Box>
      );
    default:
      return null;
  }
};

InputFormField.propTypes = {
  selectedType: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  onChange: PropTypes.func.isRequired,
};

export default InputFormField;
