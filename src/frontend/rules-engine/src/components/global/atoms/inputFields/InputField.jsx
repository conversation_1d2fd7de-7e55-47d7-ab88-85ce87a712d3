import PropTypes from "prop-types";
import { TextField } from "@mui/material";
import { useState, useEffect } from "react";
import React from "react";

/**
 * A reusable and flexible input component (atom) that supports multiple types of input.
 *
 * This component is built using Material-UI's TextField and can accept various types
 * such as "text", "email", "password", "number", and more. It supports any behavior
 * that a standard HTML input field would have, along with additional Material-UI props
 * for styling, event handling, and customization.
 *
 * @component
 * @param {object} props - Component props.
 * @param {string} props.type - The type of input (e.g., "text", "email", "password", "number").
 * @param {string} props.label - The label for the input field (default: none).
 * @param {string} props.value - The current value of the input field.
 * @param {function} props.onChange - Function to handle changes to the input field.
 * @param {string} props.placeholder - Placeholder text for the input field.
 * @param {boolean} props.required - Whether the input is required.
 * @param {boolean} props.disabled - Whether the input is disabled.
 * @param {boolean} props.fullWidth - Whether the input should take the full width of its container.
 * @param {object} props.sx - Material-UI sx style object for custom styling.
 * @param {object} props.inputProps - Additional props to pass to the underlying input element.
 * @param {function} props.onBlur - Function to handle onBlur event.
 * @param {function} props.onFocus - Function to handle onFocus event.
 * @param {string} props.size - The size of the input field (default: "small").
 * @param {string} props.color - The color of the input field (default: "primary").
 * @param {string} props.variant - The variant of the input field (default: "outlined").
 * @param {boolean} props.multiline - Whether the input field should be multiline.
 * @param {number} props.rows - The number of rows to display for multiline inputs.
 * @param {string} props.name - The name of the input field.
 * @param {boolean} props.onMountValidate - Whether to validate the input on mount.
 * @param {string} props.valueType - The type of value to validate against.
 * @param {object} props.validation - The validation object for the input field.
 * @param {boolean} props.validation.isShowError - Flag to show error message.
 * @param {string} props.validation.error - The error message to display.
 * @param {object} props.inputProps - Additional props to pass to the underlying input element.
 * @param {function} props.onBlur - Function to handle onBlur event.
 * @param {function} props.onFocus - Function to handle onFocus event.
 * @param {Function} props.validator - Function to validate the input field. If the input is valid, it returns { success: true, message: null }. If the input is invalid, it returns { success: false, message: error }.
 * @param {string} props.validateOn - The event to validate the input field on.
 * @param {object} props.props - Additional props to pass to the underlying input element.
 * @returns {JSX.Element} - The rendered input component.
 */
const InputField = ({
  type = "text", // Default input type is text
  label = "",
  value,
  onChange,
  placeholder = "",
  required = true,
  disabled = false,
  fullWidth = true, // Default is to take full width
  validator,
  validateOn = "change", // [ "change", "submit" ]
  sx = {},
  onBlur,
  onFocus,
  size = "small",
  color = "primary",
  variant = "outlined",
  multiline = false,
  rows = 1,
  name,
  onMountValidate = false,
  valueType,
  validation = {
    isShowError: false,
    error: null,
  },
  ...props
}) => {
  // const [validation, setValidation] = useState({ success: true, message: error }); // State for managing validation feedback

  if (!valueType) valueType = type;

  // Handle change event with optional validation
  const handleChange = (event) => {
    const newValue = event.target.value;
    // Call onChange prop if provided

    // Perform validation onChange if validateOnChange is true
    if (validator && validateOn === "change") {
      const validationResult = validator(newValue, valueType); // Call validator function
      if (onChange) onChange(event, validationResult);
      // setValidation(validationResult); // Set validation state based on validator response
    } else {
      if (onChange) onChange(event);
    }
  };

  const handleOnFocus = (event) => {
    if (onFocus) onFocus(event);
  };

  // Handle blur event for validation (submit-time validation)
  const handleBlur = (event) => {
    const newValue = event.target.value;
    // Perform validation onChange if validateOnChange is true
    if (validator && validateOn === "change") {
      const validationResult = validator(newValue); // Call validator function
      if (onBlur) onBlur(event, validationResult);
      // setValidation(validationResult); // Set validation state based on validator response
    } else {
      if (onBlur) onBlur(event, name);
    }
  };

  useEffect(() => {
    if (!onMountValidate) return;
    const eventT = {
      target: {
        name: name,
        value: value,
      },
    };

    onChange(eventT);
  }, []);

  return (
    <TextField
      type={type}
      label={label}
      value={value}
      color={color}
      variant={variant}
      onChange={handleChange}
      multiline={multiline}
      name={name}
      rows={rows}
      size={size}
      placeholder={placeholder}
      required={required}
      disabled={disabled}
      fullWidth={fullWidth}
      sx={sx}
      onBlur={handleBlur} // Validate onBlur if needed
      onFocus={handleOnFocus}
      {...props}
      error={validation.isShowError}
      helperText={validation.isShowError ? validation.error : ""}
      autoComplete="off"
    />
  );
};

InputField.propTypes = {
  type: PropTypes.oneOf([
    "text",
    "email",
    "password",
    "number",
    "search",
    "url",
    "tel",
    "date",
    "datetime-local",
    "month",
    "week",
    "time",
    "color",
  ]),
  label: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  fullWidth: PropTypes.bool,
  validator: PropTypes.func, // Validator function that returns { success: boolean, message: string }
  validateOn: PropTypes.oneOf(["change", "submit"]),
  sx: PropTypes.object,
  size: PropTypes.oneOf(["small", "medium", "large"]),
  color: PropTypes.oneOf(["primary", "secondary", "default"]),
  variant: PropTypes.oneOf(["outlined", "filled", "standard"]),
  multiline: PropTypes.bool,
  rows: PropTypes.number,
  name: PropTypes.string,
  onBlur: PropTypes.func,
  onMountValidate: PropTypes.bool,
  valueType: PropTypes.string,

  validation: PropTypes.object,
  ...TextField.propTypes,
};

export { InputField };
