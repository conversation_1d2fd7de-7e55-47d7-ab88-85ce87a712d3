import Pagination from "@mui/material/Pagination";
import Stack from "@mui/material/Stack";
import PropTypes from "prop-types";
import React from "react";

/**
 * PaginationAtom - A flexible and reusable atom component for pagination
 *
 * @param {object} props - The component props.
 * @param {number} props.count - Total number of pages.
 * @param {number} props.page - The current page.
 * @param {function} props.onChange - Function to handle page changes.
 * @param {string} props.color - The color of the pagination (primary, secondary).
 * @param {boolean} props.disabled - Disables the pagination if set to true.
 * @param {string} props.size - The size of the pagination (small, medium, large).
 * @param {object} props.sx - MUI sx style object for custom styling.
 * @param {object} props.sxStack - MUI sx style object for custom styling.
 * @returns {JSX.Element} - The Pagination atom component.
 */
const PaginationAtom = ({
  count = 10,
  page = 1,
  onChange,
  color = "mainColor.main",
  disabled = false,
  size = "medium",
  sx = {},
  sxStack = {},
}) => {
  return (
    <Stack
      spacing={2}
      direction="row"
      justifyContent="center"
      sx={{
        "& .MuiListItemIcon-root": {
          color: "mainColor.main",
        },
        "& .MuiTypography-root": {
          color: "mainColor.main",
        },
        "& .MuiPaginationItem-root": {
          color: "white", // Default text color for all items
        },
        "& .MuiPaginationItem-page": {
          color: "white", // Text color for page numbers
        },
        "& .MuiPaginationItem-previousNext": {
          color: "white", // Text color for previous/next arrows
        },
        ...sxStack,
      }}
    >
      <Pagination
        count={count}
        page={page}
        onChange={onChange}
        // color={color}
        disabled={disabled}
        size={size}
        sx={{
          "& .MuiPaginationItem-root": {
            color: "white", // Default text color for all items
          },
          "& .MuiPaginationItem-page": {
            color: "white", // Text color for page numbers
          },
          "& .MuiPaginationItem-previousNext": {
            color: "white", // Text color for previous/next arrows
          },
          "& .MuiPaginationItem-page.Mui-selected": {
            backgroundColor: "primary.main", // Selected button background
            color: "white", // Selected button text color
            "&:hover": {
              backgroundColor: "primary.dark", // Hover state for selected button
            },
            "& svg": {
              // Add this to target the icon
              color: "mainColor.main",
            },
            "& .MuiListItemIcon-root": {
              color: "mainColor.main",
            },
            "& .MuiTypography-root": {
              color: "mainColor.main",
            },
          },
          ...sx,
        }}
      />
    </Stack>
  );
};

// Define PropTypes for type-checking and documentation
PaginationAtom.propTypes = {
  count: PropTypes.number, // Total number of pages
  page: PropTypes.number, // Current active page
  onChange: PropTypes.func, // Function to call when page changes
  color: PropTypes.string, // Color of the pagination (primary, secondary)
  disabled: PropTypes.bool, // Disable the pagination control
  size: PropTypes.string, // Size of the pagination (small, medium, large)
  sx: PropTypes.object, // MUI sx prop for additional styling
  sxStack: PropTypes.object, // MUI sx prop for additional styling
};

export default PaginationAtom;
