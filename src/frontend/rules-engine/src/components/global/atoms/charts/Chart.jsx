import React from "react";
import PropTypes from "prop-types";
import { Bar, Line, Doughnut, PolarArea } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  RadialLinearScale,
  ArcElement, // Required for Doughnut and PolarArea charts
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { Box } from "@mui/material";

// Register required components with Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  RadialLinearScale,
  ArcElement, // For Doughnut and PolarArea charts
  Title,
  Tooltip,
  Legend
);

/**
 * A reusable Chart component that displays various types of charts using Chart.js.
 * You can pass data and options to customize the chart.
 *
 * @component
 * @example
 * const data = {
 *   labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
 *   datasets: [
 *     {
 *       label: 'Sales',
 *       data: [65, 59, 80, 81, 56, 55, 40],
 *       backgroundColor: 'rgba(75, 192, 192, 0.2)',
 *       borderColor: 'rgba(75, 192, 192, 1)',
 *       borderWidth: 1,
 *     },
 *   ],
 * };
 * const options = {
 *   responsive: true,
 *   plugins: {
 *     legend: { position: 'top' },
 *     title: { display: true, text: 'Monthly Sales Data' },
 *   },
 * };
 * return <Chart data={data} options={options} type="Doughnut" />;
 *
 * @param {Object} props - Component props
 * @param {Object} props.data - The chart data in Chart.js format
 * @param {Object} [props.options] - Configuration options for the chart
 * @param {Object} [props.sx] - Custom styles to apply to the component
 * @param {"Bar" | "Line" | "Doughnut" | "PolarArea"} [props.type] - The type of chart to render
 * @returns {JSX.Element} A Chart component
 */
const Chart = ({ data, options, sx = {}, type = "Bar" }) => {
  const ChartComponents = {
    Bar,
    Line,
    Doughnut,
    PolarArea,
  };

  const ChartComponent = ChartComponents[type];
  return (
    <Box
      sx={{
        display: "flex", // Use flexbox for centering
        justifyContent: "center", // Center horizontally
        alignItems: "center", // Center vertically (optional)
        width: "100%",
        overflowX: "scroll", // Horizontal scroll for small screens
        height: {
          xs: "300px",
          sm: "400px",
          md: "450px",
        },
        minWidth: "800px",
        ...sx,
      }}
    >
      <ChartComponent data={data} options={options} />
    </Box>
  );
};

Chart.propTypes = {
  data: PropTypes.shape({
    labels: PropTypes.arrayOf(PropTypes.string).isRequired,
    datasets: PropTypes.arrayOf(
      PropTypes.shape({
        label: PropTypes.string.isRequired,
        data: PropTypes.arrayOf(PropTypes.number).isRequired,
        backgroundColor: PropTypes.string,
        borderColor: PropTypes.string,
        borderWidth: PropTypes.number,
      })
    ).isRequired,
  }).isRequired,
  options: PropTypes.object,
  type: PropTypes.oneOf(["Bar", "Line", "Doughnut", "PolarArea"]).isRequired,
};

export default Chart;
