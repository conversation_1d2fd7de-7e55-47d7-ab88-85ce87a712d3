import PropTypes from "prop-types";
import { Radio, FormControlLabel } from "@mui/material";
import React from "react";

/**
 * A reusable radio button component that can be used globally.
 *
 * This component is built using Material-UI's Radio component and supports various options
 * for customization, including label, checked state, and change handling.
 *
 * @component
 * @param {object} props - The component props.
 * @param {string} props.label - The label to display next to the radio button.
 * @param {boolean} props.checked - Whether the radio button is checked.
 * @param {function} props.onChange - Function to handle change events for the radio button.
 * @param {string} props.value - The value of the radio button.
 * @param {string} props.color - The color of the radio button (default: "primary").
 * @param {object} props.sx - Material-UI sx style object for custom styling.
 * @returns {JSX.Element} - The rendered radio button component.
 */
const RadioButton = ({
  label,
  checked,
  onChange,
  value,
  color = "primary",
  sx = {},
}) => (
  <FormControlLabel
    control={
      <Radio
        checked={checked}
        onChange={onChange}
        value={value}
        color={color}
        sx={sx}
      />
    }
    label={label}
  />
);

RadioButton.propTypes = {
  label: PropTypes.string.isRequired,
  checked: PropTypes.bool.isRequired,
  onChange: PropTypes.func.isRequired,
  value: PropTypes.string.isRequired,
  color: PropTypes.oneOf(["primary", "secondary", "default"]),
  sx: PropTypes.object,
};

export { RadioButton };
