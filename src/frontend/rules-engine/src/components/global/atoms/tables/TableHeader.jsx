import PropTypes from "prop-types";
import { TableCell  } from "@mui/material";

/**
 * A customizable table header component.
 *
 * @component
 * @param {node} children - The content to display inside the header cell.
 * @param {string} align - The alignment of the header cell content (default: "left").
 * @param {object} sx - MUI sx style object for custom styling.
 * @returns {JSX.Element} - The rendered table header cell.
 */
const TableHeader = ({ children, align = "left", sx = {} }) => (
  <TableCell align={align} sx={{ fontWeight: 'bold', ...sx }}>
    {children}
  </TableCell>
);

TableHeader.propTypes = {
  children: PropTypes.node.isRequired,
  align: PropTypes.oneOf(["inherit", "left", "center", "right"]),
  sx: PropTypes.object,
};


export { TableHeader };
