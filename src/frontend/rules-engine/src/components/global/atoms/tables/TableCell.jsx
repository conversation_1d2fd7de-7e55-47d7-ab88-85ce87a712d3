import PropTypes from "prop-types";
import { TableCell as MuiTable<PERSON><PERSON>, } from "@mui/material";

/**
 * A customizable table cell component.
 *
 * @component
 * @param {node} children - The content to display inside the cell.
 * @param {string} align - The alignment of the cell content (default: "left").
 * @param {object} sx - MUI sx style object for custom styling.
 * @returns {JSX.Element} - The rendered table cell.
 */
const TableCell = ({ children, align = "left", sx = {} }) => (
  <MuiTableCell align={align} sx={sx}>
    {children}
  </MuiTableCell>
);

TableCell.propTypes = {
  children: PropTypes.node.isRequired,
  align: PropTypes.oneOf(["inherit", "left", "center", "right"]),
  sx: PropTypes.object,
};




export { TableCell, };
