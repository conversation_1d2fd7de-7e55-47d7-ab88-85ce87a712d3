import PropTypes from "prop-types";
import { TableRow as MuiTableRow } from "@mui/material";

/**
 * A customizable table row component.
 *
 * @component
 * @param {node} children - The content to display inside the row.
 * @param {boolean} hover - Whether the row should have a hover effect (default: false).
 * @param {object} sx - MUI sx style object for custom styling.
 * @returns {JSX.Element} - The rendered table row.
 */
const TableRow = ({ children, hover = false, sx = {} }) => (
  <MuiTableRow hover={hover} sx={sx}>
    {children}
  </MuiTableRow>
);

TableRow.propTypes = {
  children: PropTypes.node.isRequired,
  hover: PropTypes.bool,
  sx: PropTypes.object,
};

export { TableRow };
