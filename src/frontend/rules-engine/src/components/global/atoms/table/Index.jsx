import * as React from "react";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import Button from "@mui/material/Button";
import { Btn } from "../buttons/Button";
import { Box, IconButton, Tooltip } from "@mui/material";
import { DynamicIcon } from "../icons/Index";
// Default rows for demonstration
const defaultRows = [
  {
    width: 10,
    height: 2,
    id: 1,
    cols: [
      {
        width: 5,
        height: 2,
        text: "Test 1",
        type: "text",
      },
      {
        width: 5,
        height: 2,
        text: "Test 2",
        type: "button",
      },
    ],
  },
];

/**
 * A reusable table component that can render dynamic columns and rows.
 * Supports both header and data rows with flexible column configurations.
 *
 * @param {Object} props - Component props
 * @param {boolean} [props.isTableHeader=true] - Whether to show table header
 * @param {Function} [props.onBtnClick] - Callback function for button click
 * @param {Object} [props.sx={}] - MUI sx style object for custom styling
 * @param {string} [props.name="userManagement"] - Name of the table
 * @param {Array} [props.rows=[]] - Array of rows to display
 * @returns {JSX.Element} Table component with dynamic content
 */
export default function ReusableTable({
  isTableHeader = true,
  rows = defaultRows,
  onBtnClick,
  tableHeadData,
  actions = [],
  name = "userManagement",
  sx = {
    tableContainer: {},
    table: {},
    tableHead: {},
    tableBody: {},
    tableRow: {},
    tableCell: {},
    tableCellHeader: {},
    button: {},
  },
}) {
  // Handle empty rows gracefully
  if (!rows || rows.length === 0) {
    return (
      <TableContainer component={Paper}>
        <Table sx={{ minWidth: 650 }} aria-label="empty table">
          <TableBody>
            <TableRow>
              <TableCell colSpan={5} align="center">
                No data available
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TableContainer>
    );
  }
  /**
   * This function is used to get the id of the row
   * @param {Array} row - The row to get the id from
   * @returns {number} The id of the row
   */
  const loadingUser = (row) => {
    let id = row.find((col) => col.key === "id");
    
    if (id) {
      return id.value;
    }
    return null;
  };
  
  return (
    <TableContainer
      sx={{ width: "100%", ...sx.tableContainer }}
      component={Paper}
    >
      <Table
        sx={{ width: "100%", minWidth: 650, ...sx.table }}
        aria-label="simple table"
      >
        {isTableHeader && (
          <TableHead sx={sx.tableHead}>
            <TableRow sx={sx.tableRow}>
              {tableHeadData.map((col, index) => (
                <TableCell
                  key={index}
                  align="right"
                  sx={{
                    width: `${col.width * 100}px`,
                    textAlign: "left",
                    fontWeight: "bold",
                    ...sx.tableCell,
                    ...sx.tableCellHeader,
                  }}
                >
                  {col.text}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
        )}
        <TableBody sx={sx.tableBody}>
          {rows.map((row, rowIndex) => (
            <TableRow
              key={rowIndex}
              sx={{
                "&:last-child td, &:last-child th": { border: 0 },

                ...sx.tableRow,
                textAlign: "start",
              }}
            >
              {row.map((col, colIndex) => (
                <TableCell
                  key={`${rowIndex}-${colIndex}`}
                  align="left"
                  sx={{ width: `${col.width}%`, ...sx.tableCell }}
                >
                  {handleValue(name, col)}
                </TableCell>
              ))}

              {actions.length > 0 &&
                actions.map(
                  ({ text, id, handleClick, sx, loadingId }, colIndex) => (
                    <TableCell
                      key={`${rowIndex}-${colIndex}`}
                      // align="right"
                      sx={{
                        width: `${"10"}%`,
                        textAlign: "left",
                        ...sx.tableCell,
                      }}
                    >
                      {/* <Button
                      variant="contained"
                      sx={{color:"mainColor.main", ...sx.button,...sx}}
                      onClick={() => handleClick && handleClick(row)}
                    >
                      {text}
                    </Button> */}
                      <Btn
                        text={text}
                        variant="contained"
                        sx={{ color: "mainColor.main", ...sx.button, ...sx }}
                        onClick={() => handleClick && handleClick(row)}
                        isLoading={loadingId === loadingUser(row)}
                      />
                    </TableCell>
                  )
                )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}

// Function to transform user data into table format
export const createUserTableData = ({
  config = {
    cols: [],
    btns: [
      { text: "Edit", id: "edit" },
      { text: "Delete", id: "delete" },
    ],
  },
  users,
}) => {
  let rows = users.map((user) => {
    let cols = Object.keys(user).map(([key, value]) => {
      return {
        text: value,
        type: "text",
      };
    });
    cols.push(
      ...config.btns.map((btn) => {
        return {
          text: btn.text,
          type: "button",
        };
      })
    );
    return {
      id: user.id,
      cols,
    };
  });

  return rows;
};

// _____________ Components _______________

const CopyToClipboardComponent = ({ text }) => {
  return (
    <>
      <Tooltip title="Copy to clipboard">
        <IconButton onClick={() => copyToClipboard(text)}>
          <DynamicIcon iconName="ContentCopy" />
        </IconButton>
      </Tooltip>
    </>
  );
};
const ValueComponent = ({ text }) => {
  return (
    <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
      <Box
        sx={{
          overflow: "hidden",
          textOverflow: "ellipsis",
          whiteSpace: "nowrap",
          maxWidth: "200px", // Adjust this value as needed
          "&:hover": {
            whiteSpace: "normal",
            wordBreak: "break-all",
          },
        }}
      >
        {text}
      </Box>
      <CopyToClipboardComponent text={text} />
    </Box>
  );
};

// ______________________Functions ______________________
/**
 * This function is used to handle the value of the column based on the name of the table
 * @param {string} name - The name of the table
 * @param {object} col - The column to handle the value of
 * @returns {JSX.Element} The value of the column
 */
const handleValue = (name, col) => {
  if (name === "apiKeys" && col.key === "api_key") {
    return <ValueComponent text={col.value} />;
  }
  return col.value;
};
/**
 * This function is used to copy the text to the clipboard
 * @param {string} text - The text to copy to the clipboard
 */
const copyToClipboard = (text) => {
  navigator.clipboard.writeText(text);
};
