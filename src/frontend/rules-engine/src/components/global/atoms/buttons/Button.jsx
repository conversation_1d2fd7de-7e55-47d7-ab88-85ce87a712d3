import { Button, CircularProgress } from "@mui/material";
import PropTypes from "prop-types";
import React from "react";

/**
 * A loading spinner component that displays a Material-UI CircularProgress.
 *
 * This component renders a circular loading spinner with a customizable size.
 * It defaults to a size of 24 if no size is provided.
 *
 * @component
 * @param {Object} props - Component props.
 * @param {number} [props.size=24] - The size of the loading spinner. Defaults to 24.
 * @returns {JSX.Element} - The rendered loading spinner component.
 */
const LoadingSpinner = ({ size = 24 }) => (
  <CircularProgress color="white" sx={{ backgroundColor: "white",color:'white' }} size={size} />
);

LoadingSpinner.propTypes = {
  size: PropTypes.number,
};

/**
 * A customizable button component that supports loading states.
 *
 * This component renders a MUI Button with an optional loading spinner
 * when `isLoading` is true. It supports various customization options
 * such as color, variant, and full-width styling.
 *
 * @component
 * @param {Object} props - Component props.
 * @param {string} props.text - The text to display inside the button.
 * @param {function} props.onClick - Function to handle the button click event.
 * @param {string} [props.color] - Button color (default: "primary").
 * @param {boolean} [props.fullWidth] - Whether the button should take the full width of its container.
 * @param {string} [props.variant] - Button variant (default: "contained").
 * @param {object} [props.sx] - MUI sx style object for custom styling.
 * @param {boolean} [props.isLoading] - Whether the button should show a loading spinner.
 * @param {string} [props.type] - Button type (default: "button").
 * @param {string} [props.size] - Button size (default: "medium").
 * @param {boolean} [props.disabled] - Whether the button should be disabled.
 * @returns {JSX.Element} - The rendered button component.
 */
const Btn = ({
  text,
  onClick,
  color = "white.main",
  fullWidth = true,
  variant = "contained",
  sx = {},
  isLoading = false,
  type = "button",
  size = "medium",
  disabled = false
}) => (
  <Button
    onClick={onClick}
    type={type}
    fullWidth={fullWidth}
    variant={variant}
    sx={{
      backgroundColor: "primary.main",
      color,
      borderRadius: "50px",
      width: "auto",
      textTransform: "none",
      fontWeight: 500,
      '&.Mui-disabled': {
        backgroundColor: "primary.main",
        ...sx,
      },
      ...sx,
    }}
    size={size}
    disabled={isLoading  || disabled ? true : false}
    // disabled={isLoading} // Disable button during loading state
  >
    {isLoading ? <LoadingSpinner size={24} /> : text}
  </Button>
);

Btn.propTypes = {
  text: PropTypes.string.isRequired,
  onClick: PropTypes.func,
  color: PropTypes.oneOf([
    "primary",
    "secondary",
    "inherit",
    "darkGray",
    "default",
  ]),
  size: PropTypes.oneOf(["small", "medium", "large"]),
  fullWidth: PropTypes.bool,
  variant: PropTypes.oneOf(["contained", "outlined", "text"]),
  sx: PropTypes.object,
  isLoading: PropTypes.bool,
  type: PropTypes.oneOf(["button", "submit", "reset"]),
  disabled: PropTypes.bool,
};

export { Btn };
