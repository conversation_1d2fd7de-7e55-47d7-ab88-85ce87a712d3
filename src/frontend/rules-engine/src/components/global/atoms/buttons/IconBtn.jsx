import PropTypes from "prop-types";
import { IconButton as M<PERSON><PERSON>con<PERSON>utton } from "@mui/material";
import React from "react";
import DynamicIcon from "../icons/Index";

/**
 * A flexible IconButton component that wraps any passed icon or component.
 *
 * @param {Object} props - Component props.
 * @param {JSX.Element} props.icon - The icon or component to display inside the button.
 * @param {function} props.onClick - The function to call when the button is clicked.
 * @param {string} [props.color = "default"] - The color of the button (primary, secondary, etc.).
 * @param {string} [props.size = 'medium'] - The size of the button (small, medium, large).
 * @param {object} [props.sx = {}] - Additional custom styling for the button.
 * @param {object} [props.hoverSX = {}] - Custom styling for the button on hover.
 * @param {object} [props.iconParams = {}] - Custom parameters for the icon.
 * @param {string} [props.iconParams.color = "secondary"] - The color of the icon.
 * @param {number} [props.iconParams.size = 25] - The size of the icon.
 * @param {string} [props.iconParams.iconName = "ArrowRight"] - The name of the icon to display.
 * @param {string} [props.iconParams.title = "Arrow Right"] - The title of the icon.
 * 
 * @param {object} props.rest - Additional props to pass to the IconButton component.
 * 
 * @returns {JSX.Element} - A customized IconButton component.
 */
export const IconBtnAtom = ({
  onClick,
  color = "default",
  size = "medium",
  sx = {},
  hoverSX = {},
  iconParams={
    color: "secondary",
    size: 25,
    iconName:"ArrowRight",
    title: "Arrow Right"
  },
  ...rest
}) => {
  return (
    <MUIIconButton
      onClick={onClick}
      color={color}
      size={size}
      sx={{
        width: "40px",
        height: "40px",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        "&:hover": {
          ...hoverSX,
        },
        ...sx,
      }}
      {...rest}
    >
      {/* {icon} */}
      <DynamicIcon
        
        {...iconParams}
      />
    </MUIIconButton>
  );
};

// Prop types for better type checking and documentation
IconBtnAtom.propTypes = {
  icon: PropTypes.element.isRequired, // The icon or component to display inside the button
  onClick: PropTypes.func, // Function to execute on click
  color: PropTypes.oneOf([
    "default",
    "primary",
    "secondary",
    "error",
    "inherit",
  ]), // Button color
  size: PropTypes.oneOf(["small", "medium", "large"]), // Button size
  sx: PropTypes.object, // Custom style object
};
