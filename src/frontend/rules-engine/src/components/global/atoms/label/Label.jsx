import PropTypes from "prop-types";
import { FormLabel } from "@mui/material";
import React from "react";

/**
 * A reusable label component that can be used globally.
 *
 * This component is built using Material-UI's FormLabel component and supports various options
 * for customization, including color, required indication, and styling.
 *
 * @component
 * @param {object} props - The component props.
 * @param {string} props.text - The text to display for the label (required).
 * @param {boolean} props.required - Whether the label indicates that the field is required.
 * @param {string} props.color - The color of the label (default: "primary").
 * @param {object} props.sx - Material-UI sx style object for custom styling.
 * @returns {JSX.Element} - The rendered label component.
 */
const Label = ({ text, required = false, color = "primary", sx = {} }) => (
  <FormLabel required={required} sx={{ color }} {...sx}>
    {text}
  </FormLabel>
);

Label.propTypes = {
  text: PropTypes.string.isRequired,
  required: PropTypes.bool,
  color: PropTypes.string,
  sx: PropTypes.object,
};

export default Label;
