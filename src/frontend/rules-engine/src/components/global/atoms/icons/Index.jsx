import {
  HelpOutlineOutlined,
  Equalizer,
  MoreVert,
  Campaign,
  Menu,
  Delete<PERSON><PERSON>ver,
  TaskAlt,
  CampaignOutlined,
  CommentOutlined,
  Settings,
  ContentCopyOutlined,
  Close,
  <PERSON>ForwardIos,
  ArrowBackIos,
  KeyboardArrowDown,
  KeyboardArrowRight,
  ContentCopy,
  Edit,
  Delete,
  Analytics,
  Save,
  Checklist,
  Notifications,
  ListAlt,
  Download,
  Send,
  CheckCircle,
  Cancel,
  Person,
  ManageAccounts,
  Logout,
  ArrowBack,
  AccountCircle,
  PersonAdd,
  Category,
  CallMade,
  
} from "@mui/icons-material";

import PropTypes from "prop-types";
import React from "react";

// ______________________________ DynamicIcon ________________________________
// A mapping of icon names to MUI icons
const iconMap = {
  HelpOutlineOutlined,
  Equalizer,
  MoreVert,
  Campaign,
  Menu,
  DeleteForever,
  TaskAlt,
  CampaignOutlined,
  CommentOutlined,
  Settings,
  ContentCopyOutlined,
  Close,
  ArrowForwardIos,
  ArrowBackIos,
  KeyboardArrowDown,
  KeyboardArrowRight,
  ContentCopy,
  Edit,
  Delete,
  Analytics,
  Save,
  Checklist,
  Notifications,
  ListAlt,
  Download,
  Send,
  CheckCircle,
  Cancel,
  Person,
  ManageAccounts,
  Logout,
  ArrowBack,
  AccountCircle,
  PersonAdd,
  Category,
  CallMade,
};
/**
 * A general-purpose icon component that renders an icon based on the provided `iconName`.
 *
 * @param {object} props - The component props.
 * @param {string} props.iconName - The name of the icon to render.
 * @param {string} [props.color] - Color of the icon.
 * @param {number} [props.size] - Size of the icon in pixels.
 * @param {object} [props.sx] - Custom MUI sx styles for additional customization.
 * @param {string} [props.title] - Accessible title for the icon (used for screen readers).
 * @returns {JSX.Element} The rendered icon component based on the `iconName`.
 */
export const DynamicIcon = ({
  iconName,
  color = "inherit",
  size = 24,
  sx = {},
  title = "",
}) => {
  // Get the icon component based on the `iconName` prop
  const IconComponent = iconMap[iconName] || HelpOutlineOutlined; // Default to Help icon if not found

  return (
    <IconComponent sx={{ fontSize: size, color, ...sx }} titleAccess={title} />
  );
};

// Define PropTypes for type-checking and documentation
DynamicIcon.propTypes = {
  // set the icons here for intellicence this vritable 'iconMap
  iconName: PropTypes.oneOf(Object.keys(iconMap)),
  color: PropTypes.string, // The color of the icon
  size: PropTypes.number, // The size of the icon in pixels
  sx: PropTypes.object, // The custom MUI sx prop for additional styling
  title: PropTypes.string, // Accessible title for the icon
};

export default DynamicIcon;
