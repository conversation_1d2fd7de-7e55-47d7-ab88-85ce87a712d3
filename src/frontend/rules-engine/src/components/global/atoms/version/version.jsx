import React from "react";
import { Typography<PERSON><PERSON> } from "../typography/Typography";

/**
 * The version component displays the current version and the build number of the application.
 */
const Version = () => {

    // @ts-ignore
    const version = import.meta.env.VITE_PRODUCT_VERSION !== "{productVersion}" ? import.meta.env.VITE_PRODUCT_VERSION : "1.0.0";
    // @ts-ignore
    const build = import.meta.env.VITE_BUILD_NUMBER !== "{buildNumber}" ? import.meta.env.VITE_BUILD_NUMBER : "20";

  return (
    
    <TypographyAtom
      text={<><strong>Version:</strong> {version} (<strong>Build:</strong> {build})</>}
      type="heading"
      variant={"body2"}
    />
  );
};

export default Version;
