import PropTypes from "prop-types";
import { Typography as MuiTypography } from "@mui/material";
import React from "react";

/**
 * A reusable typography component that can represent various types of content,
 * such as headings and paragraphs.
 *
 * This component uses Material-UI's Typography and supports various options
 * for customization, including variant, alignment, color, and styling.
 *
 * @component
 * @param {object} props - The component props.
 * @param {string | JSX.Element} props.text - The text to display (required).
 * @param {string} [props.type = "heading"]- The type of text ("heading" or "paragraph") (default: "heading").
 * @param { "h1"| "h2"|"h3"|"h4"|"h5"|"h6"|"subtitle1"|"subtitle2"|"body1"|"body2"|"caption"|"overline"|"button"} [props.variant = "h6"] - The variant of the typography (default: "h6").
 * @param {string} [props.align = "inherit"] - The text alignment (default: "inherit").
 * @param {string} [props.color="textPrimary"] - The color of the text (default: "textPrimary").
 * @param {object} [props.sx = {}]- Material-UI sx style object for custom styling.
 * @returns {JSX.Element} - The rendered typography component.
 */
export const TypographyAtom = ({
  text,
  type = "heading",
  variant = type === "heading" ? "h6" : "body1",
  align = "inherit",
  color = "darkGray",
  sx = {},
  ...rest
}) => (
  <MuiTypography
    variant={variant}
    align={align}
    color={color}
    {...rest}
    sx={{ color: "textColor.primary", ...sx }}
    // sx={sx}
  >
    {text}
  </MuiTypography>
);

TypographyAtom.propTypes = {
  text: PropTypes.string.isRequired,
  type: PropTypes.oneOf(["heading", "paragraph"]),
  variant: PropTypes.oneOf([
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "subtitle1",
    "subtitle2",
    "body1",
    "body2",
    "caption",
    "overline",
    "button",
  ]),
  align: PropTypes.oneOf(["inherit", "left", "center", "right", "justify"]),
  color: PropTypes.string,
  sx: PropTypes.object,
  ...MuiTypography.propTypes,
};
