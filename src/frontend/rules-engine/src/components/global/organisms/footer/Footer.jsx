import { <PERSON>, <PERSON>, Divider, <PERSON><PERSON><PERSON><PERSON>on } from "@mui/material";
import Grid from "@mui/material/Grid2"; // This is Grid2
import FacebookIcon from "@mui/icons-material/Facebook";
import TwitterIcon from "@mui/icons-material/Twitter";
import LinkedInIcon from "@mui/icons-material/LinkedIn";
import EmailIcon from "@mui/icons-material/Email";
import { TypographyAtom as Typography } from "../../atoms/typography/Typography";
const Footer = () => {
  return (
    <Box
      component="footer"
      sx={{
        backgroundColor: "#F8F9FA",
        padding: "40px 20px",
        marginTop: "auto",
        borderTop: "1px solid #ddd",
      }}
    >
      <Grid container spacing={4}>
        {/* Logo & Description */}
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <Typography variant="h6" color="textPrimary" text="RuleForge" />
          <Typography
            variant="body2"
            sx={{ mt: 2 }}
            color="textPrimary"
            text=" RuleForge is a powerful campaign rules engine designed to automate
            and optimize your business campaigns, ensuring seamless integration
            and advanced functionality."
          />

          {/* <Typography variant="body2" color="textSecondary" sx={{ mt: 2 }}>
            RuleForge is a powerful campaign rules engine designed to automate
            and optimize your business campaigns, ensuring seamless integration
            and advanced functionality.
          </Typography> */}
        </Grid>

        {/* Useful Links */}
        <Grid size={{ xs: 12, sm: 6, md: 2 }}>
          <Typography variant="h6" color="textPrimary" text="Company" />
          <Box sx={{ mt: 2 }}>
            <Link
              href="/about"
              color="textSecondary"
              underline="none"
              display="block"
            >
              About Us
            </Link>
            <Link
              href="/careers"
              color="textSecondary"
              underline="none"
              display="block"
            >
              Careers
            </Link>
            <Link
              href="/contact"
              color="textSecondary"
              underline="none"
              display="block"
            >
              Contact Us
            </Link>
            <Link
              href="/blog"
              color="textSecondary"
              underline="none"
              display="block"
            >
              Blog
            </Link>
          </Box>
        </Grid>

        {/* Product Links */}
        <Grid size={{ xs: 12, sm: 6, md: 2 }}>
          <Typography variant="h6" color="textPrimary" text="Product" />
          <Box sx={{ mt: 2 }}>
            <Link
              href="/features"
              color="textSecondary"
              underline="none"
              display="block"
            >
              Features
            </Link>
            <Link
              href="/pricing"
              color="textSecondary"
              underline="none"
              display="block"
            >
              Pricing
            </Link>
            <Link
              href="/docs"
              color="textSecondary"
              underline="none"
              display="block"
            >
              Documentation
            </Link>
            <Link
              href="/support"
              color="textSecondary"
              underline="none"
              display="block"
            >
              Support
            </Link>
          </Box>
        </Grid>

        {/* Resources */}
        <Grid size={{ xs: 12, sm: 6, md: 2 }}>
          <Typography variant="h6" color="textPrimary" text="Resources" />
          <Box sx={{ mt: 2 }}>
            <Link
              href="/api"
              color="textSecondary"
              underline="none"
              display="block"
            >
              API
            </Link>
            <Link
              href="/privacy"
              color="textSecondary"
              underline="none"
              display="block"
            >
              Privacy Policy
            </Link>
            <Link
              href="/terms"
              color="textSecondary"
              underline="none"
              display="block"
            >
              Terms & Conditions
            </Link>
            <Link
              href="/security"
              color="textSecondary"
              underline="none"
              display="block"
            >
              Security
            </Link>
          </Box>
        </Grid>

        {/* Social Links */}
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <Typography variant="h6" color="textPrimary" text="Stay Connected" />
          <Box sx={{ mt: 2, display: "flex", gap: 1 }}>
            <IconButton
              aria-label="Facebook"
              href="https://facebook.com"
              target="_blank"
              color="primary"
            >
              <FacebookIcon />
            </IconButton>
            <IconButton
              aria-label="Twitter"
              href="https://twitter.com"
              target="_blank"
              color="primary"
            >
              <TwitterIcon />
            </IconButton>
            <IconButton
              aria-label="LinkedIn"
              href="https://linkedin.com"
              target="_blank"
              color="primary"
            >
              <LinkedInIcon />
            </IconButton>
            <IconButton
              aria-label="Email"
              href="mailto:<EMAIL>"
              color="primary"
            >
              <EmailIcon />
            </IconButton>
          </Box>
        </Grid>
      </Grid>

      {/* Divider */}
      <Divider sx={{ my: 4 }} />

      {/* Copyright Section */}
      <Typography variant="body2" color="textSecondary" align="center">
        © {new Date().getFullYear()} RuleForge. All rights reserved.
      </Typography>
      <Typography
        variant="body2"
        color="textPrimary"
        text={`© ${new Date().getFullYear()} RuleForge. All rights reserved.`}
      />
    </Box>
  );
};

export default Footer;
