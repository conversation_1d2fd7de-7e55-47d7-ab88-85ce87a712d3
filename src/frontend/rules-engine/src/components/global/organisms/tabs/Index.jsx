import PropTypes from 'prop-types';
import { Tabs, Tab } from '@mui/material';

/**
 * A reusable Tabs component that can be used globally with full flexibility.
 *
 * This component dynamically renders tabs with custom labels and handles enabled/disabled states.
 * It also accepts all necessary props for customization like indicatorColor, textColor, variant, etc.
 * 
 * @component
 * @param {Array<{ label: string, disabled: boolean }>} tabs - An array of tab objects with 'label' and 'disabled' status.
 * @param {number} value - The current selected tab index.
 * @param {function} onChange - Callback function to handle tab changes.
 * @param {string} indicatorColor - The color of the tab indicator.
 * @param {string} textColor - The color of the tab text.
 * @param {string} variant - The variant of the tabs (e.g., 'fullWidth', 'scrollable', etc.).
 * @param {string} orientation - The orientation of the tabs ('horizontal' or 'vertical').
 * @param {object} sx - Material-UI sx style object for custom styling.
 * @param {string} ariaLabel - ARIA label for accessibility.
 * @returns {JSX.Element} - The rendered Tabs component.
 */
const TabsMolecule = ({
  tabs,
  value = 0,
  onChange,
  indicatorColor = 'primary',
  textColor = 'inherit',
  variant = 'standard',
  orientation = 'horizontal',
  sx = {},
  ariaLabel = 'tabs',
  ...rest // Spread the rest props for additional flexibility
}) => {
  return (
    <Tabs
      value={value}
      onChange={onChange}
      indicatorColor={indicatorColor}
      textColor={textColor}
      variant={variant}
      orientation={orientation}
      aria-label={ariaLabel}
      sx={sx}
      {...rest}
    >
      {tabs.map((tab, index) => (
        <Tab key={index} label={tab.label} disabled={tab.disabled} />
      ))}
    </Tabs>
  );
};

TabsMolecule.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired, // The text label of the tab
      disabled: PropTypes.bool, // Whether the tab is disabled
    })
  ).isRequired, // List of tabs is required
  value: PropTypes.number, // The current selected tab index
  onChange: PropTypes.func.isRequired, // Callback when a tab is changed
  indicatorColor: PropTypes.oneOf(['primary', 'secondary']), // Color of the indicator line
  textColor: PropTypes.oneOf(['inherit', 'primary', 'secondary']), // Color of the tab text
  variant: PropTypes.oneOf(['standard', 'scrollable', 'fullWidth']), // Tab variant (default: standard)
  orientation: PropTypes.oneOf(['horizontal', 'vertical']), // Orientation of the tabs (default: horizontal)
  sx: PropTypes.object, // Custom styles using MUI's sx prop
  ariaLabel: PropTypes.string, // ARIA label for accessibility
};

export default TabsMolecule;
