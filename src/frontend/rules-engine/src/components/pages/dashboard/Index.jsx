import { Box } from "@mui/material";
import { withPageHOC } from "../../../hoc/PageHOC";
import DashboardTemplate from "./templates/campaigns/Index";
import { useSelector, useDispatch } from "react-redux";
import OverviewTemplate from "./templates/overview/Index";
import SettingsTemplate from "./templates/settings/Index";
import HelpTemplate from "./templates/help/Index";
import DocumentationTemplate from "./templates/documentation/Index";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import React from "react";
import ListManagementView from "./templates/campaigns/listManagment/listManagementView/ListManagementView";
import BulkNotificationView from "./templates/campaigns/listManagment/bulkNotificationView/BulkNotificationView";
import AccountManagementTemplate from "./templates/campaigns/profile/Index";
import UserManagementTemplate from "./templates/campaigns/userManagement/Index";
import AuthService from "../../../Api/AuthService";
import { setIsTokenRefreshing } from "../../../redux/slices/global/auth";
import ApiKeys from "./templates/campaigns/apiKeys/Index";
import WebhooksTemplate from "./templates/campaigns/webhooks/Index";
// import { dummyCampaignsData } from "../../../utils/dummyData/Campaigns";
/**
 * CampaignsDashboardPage component renders the appropriate dashboard page
 * based on the current tab selected in the state.
 *
 * @component
 * @returns {JSX.Element} The rendered component.
 *
 * @example
 * // Usage example:
 * <CampaignsDashboardPage />
 *
 * @remarks
 * This component uses the `useSelector` hook to access the `tab` state from the Redux store.
 * It dynamically selects and renders the corresponding page component using a higher-order component (HOC).
 *
 * @description
 * The `CampaignsDashboardPage` component is responsible for rendering the appropriate dashboard page
 * based on the current tab selected in the state. The component uses the `useSelector` hook to access the
 * `tab` state from the Redux store. The state is then used to dynamically select and render the corresponding
 * page component using a higher-order component (HOC). The HOC is used to wrap the page component with shared
 * layout and functionality. 
 */
const CampaignsDashboardPage = () => {
  // Access the current tab from the Redux store
  const tab = useSelector((state) => state.dashboard.tab);
  const { isTokenRefreshing } = useSelector((state) => state.auth);
  // Get the navigate function from the router
  const navigate = useNavigate();
  // Get the user information from the Redux store
  const { isAuthenticated } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  // Effect hook to check if the user is logged in when the component mounts
  useEffect(() => {
    // If the user is not logged in, navigate to the login page
    if (!isAuthenticated) {
      navigate("/login");
    }
    // Refresh the token every specified time
    const refreshTime = sessionStorage.getItem("refresh_token_t");
    if(refreshTime && !isTokenRefreshing){
      dispatch(setIsTokenRefreshing(true));
      AuthService.refreshToken(refreshTime);
    }
  }, [isAuthenticated, navigate]);

  // Create an object that maps the tab names to page components
  const tabs = {
    campaigns: DashboardTemplate,
    overview: OverviewTemplate,
    listManagement: ListManagementView,
    bulknotificationList: BulkNotificationView,
    settings: SettingsTemplate,
    help: HelpTemplate,
    accountManagement: AccountManagementTemplate,
    userManagement: UserManagementTemplate,
    apiKeys: ApiKeys,
    documentation: DocumentationTemplate,
    webhooks: WebhooksTemplate,
  };

  // Create a higher-order component (HOC) that wraps the page component with shared layout and functionality
  const PageHOC = withPageHOC(tabs[tab]);

  // If the user is not logged in, return an empty component to show nothing
  if (!isAuthenticated) return <></>;

  // Render the page component wrapped with the HOC
  return (
    <Box
      sx={{
        width: "100%",
      }}
    >
      <PageHOC key={tab} />
    </Box>
  );
};

export default CampaignsDashboardPage;
