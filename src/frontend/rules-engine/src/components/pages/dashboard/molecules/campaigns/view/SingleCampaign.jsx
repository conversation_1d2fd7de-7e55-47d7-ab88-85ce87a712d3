import PropTypes from "prop-types";
import {
  Box,
  CircularProgress,
  Grid2 as Grid,
  useMediaQuery,
} from "@mui/material";
import { Typo<PERSON><PERSON>tom } from "../../../../../global/atoms/typography/Typography";
import DynamicIcon from "../../../../../global/atoms/icons/Index";
import { IconBtnAtom } from "../../../../../global/atoms/buttons/IconBtn";
import ActionMenuAtom from "../../../../../global/atoms/actionMenu/Index"; // Import ActionMenu
import { useState } from "react";
import { useTheme } from "@emotion/react";
import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { setFullCampaign } from "../../../../../../redux/slices/tabs/campaigns/campaign";
import {
  setAnalyticsData,
  setCampaigns,
  setCampaignsTab,
} from "../../../../../../redux/slices/tabs/campaigns/campaigns";
import { registerConfirmation } from "../../../../../global/atoms/dialog/Index";
import { openDialog } from "../../../../../../redux/slices/global/confirmation";
import { Alert, Helpers } from "../../../../../../utils";
import CampaignService from "../../../../../../Api/CampaignService";

/**
 * A molecule component that displays a single campaign with its details.
 *
 * @component
 * @param {Object} param0 - Component props.
 * @param {object} param0.campaign - The campaign data to display.
 * @param {Function} param0.getCampaigns - Function to refetch the campaigns.
 * @returns {JSX.Element} The rendered campaign item component.
 */
const CampaignItem = ({ campaign, getCampaigns }) => {
  const [loadingHover, setLoadingHover] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const {
    name,
    entityId,
    contextId,
    startDateTime,
    endDateTime,
    lastModifiedDateTime,
    status,
  } = campaign;
  // @todo remove the console
  // console.log("the campaign is: ##",campaign)
  // const campaigns = useSelector(
  //   (state) => state.campaigns.campaignsData.campaigns
  // );
  const { receivedData } = useSelector((state) => state.campaign);
  const { entities } = receivedData;
  const { entityName, contextName } = Helpers.getEntityAndContextNames(
    entities,
    entityId,
    contextId
  );
  const dispatch = useDispatch();
  const menuItems = [
    { icon: "Analytics", label: "Analytics", value: "analytics" },
    // Add the Edit campaign button when the campaign on only draft mode
    ...(status === "DRAFT"
      ? [{ icon: "Edit", label: "Edit", value: "edit" }]
      : []),
    { icon: "ContentCopy", label: "Duplicate", value: "duplicate" },
    // Add the Activate campaign button when the campaign is in draft mode
    ...(status === "DRAFT"
      ? [
        {
          icon: "CheckCircle",
          label: "Activate Rule Set",
          value: "activate-campaign",
        },
      ]
      : []),
    // Add the Deactivate campaign button when the campaign is active
    ...(status === "ACTIVE"
      ? [
        {
          icon: "Cancel",
          label: "Deactivate Rule Set",
          value: "deactivate-campaign",
        },
      ]
      : []),
    ...(status === "DRAFT"
      ? [{ icon: "Delete", label: "Delete", value: "delete" }]
      : []),
  ];

  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));
  const isMediumScreen = useMediaQuery(theme.breakpoints.down("md"));

  const handleOptionsClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = async (value) => {
    setAnchorEl(null);

    // --------- Edit -----------
    if (value === "edit") {
      dispatch(setFullCampaign({ campaign: campaign, status: "updating" }));
      dispatch(setCampaignsTab("createCampaign"));
    }

    // --------- Analytics ------------
    if (value === "analytics") {
      dispatch(
        setAnalyticsData({
          campaignData: campaign, // Pass the entire campaign object
          analytics: [], // Reset analytics data for new campaign
        })
      )
      dispatch(setCampaignsTab("analytics"));
    }

    // --------- Duplicate -------------
    if (value === "duplicate") {

      setLoadingHover(true);

      const newCampaignId = `${campaign.rulesetId}_${Helpers.getRandomNumberByDigits(4)}`;

      let res = await CampaignService.duplicateCampaign(campaign.rulesetId, campaign.version, newCampaignId);
      if (!res.success) {
        setLoadingHover(false);
        return Alert(res.data, "error");
      }

      setLoadingHover(false);

      // Refetch the campaigns
      getCampaigns();

      Alert("RuleSet duplicated successfully", "success");

    }

    // ---------- Delete ----------------
    if (value === "delete") {
      const deleteCampaign = async () => {
        setLoadingHover(true);

        let res = await CampaignService.deleteCampaign(campaign.rulesetId);
        if (!res.success) {
          setLoadingHover(false);
          return Alert(res.data, "error");
        }
        setLoadingHover(false);

        // Refetch the campaigns
        getCampaigns();

        Alert("Rule Set deleted successfully", "success");
      };

      const confirmationId = registerConfirmation((confirmed) => {
        if (confirmed) {
          deleteCampaign();
        } else {
          // User clicked "No"
        }
      });

      dispatch(
        openDialog({
          title: "Delete rule set",
          message: "Are you sure you want to delete this rule set?",
          confirmationId,
        })
      );
    }

    // -------------- Activate campaign ----------------

    if (value === "activate-campaign") {
      setLoadingHover(true);
      let res = await CampaignService.activateCampaign(
        campaign.rulesetId,
        campaign.version
      );
      if (!res.success) {
        setLoadingHover(false);
        return Alert(res.data, "error");
      }

      // We are here it's successful
      Alert("Rule Set activated successfully", "success");

      // Refetch the campaigns
      getCampaigns();

      setLoadingHover(false);
    }

    // -------------- Deactivate campaign ----------------

    if (value === "deactivate-campaign") {
      const deactivateCampaign = async () => {
        setLoadingHover(true);
        let res = await CampaignService.deactivateCampaign(
          campaign.rulesetId,
          campaign.version
        );
        if (!res.success) {
          setLoadingHover(false);
          return Alert(res.data, "error");
        }

        // We are here it's successful
        Alert("Rule Set deactivated successfully", "success");

        // Refetch the campaigns
        getCampaigns();

        setLoadingHover(false);
      };

      const confirmationId = registerConfirmation((confirmed) => {
        if (confirmed) {
          deactivateCampaign();
        } else {
          // User clicked "No"
        }
      });

      dispatch(
        openDialog({
          title: "Deactivate rule set",
          message: "Are you sure you want to deactivate this rule set?",
          confirmationId,
        })
      );
    }
  };

  /**
   * A reusable component for displaying campaign details in a consistent format.
   * Accepts a label and value as props and renders a Typography component with
   * the label and value.
   * @param {object} props - An object with label and value properties.
   * @returns {JSX.Element} A JSX element with the campaign detail.
   */
  const DetailItem = ({ label, value }) => (
    <Box>
      {/* Render the label in a smaller font size */}
      <TypographyAtom variant="body2" text={label} />
      {/* Render the value in a larger font size */}
      <TypographyAtom variant="h6" text={value} />
    </Box>
  );
  DetailItem.propTypes = {
    label: PropTypes.string.isRequired,
    value: PropTypes.string.isRequired,
  };

  // Read the entities names from the entities array and join them with a line break
  const entitiesList = Array.isArray(entities)
    ? entities.map((entity) => entity.entityName).join("\n")
    : "No entity";

  return (
    <Box sx={{ position: "relative" }}>
      {
        // Loading hover position abpolute overlay with spinner
        loadingHover && (
          <Box
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              backgroundColor: "rgba(255,255,255,0.8)",
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <CircularProgress color="inherit" />
          </Box>
        )
      }

      <Grid
        container
        spacing={2}
        alignItems="center"
        sx={{
          py: 2,
          pl: { md: 2, xs: "auto" },
          mb: 1,
          // border: "1px solid #ccc",
          borderRadius: "8px",
          backgroundColor: "background.paper",
          width: "100%",
          boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
          flexDirection: {
            xs: "column",
            md: "row",
          },
          height: "auto",
        }}
      >
        {/* Campaign Icon */}
        <Grid
          item
          container
          size={{ xs: 1, md: 1 }}
          spacing={0}
          sx={{
            width: "100%",
            // borderLeft: {
            //   md: "1px solid #ccc",
            //   xs: "none",
            // },
            display: {
              md: "flex",
              xs: "none",
            },
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <DynamicIcon
            iconName="CampaignOutlined"
            color="secondary"
            size={25}
            title="More Options"
          />
        </Grid>

        {/* Campaign Details */}
        <Grid
          item
          size={{ xs: 12, sm: 12, md: 10, flexWrap: "wrap" }}
          container
          spacing={0}
          alignItems="center"
          justifyContent="flex-start"
          sx={{
            width: "100%",
            borderLeft: {
              md: "1px solid #ccc",
              xs: "none",
            },
            flexDirection: {
              md: "row",
              xs: "row",
            },
            order: { xs: 3, md: 2 },
            p: {
              md: 0,
              xs: 2,
            },
          }}
        >
          <Grid
            item
            size={{ md: 2, xs: 12 }}
            sx={{ pl: { md: 2, xs: 0 }, mb: 2 }}
          >
            <TypographyAtom
              sx={{
                wordBreak: "break-word", // Break long words
                overflowWrap: "break-word", // Break long words
                hyphens: "auto", // Add hyphens for better readability
                maxWidth: "100%", // Ensure it doesn't exceed container width
                overflow: "hidden", // Hide overflow
                textOverflow: "ellipsis", // Add ellipsis for very long text
                display: "-webkit-box",
                WebkitLineClamp: 2, // Limit to 2 lines
                WebkitBoxOrient: "vertical"
              }}
              variant={isMediumScreen ? "h5" : "h6"}
              text={name}
            />
            <TypographyAtom
              variant="body2"
              text={Helpers.parseDateTime(lastModifiedDateTime, "date")}
            />
          </Grid>
          <Grid item size={{ md: 2, xs: 12 }}>
            <DetailItem
              label="Entity"
              value={entityName ? entityName : "No Entity"}
            />
          </Grid>
          <Grid item size={{ md: 2, xs: 12 }}>
            <DetailItem
              label="Context"
              value={contextName ? contextName : "No Context"}
            />
          </Grid>
          <Grid item size={{ md: 2, xs: 12 }}>
            <DetailItem
              label="Start"
              value={Helpers.parseDateTime(startDateTime, "date")}
            />
          </Grid>
          <Grid item size={{ md: 2, xs: 12 }}>
            <DetailItem
              label="End"
              value={Helpers.parseDateTime(endDateTime, "date")}
            />
          </Grid>
          <Grid item size={{ md: 2, xs: 12 }}>
            <DetailItem label="Status" value={status} />
          </Grid>
        </Grid>

        {/* Options Icon */}
        <Grid
          item
          size={{ xs: 12, md: 1 }}
          display="flex"
          justifyContent="flex-end"
          alignItems="center"
          sx={{ pr: 2, order: { xs: 2, md: 3 } }}
        >
          <IconBtnAtom
            onClick={handleOptionsClick}
            iconParams={{
              iconName: "MoreVert",
              color: "secondary",
              size: 25,
              title: "More Options",
            }}
            aria-label="options"
          />
          <ActionMenuAtom
            menuItems={menuItems}
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            onMenuItemClick={handleMenuItemClick}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

// Define the prop types for the component
CampaignItem.propTypes = {
  campaign: PropTypes.shape({
    name: PropTypes.string.isRequired,
    entities: PropTypes.arrayOf(PropTypes.object).isRequired,
    startDateTime: PropTypes.string.isRequired,
    endDateTime: PropTypes.string.isRequired,
    lastModifiedDateTime: PropTypes.string.isRequired,
  }).isRequired,
};

export default CampaignItem;
