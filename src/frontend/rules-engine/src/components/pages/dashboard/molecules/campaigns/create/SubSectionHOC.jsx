/* eslint-disable no-unused-vars */
import PropTypes from "prop-types";
import { Box } from "@mui/material";
import DynamicIcon from "../../../../../global/atoms/icons/Index";
import { TypographyAtom } from "../../../../../global/atoms/typography/Typography";
import React from "react";

const OptionalHeader = ({ title, iconName, isOpen, onClickEvent }) => {
  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
      }}
      onClick={onClickEvent}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          width: "100%",

          gap: 1,
        }}
      >
        <DynamicIcon sx={{}} iconName={iconName} />
        <TypographyAtom sx={{}} variant="body1" text={title} />{" "}
        {/* Changed variant for smaller text */}
      </Box>
      <DynamicIcon
        sx={{}}
        iconName={isOpen ? "KeyboardArrowDown" : "KeyboardArrowRight"}
      />{" "}
    </Box>
  );
};

OptionalHeader.propTypes = {
  children: PropTypes.node, // Optional children
  title: PropTypes.string.isRequired, // The title of the tab
  iconName: PropTypes.string.isRequired, // The name of the icon to be displayed
  isOpen: PropTypes.bool.isRequired, // Whether the tab is open or not
  onClickEvent: PropTypes.func.isRequired,
};

/**
 * Higher-order component for rendering tab content with a consistent layout for the "Create Campaign" process.
 *
 * @component
 * @param {Object} props - The props of the component.
 * @param {JSX.Element} props.children - The content to be rendered inside the tab.
 * @param {string} props.title - The title of the tab.
 * @param {string} props.iconName - The name of the icon to be displayed.
 * @param {'viewing' | 'editing'} props.mode - The mode of the tab.
 * @param {boolean} props.isEditing - Whether the tab is in editing mode.
 * @param {JSX.Element} props.HeaderComponent - The optional header component.
 * @param {object} props.sx - Custom styles for the component.
 * @param {object} props.sx.sxContainer - Custom styles for the container.
 * @param {object} props.sx.sxContent - Custom styles for the content.
 * @param {object} [props.sx.sxHeader] - Custom styles for the header.
 * 
 * @param {object} props.sx.sxHeader - Custom styles for the header.
 * @param {object} [props.sx.sxInnerHeader] - Custom styles for the inner header.
 * @param {object} [props.sx.sxIcon] - Custom styles for the icon.
 * @param {object} [props.sx.sxTitle] - Custom styles for the title.
 * @param {object} [props.sx.sxArrowIcon] - Custom styles for the arrow icon.

 * @returns {JSX.Element} The rendered HOC component.
 */
const SubSectionHOC = ({
  children,
  title,
  iconName,
  initial,
  HeaderComponent = null,
  sx = {
    sxContainer: {},
    sxContent: {},
    sxHeader: {},
  },
  mode = "viewing",
}) => {
  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        width: "100%",
        padding: mode === "viewing" ? 0 : 1,
        backgroundColor: "background.paper",
        borderRadius: 2,
        boxShadow:
          mode === "viewing" ? "none" : "0px 4px 8px rgba(0, 0, 0, 0.1)",
        mb: 2,
        border: mode === "viewing" ? "1px solid #e0e0e0" : "none",
        transition: "all 0.3s ease",
        mt: 2,
        ...sx.sxContainer,
      }}
    >
      {/* Header Section */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: mode === "editing" ? 1 : 0, // Adjust margin for the open and closed state
          cursor: "pointer", // Change cursor to indicate clickable area
          padding: 1, // Reduced padding for a thinner header
          ...sx.sxHeader,
        }}
      >
        {HeaderComponent}
      </Box>

      {/* Content Section */}
      {mode === "editing" && (
        <Box
          sx={{
            flexGrow: 1,
            paddingTop: 1,
            minHeight: "200px",
            ...sx.sxContent,
          }}
        >
          {children}
        </Box>
      )}
    </Box>
  );
};

SubSectionHOC.propTypes = {
  children: PropTypes.node.isRequired, // Tab content
  title: PropTypes.string.isRequired, // Tab title
  iconName: PropTypes.string.isRequired, // Icon name for the tab
  HeaderComponent: PropTypes.node, // Optional header component
  isEditing: PropTypes.bool.isRequired,
  sx: PropTypes.shape({
    sxContainer: PropTypes.object, // Custom styles for the container
    sxContent: PropTypes.object, // Custom styles for the content
    sxHeader: PropTypes.object, // Custom styles for the header
    sxInnerHeader: PropTypes.object, // Custom styles for the inner header
    sxIcon: PropTypes.object, // Custom styles for the icon
    sxTitle: PropTypes.object, // Custom styles for the title
    sxArrowIcon: PropTypes.object, // Custom styles for the arrow icon
  }),
  mode: PropTypes.oneOf(["viewing", "editing"]),
};

export default SubSectionHOC;
