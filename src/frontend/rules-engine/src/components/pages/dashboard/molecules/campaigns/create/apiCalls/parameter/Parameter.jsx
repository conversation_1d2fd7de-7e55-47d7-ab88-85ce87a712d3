import React, { useEffect } from "react";
import { useState } from "react";
import { Helpers } from "../../../../../../../../../../utils";
import { useSelector } from "react-redux";
import { Box, Grid2 as Grid } from "@mui/material";
import DropdownAtom from "../../../../../../../../../global/atoms/dropdown/Dropdown";
import CategoryDropdown from "../../../../../../../../../global/atoms/dropdown/CategoryDropdown";

// React component
const Parameter = ({ params }) => {
  // Get the received data from the redux store that is received from the API.
  /** @type {import("../../../../../../../../../../../jsDocs/redux/store").Store['campaign']} */
  const { campaign, receivedData } = useSelector((state) => state.campaign);
  const { transactionVariables, entities } = receivedData;

  const [parameters, setParameters] = useState([]); // It is an array of parameters []

  // Utils
  const parametersUtil = Helpers.createRowsUtil(
    parameters,
    setParameters,
    "parameter",
    Alert
  );

  /**
   * This function save the action and variable operation data in its respective arrays.
   */
  const handleUpdateParameter = (itemIndex, data, type) => {
    const updatedItems = items.map((item, index) => {
      // If item not matched
      if (index != itemIndex) return item;
      // Get updated columns
      // let columns = variablesColumnsUtil.generateItemContentColumns(data);
      const columns = getColumnsFn ? getColumnsFn(data) : item.columns;

      // Return updated item
      return { ...item, id: Math.random(), mode: "viewing", data, columns };
    });
    setParameters(updatedItems);
  };

  const handleCancelParameter = (itemIndex) => {
    const updatedItems = items.map((item, index) =>
      index === itemIndex ? { ...item, mode: "viewing" } : item
    );
    setParameters(updatedItems);
  };

  useEffect(() => {
    if (tab.data) {
      // Update states using the received data
      setParameters(
        params.map((item) => ({
          id: Math.random(),
          columns: [{ content: item.type, gridSize: 6 }],
          mode: "viewing",
          data: item,
        }))
      );
    }
  }, [tab.data]);
  // -------------------------------------------------------

  return (
    <Grid>
      {/* Parameters   */}
      <Grid size={{ xs: 12, sm: 12, md: 12 }} item md={6}>
        <SectionHeaderWithButton title="Condition" />
      </Grid>

      <Grid size={{ xs: 12, sm: 6, md: 12 }} item md={12}>
        <Box sx={{ width: "100%", backgroundColor: "bgPrimary.main" }}>
          {parameters.length > 0 &&
            parameters.map((tab, index) => (
              <ReusableSectionHOC
                key={index}
                index={index}
                tab={tab}
                columns={tab.columns}
                sxContainer={{ backgroundColor: "bgPrimary.main" }}
                rows={tab.rows}
                onEdit={() => parametersUtil.handleEditBtnClick(tab.id)}
                onDelete={() => parametersUtil.handleDeleteItem(tab.id)}
                content={
                  <ParameterContentOrganizer
                    // handleSave={(inputs) => conditionUtil.handleSaveFunc(tab.id, inputs)}
                    handleSave={(data) => {
                      handleUpdateParameter(tab.id, data);
                    }}
                    handleCancel={() => handleCancelParameter(tab.id)}
                    tab={tab}
                  />
                }
              />
            ))}
        </Box>
      </Grid>
    </Grid>
  );
};

export default Parameter;

//
