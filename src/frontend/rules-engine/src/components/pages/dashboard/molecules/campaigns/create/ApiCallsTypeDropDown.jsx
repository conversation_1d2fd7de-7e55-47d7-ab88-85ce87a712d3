import { useEffect, useState } from "react";
import { Helpers } from "../../../../../../utils";
import DropdownAtom from "../../../../../global/atoms/dropdown/Dropdown";
import { Grid2 as Grid } from "@mui/material";
import React from "react";

/**
 *
 * Condition Type dropdown component used for the condition type and operator dropdowns to be shown in the condition content section.
 *
 * @param {object} param0 Prop
 * @param {apiCallTypes} param0.apiCallsTypes - Array of api call types
 * @param {({type: import("../../../../../../../jsDocs/global/global").ValidationObj, operator: import("../../../../../../../jsDocs/global/global").ValidationObj}} param0.onChange - Function to handle the change event
 * @param {import("../../../../../../../jsDocs/global/global").ValidationObj} param0.operator - The operator
 * @param {import("../../../../../../../jsDocs/global/global").ValidationObj} param0.type - The type
 *
 * @returns {JSX.Element} The rendered ApiCallsDropDown component.
 */
const ApiCallsTypeDropDown = ({ apiCallsTypes, onChange, type }) => {
  // The state, we will set the default value of the dropdowns to first value of the type and of the operator
  const [data, setData] = useState({
    type,
    // operator,
  });

  useEffect(() => {
    setData((prev) => ({ type }));
  }, [type]);
  

  // Get the operators of the condition type
  //   const operators = apiCallsTypes?.find(
  //     (item) => item.value === data?.type?.value
  //   ).operators;

  // ---------- Handle Condition type dropdown change ------------

  const handleDropdownChange = (name, value) => {
    // Update local state and call on change
    setData((prev) => {
      // If the change is in the operator then set the operator value else get the previous operator value
      //   let operator = name === "operator" ? value : prev.operator.value;

      // If type chages the type then Select the first operator of the selected type
      //   if (name === "type")
      //     operator = apiCallsTypes?.find((item) => item.value === value)
      //       .operators[0].value;

      // Call the callback
      onChange({
        type: prev.type,
        // operator: { ...prev.operator, value: operator },
        [name]: { ...prev[name], value },
      });

      return {
        ...prev,
        [name]: {
          value,
          isValid: true,
          error: null,
        },
        // operator: { ...prev.operator, value: operator },
      };
    });
  };

  // -------------------------------------------------------

  return (
    <Grid container spacing={2}>
      <Grid item size={{ xs: 12, sm: 6, md: 6, order: 1 }}>
        <DropdownAtom
          key={"apiCallsType"}
          label="Api Call Type *"
          options={apiCallsTypes}
          name={"type"}
          onChange={handleDropdownChange}
          selectedItem={data.type.value}
          sx={{ backgroundColor: "bgPrimary.main" }}
          validation={{
            isShowError: data.type.isShowError,
            error: data.type.error,
          }}
        />
      </Grid>

      {/* <Grid item size={{ xs: 12, sm: 6, md: 6, order: 1 }}>
        <DropdownAtom
          key={"conditionOperator"}
          label="Operator *"
          options={operators}
          name={"operator"}
          onChange={handleDropdownChange}
          selectedItem={data.operator.value}
          sx={{ backgroundColor: "bgPrimary.main" }}
          validation={{
            isShowError: data.operator.isShowError,
            error: data.operator.error,
          }}
        />
      </Grid> */}
    </Grid>
  );
};

export default ApiCallsTypeDropDown;

/**
 * @typedef {import("../../../../../../redux/slices/tabs/campaigns/campaign").CampaignInitialState["receivedData"]["apiCallsTypes"]} apiCallTypes
 */
