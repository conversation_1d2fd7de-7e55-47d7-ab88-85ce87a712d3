import React from "react";
import { Box, Grid2 as Grid } from "@mui/material";
import { TypographyAtom } from "../../../../../../global/atoms/typography/Typography";
import { InputField } from "../../../../../../global/atoms/inputFields/InputField";

const Authentication = ({ authData, setAuthData }) => {


  return (
    <Grid container spacing={2}>
      <Grid size={{ xs: 12 }} item>
        <Box
          textAlign={"center"}
          sx={{ backgroundColor: "rgb(60 60 60 / 99%)" }}
          p={1}
          mb={2}
          mt={1}
        >
          <TypographyAtom
            color={"white"}
            sx={{ color: "white" }}
            text="Authentication"
            variant="h6"
          />
        </Box>
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }} mt={2}>
        <InputField
          label="Api Username"
          value={authData.apiUsername.value}
          onChange={(e) =>
            setAuthData({
              ...authData,
              apiUsername: {
                ...authData.apiUsername,
                value: e.target.value,
                isValid: !!e.target.value,
                isShowError: !e.target.value,
              },
            })
          }
          validation={{error: authData.apiUsername.error, isShowError: authData.apiUsername.isShowError}}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }} mt={2}>
        <InputField
          label="Api Secret"
          value={authData.apiSecret.value}
          onChange={(e) =>
            setAuthData({
              ...authData,
              apiSecret: {
                ...authData.apiSecret,
                value: e.target.value,
                isValid: !!e.target.value,
                isShowError: !e.target.value,
              },
            })
          }
          validation={{error: authData.apiSecret.error, isShowError: authData.apiSecret.isShowError}}

        />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }} mt={2}>
        <InputField
          label="Crediverse Username"
          value={authData.crediverseUsername.value}
          onChange={(e) =>
            setAuthData({
              ...authData,
              crediverseUsername: {
                ...authData.crediverseUsername,
                value: e.target.value,
                isValid: !!e.target.value,
                isShowError: !e.target.value,
              },
            })
          }

          validation={{error: authData.crediverseUsername.error, isShowError: authData.crediverseUsername.isShowError}}
        />
      </Grid>
      <Grid size={{ xs: 12, sm: 6 }} mt={2}>
        <InputField
          label="Crediverse Password"
          value={authData.crediversePassword.value}
          onChange={(e) =>
            setAuthData({
              ...authData,
              crediversePassword: {
                ...authData.crediversePassword,
                value: e.target.value,
                isValid: !!e.target.value,
                isShowError: !e.target.value,
              },
            })
          }
          validation={{error: authData.crediversePassword.error, isShowError: authData.crediversePassword.isShowError}}
        />
      </Grid>
    </Grid>
  );
};

export default Authentication;
