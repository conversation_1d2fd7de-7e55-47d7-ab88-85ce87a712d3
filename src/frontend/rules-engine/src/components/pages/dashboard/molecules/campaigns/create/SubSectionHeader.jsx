import PropTypes from "prop-types";
import { Grid2 as Grid, Typography } from "@mui/material";
import React from "react";

/**
 * Reusable Section Header Component using MUI Grid
 *
 * @component
 * @param {object} props - Component props.
 * @param {Array<{ label: string, gridSize: number }>} props.columns - Array of objects defining column label and grid size (1-12).
 * @param {object} props.sx - Custom styles for the header container.
 * @param {object} props.sxColumn - Custom styles for the header column.
 * @param {object} props.sxText - Custom styles for the header text.
 * @returns {JSX.Element} The rendered section header component.
 */
const SectionHeaderMolecule = ({ columns, sx, sxColumn,sxText }) => {
  return (
    <Grid sx={{width: "100%", px: 2,py: 1,borderRadius:  1,backgroundColor: "primary.main", ...sx}} container alignItems="center">
      {columns.map((column, index) => (
        <Grid item xs={{ ...sxColumn }} size={column.gridSize} key={index}>
          <Typography
            variant="body1"
            sx={{
              color: "mainColor.main", // White text color
              fontWeight: "bold", // Bold text for the header
              textAlign: "left", // Align text to the left
              ...sxText,
              ...column.sx,
            }}
          >
            {column.label}
          </Typography>
        </Grid>
      ))}
    </Grid>
  );
};

SectionHeaderMolecule.propTypes = {
  columns: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired, 
      gridSize: PropTypes.number.isRequired, 
    })
  ).isRequired,
  sx: PropTypes.object, 
  sxColumn: PropTypes.object,
  sxText: PropTypes.object
};

export default SectionHeaderMolecule;
