import { Box, Grid2 as Grid } from "@mui/material";
import Dropdown<PERSON>tom from "../../../../../../global/atoms/dropdown/Dropdown";
import { Btn as BtnAtom } from "../../../../../../global/atoms/buttons/Button";
import { useEffect, useId, useRef, useState } from "react";
import PropTypes from "prop-types";
import { useDispatch, useSelector } from "react-redux";
import { Alert, campaignValidators, Helpers } from "../../../../../../../utils";
import { InputField } from "../../../../../../global/atoms/inputFields/InputField";
import CategoryDropdown from "../../../../../../global/atoms/dropdown/CategoryDropdown";
import React from "react";

// -------------------------------------------------------------------------

const ParameterContentOrganizer = ({
  handleCancel,
  handleSave,
  entityAndContext,
  tab = {},
}) => {
  // Get the received data from the redux store that is received from the API.
  /** @type {import("../../../../../../../../../../../jsDocs/redux/store").Store['campaign']} */
  const { campaign, receivedData } = useSelector((state) => state.campaign);

  const [data, setData] = useState(
    Helpers.defaultValues(["name", "type", "value"], { ...tab.data })
    // []
  );
 

  const { transactionVariables, entities } = receivedData;

  let transactionContextProperties = Helpers.generateTransContextOptions(
    entities,
    entityAndContext.entityId,
    entityAndContext.contextId,
    true
  );

  /** Local persistence options of the current campaign */
  const persistentVariableOptions = campaign.persistentVariables.map(
    (item) => ({
      value: "{" + item.variableId + "}",
      label: item.name,
      description: item.description,
    })
  );

  /** Local variable options of the current campaign */
  const localVariableOptions = campaign.localVariables.map(
    (item) => ({
      value: "{" + item.variableId + "}",
      label: item.name,
      description: item.description,
    })
  );

  /** Transaction variable options of the current campaign */
  const transactionVariableOptions = transactionVariables.map((item) => ({
    value: "{" + item.value + "}",
    label: item.label,
    description: item?.description ? item.description : item.label,
  }));

  // Ready the variable options to be displayed in the category dropdown
  const variableOptions = [];

  // Add the persistent variables to the variableOptions
  if (persistentVariableOptions.length > 0) {
    variableOptions.push({
      category: "Persistent Variables",
      options: persistentVariableOptions,
    });
  }

  // Add the local variables to the variableOptions
  if (localVariableOptions.length > 0) {
    variableOptions.push({
      category: "Local Variables",
      options: localVariableOptions,
    });
  }
  if (transactionVariableOptions.length > 0) {
    variableOptions.push({
      category: "Transaction Variables",
      options: transactionVariableOptions,
    });
  }
  if (transactionContextProperties.length > 0) {
    variableOptions.push({
      category: "Transaction Context Properties",
      options: transactionContextProperties,
    });
  }
 

  /**
   This function validates the fields and pass the user inputs to the upper level component for saving.
   */

  const handleSubmit = () => {
    

    // Validate all fields using isValidAllFields
    const validationResult = campaignValidators.global.isValidAllFields(data);
    

    if (validationResult.success) {
      let obj = {
        ...Helpers.returnValues(data),
      };
      

      handleSave(obj);
    } else {
      // If there are validation errors, update the inputs state to show errors
      const updatedData = { ...data };

      // Iterate over the errors array and update the state
      validationResult.errors.forEach(({ field, error }) => {
        updatedData[field] = {
          ...updatedData[field],
          isValid: false, // Set field as invalid
          isShowError: true, // Show error message
          error, // Set error message
        };
      });

      // Update the inputs state with the new validation statuses
      setData(updatedData);
    }
  };

  // ----------- On Condition type and operator change ---------

  const handleDropdownChange = (name, value) => {
    setData((prev) => ({
      ...prev,
      [name]: { isValid: true, isShowError: false, error: null, value },
    }));
  };

  // Ready the onChange function for the conditionType, leftOperand, rightoperand dropdown to update the state and when data change then call the onDataChange callback function to notify the parent component that the data has been changed so we can use getData function to validate and get the data
  const onCategoryDropdownChange =
    Helpers.handleDropdownChangeFactoryFn(setData);

  // ------------------------------------------------------------
  // @todo please write the isValid, isShowError etc code in correct format
  return (
    <Box key={"condition_content"} sx={{ width: "100%", padding: 2 }}>
      <Grid container spacing={2}>
        <Grid size={{ xs: 12, sm: 6 }} mt={2}>
          <InputField
            fullWidth
            value={data.name?.value}
            onChange={(e) =>
              setData((prev) => ({
                ...prev,
                name: {
                  isValid: true,
                  isShowError: false,
                  error: null,
                  value: e.target.value,
                },
              }))
            }
            name={"name"}
            sx={{ padding: 0 }}
            placeholder="Enter name"
            onKeyPress={(e) => e.key === "Enter" && handleAddNew()}
            variant="outlined"
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6 }} mt={2}>
          <DropdownAtom
            key={"type"}
            label="type *"
            options={receivedData.types}
            name={"type"}
            onChange={handleDropdownChange}
            selectedItem={data?.type?.value}
            sx={{ backgroundColor: "bgPrimary.main" }}
            validation={{
              isShowError: data?.type?.isShowError,
              error: data?.type?.error,
            }}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6 }} mt={2}>
          <CategoryDropdown
            categories={variableOptions}
            label="Variable Name"
            value={data?.value}
            name={"value"}
            required={true}
            onChange={onCategoryDropdownChange}
          />
        </Grid>
        <Grid
          size={{ xs: 12, sm: 12 }}
          mt={2}
          sx={{ display: "flex", justifyContent: "flex-end" }}
        >
          <BtnAtom
            onClick={handleSubmit}
            sx={{ backgroundColor: "primary.secondary", marginRight: "10px" }}
            text="Save"
          />
          <BtnAtom
            onClick={handleCancel}
            sx={{ backgroundColor: "primary.secondary" }}
            text="Cancel"
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default ParameterContentOrganizer;
