import PropTypes from "prop-types";
import { useState } from "react";
import { Box } from "@mui/material";
import DynamicIcon from "../../../../../global/atoms/icons/Index";
import { TypographyAtom } from "../../../../../global/atoms/typography/Typography";
import React from "react";

/**
 * OptionalHeader is a component that can be used to display a header with an icon and
 * a title. It also takes an isOpen prop which is used to determine whether to show
 * a right arrow or a down arrow. It also takes an onClickEvent prop which is used to
 * handle the onClick event.
 * @param {{ title: string; iconName: string; isOpen: boolean; onClickEvent: () => void; }} props
 * @returns {JSX.Element}
 */
const OptionalHeader = ({
  title,
  iconName,
  isOpen,
  onClickEvent,
  iconColor,
}) => {
  return (
    <Box
      sx={{
        width: "100%",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
      }}
      onClick={onClickEvent}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          width: "100%",

          gap: 1,
        }}
      >
        <DynamicIcon sx={{ color: iconColor }} iconName={iconName} />
        <TypographyAtom  variant="body1" sx={{fontWeight:'bold',fontSize:'1.4rem',color:"textColor.primary"}} text={title} />{" "}
        {/* Changed variant for smaller text */}
      </Box>
      <DynamicIcon
        sx={{}}
        iconName={isOpen ? "KeyboardArrowDown" : "KeyboardArrowRight"}
      />{" "}
    </Box>
  );
};

OptionalHeader.propTypes = {
  children: PropTypes.node, // Optional children
  title: PropTypes.string.isRequired, // The title of the tab
  iconName: PropTypes.string.isRequired, // The name of the icon to be displayed
  isOpen: PropTypes.bool.isRequired, // Whether the tab is open or not
  onClickEvent: PropTypes.func.isRequired,
};

/**
 * Higher-order component for rendering tab content with a consistent layout for the "Create Campaign" process.
 *
 * @component
 * @param {JSX.Element} children - The content to be rendered inside the tab.
 * @param {string} title - The title of the tab.
 * @param {string} iconName - The name of the icon to be displayed.
 * @param {JSX.Element} HeaderComponent - The optional header component.
 * @param {object} sx - Custom styles for the component.
 * @param {object} sx.sxContainer - Custom styles for the container.
 * @param {object} sx.sxContent - Custom styles for the content.
 * @param {object} sx.sxHeader - Custom styles for the header.
 * @param {object} sx.sxInnerHeader - Custom styles for the inner header.
 * @param {object} sx.sxIcon - Custom styles for the icon.
 * @param {object} sx.sxTitle - Custom styles for the title.
 * @param {object} sx.sxArrowIcon - Custom styles for the arrow icon.
 * @returns {JSX.Element} The rendered HOC component.
 */
const SectionTabHOC = ({
  children,
  title,
  iconName,
  iconColor,
  HeaderComponent = null,
  sx = {
    sxContainer: {},
    sxContent: {},
    sxHeader: {},
  },
}) => {
  const [isOpen, setIsOpen] = useState(false); // State to control whether content is visible

  const handleToggle = () => {
    setIsOpen(!isOpen); // Toggle the open state
  };

  return (
    <Box
      sx={{
        display: "flex",
        flexDirection: "column",
        width: "100%",
        px: isOpen ? 0 : 0, // Reduce padding when tab is closed
        backgroundColor: "background.paper",
        borderRadius: 2,
        // boxShadow: isOpen ? "0px 4px 8px rgba(0, 0, 0, 0.1)" : "none",
        boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)",
        mb: 2, // Add margin between sections (smaller margin for thinner look)
        border: isOpen ? "1px solid #e0e0e0" : "none", // Border only when open
        transition: "all 0.3s ease", // Smooth transition for height and border
        mt: 2,
        pb:isOpen ? 2:0,
        ...sx.sxContainer,
      }}
    >
      {/* Header Section */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: isOpen ? 1 : 0, // Adjust margin for the open and closed state
          cursor: "pointer", // Change cursor to indicate clickable area
          padding: 2, // Reduced padding for a thinner header
          ...sx.sxHeader,
        }}
        onClick={handleToggle}
      >
        {HeaderComponent ? (
          <HeaderComponent 
          // onClickEvent={handleToggle}
           />
        ) : (
          <OptionalHeader
            onClickEvent={handleToggle}
            title={title}
            iconName={iconName}
            iconColor={iconColor}
            isOpen={isOpen}
          />
        )}
      </Box>

      {/* Content Section */}
      {isOpen && (
        <Box
          sx={{
            flexGrow: 1,
            paddingTop: 1,
            minHeight: "200px",
      
            px:2,
            ...sx.sxContent,
          }}
        >
          {children}
        </Box>
      )}
    </Box>
  );
};

SectionTabHOC.propTypes = {
  children: PropTypes.node.isRequired, // Tab content
  title: PropTypes.string.isRequired, // Tab title
  iconName: PropTypes.string.isRequired, // Icon name for the tab
  HeaderComponent: PropTypes.node, // Optional header component
  sx: PropTypes.shape({
    sxContainer: PropTypes.object, // Custom styles for the container
    sxContent: PropTypes.object, // Custom styles for the content
    sxHeader: PropTypes.object, // Custom styles for the header
    sxInnerHeader: PropTypes.object, // Custom styles for the inner header
    sxIcon: PropTypes.object, // Custom styles for the icon
    sxTitle: PropTypes.object, // Custom styles for the title
    sxArrowIcon: PropTypes.object, // Custom styles for the arrow icon
  }),
};

export default SectionTabHOC;
