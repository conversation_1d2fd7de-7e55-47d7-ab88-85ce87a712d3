import PropTypes from "prop-types";
import { Box, Grid2 as Grid, Typography } from "@mui/material";
import { Btn as Btn<PERSON>tom } from "../../../../../global/atoms/buttons/Button";
import DynamicIcon from "../../../../../global/atoms/icons/Index";
import React from "react";
import { registerConfirmation } from "../../../../../global/atoms/dialog/Index";
import { openDialog } from "../../../../../../redux/slices/global/confirmation";
import { useDispatch } from "react-redux";

/**
 * A reusable component for rendering a section row in the campaign create page.
 * 
 * It renders a row with multiple columns, each column can have a different grid size.
 * The row is rendered as a flex container with justify-content set to space-between.
 * The columns are rendered as grid items with the size set to their respective gridSize.
 * The row also has a height of 50px and a padding of 1px.
 * The background color of the row is set to mainColor.main.
 * The box shadow is set to 0px 2px 4px rgba(0, 0, 0, 0.1) when the mode is not "viewing"
 * The row also has three action buttons: edit, delete, and an arrow down icon.
 * The edit and delete buttons are only visible when the mode is "viewing".
 * The arrow down icon is visible when the mode is not "viewing".
 * The delete button is only visible if the isDeletable prop is true.
 * The edit button is only visible if the isEditable prop is true.
 * 
 * @param {object} props - Component props.
 * @param {array} props.columns - Array of column configuration objects, each with the following properties:
 *   - content: The content to render in the column.
 *   - gridSize: The grid size for the column.
 * @param {function} props.onEdit - Function to handle the edit action.
 * @param {function} props.onDelete - Function to handle the delete action.
 * @param {object} props.sx - Custom styles for the container.
 * @param {boolean} [props.isEditable = true] - Whether the edit button should be visible or not.
 * @param {boolean}   [props.isDeletable = true] - Whether the delete button should be visible or not.
 * @param {'viewing' | 'editing'} props.mode - The mode of the component.
 * @param {object} props.dialogData - Object containing dialog data for the delete confirmation dialog.
 * @param {boolean} [props.dialogData.isShowDialog = true] - Whether to show the dialog or not.
 * @param {string} [props.dialogData.title = "Confirm Delete"] - The title of the dialog.
 * @param {string} [props.dialogData.message = "Are you sure you want to delete this item?"] - The message of the dialog.
 * @param {string} [props.dialogData.confirmationId="deleteConfirmationId"] - The ID of the confirmation dialog.
 * @param {object} props.tab - Object containing tab properties.

 * 
 * @returns {JSX.Element} The rendered component.
 */
const SubSectionRowMolecule = ({
  columns = [],
  onEdit,
  onDelete,
  sx,
  isEditable = true,
  isDeletable = true,
  mode = "viewing",
  tab,
  dialogData = {
    isShowDialog: true,
    title: "Confirm Delete",
    message: "Are you sure you want to delete this item?",
    confirmationId: "deleteConfirmationId",
  },
}) => {
  const dispatch = useDispatch();

  const handleDelete = () => {
    // if(typeof tab.id === "string" && tab.id.startsWith("new")){
    //   return onDelete()
    // }

    const confirmationId = registerConfirmation((confirmed) => {
      if (confirmed) {
        onDelete();
        // Perform delete operation
      } else {
        // User clicked "No"
      }
    });

    dispatch(
      openDialog({
        title: "Confirm Delete",
        message: "Are you sure you want to delete this item?",
        confirmationId,
      })
    );
  };
  // Calculate the remaining gridSize for the action column
  const actionGridSize =
    12 - columns.reduce((acc, column) => acc + column.gridSize, 0);

  return (
    <Box
      sx={{
        width: "100%",
        // padding: 1,
        // maxHeight:"100px",
        height: "50px",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        px: 2,
        // backgroundColor: "mainColor.main",
        borderRadius: "8px",
        backgroundColor: "transparent",
        boxShadow:
          mode !== "viewing" ? "0px 2px 4px rgba(0, 0, 0, 0.1)" : "none",

        ...sx, // Allow passing additional custom styles
      }}
    >
      <Grid
        container
        alignItems="center"
        sx={{
          width: "100%",
          justifyContent: "space-between",
          alignItems: "center",
        }}
        // spacing={2}
      >
        {columns.map((column, index) => (
          <Grid item size={column.gridSize} key={index}>
            <Typography
              variant="body1"
              sx={{
                whiteSpace: "nowrap" /* Prevents text from wrapping */,
                overflow: "hidden" /* Hides overflowed content */,
                textOverflow: "ellipsis" /* Adds the '...' */,
                maxWidth: "90%" /* Set max width as needed */,
              }}
            >
              {column.content}
            </Typography>
          </Grid>
        ))}
        {mode !== "viewing" && (
          <Grid
            item
            size={actionGridSize}
            sx={{
              display: "flex",
              justifyContent: "flex-end",
              alignItems: "center",
              ml: "auto",
            }}
          >
            <DynamicIcon iconName="KeyboardArrowDown" />
          </Grid>
        )}

        {(isEditable || isDeletable) && (
          <Grid
            item
            sx={{
              display: "flex",
              gap: 1,
              justifyContent: "flex-end",
              ml: "auto",
            }}
          >
            {mode === "viewing" && isEditable && (
              <BtnAtom
                sx={{ backgroundColor: "secondary.main" }}
                text="Edit"
                onClick={onEdit}
              />
            )}
            {mode === "viewing" && isDeletable && (
              <BtnAtom
                sx={{ backgroundColor: "danger.main" }}
                text="Delete"
                onClick={() =>
                  dialogData.isShowDialog ? handleDelete() : onDelete()
                }
              />
            )}
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

SubSectionRowMolecule.propTypes = {
  sections: PropTypes.arrayOf(
    PropTypes.shape({
      content: PropTypes.oneOfType([PropTypes.node, PropTypes.string])
        .isRequired,
      gridSize: PropTypes.number.isRequired,
    })
  ).isRequired,
  onEdit: PropTypes.func.isRequired,
  columns: PropTypes.array.isRequired,
  onDelete: PropTypes.func.isRequired,
  sx: PropTypes.object,
  isEditable: PropTypes.bool,
  isDeletable: PropTypes.bool,
  mode: PropTypes.oneOf(["viewing", "editing"]),
};

export default SubSectionRowMolecule;
