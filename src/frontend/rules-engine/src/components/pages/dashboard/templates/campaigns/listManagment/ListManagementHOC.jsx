import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Box } from "@mui/material";
import { Typography<PERSON>tom } from "../../../../../global/atoms/typography/Typography";
import { Btn } from "../../../../../global/atoms/buttons/Button";
import AddListModal from "../../../atoms/addListModal/AddListModal";
import ListRowItem from "../../../organisms/campaigns/listManagement/ListRowItem";
import { useDispatch, useSelector } from "react-redux";
import { setReceivedData } from "../../../../../../redux/slices/tabs/campaigns/campaign";
import LoadingBackdropAtom from "../../../../../global/atoms/loading/LoadingBackdrop";
import ListService from "../../../../../../Api/ListService";
import { Alert } from "../../../../../../utils/index";
import { ErrorMessage } from "../../../../../global/atoms/errors/Errors";
/**
 * Higher-Order Component for List Management Views.
 *
 * Dynamically generates a list management component based on the provided configuration.
 *
 * @param {Object} config - Configuration for the HOC.
 * @param {string} config.title - Title of the component (e.g., "List Management").
 * @param {string} config.id - ID of the component (e.g., "campaigns").
 * @param {{label: string, id: string}[]} config.headers - Column headers for the list.
 * @param {import("../../../../../../Api/ListService").default["downloadList"]} config.apiDownloadListFunction - API function to download the list
 * @param {import("../../../../../../Api/ListService").default["getAllList"]} config.apiGetAllFunction - API function to call for fetching all items.
 * @param {import("../../../../../../Api/ListService").default["deleteList"]} config.apiDeleteFunction - API function to call for deleting an item.
 * @param {import("../../../../../../Api/ListService").default["addList"]} config.apiSubmitFunction - API function to call for submitting a new item.
 * @param {import("../../../../../../Api/ListService").default["sendBulkSms"]} config.apiSendBulkSmsFunction - API function to call for sending the bulk sms for the bulk notification list
 * @param {string} config.receivedDataObjectPropKey - The key in the receivedData redux object where the list data is stored.
 * @param {Array} [config.fields] - Additional fields to include in the modal and list.
 * @returns {JSX.Element} A React component for managing a list.
 */
const ListManagmentHOC = (config) => {
  const {
    title,
    headers,
    apiDownloadListFunction,
    apiGetAllFunction,
    apiDeleteFunction,
    apiSubmitFunction,
    apiSendBulkSmsFunction,
    fields = [],
    id,
    receivedDataObjectPropKey,
  } = config;

  // Return the functional component
  return function ComponentHOC() {
    const [isModalOpen, setIsModalOpen] = useState(false); // State for modal visibility
    const [error, setError] = useState(""); // State for error handling
    const [loading, setLoading] = useState(true); // State for loading state
    /** @type {import("../../../../../../../jsDocs/redux/store").Store['campaign']} */
    const { receivedData } = useSelector((state) => state.campaign);
    const dispatch = useDispatch();
    const lists = id === "bulknotificationList" ? receivedData[receivedDataObjectPropKey] || [] : receivedData[receivedDataObjectPropKey]?.lists || []; // Get the list from the redux store

    // Open the modal
    const handleOpenModal = () => setIsModalOpen(true);

    // Close the modal
    const handleCloseModal = () => setIsModalOpen(false);

    // --------- On component mount, fetch the list -------------

    useEffect(() => {
      fetchList();
    }, []);
    /**
     * Function to fetch the list and save to the state
     * @returns {Promise<void>}
     */
    const fetchList = async () => {
      setLoading(true); // Set loading to true

      try {
        const apiResponse = await apiGetAllFunction(); // Call the API to get all items

        // If error
        if (!apiResponse.success) {
          return setError(apiResponse.data);
        }
        // Update the state
        dispatch(
          setReceivedData({ [receivedDataObjectPropKey]: apiResponse.data })
        );
      } catch (error) {
        return setError(error);
      } finally {
        setLoading(false); // Set loading to false
      }
    };

    // ---------------------------------------------------------

    /**
     *
     * @param {*} newItem
     * @param {(message)=>  void} [successCallback]
     * @param {(error)=> void} [failCallback]
     */
    const handleAddItem = async (
      newItem,
      successCallback = undefined,
      failCallback = undefined
    ) => {
      try {
        if (apiSubmitFunction) {
          // Call our api
          const apiResponse = await apiSubmitFunction(newItem); // Call the API to add the item

          // If not success and if fail callback is provided then call it
          if (!apiResponse.success && failCallback) {
            return failCallback(apiResponse.data);
          }

          // We are here it means it's success
          if (successCallback) {
            successCallback(apiResponse.data);
          }

          // Refetch the list
          fetchList();

          handleCloseModal(); // Close the modal after adding the item
        }
      } catch (error) {
        Alert(`${error}`, "error");
        console.error("Error adding item:", error);
      }
    };


    return (
      <>
        {/* Back Button */}
        {/* <IconBtnAtom
          sx={{ my: 2 }}
          iconParams={{
            iconName: "ArrowBackIos",
            color: "secondary",
            size: 25,
            title: "More Options",
          }}
          onClick={() => dispatch(setCampaignsTab("viewCampaigns"))}
        /> */}

        {/* Main List Container */}
        <Box
          sx={{
            padding: 2,
            boxShadow: 2,
            borderRadius: 2,
            maxWidth: "1200px",
            margin: "auto",
            backgroundColor: "#fff",
            position: "relative",
          }}
        >
          {
            // Is loading
            loading && (
              <LoadingBackdropAtom message={"Loading list..."} open={loading} />
            )
          }

          {!loading && error && (
            <ErrorMessage errorMessage={error} />
          )}

          {
            // If not loading and not any error then show the list
            !loading && !error && (
              <>
                {/* Add List Button */}
                <Btn
                  variant="contained"
                  // color="primary"
                  sx={{ mb: 2, position: "absolute", right: 5 }}
                  onClick={handleOpenModal}
                  text="Add List"
                />

                {/* Title */}
                <TypographyAtom
                  variant="h4"
                  sx={{ fontWeight: "bold", mb: 3, textAlign: "left" }}
                  text={title}
                />

                {/* Header Row */}
                <Box
                  sx={{
                    display: "grid",
                    gridTemplateColumns: `repeat(${headers.length}, 1fr)`,
                    gap: 2,
                    textAlign: "left",
                    padding: "8px 16px",
                    backgroundColor: "#f4f4f4",
                    fontWeight: "bold",
                    borderBottom: "2px solid #ddd",
                  }}
                >
                  {headers.map((header, idx) => (
                    <TypographyAtom
                      key={idx}
                      variant="h6"
                      text={header.label}
                    />
                  ))}
                </Box>

                {/* List Rows */}
                {lists.map((item, index) => (
                  <ListRowItem
                    key={index}
                    item={item}
                    index={index}
                    headers={headers}
                    // apiDownloadListFunction={()=>handleDownloadList(id === "bulknotificationList" ? item.id : item.listId)}
                    apiDownloadListFunction={apiDownloadListFunction}
                    // apiDeleteFunction={()=>{
                    //   console.log(item,"item");
                    //   id === "bulknotificationList" ? handleDelete(item.id) : handleDelete(item.listId)
                    //   // apiDeleteFunction(item.id)}
                    // }}
                    apiDeleteFunction={apiDeleteFunction}
                    apiSendBulkSmsFunction={apiSendBulkSmsFunction}
                    fetchList={fetchList}
                    fields={fields}
                  />
                ))}

                {/* Empty List Fallback */}
                {lists.length === 0 && (
                  <Box sx={{ display: "flex", flexDirection: "column" }}>
                    <TypographyAtom
                      variant="body1"
                      sx={{ textAlign: "center", mt: 3 }}
                      text="No list available"
                    />
                  </Box>
                )}

                {/* Modal for Adding Items */}
                <AddListModal
                  open={isModalOpen}
                  onClose={handleCloseModal}
                  onSubmit={handleAddItem}
                  fields={fields}
                  id={id}
                />
              </>
            )
          }
        </Box>
      </>
    );
  };
};

/**
 *
 * @param {Array} data
 * @param {*} filename
 */
async function downloadListCsv(data, filename) {
  const blob = new Blob([data], { type: "text/csv" });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  a.click();
  window.URL.revokeObjectURL(url);
}

// PropTypes Validation for Config
ListManagmentHOC.propTypes = {
  title: PropTypes.string.isRequired,
  id: PropTypes.string.isRequired,
  headers: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      id: PropTypes.string.isRequired,
    })
  ).isRequired,
  apiDownloadListFunction: PropTypes.func,
  apiGetAllFunction: PropTypes.func,
  apiDeleteFunction: PropTypes.func,
  apiSubmitFunction: PropTypes.func,
  fields: PropTypes.arrayOf(PropTypes.string),
  receivedDataObjectPropKey: PropTypes.string.isRequired,
};

export default ListManagmentHOC;
