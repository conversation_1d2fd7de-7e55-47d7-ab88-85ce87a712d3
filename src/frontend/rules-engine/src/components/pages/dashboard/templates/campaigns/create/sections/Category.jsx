import { Box } from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../../../global/atoms/typography/Typography";
import { Btn as BtnAtom } from "../../../../../../global/atoms/buttons/Button";
import { useEffect,  useState } from "react";
import SectionHeaderMolecule from "../../../../molecules/campaigns/create/SubSectionHeader";
import KeyMappingContentOrganizer from "../../../../organisms/campaigns/create/colMappings/CollectionMappingContent";
import { useDispatch, useSelector } from "react-redux";
import { Alert, campaignValidators, Helpers } from "../../../../../../../utils";
import { setCampaign } from "../../../../../../../redux/slices/tabs/campaigns/campaign";
import React from "react";
import { InputField } from "../../../../../../global/atoms/inputFields/InputField";
/**
 * @typedef {import('../../../../../../../../jsDocs/components/dashboard').CollectionMappingInputData} InputsState
 */

const CategorySection = () => {
    const { campaign } = useSelector((state) => state.campaign);

  

    const { category } = campaign;
  
  
      
    const [isShowSaveBtn, setIsShowSaveBtn] = useState(false);
    const [categoryInput, setCategoryInput] = useState({
        value: category,
        isValid: true,
        error: "",
        isShowError: false,
    });

    const handleInputChange = (event) => {
        setCategoryInput({
            ...categoryInput,
            value: event.target.value,
        });
    };

    const handleBluer = (event) => {
        setCategoryInput({
            ...categoryInput,
            isShowError: true,
        });
    };
    const handleSubmitClick = () => {
        setCampaign({
            ...campaign,
            category: categoryInput.value,
        });
    };

    useEffect(() => {
        setCategoryInput({
            value: category,
            isValid: true,
            error: "",
            isShowError: false,
        });
    }, [category]);
  return (
    <Box>
      {/* Campaign Name Input */}
      <InputField
        label="Category"
        placeholder="Enter category"
        key={"Category"}
        validator={campaignValidators.details.name}
        validation={{
          isShowError: categoryInput.isShowError,
          error: categoryInput.error,
        }}
        validateOn="change"
        onBlur={handleBluer}
        name="category"
        value={categoryInput.value}
        onChange={handleInputChange}
        sx={{
          width: {
            xs: "100%",
            md: "40%",
          },
        }}
      />
       {isShowSaveBtn && (
        <BtnAtom
          text="Save"
          sx={{
            width: "100%",
            maxWidth: "200px",
            backgroundColor: "primary.secondary",
            ml: "auto",
          }}
          onClick={handleSubmitClick}
        />
      )}
    </Box>
  );
};

export default CategorySection;
