import ListServiceClass from "../../../../../../../Api/ListService";
import ListManagmentHOC from "../ListManagementHOC";
import endPoints from "../../../../../../../Api/EndPoints";

const ListService = new ListServiceClass(endPoints.list);

const ListManagementView = ListManagmentHOC({
  title: "List Management",
  id: "List",
  headers: [{label: "Name", id: "name"}, {label: "Rows Count", id: "elemsCount"}, {label: "Action", id: "action"}],
  fields: [
    { name: "name", label: "Name", type: "string", required: true },
    { name: "dataType", label: "Data Type", type: "string", required: false },
  ],
  receivedDataObjectPropKey: "lists",
  apiDownloadListFunction: ListService.downloadList,
  apiGetAllFunction: ListService.getAllList,
  apiDeleteFunction: ListService.deleteList,
  apiSubmitFunction: ListService.addList,
});

export default ListManagementView;
