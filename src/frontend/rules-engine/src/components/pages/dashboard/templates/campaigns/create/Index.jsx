import React from "react";
import { Box } from "@mui/material";
import DetailsSection from "./sections/Details";
import ColMappingSection from "./sections/ColMapping";
import LocalVarSection from "./sections/LocalVar";
import PersistentVarSection from "./sections/PersistentVar";
import SectionTabHOC from "../../../molecules/campaigns/create/SectionTabHOC";
import { useSelector } from "react-redux";
import { Helpers } from "../../../../../../utils";
import EvaluationRulesSection from "./sections/EvaluationRules";
import OutcomeRulesSection from "./sections/OutcomeRules";
// import CategorySection from "./sections/Category";

/**
 * The main component for the campaign creation page.
 * @returns {React.ReactElement} The main component for the campaign creation page.
 */
const Index = () => {
  // Get the campaign data from the Redux store
  /** @type {import("../../../../../../../jsDocs/redux/store").Store['campaign']} */
  const { campaign } = useSelector(
    /** @param {{ campaign: any }} state */ (state) => state.campaign
  );

  /**
   * Defining the sections of the campaign form
   * @type {Array.<{id: string, name: string, component: React.ComponentType, icon: string, option: any}>}
   */
  const sections = [
    {
      id: "collectionMappings",
      name: "Collection Mapping",
      component: ColMappingSection,
      icon: "TaskAlt",
      option: campaign.collectionMappings,
    },
    {
      id: "persistentVariables",
      name: "Persistent Variables",
      component: PersistentVarSection,
      icon: "TaskAlt",
      option: campaign.persistentVariables,
    },
    {
      id: "localVariables",
      name: "Local Variables",
      component: LocalVarSection,
      icon: "TaskAlt",
      option: campaign.localVariables,
    },
    // {
    //   id: "rules",
    //   name: "Rules",
    //   component: RulesSection,
    //   icon: "TaskAlt",
    //   option: campaign.rules,
    // },
    {
      id: "evaluationRules",
      name: "Evaluation Rules",
      component: EvaluationRulesSection,
      icon: "TaskAlt",
      option: campaign.evaluationRules,
    },
    {
      id: "outcomeRules",
      name: "Outcome Rules",
      component: OutcomeRulesSection,
      icon: "TaskAlt",
      option: campaign.outcomeRules,
    },
  ];

  return (
    <Box>
      <DetailsSection />
      {/* Render each of the sections of the campaign form */}
      {sections.map((section, index) => (
        <SectionTabHOC
          key={section.id}
          title={section.name}
          iconName={section.icon}
          iconColor={Helpers.tabStatusColor(section.id, section.option)}
        >
          {/* Render the component for this section */}
          <section.component key={section.id} />
        </SectionTabHOC>
      ))}
    </Box>
  );
};

export default Index;
