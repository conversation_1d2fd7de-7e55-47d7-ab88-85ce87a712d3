import { Box, Container } from "@mui/material";
// import DropdownAtom from "../../../../global/atoms/dropdown/Dropdown";
import DropdownAtom from "../../../../../global/atoms/dropdown/Dropdown";
import PaginationAtom from "../../../../../global/atoms/pagination/Index";
import TabMolecule from "../../../../../global/molecules/tabs/Tabs";
import { useEffect, useState } from "react";
import CampaignsListOrganizer from "../../../organisms/campaigns/view/Index";
import { useDispatch, useSelector } from "react-redux";
import CampaignService, {
  campaignsStatusEnums,
} from "../../../../../../Api/CampaignService";
import ListServiceClass from "../../../../../../Api/ListService";
import endPoints from "../../../../../../Api/EndPoints";
import {
  setCampaigns,
  setCampaignsTab,
} from "../../../../../../redux/slices/tabs/campaigns/campaigns";
import LoadingBackdropAtom from "../../../../../global/atoms/loading/LoadingBackdrop";
import { TypographyAtom } from "../../../../../global/atoms/typography/Typography";
import { ErrorMessage } from "../../../../../global/atoms/errors/Errors";
import {
  resetData,
  setReceivedData,
} from "../../../../../../redux/slices/tabs/campaigns/campaign";
import React from "react";
import { Btn } from "../../../../../global/atoms/buttons/Button";
import GlobalService from "../../../../../../Api/GlobalService";

const LookupListService = new ListServiceClass(endPoints.list);

/**
 * Description placeholder
 *
 * @type {{}}
 */
const options = [{ label: "All", value: 2 }];
/**
 * CampaignsOrganizer component is responsible for organizing and displaying campaigns
 * in different tabs and providing filtering options.
 *
 * @component
 * @example
 * return (
 *   <CampaignsOrganizer />
 * )
 *
 * @returns {JSX.Element} The rendered CampaignsOrganizer component.
 *
 * @description
 * This component includes:
 * - A dropdown for filtering campaigns.
 * - Tabs for categorizing campaigns into Active, Paused, Draft, and Completed.
 * - Pagination for navigating through the list of campaigns.
 *
 * @typedef {Object} Campaign
 * @property {string} name - The name of the campaign.
 * @property {string[]} entities - The entities associated with the campaign.
 * @property {string} startDate - The start date of the campaign.
 * @property {string} endDate - The end date of the campaign.
 * @property {string} createdAt - The creation date of the campaign.
 *
 * @typedef {Object} Tab
 * @property {Object} tab - The tab object containing label information.
 * @property {string} tab.label - The label of the tab.
 * @property {JSX.Element} component - The component to be rendered within the tab.
 *
 * @typedef {Object} DropdownOption
 * @property {string} label - The label of the dropdown option.
 * @property {any} value - The value of the dropdown option.
 *
 * @typedef {Object} TabProps
 * @property {number} selectedTab - The index of the currently selected tab.
 * @property {function} onTabChange - The function to handle tab change.
 * @property {Tab[]} tabs - The array of tabs to be displayed.
 * @property {string} orientation - The orientation of the tabs ('horizontal' or 'vertical').
 * @property {string} variant - The variant of the tabs ('scrollable', 'standard', or 'fullWidth').
 * @property {string} indicatorColor - The color of the tab indicator.
 * @property {string} textColor - The color of the tab text.
 * @property {Object} tabSx - The styles for the tab.
 * @property {Object} tabContentSx - The styles for the tab content.
 *
 * @typedef {Object} PaginationProps
 * @property {number} count - The total number of pages.
 * @property {number} page - The current page number.
 * @property {function} onChange - The function to handle page change.
 */
const CampaignsOrganizer = () => {
  const [errors, setErrors] = useState({ campaigns: null, entities: null });
  const [selectedTab, setSelectedTab] = useState(0); // State variable to keep track of the currently selected tab
  const [selectedItem, setSelectedItem] = useState(options[0]); // State variable to keep track of the currently selected dropdown item
  let { campaigns } = useSelector((state) => state.campaigns?.campaignsData);
  /** @type {import("../../../../../../redux/slices/tabs/campaigns/campaign").CampaignInitialState} */
  const { receivedData } = useSelector((state) => state.campaign);
  const entitiesLoaded = Array.isArray(receivedData?.entities);
  const [loading, setLoading] = useState({
    campaigns: true,
    entities: !entitiesLoaded,
  }); // If we already have the entity data, we don't need to show the loading spinner
  const handleDropdownChange = (name, value) => {
    // Function to handle dropdown change
    setSelectedItem(value);
  };

  const dispatch = useDispatch();

  /**
   * Handles the response from the API. If the response is successful, it will
   * set the data in the store. If not, it will set the error in the store.
   *
   * @param {boolean} isSuccess - Whether the response is successful or not.
   * @param {string} type - The type of data, either 'campaigns' or 'entities'.
   * @param {any} data - The data from the API.
   */
  const handleReq = (isSuccess, type, data) => {
    // Set the loading
    setLoading((prev) => ({ ...prev, [type]: false }));

    // If not success then set the error
    if (!isSuccess) return setErrors((prev) => ({ ...prev, [type]: data }));

    // We are here it means it's success

    switch (type) {
      case "campaigns":
        dispatch(setCampaigns({ campaigns: data }));
        break;
      default:
        break;
    }
  };

  useEffect(() => {

  ;

    getCampaigns();

   
  }, []);

  /**
   * Get the campaigns
   */
  const getCampaigns = async () => {
    let res = await CampaignService.getCampaigns();

    let { data } = res;

    if (res.success) {
      data = Array.isArray(data) ? data : [];
    }

    handleReq(res.success, "campaigns", data);
  };

  /**
   * Creates a new campaig btn handler
   */
  const createCampaignBtnHandler = () => {
    // Reset the campaign
    dispatch(resetData());

    dispatch(setCampaignsTab("createCampaign"));
  };

  const handleTabChange = (event, newValue) => {
    // Function to handle tab change
    setSelectedTab(newValue);
  };

  /**
   * Filters the campaigns based on the given status.
   *
   * @param {Campaign[]} campaigns - The array of campaigns to be filtered.
   * @param {string} status - The status to filter by.
   * @returns {Campaign[]} The filtered array of campaigns.
   */
  const filterCampaigns = (campaigns, status) => {
    return campaigns.filter(
      (campaign) => campaign?.status?.toLowerCase() === status.toLowerCase()
    );
  };

  const isLoading = loading.campaigns || loading.entities;
  const haveError = errors.campaigns || errors.entities;

  // const staticCampaigns = [
  //   {
  //     name: "Summer Promo",
  //     entities: ["Entity A", "Entity B"],
  //     startDate: "2023-06-01",
  //     endDate: "2023-08-31",
  //     createdAt: "2023-05-15T10:30:00",
  //   },
  //   {
  //     name: "Holiday Sale",
  //     entities: ["Entity C"],
  //     startDate: "2023-12-01",
  //     endDate: "2023-12-25",
  //     createdAt: "2023-11-10T08:45:00",
  //   },
  // ]; // Example of campaigns data

  // Create the tabs array by the status of the campaigns
  const tabs = campaignsStatusEnums.map((status) => {
    return {
      tab: { label: status },
      component: (
        <CampaignsListOrganizer
          getCampaigns={getCampaigns}
          campaigns={filterCampaigns(campaigns, status)}
        />
      ), // Component to be rendered within the tab
    };
  });

  // Push the all campaigns to the top of the tabs
  tabs.unshift({
    tab: { label: "All Rule Sets" },
    component: (
      <CampaignsListOrganizer
        getCampaigns={getCampaigns}
        campaigns={campaigns}
      />
    ), // Component to be rendered within the tab
  });

  return (
    <>
      <Btn
        text="Create Rule Set"
        onClick={createCampaignBtnHandler}
        sx={{ backgroundColor: "primary.secondary" }}
      />

      {
        // Is loading
        isLoading && (
          <LoadingBackdropAtom
            message={"Loading rule sets..."}
            open={isLoading}
          />
        )
      }

      {
        // Place campaigns error message if any
        errors.campaigns && (
        <ErrorMessage errorMessage={errors.campaigns} />
        )
      }

      {
        // Place enitity error message if any
        errors.entities && errors.entities !== 'Unable to connect to server. Please try again later.' && (
          <ErrorMessage errorMessage={errors.entities} />
        )
      }

      {
        // If not loading and not error
        !isLoading && !haveError && (
          <Box
            sx={{ my: 3 }} // Container element with margin
          >
            {/* <DropdownAtom
              label="Filter Campaigns" // Label for the dropdown
              selectedItem={selectedItem} // Currently selected dropdown item
              name="filterCampaigns" // Name of the dropdown
              onChange={handleDropdownChange} // Function to handle dropdown change
              options={options} // Dynamic options passed here
              size="small" // Size of the dropdown
              sx={{ maxWidth: "300px", mb: 2 }} // Styles for the dropdown
            /> */}

            <TabMolecule
              selectedTab={selectedTab} // Currently selected tab
              onTabChange={handleTabChange} // Function to handle tab change
              tabs={tabs} // Array of tabs to be displayed
              orientation="horizontal" // Orientation of the tabs ('horizontal' or 'vertical')
              variant="scrollable" // Variant of the tabs ('scrollable', 'standard', or 'fullWidth')
              indicatorColor="secondary" // Color of the tab indicator
              textColor="inherit" // Color of the tab text
              tabSx={{ minWidth: "150px" }} // Styles for the tab
              tabContentSx={{ minHeight: "200px" }} // Styles for the tab content
            />

            {/* <PaginationAtom count={10} page={1} onChange={() => {}} /> */}
          </Box>
        )
      }
    </>
  );
};

export default CampaignsOrganizer;
