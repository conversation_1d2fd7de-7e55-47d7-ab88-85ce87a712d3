import { Box, useMediaQuery } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import ViewCampaignsTemplate from "./view/Index";
import CreateCampaignTemplate from "./create/Index";
import { useTheme } from "@emotion/react";
import React from "react";
import Profile from "./profile/Index";
import UserManagement from "./userManagement/Index";
import Analytics from "./analytics/Index";
// import WebhooksTemplate from "./webhooks/Index";
/**
 * MainTemplate component renders the appropriate campaign tab based on the current state.
 *
 * This component uses the `useSelector` hook to access the `campaignsTab` from the Redux store.
 * Depending on the value of `campaignsTab`, it dynamically selects and renders either the
 * `CreateCampaignTemplate` or `ViewCampaignsTemplate` component.
 *
 * @component
 * @example 
 * return (
 *   <MainTemplate />
 * )
 */
const MainTemplate = () => {
  const { campaignsTab } = useSelector((state) => state.campaigns);

  const dispatch = useDispatch();
  const theme = useTheme();
  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));

  const tabs = {
    createCampaign: CreateCampaignTemplate,
    viewCampaigns: ViewCampaignsTemplate,
    analytics: Analytics,
    profile: Profile,
    userManagement: UserManagement,
    // listManagement: ListManagementView,
    // bulkNotification: BulkNotificationView,
    // webhooks: WebhooksTemplate,
  };

  let Tab = tabs[campaignsTab];
  return (
    <Box>
      <Tab key={"Tab"} />
    </Box>
  );
};

export default MainTemplate;
