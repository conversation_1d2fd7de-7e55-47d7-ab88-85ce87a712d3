import React, { useState } from "react";
import {
  Box,
} from "@mui/material";
import { Typography<PERSON><PERSON> } from "../../../../../global/atoms/typography/Typography";
import { useDispatch, useSelector } from "react-redux";
import { setCampaignsTab } from "../../../../../../redux/slices/tabs/campaigns/campaigns";
import { IconBtnAtom } from "../../../../../global/atoms/buttons/IconBtn";
import AnalyticsFilter from "../../../../../../components/pages/dashboard/organisms/campaigns/analytics/AnalyticsFilter";
import AnalyticsChart from "../../../../../../components/pages/dashboard/organisms/campaigns/analytics/AnalyticsChart";
import { Helpers } from "../../../../../../utils/generalFunctions/index";

/**
 * The Analytics component - Main analytics dashboard
 *
 * @component
 * @example
 * return <Analytics />;
 *
 * @returns {JSX.Element} The JSX element representing the Analytics component.
 */
const Analytics = () => {
  const { analyticsData } = useSelector((state) => state.campaigns);
  const { campaignData } = analyticsData;
  const dispatch = useDispatch();

  // State to manage chart filters (no showChart state)
  const [chartFilters, setChartFilters] = useState({});

  /**
   * Handle back button click to return to campaigns view
   */
  const handleBackClick = () => {
    dispatch(setCampaignsTab("viewCampaigns"));
  };

  /**
   * Handle filter submission from AnalyticsFilter component
   * @param {Object} filters - The filter data to apply to the chart
   */
  const handleFilterSubmit = (filters) => {
    setChartFilters(filters);
  };

  /**
   * Handle filter reset
   */
  const handleFilterReset = () => {
    const baseFilters = campaignData ? {

      entity_id: campaignData.entityId,
      context_id: campaignData.contextId,
      rule_set_id: campaignData.rulesetId,
      rule_set_version: campaignData.version,
      time_bucket: 0
    } : {};
    setChartFilters(baseFilters);
  };

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: 'column',
      mb: 3,
      p: 3,
      backgroundColor: 'background.paper',
      borderRadius: 2,
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
      border: '1px solid',
      borderColor: 'divider',
      gap: 2
    }}>
      {/* Header Section */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        width: '100%'
      }}>
        {/* Left - Back Button */}
        <Box>
          <IconBtnAtom
            sx={{ mb: 2 }}
            iconParams={{
              iconName: "ArrowBackIos",
              color: "secondary",
              size: 25,
              title: "Back to Campaigns",
            }}
            onClick={handleBackClick}
          />
        </Box>

        {/* Center - Analytics Heading */}
        <Box sx={{
          flex: 1,
          textAlign: 'center'
        }}>
          <TypographyAtom
            variant="h4"
            text={`Analytics For ${Helpers.capitalizeFirstLetter(campaignData.name)}`}
            sx={{
              fontWeight: 'bold',
              color: 'primary.main',
              mb: 0.5
            }}
          />
        </Box>

        {/* Right - Filters */}
        <Box>
          <AnalyticsFilter
            onFilterSubmit={handleFilterSubmit}
            onFilterReset={handleFilterReset}
          />
        </Box>
      </Box>

      {/* Analytics Chart Component */}
      <AnalyticsChart
        filters={chartFilters}
      />
    </Box>
  );
};

export default Analytics;