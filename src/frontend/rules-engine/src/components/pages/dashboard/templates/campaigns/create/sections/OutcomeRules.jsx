import { Box, Grid2 as Grid } from "@mui/material";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Helpers, Alert } from "../../../../../../../utils";
import { Btn as Btn<PERSON>tom } from "../../../../../../global/atoms/buttons/Button";
import { Typo<PERSON><PERSON><PERSON> } from "../../../../../../global/atoms/typography/Typography";
import SectionHeaderMolecule from "../../../../molecules/campaigns/create/SubSectionHeader";
import ReusableSectionHOC from "../../../../atoms/global/reusableSectionHOC";
import RuleContentOrganizer from "../../../../organisms/campaigns/create/rules/RulesOrganizer";
import React from "react";
import { setCampaign } from "../../../../../../../redux/slices/tabs/campaigns/campaign";

// Ready the columns util
const columnsUtils = Helpers.createColumnsUtil([
  {
    label: "Rules Name",
    gridSize: 5,
    content: { key: "name", def: "Create rule" },
  },
  {
    label: "Actions",
    gridSize: 5,
    content: { key: "actions", type: "arrayLength" },
  },
]);

const OutcomeRulesSection = () => {
  // Get the received data from the redux store that is received from the API.
  /** @type {import("../../../../../../../../jsDocs/redux/store").Store['campaign']} */
  const { campaign, receivedData } = useSelector(
    /** @param {{ campaign: any }} state */ (state) => state.campaign
  );
  const dispatch = useDispatch();
  const { outcomeRules, evaluationRules, entityId, contextId } = campaign;
  /** This state is used to show or hide the save button */
  const [isSaveBtnShow, setIsSaveBtnShow] = useState(false);

  const [data, setData] = useState([]);

  const [contextOptions, setContextOptions] = useState([]);

  /**
   * This function validate all fields and pass the transaction context data to save it in the parent component
   */
  const handleSubmit = () => {
    let newCampaign = {
      outcomeRules: data.map((rule) => rule.data),
    };

    dispatch(setCampaign(newCampaign));
    setIsSaveBtnShow(false);
    Alert("Rules saved successfully.", "success");
  };

  /**
   * This function puts the item in editing mode
   */
  const handleEditBtnClick = (id) => {
    const updatedRules = data.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          mode: "editing",
        };
      }
      return item;
    });
    setData(updatedRules);
  };

  /**
   * This function deletes an item from the transaction context data
   */
  /**
   * This function deletes an item from the transaction context data
   */
  const handleDeleteItem = (id) => {
    const updatedData = data.filter((item) => item.id !== id);
    setData(updatedData);
    // Update the campaign data in the Redux store after deletion
    const updatedCampaign = {
      outcomeRules: updatedData.map((rule) => rule.data),
    };

    dispatch(setCampaign(updatedCampaign));

    // Show a success message after deletion
    Alert("Rule deleted successfully.", "success");
  };
  /**
   * This function adds a new rule to the rules array
   */
  const handleAddNewItem = () => {
    if (data.length >= 30) {
      Alert("Cannot add more than 30 outcome rules. Limit exceeded", "error");
      return;
    }
    if (!entityId) {
      Alert("Please select an entity first.", "error");
      return;
    }
    if (!contextId) {
      Alert("Please select a context first.", "error");
      return;
    }
    let isNewRule = data.find((item) => item.id === "newRule");

    if (isNewRule) {
      return Alert("Cannot add multiple new collections", "error");
    }

    const newItem = {
      id: "newRule",
      mode: "editing", // ["viewing", "editing"]
      columns: columnsUtils.generateItemContentColumns(),
      isEditable: true,
      isDeletable: true,
      isAllFieldsValid: false,
      validations: {},
      data: {
        ruleId: "",
        name: "",
        description: "",
        priority: data.length + 1,
        condition: {},
        variableAssignments: [],
        webhookCalls: [],
      },
    };

    setData([...data, newItem]);
  };

  /**
   * This function is called when the user clicks the Save button inside the child component.
   */
  const handleSaveRule = (id, inputData) => {
    const isDuplicateInOutcomeRules = data.some(
      (item) => item.id !== id && item.data.ruleId === inputData.ruleId
    );

    // Check if the ruleId already exists in outcome rules
    const isDuplicateInEvaluationRules = evaluationRules.some(
      (rule) => rule.ruleId === inputData.ruleId
    );

    if (isDuplicateInEvaluationRules) {
      Alert(
        "Rule ID must be unique. This ID already exists in evaluation rules.",
        "error"
      );
      return;
    }
    if (isDuplicateInOutcomeRules) {
      Alert(
        "Rule ID must be unique. This ID already exists in outcome rules.",
        "error"
      );
      return;
    }
    const rules = data.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          mode: "viewing",
          id: inputData.ruleId,
          data: inputData,
          isAllFieldsValid: true,
          columns: columnsUtils.generateItemContentColumns(inputData),
        };
      }
      return item;
    });
    // setData(rules);
    let newCampaign = {
      outcomeRules: rules.map((rule) => rule.data),
    };

    dispatch(setCampaign(newCampaign));
    setIsSaveBtnShow(true);
    Alert("Rule saved successfully.", "success");
  };

  /**
   * This function is called when the user clicks the Cancel button inside the child component.
   */
  const handleCancelRule = (id) => {
    if (id === "newRule") {
      // If it's a new unsaved rule, remove it completely
      let updatedRules = data.filter((item) => item.id !== id);
      setData(updatedRules);
    } else {
      // For existing rules, just switch back to viewing mode
      let updatedRules = data.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            mode: "viewing",
          };
        }
        return item;
      });
      setData(updatedRules);
    }
  };

  useEffect(() => {
    if (!entityId) {
      Alert("Please select an entity first.", "error");
      return;
    }
    if (!contextId) {
      Alert("Please select a context first.", "error");
      return;
    }
    let newRules = outcomeRules.map((rule) => {
      const newItem = {
        id: rule.ruleId,
        mode: "viewing", // ["viewing", "editing"]
        columns: columnsUtils.generateItemContentColumns(rule),
        isEditable: true,
        isDeletable: true,
        isAllFieldsValid: false,
        validations: {},
        data: rule,
      };
      return newItem;
    });
    setData(newRules);
  }, [outcomeRules]);

  const entityAndContext = {
    entityId: entityId,
    contextId: contextId,
  };

  return (
    <Grid container spacing={2}>
      <Grid size={{ xs: 12, sm: 12, md: 12 }} item>
        <Box
          sx={{
            mt: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          {/* Section title & Add button   */}
          <TypographyAtom text="Rules" variant="h6" sx={{}} />
          <BtnAtom
            onClick={handleAddNewItem}
            sx={{ backgroundColor: "primary.secondary" }}
            text="Add"
          />
        </Box>
      </Grid>
      <Grid size={{ xs: 12, sm: 12, md: 12 }} item md={12}>
        <Box sx={{ mt: 2, width: "100%" }}>
          {/* Section header */}
          <SectionHeaderMolecule
            sx={{}}
            columns={columnsUtils.columnsOfSectionHeader}
          />
          {/* Rules over rules array */}
          {data.length > 0 &&
            data.map((tab, index) => (
              <ReusableSectionHOC
                key={index}
                index={index}
                tab={tab}
                columns={tab.columns}
                sxContainer={{ backgroundColor: "bgSecondary.main" }}
                rows={tab.rows}
                onEdit={() => handleEditBtnClick(tab.id)}
                onDelete={() => handleDeleteItem(tab.id)}
                content={
                  <RuleContentOrganizer
                    handleSave={(data) => handleSaveRule(tab.id, data)}
                    handleCancel={handleCancelRule}
                    tab={tab}
                    entityAndContext={entityAndContext}
                    lastPriority={data.length}
                    ruleType="outcome"
                  />
                }
              />
            ))}
          {/* If not rules then show message */}
          {data.length < 1 && (
            <Box sx={{ mt: 2 }}>
              <TypographyAtom
                sx={{ textAlign: "center", color: "textColor.secondary" }}
                text="No rules added yet."
                variant="h6"
              />
            </Box>
          )}
        </Box>
      </Grid>
    </Grid>
  );
};

export default OutcomeRulesSection;
