import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import ReusableTable from "../../../../../global/atoms/table/Index";

import React, { useEffect, useState } from "react";
import { Helpers } from "../../../../../../utils/generalFunctions/index";
import { Btn } from "../../../../../global/atoms/buttons/Button";
import AddUser from "../../../../../pages/dashboard/organisms/campaigns/userManagement/addUser/Index";
import AuthService from "../../../../../../Api/AuthService";
import { Alert } from "../../../../../../utils";
import { TypographyAtom } from "../../../../../global/atoms/typography/Typography";
import { DynamicIcon } from "../../../../../global/atoms/icons/Index";
import { useDispatch, useSelector } from "react-redux";
import {
  closeDialog,
  openDialog,
} from "../../../../../../redux/slices/global/confirmation";
import { registerConfirmation } from "../../../../../global/atoms/dialog/Index";
import PaginationAtom from "../../../../../global/atoms/pagination/Index";
import GenericModal from "../../../../../../components/pages/dashboard/atoms/genericModal/Index";
/**
 * UserManagement component for handling user administration
 * @returns {JSX.Element} The UserManagement component
 */
const UserManagement = () => {
  /**
   * This is the state for the page to show
   * @type {[string, React.Dispatch<React.SetStateAction<string>>]}
   */
  const [showPage, setShowPage] = useState("list"); //list,add,edit
  /**
   * This is the state for the user to edit
   * @type {[object, React.Dispatch<React.SetStateAction<object>>]}
   */
  const [editUserData, setEditUserData] = useState({});
  /**
   * This is the state for the users
   * @type {[object, React.Dispatch<React.SetStateAction<object>>]}
   */
  const [usersData, setUsersData] = useState({
    users: [],
    page: 1,
    total: 0,
    limit: 2,
  });
  /**
   * This is the state for the add user button loading
   * @type {[boolean, React.Dispatch<React.SetStateAction<boolean>>]}
   */
  const [addUserBtnLoading, setAddUserBtnLoading] = useState(false);
  const [deleteUserBtnLoading, setDeleteUserBtnLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [modalData, setModalData] = useState({
    open: false,
    inputs: {},
  });

  const dispatch = useDispatch();
  /**
   * This is the table data for the users
   * @type {Array<Array<{value: string, width: string, key: string}>>}
   */

  let tableData =
    usersData?.users.length > 0
      ? usersData?.users.map((user, index) => {
          return Object.entries(user)
            .filter(([key]) => key == "username")
            .map(([key, value]) => ({ value, width: "10%", key }));
          // return Object.entries(user).map(([key, value]) => {
          //   switch (key) {
          //     // case "id":
          //     //   return {
          //     //     value: (usersData.page - 1) * usersData.limit + (index + 1),
          //     //     width: "10%",
          //     //     key,
          //     //   };
          //     // case "name":
          //     //   return { value: value, width: "15%", key };
          //     case "username":
          //       return { value: value, width: "10%", key };

          //     default:
          //       return { value: value, width: "10%", key };
          //   }
          // });
        })
      : [];

  let tableHeadData = [
    // {
    //   id: 1,
    //   text: "ID",
    //   width: "10%",
    // },
    // {
    //   id: 2,
    //   text: "Name",
    //   width: "20%",
    // },
    {
      id: 1,
      text: "Username",
      width: "10%",
    },
    // {
    //   id: 4,
    //   text: "Role",
    //   width: "20%",
    // },
    // {
    //   id: 5,
    //   text: "Edit",
    //   width: "10%",
    // },
    {
      id: 6,
      text: "Delete",
      width: "50%",
    },
  ];

  /**
   *
   * @param {object} inputs The inputs of the user
   * @param {string} inputs.name The name of the user
   * @param {string} inputs.email The email of the user
   * @param {string} inputs.roleId The role of the user
   * @param {string} inputs.password The password of the user
   * @param {string} inputs.confirmPassword The confirm password
   * @param {string} inputs.profile_data The profile data of the user
   * @param {boolean} inputs.sendEmail Whether to send an email to the user
   * @returns
   */
  const handleAddUserSubmit = async (inputs) => {
    try {
      setAddUserBtnLoading(true);
      const response = await AuthService.register(inputs);
      if (!response.success) {
        return Alert(response.data || "Error while creating user", "error");
      }

      Alert(
        response.data || response.message || "User created successfully",
        "success"
      );

      // Show success modal with login details
      setModalData({
        open: true,
        inputs: inputs,
      });

      // Show success modal with login details

      // Reload the users
      handleGetUsers(usersData.page);

      setShowPage("list");
    } catch (error) {
      Alert("Error while creating user", "error");
    } finally {
      setAddUserBtnLoading(false);
    }
  };

  /**
   *
   * @param {object} inputs The inputs of the user
   * @param {string} inputs.name The name of the user
   * @param {string} inputs.email The email of the user
   * @param {string} inputs.roleId The role of the user
   * @param {string} inputs.password The password of the user
   * @param {string} inputs.confirmPassword The confirm password
   * @returns
   */
  const handleEditUserSubmit = async (inputs) => {
    try {
      setEditUserBtnLoading(true);
      const response = await AuthService.register(inputs);
      if (!response.success) {
        return Alert("Error while creating user", "error");
      }
      Alert("User updated successfully", "success");
      setShowPage("list");
    } catch (error) {
      Alert("Error while creating user", "error");
    } finally {
      setEditUserBtnLoading(false);
    }
  };
  /**
   * Handles deleting a user
   */
  const handleDeleteUserSubmit = async (row) => {
    try {
      let idCol = row.find((col) => col.key === "username");
      const actualUser = usersData.users.find(
        (user) => user.username === idCol.value
      );

      // Register confirmation callback
      const confirmationId = registerConfirmation(async (confirmed) => {
        if (confirmed) {
          setDeleteUserBtnLoading(true);
          try {
            const response = await AuthService.deleteUser(actualUser.id);
            if (!response.success) {
              return Alert("Error while deleting user", "error");
            }
            let newUsersData = usersData.users.filter(
              (user) => user.id !== actualUser.id
            );
            setUsersData({ ...usersData, users: newUsersData });
            Alert("User deleted successfully", "success");
          } finally {
            setDeleteUserBtnLoading(false);
          }
        }
      });

      // Open confirmation dialog
      dispatch(
        openDialog({
          title: "Delete User",
          message: `Are you sure you want to delete user ${actualUser.username}?`,
          confirmationId,
        })
      );
    } catch (error) {
      Alert("Error while deleting user", "error");
    }
  };

  /**
   * Handles getting the users from the database
   * @param {number} page The page number
   * @param {number} limit The limit of the users
   */
  const handleGetUsers = async (page) => {
    try {
      setIsLoading(true);
      const response = await AuthService.getAllUsers({
        page: page,
        // limit: limit,
      });

      if (!response.success) {
        return Alert("Error while getting users", "error");
      }

      // Update users data
      setUsersData({
        users: response.data.users.map((user) => ({
          id: user.id,
          // name: user.userProfile?.full_name?.toUpperCase(),
          username: user.username,
          role_label: user.role_label,
        })),
        page: page,
        total: response.data.total,
        limit: response.data.limit,
      });
    } catch (error) {
      Alert("Error while fetching users", "error");
    } finally {
      setIsLoading(false);
    }
  };

  const sx = {
    commonBox: {
      width: "50%",
    },
  };

  useEffect(() => {
    handleGetUsers(usersData.page, usersData.limit);
  }, []);

  return (
    <>
      <Box
        sx={{
          width: "100%",
          maxWidth: "700px",
          margin: "auto",
          display: "flex",
          flexDirection: "column",
          gap: 2,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {showPage === "list" && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              my: 2,
              flexDirection: "column",
              width: "100%",
            }}
          >
            <Box
              sx={{ display: "flex", justifyContent: "space-between", my: 2 }}
            >
              <Typography variant="h4">User Management</Typography>
              <Btn
                text="Add User"
                variant="contained"
                sx={{ color: "mainColor.main" }}
                onClick={() => setShowPage("add")}
              />
            </Box>
            <ReusableTable
              tableHeadData={tableHeadData}
              actions={[
                // {
                //   text: "Edit",
                //   id: "edit",
                //   sx: {
                //     color: "mainColor.main",

                //     backgroundColor: "secondary.main",
                //   },
                //   handleClick: handleEditUser,
                // },
                {
                  text: "Delete",
                  id: "delete",
                  sx: {
                    color: "mainColor.main",
                    backgroundColor: "danger.main",
                  },
                  handleClick: handleDeleteUserSubmit,
                  loadingId: deleteUserBtnLoading,
                },
              ]}
              sx={{ tableCell: { textAlign: "left" } }}
              rows={tableData}
              onBtnClick={(id, row) => {}}
            />
            {usersData.total > usersData.limit && (
              <PaginationAtom
                count={Math.ceil(usersData.total / usersData.limit)}
                page={usersData.page}
                onChange={(event, newPage) => {
                  if (newPage !== usersData.page) {
                    handleGetUsers(newPage);
                  }
                }}
                disabled={isLoading}
                size="medium"
                sx={{}}
                sxStack={{ mt: 4 }}
              />
            )}
          </Box>
        )}

        {showPage === "add" && (
          <Box sx={sx.commonBox}>
            <AddUser
              onSubmit={handleAddUserSubmit}
              data={{}}
              status="creating"
              isLoading={addUserBtnLoading}
              setIsLoading={setAddUserBtnLoading}
            />
          </Box>
        )}
        {showPage === "edit" && (
          <Box sx={sx.commonBox}>
            <AddUser
              onSubmit={handleEditUserSubmit}
              data={editUserData}
              status="editing"
              isLoading={addUserBtnLoading}
              setIsLoading={setAddUserBtnLoading}
            />
          </Box>
        )}
        {showPage !== "list" && (
          <Box sx={sx.commonBox}>
            <Button
              variant="text"
              sx={{ color: "textColor.main", width: "100%", mt: 2 }}
              onClick={() => setShowPage("list")}
            >
              <DynamicIcon iconName="ArrowBack" />
              <TypographyAtom text="Back" sx={{ ml: 1 }} />
            </Button>
          </Box>
        )}
      </Box>
      {modalData.open && (
        <GenericModal
          open={modalData.open}
          onClose={() =>
            setModalData({
              open: false,
              inputs: {},
            })
          }
          title=<p
            style={{
              textAlign: "center",
              backgroundColor: "black",
              color: "white",
            }}
          >
            User Created Successfully
          </p>
          showCloseButton={false}
        >
          <>
            <Box>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Following are the login details for the created user:
              </Typography>
              <Box sx={{ mb: 2 }}>
                <Typography component="span" sx={{ fontWeight: "bold" }}>
                  Username:
                </Typography>{" "}
                <Typography component="span">
                  {modalData.inputs.username}
                </Typography>
              </Box>
              <Box sx={{ mb: 2 }}>
                <Typography component="span" sx={{ fontWeight: "bold" }}>
                  Password:
                </Typography>{" "}
                <Typography component="span">
                  {modalData.inputs.password}
                </Typography>
              </Box>
              <Box sx={{ display: "flex", justifyContent: "center" }}>
                <Btn
                  text="Copy Login Details"
                  onClick={() => {
                    navigator.clipboard.writeText(
                      `Username: ${modalData.inputs.username}\nPassword: ${modalData.inputs.password}`
                    );
                    Alert("Login details copied to clipboard", "success");
                  }}
                  sx={{ mt: 2 }}
                />
              </Box>
            </Box>
          </>
        </GenericModal>
      )}
    </>
  );
};

export default UserManagement;
