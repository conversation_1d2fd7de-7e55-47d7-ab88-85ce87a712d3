import { Box } from "@mui/material";
import { <PERSON>po<PERSON><PERSON><PERSON> } from "../../../../../../global/atoms/typography/Typography";
import { InputField } from "../../../../../../global/atoms/inputFields/InputField";
import { <PERSON><PERSON>, campaignValidators, Helpers } from "../../../../../../../utils";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Btn as BtnAtom } from "../../../../../../global/atoms/buttons/Button";
import { setCampaign } from "../../../../../../../redux/slices/tabs/campaigns/campaign";
import DatePickerAtom from "../../../../../../global/atoms/datePicker/Index";
import Dropdown<PERSON>tom from "../../../../../../global/atoms/dropdown/Dropdown";
import React from "react";

/**
 * @typedef {import('../../../../../../../../jsDocs/components/dashboard').DetailsInputsSection} Inputs
 */

/**
 * DetailsSection component for entering campaign details.
 *
 * @component
 * @returns {JSX.Element} The rendered details section.
 */
const keys = [
  "name",
  "description",
  "startDateTime",
  "endDateTime",
  "category",
  "entityId",
  "contextId",
];
let optionalKeys = [];
/**
 * DetailsSection component for entering campaign details.
 *
 * @component
 * @returns {JSX.Element} The rendered details section.
 * @example
 * <DetailsSection />
 */
const DetailsSection = () => {
  const dispatch = useDispatch();
  const { campaign, receivedData } = useSelector((state) => state.campaign);

  const {
    name,
    description,
    startDateTime,
    endDateTime,
    category,
    entityId,
    contextId,
  } = campaign;

  const [entityOptions, setEntityOptions] = useState([]);
  // const [entityId, setEntityId] = useState("");

  const [contextOptions, setContextOptions] = useState([]);
  // const [contextId, setContextId] = useState("");

  let initialState = Helpers.defaultValues(
    keys,
    {
      name,
      description,
      startDateTime,
      endDateTime,
      category,
      entityId,
      contextId,
    },
    optionalKeys
  );
  const [isShowSaveBtn, setIsShowSaveBtn] = useState(false);

  /** @type {[Inputs,  React.Dispatch<React.SetStateAction<Inputs>>]} */
  const [inputs, setInputs] = useState(initialState);

  // Handle input change for all fields
  const handleInputChange = (event, status) => {
    const { name, value } = event.target;
    !isShowSaveBtn ? setIsShowSaveBtn(true) : null;

    setInputs((prev) => ({
      ...prev,
      [name]: {
        value,
        isValid: status.success,
        error: status.message,
        isShowError: value ? !status.success : false,
      },
    }));
  };

  useEffect(() => {
    if (receivedData?.entities?.length > 0) {
      const options = receivedData.entities.map((entity) => ({
        value: entity.entityId,
        label: entity.name,
      }));

      setEntityOptions(options);

      // Set selected entity from campaign if it exists
    }
  }, [receivedData, campaign.entityId]);

  const handleBluer = (event) => {
    // const { name, value } = event.target;
  };
  // Generate context options based on selected entity
  useEffect(() => {
    if (receivedData?.entities && campaign.entityId) {
      const selectedEntity = receivedData.entities.find(
        (entity) => entity.entityId === campaign.entityId
      );

      if (selectedEntity) {
        const options = selectedEntity.transactionContexts.map((context) => ({
          value: context.contextId,
          label: context.name,
        }));
        setContextOptions(options);

        // Set selected context from campaign if it exists
      }
    } else {
      // Clear context options if no entity is selected
      setContextOptions([]);
    }
  }, [receivedData, campaign.entityId, campaign.contextId]);

  // Handle context selection change
  const handleContextChange = (name, value) => {
    if (!inputs.entityId.value) {
      return Alert("Please select an entity first", "error");
    }
    // Show save button when context changes
    !isShowSaveBtn ? setIsShowSaveBtn(true) : null;

    // Update inputs state instead of directly dispatching
    setInputs((prev) => ({
      ...prev,
      contextId: {
        value,
        isValid: true,
        error: "",
        isShowError: false,
      },
    }));
  };
  // Handle entity selection change
  const handleEntityChange = (name, value) => {
    // Show save button when entity changes
    !isShowSaveBtn ? setIsShowSaveBtn(true) : null;

    // Update inputs state instead of directly dispatching
    setInputs((prev) => ({
      ...prev,
      entityId: {
        value,
        isValid: true,
        error: "",
        isShowError: false,
      },
    }));
    // also generate context options based on the selected entity
    const selectedEntity = receivedData.entities.find(
      (entity) => entity.entityId === value
    );
    if (selectedEntity) {
      const options = selectedEntity.transactionContexts.map((context) => ({
        value: context.contextId,
        label: context.name,
      }));
      setContextOptions(options);
    }
  };
  /**
   * Handles date change for the start and end date input fields.
   *
   * When the user selects a new date for either the start date or end date input field,
   * this function is called with the new date and the key for the input field (either "startDate"
   * or "endDate").
   *
   * @param {Date} date - The newly selected date.
   * @param {string} key - The key for the input field, either "startDate" or "endDate".
   * @returns {void}
   *
   * This function updates the inputs state to reflect the new date selection by setting the
   * value, isValid, error, and isShowError properties of the input field to the new date, true,
   * "", and false respectively.
   */
  const onDateChange = (date, key) => {
    !isShowSaveBtn ? setIsShowSaveBtn(true) : null;
    setInputs((prev) => ({
      ...prev,
      [key]: {
        value: date,
        isValid: true,
        error: "",
        isShowError: false,
      },
    }));
  };

  /**
   * Handles the form submission when the user clicks the submit button.
   *
   * This function constructs an object based on the input fields, validates the fields,
   * and dispatches the campaign data if the validation is successful. If there are validation
   * errors, it updates the input state to display the errors.
   */

  const handleSubmitClick = () => {
    /**
     * @type {{[key: string]: any}}
     */
    let obj = {};

    // Loop through the keys of inputs and dynamically add them to the obj
    Object.keys(inputs).forEach((key) => {
      obj[key] = inputs[key].value?.trim(); // Add each key-value pair from inputs
    });
    // Check if the description field is empty and alert an error message if it is
    if (!inputs.description.value?.trim()) {
      Alert("Description cannot be empty", "error");
      return; // Exit the function early if the description is empty
    }
    // Check if the entityId field is empty and alert an error message if it is
    if (!inputs.entityId.value?.trim()) {
      Alert("Entity ID cannot be empty", "error");
      return; // Exit the function early if the entityId is empty
    }

    // Check if the contextId field is empty and alert an error message if it is
    if (!inputs.contextId.value?.trim()) {
      Alert("Context ID cannot be empty", "error");
      return; // Exit the function early if the contextId is empty
    }
    // Check if the startDateTime field is empty and alert an error message if it is
    if (!inputs.startDateTime.value) {
      Alert("Start Date cannot be empty", "error");
      return; // Exit the function early if the startDateTime is empty
    }

    // Check if the endDateTime field is empty and alert an error message if it is
    if (!inputs.endDateTime.value) {
      Alert("End Date cannot be empty", "error");
      return; // Exit the function early if the endDateTime is empty
    }

    // Validate all fields using isValidAllFields
    const validationResult = campaignValidators.global.isValidAllFields(inputs);

    if (validationResult.success) {
      // If the form is valid, dispatch the campaign data
      dispatch(
        setCampaign({
          ...campaign,
          ...obj, // Use the dynamically built object for the campaign data
        })
      );
      setIsShowSaveBtn(false);
      Alert("Rule Set details saved successfully", "success");
    } else {
      // If there are validation errors, update the inputs state to show errors
      const updatedInputs = { ...inputs };

      // Iterate over the errors array and update the state
      validationResult.errors.forEach(({ field, error }) => {
        updatedInputs[field] = {
          ...updatedInputs[field],
          isValid: false, // Set field as invalid
          isShowError: true, // Show error message
          error, // Set error message
        };
      });

      // Update the inputs state with the new validation statuses
      setInputs(updatedInputs);

      // // Display the first error message
      // const firstErrorMessage = validationResult.errors[0].error;
      // Alert(firstErrorMessage, "error");
    }
  };

  return (
    <Box
      sx={{
        padding: 3, // Add padding around the section
        backgroundColor: "background.paper", // Optional background color
        borderRadius: 2, // Rounded corners for a clean UI
        boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.1)", // Subtle shadow for depth
        display: "flex",
        flexDirection: "column",
        gap: 2, // Add space between elements
        width: "100%",
        mb: 2,
      }}
    >
      {/* Section Title */}
      <TypographyAtom
        onClick={() => {
          const res = campaignValidators.global.isValidAllFields(inputs);
        }}
        variant="h4"
        sx={{ color: "textColor.primary" }}
        text="Rule Set Details"
      />

      {/* Campaign Name Input */}
      <InputField
        label="Rule Set Name"
        placeholder="Enter rule set name"
        key={"Rule Set Name"}
        validator={campaignValidators.details.name}
        validation={{
          isShowError: inputs.name.isShowError,
          error: inputs.name.error,
        }}
        validateOn="change"
        onBlur={handleBluer}
        name="name"
        value={inputs.name.value}
        onChange={handleInputChange}
        sx={{
          width: {
            xs: "100%",
            md: "40%",
          },
        }}
      />

      {/* Campaign Description Input */}
      <InputField
        label="Description "
        placeholder="Enter rule set description"
        key={"Description"}
        multiline={true}
        onChange={handleInputChange}
        name="description"
        value={inputs.description.value}
        required={true}
        validator={campaignValidators.details.description}
        validateOn="change"
        validation={{
          isShowError: inputs.description.isShowError,
          error: inputs.description.error,
        }}
        onBlur={handleBluer}
        sx={{
          width: {
            xs: "100%",
            md: "40%",
          },
        }}
        rows={3} // Adjust rows for multiline
      />
      <Box sx={{ width: { sm: "50%", md: "40%", xs: "100%" } }}>
        <DropdownAtom
          label="Select Entity ID *"
          options={entityOptions}
          name="entityId"
          selectedItem={inputs.entityId.value}
          onChange={handleEntityChange}
          sx={{ backgroundColor: "bgPrimary.main" }}
        />
      </Box>
      <Box sx={{ width: { sm: "50%", md: "40%", xs: "100%" } }}>
        <DropdownAtom
          label="Select Context ID *"
          options={contextOptions}
          name="contextId"
          selectedItem={inputs.contextId.value}
          onChange={handleContextChange}
          disabled={!inputs.entityId.value}
          sx={{ backgroundColor: "bgPrimary.main" }}
        />
      </Box>
      {/* Campaign Description Input */}
      <InputField
        label="Category *"
        placeholder="Enter rule set category"
        key={"Category"}
        onChange={handleInputChange}
        name="category"
        value={inputs.category.value}
        required={false}
        validator={campaignValidators.details.category}
        validateOn="change"
        validation={{
          isShowError: inputs.category.isShowError,
          error: inputs.category.error,
        }}
        onBlur={handleBluer}
        sx={{
          width: {
            xs: "100%",
            md: "40%",
          },
        }}
        rows={3} // Adjust rows for multiline
      />

      {/* Start and End Date Fields (Side by Side on Large Screens) */}
      <Box
        sx={{
          display: "flex",
          flexDirection: { xs: "column", md: "row" }, // Column on small screens, row on large screens
          gap: 2, // Space between date fields
        }}
      >
        <DatePickerAtom
          label="Start Date  *"
          name={"startDateTime"}
          onChange={onDateChange}
          time="00:00:00"
          value={inputs.startDateTime.value}
          disabled={!inputs.startDateTime.value}
          defaultValue={inputs.startDateTime.value}
          validation={{
            isShowError: inputs.startDateTime.isShowError,
            error: inputs.startDateTime.error,
          }}
          component="dateTime"
        />

        {/* End Date */}
        <DatePickerAtom
          label="End Date *"
          name={"endDateTime"}
          onChange={onDateChange}
          time="0:0:0"
          required={true}
          value={inputs.endDateTime.value}
          disabled={!inputs.endDateTime.value}
          defaultValue={inputs.endDateTime.value}
          validation={{
            isShowError: inputs.endDateTime.isShowError,
            error: inputs.endDateTime.error,
          }}
          component="dateTime"
        />
      </Box>
      {isShowSaveBtn && (
        <BtnAtom
          text="Save"
          sx={{
            width: "100%",
            maxWidth: "200px",
            backgroundColor: "primary.secondary",
            ml: "auto",
          }}
          onClick={handleSubmitClick}
        />
      )}
    </Box>
  );
};

export default DetailsSection;
