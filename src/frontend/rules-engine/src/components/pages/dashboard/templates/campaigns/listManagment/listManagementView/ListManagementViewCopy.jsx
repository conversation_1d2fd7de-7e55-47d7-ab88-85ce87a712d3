import { useState } from "react";

import ListRowItem from "../../../../organisms/campaigns/listManagement/ListRowItem.jsx";
import { Typo<PERSON><PERSON><PERSON> } from "../../../../../../global/atoms/typography/Typography.jsx";
import { Btn } from "../../../../../../global/atoms/buttons/Button.jsx";
import { Box, Button } from "@mui/material";
import AddListModal from "../../../../atoms/addListModal/AddListModal.jsx";
import { setCampaignsTab } from "../../../../../../../redux/slices/tabs/campaigns/campaigns.js";
import { useDispatch } from "react-redux";
import { IconBtnAtom } from "../../../../../../global/atoms/buttons/IconBtn.jsx";
import React from "react";

/**
 * ListManagementView Component
 *
 * A React component that manages and displays a list of management items,
 * with the ability to add new items via a modal form.
 * This component maintains a stateful list of management items and integrates
 * features for displaying, adding, and handling empty lists.
 *
 * @component
 *
 * @example
 * return <ListManagementView />;
 *
 * @description
 * - Displays a list of management items with "Name", "Data Type", and "Actions" columns.
 * - Includes a button to add a new item to the list. Clicking the button opens a modal form.
 * - The modal form allows users to input the list name, data type, and other details.
 * - If no items exist in the list, a fallback message with a button to add a new list is shown.
 *
 * @returns {JSX.Element} The rendered ListManagementView component.

 *
 */

const ListManagementView = () => {
  /**
   * Initial dummy data for the management list.
   * Each item has a name and a data type.
   */
  const initialManagementData = [
    { name: "User Management", dataType: "String" },
    { name: "Role Management", dataType: "Number" },
    { name: "Permissions", dataType: "Number" },
  ];

  const dispatch = useDispatch();
  /**
   * @state {Array} managementList - State to store the list of management items.
   */
  const [managementList, setManagementList] = useState(initialManagementData);

  /**
   * @state {boolean} isModalOpen - State to track the visibility of the modal form.
   */
  const [isModalOpen, setIsModalOpen] = useState(false);

  /**
   * Opens the "Add List" modal.
   */
  const handleOpenModal = () => setIsModalOpen(true);

  /**
   * Closes the "Add List" modal.
   */
  const handleCloseModal = () => setIsModalOpen(false);

  /**
   * Adds a new item to the management list.
   *
   * @param {Object} newItem - The new item to add to the list.
   * The object should have `name` and `dataType` properties.
   */
  const handleAddItem = (newItem) => {
    setManagementList((prevList) => [...prevList, newItem]);
    handleCloseModal(); // Close the modal after adding the item
  };

  // Back move back to campaign view
  const onBackBtnClick = () => {
    dispatch(setCampaignsTab("viewCampaigns"));
  };

  return (
    <>
      <IconBtnAtom
        sx={{ my: 2 }}
        iconParams={{
          iconName: "ArrowBackIos",
          color: "secondary",
          size: 25,
          title: "More Options",
        }}
        onClick={onBackBtnClick}
      />
      <Box
        sx={{
          padding: 2,
          boxShadow: 2,
          borderRadius: 2,
          maxWidth: "1200px",
          margin: "auto",
          backgroundColor: "#fff",
          position: "relative",
        }}
      >
        {/* Add List Button */}
        <Btn
          variant="contained"
          color="primary"
          sx={{ mb: 2, position: "absolute", right: 5 }}
          onClick={handleOpenModal}
          text={"Add List"}
        />

        {/* Title */}
        <TypographyAtom
          variant="h4"
          sx={{ fontWeight: "bold", mb: 3, textAlign: "left" }}
          text="List Management"
        />

        {/* Header Row */}
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr 1fr",
            gap: 2,
            textAlign: "left",
            padding: "8px 16px",
            backgroundColor: "#f4f4f4",
            fontWeight: "bold",
            borderBottom: "2px solid #ddd",
          }}
        >
          <TypographyAtom variant="h6" text="Name" />
          <TypographyAtom variant="h6" text="Data Type" />
          <TypographyAtom variant="h6" text="Actions" />
        </Box>

        {/* Management List */}
        {managementList.map((item, index) => (
          <ListRowItem
            key={index}
            item={item}
            index={index}
            setManagementList={setManagementList}
            managementList={managementList}
          />
        ))}

        {/* Fallback for Empty List */}
        {managementList.length === 0 && (
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
            }}
          >
            <TypographyAtom
              variant="body1"
              sx={{ textAlign: "center", mt: 3 }}
              text="No list available"
            />
          </Box>
        )}

        {/* Add List Modal */}
        <AddListModal
          open={isModalOpen}
          onClose={handleCloseModal}
          onSubmit={handleAddItem}
        />
      </Box>
    </>
  );
};

export default ListManagementView;
