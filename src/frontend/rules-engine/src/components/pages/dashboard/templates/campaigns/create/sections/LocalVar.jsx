import { Box } from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../../../global/atoms/typography/Typography";
import { Btn as Btn<PERSON>tom } from "../../../../../../global/atoms/buttons/Button";
import { useEffect, useState } from "react";
import SectionHeaderMolecule from "../../../../molecules/campaigns/create/SubSectionHeader";
import { collectionMappingTabData } from "../../../../../../../utils/dummyData/Campaigns";
import VariablesContentOrganizer from "../../../../organisms/campaigns/create/variables/VariablesContent";
import { useDispatch, useSelector } from "react-redux";
import { Alert, Helpers } from "../../../../../../../utils";
import { setCampaign } from "../../../../../../../redux/slices/tabs/campaigns/campaign";
import ReusableSectionHOC from "../../../../atoms/global/reusableSectionHOC";
import React from "react";
/**
 * @typedef {import('../../../../../../../../jsDocs/components/dashboard').VariablesInputDataInputData} InputsState
 */

const keys = ["variableId", "name", "description", "type", "defaultValue"];
const optionalKeys = ["variableId"];
/**
 * LocalVarSection component for entering campaign local variables.
 *
 * @returns {JSX.Element} The rendered local variable section.
 */
const LocalVarSection = () => {
  /**
   * Redux store state object.
   * @type {import("../../../../../../../../jsDocs/redux/store.js").Store["campaign"]}
   */
  const { campaign } = useSelector((state) => state.campaign);

  const { persistentVariables, localVariables, evaluationRules, outcomeRules } =
    campaign;
  const dispatch = useDispatch();

  /** @type {[InputsState[],  React.Dispatch<React.SetStateAction<InputsState[]>>]} */
  const [variables, setVariables] = useState([]);

  const columnsOfSectionHeader = [
    { label: "Variable Name", gridSize: 10, sx: {} }, // 6 out of 12 columns
    { label: "Actions", gridSize: 2, sx: {} }, // 6 out of 12 columns
  ];
  useEffect(() => {
    if (campaign.localVariables?.length < 0) return;
    /**
     * Updates the state of localVariableDefinitions based on the campaign state.
     *
     * It iterates through the campaign.localVariableDefinitions and creates a new
     * object for each item. This new object contains the original item's data and
     * the default values of the keys array.
     *
     * The new objects are then saved to the localVariableDefinitions state.
     */
    const updateStateHandler = () => {
      let data = campaign?.localVariables.map((item) => {
        return {
          id: item.variableId,
          mode: "viewing",
          columns: [{ content: item.name, gridSize: 6 }],
          isEditable: true,
          isDeletable: true,
          isAllFieldsValid: false,
          validations: {},
          inputs: Helpers.defaultValues(keys, item, optionalKeys),
        };
      });

      setVariables(data);
    };
    updateStateHandler();
  }, [campaign]);

  // Function to handle adding a new item to the headerComponentData
  const handleAddNewItem = () => {
    let isIncompleteVar = variables.find((item) => {
      return item.id === "newVariable";
    });
    if (isIncompleteVar)
      return Alert("Please fill the incomplete variable", "error");

    let inputs = Helpers.defaultValues(keys, {}, optionalKeys);
    const newItem = {
      id: "newVariable",
      mode: "editing", // ["viewing", "editing"]
      columns: [{ content: "Create Variable", gridSize: 6 }],
      isEditable: true,
      isDeletable: true,
      isAllFieldsValid: false,
      validations: {},
      inputs: inputs,
    };

    setVariables([...variables, newItem]);
  };
  /**
   * The function that puts a local variable in edit mode.
   * It takes the id of the local variable as an argument.
   * It iterates through the variableDefinitions state and sets the mode of the
   * local variable with the given id to "editing".
   * The updated state is then saved to the variableDefinitions state.
   * @param {string} id The id of the local variable to be edited.
   */
  const handleEditItem = (id) => {
    const updatedData = variables.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          mode: "editing",
        };
      }
      return item;
    });
    setVariables(updatedData);
  };
  /**
   * The function that handles deleting a local variable.
   * It takes the id of the local variable as an argument.
   * It iterates through the variableDefinitions state and removes the local
   * variable with the given id from the state.
   * Then it updates the campaign state by removing the deleted local variable
   * from the localVariableDefinitions array.
   * @param {string} id The id of the local variable to be deleted.
   */
  const handleDeleteItem = (id) => {
    // /** Check the variable if it is used in the entities or not */
    const isVarUsedEvalRules = Helpers.isVariableUsed(id, evaluationRules);
    const isVarUsedOutcomeRules = Helpers.isVariableUsed(id, outcomeRules);
    if (isVarUsedEvalRules)
      return Alert(
        "Variable is used in the campaign. To delete or edit this variable, please remove it from the rules section first.",
        "error"
      );
    if (isVarUsedOutcomeRules)
      return Alert(
        "Variable is used in the campaign. To delete or edit this variable, please remove it from the rules section first.",
        "error"
      );
    // Filter out the local variable with the given id from the state
    const updatedData = variables.filter((item) => item.id !== id);

    // Update the variableDefinitions state with the new array
    setVariables(updatedData);

    // Update the campaign state by removing the deleted local variable
    // from the localVariableDefinitions array
    let newCampaign = {
      ...campaign,
      localVariables: updatedData.map((item) => ({
        ...Helpers.returnValues(item.inputs),
      })),
    };

    // Dispatch the action to update the campaign state
    dispatch(setCampaign(newCampaign));
  };
  // Function to handle cancel action for a specific item
  const handleCancel = (id) => {
    if (id === "newVariable") {
      // If it's a new unsaved variable, remove it completely
      const updatedData = variables.filter((item) => item.id !== id);
      setVariables(updatedData);
    } else {
      // For existing variables, just switch back to viewing mode
      const updatedData = variables.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            mode: "viewing",
          };
        }
        return item;
      });
      setVariables(updatedData);
    }
  };
  /**
   * Handles the save action for a specific local variable.
   * @param {string} id The id of the local variable to be saved.
   * @param {object} inputs The input values to be saved.
   * It first checks if the variable name already exists in the local variables
   * or in the persistent variables. If it does, it displays an error alert.
   * If it doesn't, it updates the local variable definitions with the new input values
   * and dispatches an action to update the campaign state with the new
   * localVariableDefinitions array. If the save is successful, it displays a success alert.
   */
  const handleSave = (id, inputs) => {
    const isLocalVariable = variables.some((item) => {
      return (
        item.inputs?.name?.value.toLowerCase() === inputs.name?.toLowerCase()
      );
    });
    if (isLocalVariable && id === "newVariable") {
      return Alert("Variable name already exists in local variables", "error");
    }
    let isInvalidName = persistentVariables.some((item) => {
      return item.name.toLowerCase() === inputs.name.toLowerCase(); // Compare the name property
    });
    if (isInvalidName) {
      return Alert(
        "Variable name already exists in persistent variables",
        "error"
      );
    }

    const updatedData = variables.map((item) => {
      return item.id === id
        ? {
            ...inputs,
            variableId: Helpers.idGenerator(inputs.name, "camel", {
              removeSpecialChars: true,
              maxLength: 50,
            }),
          }
        : { ...Helpers.returnValues(item.inputs) };
    });

    let newCampaign = {
      localVariables: updatedData,
    };

    dispatch(setCampaign(newCampaign));

    // Set the alert here if successfully
    Alert("Variable created successfully", "success");
  };

  return (
    <Box>
      <Box
        sx={{
          mt: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <TypographyAtom text="Add Variable" variant="h6" sx={{}} />
        <BtnAtom
          onClick={handleAddNewItem}
          sx={{ backgroundColor: "primary.secondary" }}
          text="Add"
        />
      </Box>

      <Box sx={{ mt: 2, width: "100%" }}>
        {/* {variableDefinitions.length > 0 && ( */}
        <SectionHeaderMolecule sx={{}} columns={columnsOfSectionHeader} />
        {/* )} */}
        {/*  Here we are mapping over the variableDefinitions array to render the sub sections of the  variable */}
        {variables.length > 0 &&
          variables.map((tab, index) => (
            <ReusableSectionHOC
              key={tab.id}
              index={index}
              tab={tab}
              columns={collectionMappingTabData.columns}
              rows={collectionMappingTabData.rows}
              onEdit={() => handleEditItem(tab.id)}
              onDelete={() => handleDeleteItem(tab.id)}
              sxContainer={{ backgroundColor: "bgSecondary.main" }}
              sxHeaderRow={{ backgroundColor: "bgSecondary.main" }}
              content={
                <VariablesContentOrganizer
                  handleCancel={handleCancel}
                  handleSave={handleSave}
                  key={index}
                  tab={tab}
                />
              }
            />
          ))}
        {/* If the variableDefinitions array is empty, we display a message */}
        {variables.length < 1 && (
          <Box sx={{ mt: 2 }}>
            <TypographyAtom
              sx={{ textAlign: "center", color: "textColor.secondary" }}
              text="No variables added yet"
              variant="h6"
            />
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default LocalVarSection;
