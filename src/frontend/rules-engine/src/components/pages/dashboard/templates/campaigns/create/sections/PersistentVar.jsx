import { Box } from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../../../global/atoms/typography/Typography";
import { Btn as Btn<PERSON>tom } from "../../../../../../global/atoms/buttons/Button";
import { useEffect, useState } from "react";
import SectionHeaderMolecule from "../../../../molecules/campaigns/create/SubSectionHeader";
import { collectionMappingTabData } from "../../../../../../../utils/dummyData/Campaigns";
import VariablesContentOrganizer from "../../../../organisms/campaigns/create/variables/VariablesContent";
import { useDispatch, useSelector } from "react-redux";
import { Alert, Helpers } from "../../../../../../../utils";
import { setCampaign } from "../../../../../../../redux/slices/tabs/campaigns/campaign";
import ReusableSectionHOC from "../../../../atoms/global/reusableSectionHOC";
import React from "react";
/**
 * @typedef {import('../../../../../../../../jsDocs/components/dashboard').VariablesInputDataInputData} InputsState
 */

const keys = [
  "variableId",
  "name",
  "description",
  "type",
  "defaultValue",
  "collectionId",
];
const optionalKeys = ["variableId"];
/**
 * LocalVarSection component for entering campaign local variables.
 *
 * @returns {JSX.Element} The rendered local variable section.
 */

const generateColMappingOptions = (collMappings) => {
  return collMappings.map((item) => {
    return {
      value: item.collectionId,
      label: item.name,
    };
  });
};
/**
 * Component for displaying and editing persistent variable definitions of a campaign.
 *
 * @returns {JSX.Element} The rendered persistent variable section.
 *
 * @description
 * This component displays the persistent variable definitions of a campaign. Each
 * persistent variable definition is displayed as a row in a table. The user can
 * add, edit, and delete persistent variable definitions. If the campaign has no
 * persistent variable definitions, it displays a message indicating that no variables
 * have been added yet.
 
 */

const PersistentVarSection = () => {
  /** @type {import("../../../../../../../../jsDocs/redux/store").Store['campaign']} */
  const { campaign } = useSelector(
    /** @param {{ campaign: any }} state */ (state) => state.campaign
  );
  let {
    persistentVariables,
    collectionMappings,
    evaluationRules,
    outcomeRules,
  } = campaign;

  const dispatch = useDispatch();
  /** @type {[InputsState[],  React.Dispatch<React.SetStateAction<InputsState>>]} */
  const [variables, setVariables] = useState([]);
  const [collMappings, setCollectionMappings] = useState([]);

  const columnsOfSectionHeader = [
    { label: "Variable Name", gridSize: 10, sx: {} }, // 6 out of 12 columns
    { label: "Actions", gridSize: 2, sx: {} }, // 6 out of 12 columns
  ];

  useEffect(() => {
    if (persistentVariables?.length < 0) return;
    /**
     * paign state.
     *
     * It iterates through the campaign.localVariableDefinitions and creates a new
     * object for each item. This new object contains the original item's data and
     * the default values of the keys array.
     *
     * The new objects are then saved to the localVariableDefinitions state.
     */

    /**
     * Updates the state of persistentVariableDefinitions based on the campaign state.
     *
     * It iterates through the campaign.persistentVariableDefinitions and creates a new
     * object for each item. This new object contains the original item's data and
     * the default values of the keys array.
     *
     * The new objects are then saved to the persistentVariableDefinitions state.
     */
    const updateStateHandler = () => {
      if (campaign?.persistentVariables?.length < 1) return;

      let data = campaign?.persistentVariables?.map((item) => {
        return {
          id: item.variableId,
          mode: "viewing",
          columns: [{ content: item.name, gridSize: 6 }],
          isEditable: true,
          isDeletable: true,
          isAllFieldsValid: false,
          validations: {},
          inputs: Helpers.defaultValues(keys, item, optionalKeys),
        };
      });

      setVariables(data);
    };

    updateStateHandler();
  }, [persistentVariables]);

  useEffect(() => {
    let colM = generateColMappingOptions(collectionMappings);

    setCollectionMappings(colM);
  }, [collectionMappings]);

  /**
   * Handles adding a new persistent variable definition item to the state.
   *
   * If the campaign's collection mappings are empty, it displays an error alert.
   * If there is already an incomplete variable, it displays an error alert and
   * doesn't add a new item to the state.
   *
   * Otherwise, it creates a new item object with default values for the keys
   * array and adds it to the variableDefinitions state.
   */
  const handleAddNewItem = () => {
    // If  campaign's collection mappings are empty, it displays an error alert.
    if (campaign.collectionMappings.length < 1) {
      return Alert("Please add collection mappings first", "error");
    }
    // If there is already an incomplete variable, it displays an error alert and
    let isIncompleteVar = variables.find((item) => {
      return item.id === "newVariable";
    });
    if (isIncompleteVar)
      return Alert("Please fill the incomplete variable", "error");
    // Otherwise, it creates a new item object with default values for the keys
    const newItem = {
      id: "newVariable",
      mode: "editing", // ["viewing", "editing"]
      columns: [{ content: "Create Variable", gridSize: 6 }],
      isEditable: true,
      isDeletable: true,
      isAllFieldsValid: false,
      validations: {},
      inputs: Helpers.defaultValues(keys, {}, optionalKeys),
    };

    setVariables([...variables, newItem]);
  };

  /**
   * Edits a persistent variable definition based on the given id.
   *
   * It iterates through the variableDefinitions state and updates the item with the
   * given id by setting its mode to "editing". The updated state is then set to the
   * variableDefinitions state.
   *
   * @param {string} id - The id of the persistent variable definition to edit.
   */
  const handleEditItem = (id) => {
    // /** Check the variable if it is used in the entities or not */
    // const isVarUsed = Helpers.isVariableUsed(id, entities);
    // if (isVarUsed)
    //   return Alert(
    //     "Variable is used in the campaign. To delete or edit this variable, please remove it from the entities section first.",
    //     "error"
    //   );

    const updatedData = variables.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          mode: "editing",
        };
      }
      return item;
    });
    setVariables(updatedData);
  };

  /**
   * Deletes a persistent variable definition based on the given id.
   *
   * It filters out the item with the given id from the variableDefinitions state and
   * updates the campaign state with the new variable definitions.
   *
   * @param {string} id - The id of the persistent variable definition to delete.
   */
  const handleDeleteItem = (id) => {
    /** Check the variable if it is used in the entities or not */
    //  check if the variable is used in the rules or not for now we are not checking it
    const isVarUsedEvalRules = Helpers.isVariableUsed(id, evaluationRules);
    const isVarUsedOutcomeRules = Helpers.isVariableUsed(id, outcomeRules);
    if (isVarUsedEvalRules)
      return Alert(
        "Variable is used in the campaign. To delete or edit this variable, please remove it from the rules section first.",
        "error"
      );
    if (isVarUsedOutcomeRules)
      return Alert(
        "Variable is used in the campaign. To delete or edit this variable, please remove it from the rules section first.",
        "error"
      );

    const updatedData = variables.filter((item) => item.id !== id);

    setVariables(updatedData);
    let newCampaign = {
      ...campaign,
      persistentVariables: updatedData.map((item) => ({
        ...Helpers.returnValues(item.inputs),
      })),
    };
    dispatch(setCampaign(newCampaign));
  };

  // Functions for handling save and cancel button click events
  const handleCancel = (id) => {
    if (id === "newVariable") {
      // If it's a new unsaved variable, remove it completely
      const updatedData = variables.filter((item) => item.id !== id);
      setVariables(updatedData);
    } else {
      // For existing variables, just switch back to viewing mode
      const updatedData = variables.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            mode: "viewing",
          };
        }
        return item;
      });
      setVariables(updatedData);
    }
  };
  /**
   * Handles the save action for a specific persistent variable.
   * @param {string} id The id of the persistent variable to be saved.
   * @param {object} inputs The input values to be saved.
   * It first checks if the variable name already exists in the persistent variables
   * or in the local variables. If it does, it displays an error alert.
   * If it doesn't, it updates the persistent variable definitions with the new input values
   * and dispatches an action to update the campaign state with the new
   * persistentVariableDefinitions array. If the save is successful, it displays a success alert.
   */
  const handleSave = (id, inputs) => {
    const isLocalVariable = variables.some((item) => {
      return (
        item.inputs?.name?.value.toLowerCase() === inputs.name?.toLowerCase()
      );
    });
    if (isLocalVariable && id === "newVariable") {
      return Alert(
        "Variable name already exists in persistent variables",
        "error"
      );
    }
    let isInvalidName = persistentVariables.some((item) => {
      return item.name === inputs.name; // Compare the name property
    });
    if (isInvalidName && id === "newVariable") {
      return Alert("Variable name already exists in local variables", "error");
    }
    const updatedData = variables.map((item) => {
      return item.id === id
        ? {
            ...inputs,
            variableId: Helpers.idGenerator(inputs.name, "camel", {
              removeSpecialChars: true,
              maxLength: 50,
            }),
          }
        : { ...Helpers.returnValues(item.inputs) };
    });

    let newCampaign = {
      persistentVariables: updatedData,
    };

    dispatch(setCampaign(newCampaign));
    // Set the alert here if successfully
    Alert("Variable created successfully", "success");
  };

  return (
    <Box>
      <Box
        sx={{
          mt: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <TypographyAtom text="Add Variable" variant="h6" sx={{}} />
        <BtnAtom
          onClick={handleAddNewItem}
          sx={{ backgroundColor: "primary.secondary" }}
          text="Add"
        />
      </Box>

      <Box sx={{ mt: 2, width: "100%" }}>
        {/* {variableDefinitions.length > 0 && ( */}
        <SectionHeaderMolecule sx={{}} columns={columnsOfSectionHeader} />
        {/* )} */}

        {variables.length > 0 &&
          variables.map((tab, index) => (
            <ReusableSectionHOC
              key={tab.id}
              index={index}
              tab={tab}
              columns={collectionMappingTabData.columns}
              rows={collectionMappingTabData.rows}
              onEdit={() => handleEditItem(tab.id)}
              onDelete={() => handleDeleteItem(tab.id)}
              sxContainer={{ backgroundColor: "bgSecondary.main" }}
              sxHeaderRow={{ backgroundColor: "bgSecondary.main" }}
              content={
                <VariablesContentOrganizer
                  tabType="persistent"
                  handleCancel={handleCancel}
                  handleSave={handleSave}
                  key={index}
                  tab={tab}
                  collections={collMappings}
                />
              }
            />
          ))}
        {variables.length < 1 && (
          <Box sx={{ mt: 2 }}>
            <TypographyAtom
              sx={{ textAlign: "center", color: "textColor.secondary" }}
              text="No variables added yet"
              variant="h6"
            />
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default PersistentVarSection;
