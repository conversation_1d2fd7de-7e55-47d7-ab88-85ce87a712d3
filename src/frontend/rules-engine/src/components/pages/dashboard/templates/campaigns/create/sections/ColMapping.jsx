import { Box } from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../../../global/atoms/typography/Typography";
import { Btn as BtnAtom } from "../../../../../../global/atoms/buttons/Button";
import { useEffect, useState } from "react";
import SectionHeaderMolecule from "../../../../molecules/campaigns/create/SubSectionHeader";
import KeyMappingContentOrganizer from "../../../../organisms/campaigns/create/colMappings/CollectionMappingContent";
import { useDispatch, useSelector } from "react-redux";
import { Alert, Helpers } from "../../../../../../../utils";
import { setCampaign } from "../../../../../../../redux/slices/tabs/campaigns/campaign";
import React from "react";
import ReusableSectionHOC from "../../../../atoms/global/reusableSectionHOC";
/**
 * @typedef {import('../../../../../../../../jsDocs/components/dashboard').CollectionMappingInputData} InputsState
 */

/**
 * This is the main component for handling the collecton mapping tab in the campaign
 *
 * It renders a section header with a button to add new collection mappings
 * And renders a list of all the collection mappings with their respective key mappings
 * Each collection mapping can be edited or deleted
 * And a save button is rendered to save all the collection mappings
 *
 * State:
 * - collectionMappings: an array of objects containing the collection mapping data
 *   Each object contains the following properties:
 *     - id: the id for identifying the tab
 *     - mode: the mode of the tab, can be either "viewing" or "editing"
 *     - columns: an array of objects containing the column data
 *     - rows: an array of objects containing the row data
 *     - isEditable: a boolean indicating if the tab can be edited
 *     - isDeletable: a boolean indicating if the tab can be deleted
 *     - collectionName: the name of the collection
 *     - keyMappings: an array of objects containing the key mapping data
 *       Each object contains the following properties:
 *         - id: the id for identifying the key mapping
 *         - entityId: the id of the entity
 *         - contextId: the id of the context
 *         - propertyName: the name of the property
 *         - inputType: the input type of the key mapping, can be either "text", "date", or "number"
 *         - defaultValue: the default value of the key mapping
 *         - isRequired: a boolean indicating if the key mapping is required
 *         - isEditable: a boolean indicating if the key mapping can be edited
 *         - validations: an object containing the validation rules for the key mapping
 *           Each property is a function that takes the value as an argument and returns a boolean indicating if the value is valid or not
 * Actions:
 * - handleAddNewItem: a function that adds a new collection mapping to the collectionMappings state
 * - handleEditItem: a function that puts the collection mapping item in edit mode
 * - handleDeleteItem: a function that deletes a collection mapping item
 * - handleSave: a function that saves all the collection mappings data
 * - handleCancelCollMapping: a function that cancels the edit mode of a collection mapping item
 *
 */
const ColMappingSection = () => {
  const dispatch = useDispatch();
  /** @type {} */
  const { campaign } = useSelector((state) => state.campaign);

  const { persistentVariables } = campaign;

  /** @type {[InputsState[],  React.Dispatch<React.SetStateAction<InputsState[]>>]} */
  const [collectionMappings, setCollectionMappings] = useState([]);
  /** @type {[boolean, React.Dispatch<React.SetStateAction<boolean>>]}  it is used to disable and enable the save button*/
  const [isSaveDisabled, setIsSaveDisabled] = useState(true);

  /** 
  The main collection mapping table initial header data 
  */
  const columnsOfSectionHeader = [
    { label: "New Collection", gridSize: 10, sx: {} }, // 6 out of 12 columns
    { label: "Actions", gridSize: 2, sx: {} }, // 6 out of 12 columns
  ];
  /**
   * 
    Function to handle adding a new item to the collectionMappings
   */
  const handleAddNewItem = () => {
    let isNewCollectionExist = collectionMappings.some(
      (item) => item.collectionId === "newCollection"
    );

    if (isNewCollectionExist) {
      return Alert("Cannot add multiple new collections", "error");
    }
    // Add a new item to the collectionMappings
    const newItem = {
      collectionId: "newCollection",
      mode: "editing", // ["viewing", "editing"]
      columns: [{ content: "Create Collection Mappings", gridSize: 6 }],
      isEditable: true,
      isDeletable: true,
      validations: {},
      name: "",
      keyMapping: { propertyId: "" },
    };

    setCollectionMappings([...collectionMappings, newItem]);
    setIsSaveDisabled(false);
  };
  /**
   * The function that adding the collection mapping item in the collectionMappings
   * @param {string} id The id for identifying the tab
   * @param {object} data
   */
  const handleSaveCollMapping = (id, data) => {
    // Check if the collection name already exists in other collection mappings
    const isNameDuplicate = collectionMappings.some(
      (item) => item.collectionId != id && item.name == data.name
    );

    if (isNameDuplicate) {
      return Alert(
        "Collection name already exists. Please use a unique collection name.",
        "error"
      );
    }
    let updatedItem = collectionMappings.map((item) => {
      if (item.collectionId === id) {
        return {
          ...item,
          collectionId: Math.random().toString(),
          mode: "viewing",
          keyMapping: { propertyId: data.keyMapping.propertyId },
          name: data.name,
          columns: [{ content: data.name, gridSize: 6 }],
        };
      } else {
        return item;
      }
    });

    setCollectionMappings(updatedItem);
  };

  /**
   * The function that puts the collection mapping item in edit mode.
   * @param {string} id The id for identifying the tab
   */
  const handleEditItem = (id) => {
    // Check if this collection is referenced in persistent variables
    const isUsedInPersistentVariables = persistentVariables?.some(
      (variable) => variable.collectionId == id
    );

    if (isUsedInPersistentVariables) {
      return Alert(
        "This collection is used in persistent variables. Please remove it from persistent variables first.",
        "error"
      );
    }
    setIsSaveDisabled(false);
    const updatedData = collectionMappings.map((item) => {
      if (item.collectionId === id) {
        return {
          ...item,
          mode: "editing",
        };
      }
      return item;
    });

    setCollectionMappings(updatedData);
  };

  /**
   * The function that deletes a collection mapping item.
   * @param {string} id The id for identifying the tab
   */
  const handleDeleteItem = (id) => {
    // Check if this collection is referenced in persistent variables
    const isUsedInPersistentVariables = persistentVariables?.some(
      (variable) => variable.collectionId == id
    );

    if (isUsedInPersistentVariables) {
      return Alert(
        "This collection is used in persistent variables. Please remove it from persistent variables first.",
        "error"
      );
    }
    setIsSaveDisabled(false);
    const updatedData = collectionMappings.filter(
      (item) => item.collectionId !== id
    );
    setCollectionMappings(updatedData);
    const updatedStateData = updatedData.map(
      ({ collectionId, name, keyMapping }) => ({
        collectionId,
        name,
        keyMapping,
      })
    );
    dispatch(setCampaign({ collectionMappings: updatedStateData }));
  };

  /**
   * The function that cancels the edit mode of a collection mapping item.
   * @param {string} id The id for identifying the tab
   */
  const handleCancelCollMapping = (id) => {
    if (id === "newCollection") {
      // If it's a new unsaved collection, remove it completely
      const updatedData = collectionMappings.filter(
        (item) => item.collectionId !== id
      );
      setCollectionMappings(updatedData);
    } else {
      // For existing collections, just switch back to viewing mode
      const updatedData = collectionMappings.map((item) =>
        item.collectionId === id ? { ...item, mode: "viewing" } : item
      );
      setCollectionMappings(updatedData);
    }
  };
  /**
   * The function that saves all the collection mappings data.
   * It takes all the data from the collectionMappings state and
   * sends it to the campaign state by dispatching the setCampaign action.
   * @function
   */
  const handleSave = () => {
    if (collectionMappings.length < 1) {
      return Alert("Please add at least one collection mapping", "error");
    }
    if (
      collectionMappings.some((item) => item.collectionId === "newCollection")
    ) {
      return Alert("Please first save the new collection mapping", "error");
    }

    const updatedData = collectionMappings.map(
      ({ collectionId, name, keyMapping }) => ({
        collectionId: Helpers.toCamelCase(name.trim()),
        name,
        keyMapping,
      })
    );
    let obj = {
      collectionMappings: updatedData,
    };
    dispatch(setCampaign(obj));
    setIsSaveDisabled(true);

    Alert("Collection mapping saved successfully", "success");
  };
  const handleCancel = (id) => {
    let updatedData = collectionMappings.filter(
      (item) => item.collectionId !== id
    );
    setCollectionMappings(updatedData);
    setIsSaveDisabled(true);
  };

  useEffect(() => {
    // if the campaign collectionMappings is less than 0 then return undefined else
    // map the collectionMappings and set the collectionMappings state
    if (campaign.collectionMappings?.length < 0) return;
    let obj = campaign.collectionMappings?.map((item) => {
      return {
        collectionId: item.collectionId,
        mode: "viewing", // ["viewing", "editing"]
        columns: [{ content: item.name, gridSize: 6 }],
        isEditable: true,
        isDeletable: true,
        ...item,
      };
    });
    setCollectionMappings(obj);
  }, [campaign.collectionMappings]);

  return (
    <Box>
      <Box
        sx={{
          mt: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <TypographyAtom text="Collection Mapping" variant="h6" sx={{}} />
        <BtnAtom
          onClick={handleAddNewItem}
          sx={{ backgroundColor: "primary.secondary" }}
          text="Add"
        />
      </Box>
      {/* Loop through the collectionMappings array and render the ColMappingSubSection component */}
      <Box sx={{ mt: 2, width: "100%" }}>
        <SectionHeaderMolecule sx={{}} columns={columnsOfSectionHeader} />
        {collectionMappings.map((tab, index) => (
          <ReusableSectionHOC
            key={index}
            index={index}
            tab={tab}
            columns={tab.columns}
            rows={tab.rows}
            onEdit={() => handleEditItem(tab.collectionId)}
            onDelete={() => handleDeleteItem(tab.collectionId)}
            sxContainer={{ backgroundColor: "bgSecondary.main" }}
            sxHeaderRow={{ backgroundColor: "bgSecondary.main" }}
            content={
              <KeyMappingContentOrganizer
                tab={tab}
                handleSaveCollMapping={handleSaveCollMapping}
                // handleCancelCollMapping={() => handleCancelCollMapping(tab.id)}
                handleCancel={() => handleCancelCollMapping(tab.collectionId)}
              />
            }
          />
        ))}
        {/* Display a message if there are no collection mappings */}
        {collectionMappings.length < 1 && (
          <Box sx={{ mt: 2 }}>
            <TypographyAtom
              sx={{ textAlign: "center", color: "textColor.secondary" }}
              text="No collection mappings added yet."
              variant="h6"
            />
          </Box>
        )}
      </Box>

      {!isSaveDisabled && collectionMappings.length > 0 && (
        <Box
          item
          size={{ xs: 12, sm: 12, md: 12 }}
          sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}
        >
          <BtnAtom
            onClick={handleSave}
            text="Save"
            sx={{ backgroundColor: "primary.secondary" }}
          />
        </Box>
      )}
    </Box>
  );
};

export default ColMappingSection;
