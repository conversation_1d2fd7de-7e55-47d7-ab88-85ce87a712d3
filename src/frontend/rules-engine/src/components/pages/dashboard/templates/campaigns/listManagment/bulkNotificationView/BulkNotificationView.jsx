import ListServiceClass from "../../../../../../../Api/ListService";
import ListManagmentHOC from "../ListManagementHOC";
import endPoints from "../../../../../../../Api/EndPoints";

const ListService = new ListServiceClass(endPoints.bulkNotificationList);


const BulkNotificationView = ListManagmentHOC({
  title: "Bulk Notification List",
  id: "bulknotificationList",
  headers: [{label: "Name", id: "name"}, {label: "Text", id: "text"}, {label: "Rows Count", id: "elemsCount"}, {label: "Action", id: "action"}],
  fields: [
    { name: "name", label: "Name", type: "string", required: true },
    { name: "dataType", label: "Data Type", type: "string", required: false },
    { name: "text", label: "Text", type: "textarea", required: true },
  ],
  receivedDataObjectPropKey: "bulk_notify_lists",
  apiDownloadListFunction: ListService.downloadList,
  apiGetAllFunction: ListService.getAllList,
  apiDeleteFunction: ListService.deleteList,
  apiSubmitFunction: ListService.addList,
  apiSendBulkSmsFunction: ListService.sendBulkSms,
});

export default BulkNotificationView;
