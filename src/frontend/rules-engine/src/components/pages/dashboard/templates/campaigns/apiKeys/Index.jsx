import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import ReusableTable from "../../../../../global/atoms/table/Index";

import React, { useEffect, useState } from "react";
import { Helpers } from "../../../../../../utils/generalFunctions/index";
import { Btn } from "../../../../../global/atoms/buttons/Button";
import AddUser from "../../../../../pages/dashboard/organisms/campaigns/userManagement/addUser/Index";
import AuthService from "../../../../../../Api/AuthService";
import { Alert } from "../../../../../../utils";
import { TypographyAtom } from "../../../../../global/atoms/typography/Typography";
import { DynamicIcon } from "../../../../../global/atoms/icons/Index";
import { useDispatch, useSelector } from "react-redux";
import {
  closeDialog,
  openDialog,
} from "../../../../../../redux/slices/global/confirmation";
import { registerConfirmation } from "../../../../../global/atoms/dialog/Index";
import PaginationAtom from "../../../../../global/atoms/pagination/Index";
import GenericModal from "../../../../../../components/pages/dashboard/atoms/genericModal/Index";
/**
 * UserManagement component for handling user administration
 * @returns {JSX.Element} The UserManagement component
 */
const ApiKeys = () => {
  /**
   * This is the state for the page to show
   * @type {[string, React.Dispatch<React.SetStateAction<string>>]}
   */
  const [showPage, setShowPage] = useState("list"); //list,add,edit
  /**
   * This is the state for the user to edit
   * @type {[object, React.Dispatch<React.SetStateAction<object>>]}
   */
  const [editUserData, setEditUserData] = useState({});
  /**
   * This is the state for the users
   * @type {[object, React.Dispatch<React.SetStateAction<object>>]}
   */
  const [keysData, setKeysData] = useState({
    keys: [],
    page: 1,
    total: 0,
    limit: 10,
  });
  /**
   * This is the state for the add user button loading
   * @type {[boolean, React.Dispatch<React.SetStateAction<boolean>>]}
   */
  const [addUserBtnLoading, setAddUserBtnLoading] = useState(false);
  const [deleteUserBtnLoading, setDeleteUserBtnLoading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const dispatch = useDispatch();
  /**
   * This is the table data for the users
   * @type {Array<Array<{value: string, width: string, key: string}>>}
   */

  let tableData =
    keysData?.keys.length > 0
      ? keysData?.keys.map((key, index) => {
          return Object.entries(key)
            .filter(([key, value]) => key !== "id")
            .map(([key, value]) => ({ value: value, width: "50%", key }));
        })
      : [];

  let tableHeadData = [
    // {
    //   id: 1,
    //   text: "ID",
    //   width: "10%",
    // },

    {
      id: 1,
      text: "API Key",
      width: "10%",
    },

    {
      id: 6,
      text: "Delete",
      width: "10%",
    },
  ];

  /**
   *
    Generate API Key
   * @returns
   */
  const handleAddApiKeySubmit = async () => {
    try {
      setAddUserBtnLoading(true);
      const response = await AuthService.createApiKey();

      setKeysData({
        keys: [
          ...keysData.keys,
          {
            api_key: response.data.api_key,
            id: response.data.id,
          },
        ],
        page: keysData.page,
        total: keysData.total,
        limit: keysData.limit,
      });

      if (!response?.success) {
        return Alert(
          response?.message || "Error while generating api key",
          "error"
        );
      }

      Alert(response?.message || "Api key generated successfully", "success");

      // Show success modal with login details

      // Show success modal with login details

      // Reload the users
      // handleGetKeys(keysData.page);

      setShowPage("list");
    } catch (error) {
      console.log(error, "error");
      Alert("Error while generating api key", "error");
    } finally {
      setAddUserBtnLoading(false);
    }
  };

  /**
   * Handles deleting a user
   */
  const handleDeleteUserSubmit = async (row) => {
    try {
      let idCol = row.find((col) => col.key === "api_key");
      const actualUser = keysData.keys.find(
        (key) => key.api_key === idCol.value
      );

      // Register confirmation callback
      const confirmationId = registerConfirmation(async (confirmed) => {
        if (confirmed) {
          setDeleteUserBtnLoading(true);
          try {
            const response = await AuthService.deleteApiKey(actualUser.id);
            if (!response.success) {
              return Alert("Error while deleting api key", "error");
            }
            let newKeysData = keysData.keys.filter(
              (key) => key.id !== actualUser.id
            );
            setKeysData({ ...keysData, keys: newKeysData });
            Alert("Api Key deleted successfully", "success");
          } finally {
            setDeleteUserBtnLoading(false);
          }
        }
      });

      // Open confirmation dialog
      dispatch(
        openDialog({
          title: "Delete Api Key",
          message: `Are you sure you want to delete api key ${
            actualUser.api_key.split("").slice(0, 30).join("") + "..."
          }?`,
          confirmationId,
        })
      );
    } catch (error) {
      Alert("Error while deleting user", "error");
    }
  };

  /**
   * Handles getting the users from the database
   * @param {number} page The page number
   * @param {number} limit The limit of the users
   */
  const handleGetKeys = async (page) => {
    try {
      setIsLoading(true);
      const response = await AuthService.getAllApiKeys({
        // page: page,
        // limit: limit,
      });

      if (!response.data.success) {
        return Alert("Error while getting users", "error");
      }

      // Update users data
      setKeysData({
        keys: response.data.data.apiKeys.map((key) => ({
          id: key.id,
          // name: user.userProfile?.full_name?.toUpperCase(),
          api_key: key.api_key,
        })),
        page: response.data?.data?.page,
        total: response.data?.data?.total,
        limit: response.data?.data?.limit,
      });
    } catch (error) {
      Alert("Error while fetching users", "error");
    } finally {
      setIsLoading(false);
    }
  };

  const sx = {
    commonBox: {
      width: "50%",
    },
  };

  useEffect(() => {
    handleGetKeys(keysData.page);
  }, []);

  return (
    <>
      <Box
        sx={{
          width: "100%",
          maxWidth: "700px",
          margin: "auto",
          display: "flex",
          flexDirection: "column",
          gap: 2,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {showPage === "list" && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              my: 2,
              flexDirection: "column",
              width: "100%",
            }}
          >
            <Box
              sx={{ display: "flex", justifyContent: "space-between", my: 2 }}
            >
              <Typography variant="h4">API Keys</Typography>
              <Btn
                text="Generate API Key"
                variant="contained"
                sx={{ color: "mainColor.main" }}
                onClick={handleAddApiKeySubmit}
              />
            </Box>
            <ReusableTable
              tableHeadData={tableHeadData}
              name="apiKeys"
              actions={[
                // {
                //   text: "Edit",
                //   id: "edit",
                //   sx: {
                //     color: "mainColor.main",

                //     backgroundColor: "secondary.main",
                //   },
                //   handleClick: handleEditUser,
                // },
                {
                  text: "Delete",
                  id: "delete",
                  sx: {
                    color: "mainColor.main",
                    backgroundColor: "danger.main",
                  },
                  handleClick: handleDeleteUserSubmit,
                  loadingId: deleteUserBtnLoading,
                },
              ]}
              sx={{ tableCell: { textAlign: "left" } }}
              rows={tableData}
              onBtnClick={(id, row) => {}}
            />
            {keysData.total > keysData.limit && (
              <PaginationAtom
                count={Math.ceil(keysData.total / keysData.limit)}
                page={keysData.page}
                onChange={(event, newPage) => {
                  if (newPage !== keysData.page) {
                    handleGetKeys(newPage);
                  }
                }}
                disabled={isLoading}
                size="medium"
                sx={{}}
                sxStack={{ mt: 4 }}
              />
            )}
          </Box>
        )}

        {showPage !== "list" && (
          <Box sx={sx.commonBox}>
            <Button
              variant="text"
              sx={{ color: "textColor.main", width: "100%", mt: 2 }}
              onClick={() => setShowPage("list")}
            >
              <DynamicIcon iconName="ArrowBack" />
              <TypographyAtom text="Back" sx={{ ml: 1 }} />
            </Button>
          </Box>
        )}
      </Box>
    </>
  );
};

export default ApiKeys;
