import React, { useState, useEffect, useRef } from "react";
import { Box, Typography } from "@mui/material";
import { InputField } from "../../../../../global/atoms/inputFields/InputField";
import { Btn } from "../../../../../global/atoms/buttons/Button";
import LoadingBackdropAtom from "../../../../../global/atoms/loading/LoadingBackdrop";
import { useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { Alert } from "../../../../../../utils/alert/alertUtils";
import { Helpers } from "../../../../../../utils/generalFunctions/index";
import { loginSuccess } from "../../../../../../redux/slices/global/auth";
import AuthService from "../../../../../../Api/AuthService";
import { authValidators } from "../../../../../../utils/validators/auth";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import IconButton from "@mui/material/IconButton";
import Tooltip from "@mui/material/Tooltip";

const Profile = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  /**
   * This is the state for the user inputs.
   * @type {Object}
   * @property {string} email - The email of the user.
   * @property {string} name - The name of the user.
   * @property {string} password - The password of the user.
   * @property {string} newPassword - The new password of the user.
   * @property {string} confirmPassword - The confirm password of the user.
   */
  const [inputs, setInputs] = useState({
    email: "",
    // name: "",
    password: "",
    newPassword: "",
    confirmPassword: "",
  });
  /**
   * This is the state for the loading of the user.
   * @type {[boolean, React.Dispatch<React.SetStateAction<boolean>>]}
   */
  const [isLoading, setIsLoading] = useState(false);
  const apiToken = useRef("");

  useEffect(() => {
    // Fetch user data from backend
    const fetchUserData = async () => {
      setIsLoading(true);
      try {
        /* receiving response from the backend from response.data
        {"id":1,"tenant_id":1,"email":"<EMAIL>","role_id":1,"profile_completed":true,"userProfile":{"name":"Admin"},"role_name":"user-admin"}
         */
        const response = await AuthService.getUserProfileData();

        if (response.success) {
          let { userProfile, ...rest } = response.data;
          setInputs({
            ...inputs,
            ...rest,
            // name: userProfile.full_name,
          });
        } else {
          Alert("Failed to fetch user data");
        }
      } catch (error) {
        console.error("Error fetching user data", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchUserData();
  }, []);

  /**
   * This function updates the user profile data. It validates the name and then sends the data to the backend.
   */
  const handleSubmit = async () => {
    try {
      let obj = {
        profile_data: {
          // full_name: inputs.name?.trim(),
        },
      };
      // let isValidName = authValidators.global.name(obj.profile_data.full_name);
      // if (!isValidName.success) {
      //   return Alert("Invalid name");
      // }
      setIsLoading(true);
      let response = await AuthService.updateUserProfileData(obj);
      if (!response.success) {
        return Alert(response.data);
      }
      return Alert("Profile updated successfully", "success");
    } catch (error) {
      console.error("Profile update error", error);
    } finally {
      setIsLoading(false);
    }
  };
  /** This function set the new data of the user inputs */
  const handleInputChange = (event) => {
    let { name, value } = event.target;
    setInputs({
      ...inputs,
      [name]: value,
    });
  };

  /**
   * This function updates the user password. It validates the password, new password and
   * confirm password and then sends the data to the backend.
   */
  const handlePasswordSubmit = async () => {
    try {
      let obj = {
        password: inputs.password?.trim(),
        newPassword: inputs.newPassword?.trim(),
        confirmPassword: inputs.confirmPassword?.trim(),
      };
      const isValid = authValidators.validatePasswords(obj);
      if (!isValid.success) {
        return Alert(isValid.data);
      }

      setIsLoading(true);

      let response = await AuthService.updatePassword(obj);

      if (!response.success) {
        return Alert(response.data);
      }
      Alert("Password updated successfully", "success");

      // Clear password fields after successful update
      setInputs((prev) => ({
        ...prev,
        password: "",
        newPassword: "",
        confirmPassword: "",
      }));
    } catch (error) {
      console.error("Password update error", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Reusable styles
  const styles = {
    container: {
      maxWidth: { xs: "100%", sm: "90%", md: 600 },
      margin: { xs: "16px", sm: "32px auto" },
      padding: { xs: 2, sm: 3 },
      display: "flex",
      flexDirection: "column",
      gap: 3,
    },
    innerBox: {
      display: "flex",
      flexDirection: "column",
      gap: "16px",
      padding: { xs: 2, sm: 3 },
      boxShadow: 3,
      borderRadius: 2,
      backgroundColor: "#fff",
      width: "100%",
    },
    sectionTitle: {
      borderBottom: "1px solid #eee",
      paddingBottom: 1,
      marginBottom: 2,
    },
    fieldRow: {
      display: "flex",
      alignItems: { xs: "flex-start", sm: "center" },
      flexDirection: { xs: "column", sm: "row" },
      gap: { xs: 1, sm: 2 },
      width: "100%",
    },
    label: {
      minWidth: { xs: "auto", sm: 120 },
      fontWeight: 500,
      mb: { xs: 1, sm: 0 },
    },
  };

  return (
    <Box sx={styles.container}>
      <Typography variant="h4" sx={{ textAlign: "center", mb: 3 }}>
        Profile Settings
      </Typography>
      <LoadingBackdropAtom message={"Loading..."} open={isLoading} />

      <Box sx={styles.innerBox}>
        <Typography variant="h6" sx={styles.sectionTitle}>
          Personal Information
        </Typography>

        <Box sx={styles.fieldRow}>
          <Typography sx={styles.label}>Username:</Typography>
          <Typography>{inputs.username}</Typography>
        </Box>

        {/* <Box sx={styles.fieldRow}> */}
        {/* <Typography sx={styles.label}>Full Name:</Typography> */}
        {/* <InputField
            label="Full Name"
            type="text"
            name="name"
            fullWidth
            required
            value={inputs.name}
            onChange={handleInputChange}
            color="primary"
            onFocus={() => {}}
            onBlur={() => {}}
            disabled={false}
            validation={{ isShowError: false, error: "" }}
            placeholder="Enter your full name"
          /> */}
        {/* </Box> */}

        {apiToken.current && (
          <Box sx={styles.fieldRow}>
            <Typography sx={styles.label}>Api Token:</Typography>
            <Box
              sx={{
                flex: 1,
                display: "flex",
                alignItems: "center",
                gap: 1,
                overflow: "hidden",
              }}
            >
              <Typography
                sx={{
                  wordBreak: "break-all",
                  overflowWrap: "break-word",
                }}
              >
                {apiToken.current}
              </Typography>
              <Tooltip title="Copy to clipboard">
                <IconButton
                  onClick={async () => {
                    await navigator.clipboard.writeText(apiToken.current);
                    Alert("Token copied to clipboard!", "success");
                  }}
                  size="small"
                >
                  <ContentCopyIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        )}

        {/* <Btn text="Update Profile" onClick={handleSubmit} fullWidth /> */}
      </Box>

      <Box sx={styles.innerBox}>
        <Typography variant="h6" sx={styles.sectionTitle}>
          Change Password
        </Typography>

        <InputField
          label="Current Password"
          type="password"
          name="password"
          fullWidth
          value={inputs.password}
          onChange={handleInputChange}
          color="primary"
          onFocus={() => {}}
          onBlur={() => {}}
          disabled={false}
          validation={{ isShowError: false, error: "" }}
          placeholder="Enter current password"
          helperText="Leave empty to keep current password"
        />
        <InputField
          label="New Password"
          type="password"
          name="newPassword"
          fullWidth
          value={inputs.newPassword}
          onChange={handleInputChange}
          color="primary"
          onFocus={() => {}}
          onBlur={() => {}}
          disabled={false}
          validation={{ isShowError: false, error: "" }}
          placeholder="Enter new password"
          helperText="Leave empty to keep current password"
        />
        <InputField
          label="Confirm Password"
          type="password"
          name="confirmPassword"
          fullWidth
          value={inputs.confirmPassword}
          onChange={handleInputChange}
          color="primary"
          onFocus={() => {}}
          onBlur={() => {}}
          disabled={false}
          validation={{ isShowError: false, error: "" }}
          placeholder="Confirm new password"
          helperText="Leave empty to keep current password"
        />
        <Btn text="Update Password" onClick={handlePasswordSubmit} fullWidth />
      </Box>
    </Box>
  );
};

export default Profile;
