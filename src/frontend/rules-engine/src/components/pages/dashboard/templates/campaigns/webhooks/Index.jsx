import { Box } from "@mui/material";
import React, { useEffect, useState } from "react";
import WebhookOrganizers from "../../../organisms/campaigns/webhooks/Index";
import { Typo<PERSON><PERSON><PERSON> } from "../../../../../../components/global/atoms/typography/Typography";
import {
  Btn,
  Btn as BtnAtom,
} from "../../../../../../components/global/atoms/buttons/Button";
import ReusableSectionHOC from "../../../../../../components/pages/dashboard/atoms/global/reusableSectionHOC";
import SectionHeaderMolecule from "../../../../../../components/pages/dashboard/molecules/campaigns/create/SubSectionHeader";
import { collectionMappingTabData } from "../../../../../../utils/dummyData/Campaigns";
import { Helpers, Alert, Data } from "../../../../../../utils";
import WebhookService from "../../../../../../Api/Webhook";
import LoadingComponent from "../../../../../../components/global/molecules/LoadingComponent";
import { registerConfirmation } from "../../../../../../components/global/atoms/dialog/Index";
import { openDialog } from "../../../../../../redux/slices/global/confirmation";
import { useDispatch } from "react-redux";
let { inputKeys, sectionHeaders } = Data.services.webhook;
let { detailsKeys } = inputKeys;
let { detailsColumns } = sectionHeaders;

const WebhooksTemplate = () => {
  /**
   * State to handle the webhooks.
   *
   * @type {[any[], React.Dispatch<React.SetStateAction<any[]>>]}
   */
  let [webhooks, setWebhooks] = useState([]);
  /**
   * State to handle the loading of the webhooks.
   *
   * @type {[boolean, React.Dispatch<React.SetStateAction<boolean>>]}
   */
  const [isLoading, setIsLoading] = useState(true);
  const [refresh, setRefresh] = useState(true);
  const [queryParams, setQueryParams] = useState({
    page: 0,
    pageSize: 20,
    securityLevel: "",
  });
  /**
   * Handles the addition of a new webhook.
   *
   * This function checks if there is an incomplete webhook in the webhooks array.
   * If an incomplete webhook exists, it returns an error alert.
   *
   * @returns {void}
   */
  const handleAddNewWebhook = () => {
    let isIncompleteWebhook = webhooks.find((item) => {
      return item.id === "newWebhook";
    });

    if (isIncompleteWebhook)
      return Alert("Please fill the incomplete webhook", "error");


    const newItem = {
      id: "newWebhook",
      mode: "editing", // ["viewing", "editing"]
      columns: [{ content: "Details", gridSize: 5 }],
      isBtnDisabled: false,
      validations: {},
      data: {
        name: "",
        description: "",
        url: "",
        method: "POST",
        headers: [],
        parameters: [],
        bodyTemplate: {},
        timeout: 5000,
        retryPolicy: {
          maxRetries: 3,
          initialDelaySeconds: 30,
          backoffMultiplier: 2.1,
        },
        securityLevel: "SYSTEM",
      },
    };

    setWebhooks([...webhooks, newItem]);
  };
  /**
   * Sets webhook to edit mode
   * @param {string} id - Webhook ID
   */
  const handleEditItem = async (id) => {
    try {
      const updatedData = webhooks.map((item) => {
        if (item.id === id) {
          return {
            ...item,
            mode: "editing",
            columns: [{ content: "Details", gridSize: 6 }],
          };
        }
        return item;
      });
      setWebhooks(updatedData);
    } catch (error) {}
  };
  /**
   * Handles the deletion of a webhook.
   *
   * This function removes the webhook with the given id from the webhooks array.
   * It iterates through the webhooks array and removes the webhook with the given id
   * from the array. The updated state is then saved to the webhooks array.
   *
   * @param {string} id The id of the webhook to be deleted.
   */
  const handleDeleteItem = async (id) => {
    try {
      let res = await WebhookService.deleteWebhook(id);

      if (!res.success) {
        Alert(res.data, "error");
        return;
      }
      // /** Check the variable if it is used in the entities or not */

      // Filter out the local variable with the given id from the state
      const updatedData = webhooks.filter((item) => item.id !== id);

      // Update the variableDefinitions state with the new array
      setWebhooks(updatedData);
      Alert("Webhook deleted successfully", "success");
    } catch (error) {
      Alert(error.data, "error");
    }
  };
  /**
   * This function handles the cancel action for a specific item.
   * It updates the mode of the item with the given id to "viewing".
   * It iterates through the webhooks array and updates the mode of the item with the given id
   * to "viewing". The updated state is then saved to the webhooks array.
   *
   * @param {string} id The id of the item to be cancelled.
   */
  const handleCancel = (id) => {
    // Map over the variableDefinitions array and update the mode of the specific item to "viewing"
    let updatedData = webhooks.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          mode: "viewing",
          columns: [
            {
              content: id === "newWebhook" ? "Create Webhook" : item.data.name,
              gridSize: 4,
            },
            {
              content: id === "newWebhook" ? "---" : item.data.description,
              gridSize: 5,
            },
          ],
        };
      }
      return item;
    });
    // Update the variableDefinitions state with the modified data
    setWebhooks(updatedData);
  };
  /**
   * Saves a webhook with the given id and inputs
   * @param {string} id - Webhook id
   * @param {object} inputs - Webhook input values
   */
  const handleSave = async (id, inputs) => {

   

    setWebhooks((prev)=>prev.map((item)=>item.id===id?{
      ...item,
      id: inputs.webhookId,
      columns: [{ content: inputs.name, gridSize: 4 },{content:inputs.description,gridSize:5}],
      mode: "viewing",
      data: {
        ...inputs,
        webhookId: inputs.webhookId,
        bodyTemplate: inputs.bodyTemplate,
      },
    }:item))

   
  }; 

  useEffect(() => {
    /**
     * Fetches the webhooks from the server and updates the webhooks state.
     *
     * This function sends a GET request to the server to retrieve all webhooks.
     * If the request is successful, it updates the webhooks state with the retrieved data.
     * If the request fails, it displays an error alert.
     */
    const getWebhooks = async () => {
      try {
        let res = await WebhookService.getWebhooks({...queryParams});

        if (!res.success) {
          Alert(res.data, "error");
          return;
        }

        let receivedWebhooks = res.data.webhooks.map((item) => {
         
          return {
            id: item.webhookId,
            mode: "viewing", // ["viewing", "editing"]
            columns: [{ content: item.name, gridSize: 4 },{content:item.description,gridSize:5}],
            data: {
              ...item,
            },
          };
        });

        setWebhooks(receivedWebhooks);
      } catch (error) {
        console.log(error);
      } finally {
        setIsLoading(false);
      }
    };
    if (refresh) {
      getWebhooks();
      setRefresh(false);
    }
  }, [refresh]);

  if (isLoading) {
    return (
      <LoadingComponent
        fullPage={true}
        type="circular"
        message="Loading webhooks..."
        size={100}
      />
    );
  }

  return (
    <Box>
      <Box
        sx={{
          mt: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <TypographyAtom text="Webhooks" variant="h6" sx={{}} />
        <BtnAtom
          onClick={handleAddNewWebhook}
          sx={{ backgroundColor: "primary.secondary" }}
          text="Add"
        />
      </Box>

      <Box sx={{ mt: 2, width: "100%" }}>
        <SectionHeaderMolecule sx={{}} columns={detailsColumns} />
        {/*  Here we are mapping over the webhooks array to render the sub sections of the  webhook */}
        {webhooks.length > 0 &&
          webhooks.map((tab, index) => (
            <ReusableSectionHOC
              key={tab.id + index}
              index={index}
              tab={tab}
              columns={tab.columns}
              rows={[]}
              onEdit={() => handleEditItem(tab.id)}
              onDelete={() => handleDeleteItem(tab.id)}
              sxContainer={{ backgroundColor: "bgSecondary.main" }}
              sxHeaderRow={{ backgroundColor: "bgSecondary.main" }}
              content={
                <WebhookOrganizers
                  handleCancel={handleCancel}
                  handleSave={handleSave}
                  key={index}
                  tab={{ ...tab, webhooks: webhooks, setRefresh: setRefresh }}
                />
              }
            />
          ))}
        {/* If the webhooks array is empty, we display a message */}
        {webhooks.length < 1 && (
          <Box sx={{ mt: 2 }}>
            <TypographyAtom
              sx={{ textAlign: "center", color: "textColor.secondary" }}
              text="No webhooks found"
              variant="h6"
            />
          </Box>
        )}

        {/* <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
            <Btn
              text="Reset"
              sx={{
                width: "100%",
                maxWidth: "200px",
                backgroundColor: "danger.main",
              }}
              onClick={handleResetWebhooks}
            />
            <Btn
              text="Save"
              sx={{
                width: "100%",
                maxWidth: "200px",
                backgroundColor: "primary.secondary",
              }}
              onClick={handleUpdateWebhooks}
            />
          </Box> */}
      </Box>
    </Box>
  );
};

export default WebhooksTemplate;
