import { Box } from "@mui/material";
import DropdownAtom from "../../../../../../global/atoms/dropdown/Dropdown";
import { Typo<PERSON><PERSON><PERSON> } from "../../../../../../global/atoms/typography/Typography";
import { Btn as Btn<PERSON>tom } from "../../../../../../global/atoms/buttons/Button";
import { useId, useState } from "react";
import SectionHeaderMolecule from "../../../../molecules/campaigns/create/SubSectionHeader";
import SectionTabHOC from "../../../../molecules/campaigns/create/SectionTabHOC";
import PropTypes from "prop-types";
import {
  collectionDropdown,
  collectionMappingTabData,
} from "../../../../../../../utils/dummyData/Campaigns";
import SubSectionHOC from "../../../../molecules/campaigns/create/SubSectionHOC";
import SubSectionRowMolecule from "../../../../molecules/campaigns/create/SubSectionRow";
import KeyMappingContentOrganizer from "./KeysMappingContent";
import { useDispatch, useSelector } from "react-redux";
import { Alert, campaignValidators, Helpers } from "../../../../../../../utils";
import { setCampaign } from "../../../../../../../redux/slices/tabs/campaigns/campaign";
import { InputField } from "../../../../../../global/atoms/inputFields/InputField";
import { useEffect } from "react";
import ReusableSectionHOC from "../../../../atoms/global/reusableSectionHOC";
import React from "react";

/**
 * @typedef {import('../../../../../../../../jsDocs/components/dashboard').CollectionMappingInputData} InputsState
 * * @typedef {import('../../../../../../../../jsDocs/components/dashboard').InputField} InputField
 */
const keys = ["entityId", "entityName", "propertyName"];
/**
 * This component renders the collection mapping part of the campaign that is creating or editing.
 * It displays a dropdown for selecting a collection, an input field for entering a collection name, and a button for adding a new key mapping.
 * The component also renders a table with columns for the key mappings and a button for deleting a key mapping.
 * The component also renders a button for saving the collection mapping.
 * The component uses the useSelector and useDispatch hooks from react-redux to get the campaign state and dispatch actions to update the campaign state.
 * @param {object} props The props for this component.
 * @param {string} props.handleSaveCollMapping The function that is called when the user clicks on the "Save" button for the collection mapping.
 * @param {object} props.tab The tab object that contains the data for the collection mapping.
 * @param {function} props.handleCancel The function that is called when the user clicks on the "Cancel" button for the collection mapping.
 * @returns {React.ReactElement} The component that renders the collection mapping part of the campaign.
 */
const ColMappingSection = ({ handleSaveCollMapping, tab, handleCancel }) => {
  const { campaign } = useSelector((state) => state.campaign);

  /** @type {[InputsState[],  React.Dispatch<React.SetStateAction<InputsState>>]} */
  const [keyMapping, setkeyMapping] = useState(null);
  /** @type {[InputField,  React.Dispatch<React.SetStateAction<InputField>>]} */
  const [collectionName, setCollectionName] = useState({
    value: tab.name ? tab.name : "",
    isValid: tab.name ? true : false,
    error: tab.name ? "" : "Collection Name is required",
    isShowError: false,
  });

  const columnsOfSectionHeader = [
    { label: "Property Name", gridSize: 10, sx: {} }, // 6 out of 12 columns
    { label: "Actions", gridSize: 2, sx: {} }, // 6 out of 12 columns
  ];

  const handleEditBtnClick = () => {
    setkeyMapping({
      ...keyMapping,
      mode: "editing",
    });
  };
  const handleSubmit = () => {
    if (!collectionName.isValid) {
      return Alert("Please enter collection name", "error");
    }

    // Validate keyMapping
    if (
      !keyMapping ||
      !keyMapping.inputs ||
      !keyMapping.inputs.propertyId ||
      keyMapping.propertyId === ""
    ) {
      return Alert("Please select a property", "error");
    }

    let obj = {
      name: collectionName.value.trim(),
      keyMapping: { propertyId: keyMapping.inputs.propertyId.value },
    };
    handleSaveCollMapping(tab.collectionId, obj);
  };

  const handleInputChange = (e, status) => {
    let { value } = e.target;
    setCollectionName({
      value,
      isShowError: value ? !status.success : false,
      error: status.message,
      isValid: status.success,
    });
  };

  useEffect(() => {
    const updateStateHandler = () => {
      if (tab.keyMapping?.propertyId) {
        setkeyMapping({
          propertyId: tab.keyMapping.propertyId,
          mode: "editing",
          columns: [{ content: tab.keyMapping.propertyId, gridSize: 6 }],
          isEditable: true,
          isDeletable: false,
          isAllFieldsValid: false,
          validations: {},
          inputs: {
            propertyId: {
              value: tab.keyMapping.propertyId,
              isValid: true,
              isShowError: false,
              error: "",
            },
          },
        });
      } else {
        // const id = Math.random();
        setkeyMapping({
          propertyId: "",
          mode: "editing",
          columns: [{ content: "Create Key Mapping", gridSize: 6 }],
          isEditable: true,
          isDeletable: false,
          isAllFieldsValid: false,
          validations: {},
          inputs: {
            propertyId: {
              value: "",
              isValid: true,
              isShowError: false,
              error: "",
            },
          },
        });
      }
    };
    updateStateHandler();
  }, [tab.keyMapping]);

  return (
    <Box sx={{ pb: 2 }}>
      <Box>
        <InputField
          label="Collection Name"
          name={"collectionName"}
          value={collectionName.value}
          validateOn="change"
          validator={campaignValidators.colMapping.collectionName}
          onChange={handleInputChange}
          placeholder="Enter Collection Name"
          sx={{
            width: { sm: "50%", md: "40%", xs: "100%" },
            backgroundColor: "bgPrimary.main",
          }}
          validation={{
            isShowError: collectionName.isShowError,
            error: collectionName.error,
          }}
        />
      </Box>

      <Box
        sx={{
          mt: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <TypographyAtom text="Key Mappings" variant="h6" sx={{}} />
        {/* <BtnAtom
          onClick={handleAddNewItem}
          sx={{ backgroundColor: "primary.secondary" }}
          text="Add"
        /> */}
      </Box>

      <Box sx={{ mt: 2, width: "100%" }}>
        <SectionHeaderMolecule sx={{}} columns={columnsOfSectionHeader} />
        {keyMapping ? ( // Changed from keyMapping.map
          <ReusableSectionHOC
            key={keyMapping.propertyId}
            index={0}
            tab={keyMapping}
            columns={keyMapping.columns}
            sxContainer={{ backgroundColor: "bgPrimary.main" }}
            rows={keyMapping.rows}
            onEdit={() => handleEditBtnClick()}
            content={
              <KeyMappingContentOrganizer
                handleCancel={handleCancel}
                tab={keyMapping}
                setTab={setkeyMapping}
              />
            }
          />
        ) : (
          <Box sx={{ mt: 2 }}>
            <TypographyAtom
              sx={{ textAlign: "center", color: "textColor.secondary" }}
              text="No key mapping added yet."
              variant="h6"
            />
          </Box>
        )}
      </Box>

      <Box
        item
        size={{ xs: 12, sm: 12, md: 12 }}
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "flex-end",
          gap: 2,
        }}
      >
        <BtnAtom
          sx={{ backgroundColor: "danger.main" }}
          onClick={() => handleCancel(tab.id)}
          text="Cancel"
        />{" "}
        {
          // keyMapping.length > 0 && (
          //   <BtnAtom
          //     onClick={handleSubmit}
          //     sx={{ backgroundColor: "primary.secondary" }}
          //     text="Save"
          //   />
          // )
          keyMapping && ( // Changed from keyMapping.length > 0
            <BtnAtom
              onClick={handleSubmit}
              sx={{ backgroundColor: "primary.secondary" }}
              text="Save"
            />
          )
        }{" "}
      </Box>
    </Box>
  );
};

export default ColMappingSection;
