// VariablesSection.jsx
import {
    Box,
    Card,
    Typography,
    Grid,
    FormControl,
    Select,
    MenuItem,
    InputLabel
} from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import BarChartIcon from '@mui/icons-material/BarChart';
import ShowChartIcon from '@mui/icons-material/ShowChart';
import { TypographyAtom } from '../../../../../global/atoms/typography/Typography';
import { Helpers } from '../../../../../../utils/generalFunctions/index';

/**
 * VariablesSection Component
 * 
 * Displays variable summary cards and chart type selector
 * 
 * @component
 * @param {Object} props - Component props
 * @param {Object} props.aggregationData - Aggregation data with variables
 * @param {Set} props.selectedVariables - Set of selected variable IDs
 * @param {Object} props.chartData - Chart data with datasets
 * @param {Array} props.colorPalette - Color palette for variables
 * @param {Object} props.filters - Current filters
 * @param {string} props.chartType - Current chart type ('line' or 'bar')
 * @param {Function} props.onChartTypeChange - Chart type change handler
 * @returns {JSX.Element} The rendered variables section component
 */
const ChartVariableSection = ({
    aggregationData,
    selectedVariables,
    chartData,
    colorPalette,
    filters,
    chartType,
    onChartTypeChange
}) => {
    if (!aggregationData?.variables) {
        return null;
    }

    const shouldShowChartTypeSelector = filters &&
        Object.keys(filters).length > 0 &&
        filters.time_bucket !== null &&
        filters.time_bucket !== 0;

    return (
        <Card sx={{ mb: 3, p: 2 }}>
            {/* Header with Chart Type Selection */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Box sx={{ flex: 1, display: 'flex', justifyContent: 'center' }}>
                    <TypographyAtom
                        variant="h4"
                        text="📈 Variables"
                        sx={{ fontWeight: 'medium' }}
                    />
                </Box>

                {/* Chart Type Selector - Only show when filters meet criteria */}
                {shouldShowChartTypeSelector && (
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                        <FormControl size="small" sx={{ minWidth: 140 }}>
                            <InputLabel>Chart Type</InputLabel>
                            <Select
                                value={chartType}
                                label="Chart Type"
                                onChange={(e) => onChartTypeChange(e.target.value)}
                                sx={{
                                    '& .MuiSelect-select': {
                                        display: 'flex',
                                        alignItems: 'center',
                                        gap: 1
                                    }
                                }}
                            >
                                <MenuItem value="line" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <ShowChartIcon fontSize="small" />
                                    Line Chart
                                </MenuItem>
                                <MenuItem value="bar" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                    <BarChartIcon fontSize="small" />
                                    Bar Chart
                                </MenuItem>
                            </Select>
                        </FormControl>
                    </Box>
                )}
            </Box>

            {/* Aggregation Summary Cards */}
            <Grid container spacing={2} sx={{ mb: 3, justifyContent: 'center', alignItems: 'center' }}>
                {aggregationData.variables
                    .filter(variable => selectedVariables.has(variable.variable_id))
                    .map((variable) => {
                        // Find the matching dataset index using the exact same label format as chart datasets
                        // Chart datasets use variable.variable_name (which is already processed by getVariableName)
                        const targetLabel = variable.variable_name || variable.variable_id;
                        const datasetIndex = chartData?.datasets?.findIndex(
                            dataset => dataset.label === targetLabel
                        ) ?? 0;

                        // Fallback: If exact match fails, try to find by variable_id match
                        const fallbackIndex = datasetIndex === -1
                            ? chartData?.datasets?.findIndex(dataset =>
                                dataset.label.toLowerCase() === (variable.variable_name || variable.variable_id).toLowerCase()
                            ) ?? 0
                            : datasetIndex;

                        const finalIndex = fallbackIndex === -1 ? 0 : fallbackIndex;

                        return (
                            <Grid item xs={12} sm={6} md={3} key={variable.variable_id}>
                                <Card sx={{
                                    p: 2,
                                    borderTop: `4px solid ${colorPalette[finalIndex % colorPalette.length]}`,
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    width: '100%',
                                    gap: 1
                                }}>
                                    <Box sx={{
                                        display: 'flex',
                                        justifyContent: 'space-between',
                                        alignItems: 'center',
                                    }}>
                                        <Typography variant="body1" sx={{
                                            color: 'primary.main',
                                            fontWeight: 'bold',
                                            flex: 1
                                        }}>
                                            {Helpers.capitalizeFirstLetter(variable.variable_name)}
                                        </Typography>
                                        <Box sx={{
                                            color: 'primary.main',
                                            display: 'flex',
                                            alignItems: 'center',
                                        }}>
                                            {finalIndex % 2 == 0 ? (
                                                <TrendingUpIcon sx={{ fontSize: '20px' }} />
                                            ) : (
                                                <BarChartIcon sx={{ fontSize: '20px' }} />
                                            )}
                                        </Box>
                                    </Box>
                                    <Typography variant="body1" sx={{ color: 'text.secondary' }}>
                                        Total: <Typography component="span" variant="h2" sx={{ fontWeight: 'bold', color: 'text.secondary' }}>
                                            {parseFloat(variable.totalValue).toLocaleString()}
                                        </Typography>
                                    </Typography>
                                    <Typography variant="body2" sx={{ color: 'text.secondary', fontSize: '12px' }}>
                                        Total Record {variable.record_count}
                                    </Typography>
                                </Card>
                            </Grid>
                        );
                    })}
            </Grid>
        </Card>
    );
};

export default ChartVariableSection;