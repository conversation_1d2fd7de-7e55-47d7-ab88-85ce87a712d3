import React from "react";
import {
    Box,
    TextField,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Checkbox,
    ListItemText,
    OutlinedInput,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    IconButton
} from "@mui/material";
import { Typo<PERSON><PERSON><PERSON> } from "../../../../../global/atoms/typography/Typography";
import { useSelector } from "react-redux";
import { useState, useMemo } from "react";
import { DropdownAtom } from "../../../../../global/atoms/dropdown/Dropdown";
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import FilterListIcon from '@mui/icons-material/FilterList';
import CloseIcon from '@mui/icons-material/Close';
import { Btn as Btn<PERSON>tom } from "../../../../../global/atoms/buttons/Button";
import { Helpers } from "../../../../../../utils";
import { Alert } from "../../../../../../utils/index";

/**
 * The AnalyticsFilter component displays analytics filter form for campaigns.
 * It allows users to select collections, enter property values, select variables, and dates.
 *
 * @component
 * @param {Object} props - Component props
 * @param {Function} props.onFilterSubmit - Callback function when filters are submitted
 * @param {Function} props.onFilterReset - Callback function when filters are reset
 * @example
 * return <AnalyticsFilter onFilterSubmit={handleSubmit} onFilterReset={handleReset} />;
 *
 * @returns {JSX.Element} The JSX element representing the AnalyticsFilter component.
 */
const AnalyticsFilter = ({ onFilterSubmit, onFilterReset }) => {

    // Get analytics data including campaign data from Redux store
    const { analyticsData } = useSelector((state) => state.campaigns);
    const { campaignData } = analyticsData;
    const { receivedData } = useSelector((state) => state.campaign);
    let { entities } = receivedData;

    // State for form fields
    const [selectedCollection, setSelectedCollection] = useState(null);
    const [propertyValue, setPropertyValue] = useState("");
    const [selectedVariables, setSelectedVariables] = useState([]);
    const [selectedRuleIds, setSelectedRuleIds] = useState([]);
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);
    const [selectedTimeBucket, setSelectedTimeBucket] = useState(null);
    const [isModalOpen, setIsModalOpen] = useState(false);

    /**
     * Prepare collection options for dropdown
     */
    const collectionOptions = useMemo(() => {
        if (!campaignData?.collectionMappings) return [];

        return campaignData.collectionMappings.map(collection => ({
            label: Helpers.capitalizeFirstLetter(collection.name),
            value: collection.collectionId
        }));
    }, [campaignData]);

    /**
     * Time bucket options for analytics aggregation
     */
    const timeBucketOptions = [
        { label: "Raw Data", value: 0 },
        { label: "5 Minutes", value: 5 },
        { label: "10 Minutes", value: 10 },
        { label: "1 Hour", value: 60 },
        { label: "6 Hours", value: 360 },
        { label: "12 Hours", value: 720 }
    ];

    /**
     * Get the selected collection data
     */
    const selectedCollectionData = useMemo(() => {
        if (!selectedCollection || !campaignData?.collectionMappings) return null;

        return campaignData.collectionMappings.find(
            collection => collection.collectionId === selectedCollection
        );
    }, [selectedCollection, campaignData]);

    /**
     * Prepare persistent variables options based on selected collection
     * If no collection is selected, show all persistent variables of the campaign
     * If collection is selected, show only variables related to that collection
     */
    const persistentVariablesOptions = useMemo(() => {
        if (!campaignData?.persistentVariables) return [];

        let filteredVariables = campaignData.persistentVariables;

        // If a collection is selected, filter variables by that collection
        if (selectedCollection) {
            filteredVariables = campaignData.persistentVariables.filter(
                variable => variable.collectionId === selectedCollection
            );
        }
        // If no collection is selected, show all persistent variables

        return filteredVariables.map(variable => ({
            label: Helpers.capitalizeFirstLetter(variable.name),
            value: variable.variableId
        }));
    }, [selectedCollection, campaignData]);

    /**
     * Prepare rule options from both evaluation and outcome rules
     */
    const ruleOptions = useMemo(() => {
        if (!campaignData) return [];

        const evaluationOptions = (campaignData.evaluationRules || []).map(rule => ({
            label: Helpers.capitalizeFirstLetter(rule.name) + " (Evaluation)",
            value: rule.ruleId,
            type: 'Evaluation'
        }));

        const outcomeOptions = (campaignData.outcomeRules || []).map(rule => ({
            label: Helpers.capitalizeFirstLetter(rule.name) + " (Outcome)",
            value: rule.ruleId,
            type: 'Outcome'
        }));

        return [...evaluationOptions, ...outcomeOptions];
    }, [campaignData]);

    /**
     * Handle collection selection
     */
    const handleCollectionChange = (name, value) => {
        setSelectedCollection(value);
        setPropertyValue(""); // Reset property value when collection changes
        setSelectedVariables([]); // Reset selected variables
    };

    /**
     * Handle time bucket selection
     */
    const handleTimeBucketChange = (name, value) => {
        setSelectedTimeBucket(value);
    };

    /**
     * Handle property value input change
     */
    const handlePropertyValueChange = (event) => {
        setPropertyValue(event.target.value);
    };

    /**
     * Handle modal open
     */
    const handleOpenModal = () => {
        setIsModalOpen(true);
    };

    /**
     * Handle modal close
     */
    const handleCloseModal = () => {
        setIsModalOpen(false);
    };

    /**
     * Handle form submission
     */
    const handleSubmit = () => {
        if (!hasAnyFilter) {
            Alert("Please select at least one filter to generate analytics", "error");
            return;
        }

        if (Object.keys(validationErrors).length > 0) {
            Alert("Please fix the validation errors", "error");
            return;
        }

        const formData = {
            // Campaign context (always included)
            entity_id: campaignData?.entityId,
            context_id: campaignData?.contextId,
            rule_set_id: campaignData?.rulesetId,
            rule_set_version: campaignData?.version,
            // User-selected filters (optional)
            collection_id: selectedCollection || null,
            collection_key: selectedCollectionData?.keyMapping?.propertyId || null,
            collection_value: propertyValue || null,
            variable_ids: selectedVariables.length > 0 ? selectedVariables : null,
            initiating_rule_ids: selectedRuleIds.length > 0 ? selectedRuleIds : null,
            start_date: startDate ? `${startDate.format('YYYY-MM-DD')}T12:00:00.000Z` : null,
            end_date: endDate ? `${endDate.format('YYYY-MM-DD')}T12:00:00.000Z` : null,
            time_bucket: selectedTimeBucket !== null ? selectedTimeBucket : null,
        };

        // Call the parent component's callback with the filter data
        if (onFilterSubmit) {
            onFilterSubmit(formData);
        }
        // Close the modal after successful submission
        setIsModalOpen(false);
    };

    /**
     * Handle filter reset - clears all form fields
     */
    const handleReset = () => {
        // Reset all form state
        setSelectedCollection(null);
        setPropertyValue("");
        setSelectedVariables([]);
        setSelectedRuleIds([]);
        setStartDate(null);
        setEndDate(null);
        setSelectedTimeBucket(null);

        // Call the parent component's reset callback
        if (onFilterReset) {
            onFilterReset();
        }
        setIsModalOpen(false);
        Alert("All filters have been reset", "info");
    };

    /**
     * Validation for filter form - only validates logical errors, not required fields
     */
    const validationErrors = useMemo(() => {
        const errors = {};

        // Only validate logical errors, not required field errors
        if (startDate && endDate && startDate.isAfter(endDate)) {
            errors.dateRange = 'Start date must be before end date';
        }

        return errors;
    }, [selectedCollection, propertyValue, startDate, endDate]);

    /**
     * Check if at least one filter is applied for submission
     */
    const hasAnyFilter = useMemo(() => {
        return (
            selectedCollection ||
            propertyValue.trim() ||
            selectedVariables.length > 0 ||
            selectedRuleIds.length > 0 ||
            startDate ||
            endDate ||
            selectedTimeBucket !== null
        );
    }, [selectedCollection, propertyValue, selectedVariables, selectedRuleIds, startDate, endDate, selectedTimeBucket]);

    // get the property label name
    const generatePropertyName = (propertyId) => {
        if (!campaignData.entityId || !campaignData.contextId) {
            return [];
        }
        let entity = entities.find((entity) => entity.entityId === campaignData.entityId);
        let context = entity.transactionContexts.find(
            (context) => context.contextId === campaignData.contextId
        );
        let propertiesOptions = context.properties.map((property) => {
            return {
                value: property.propertyId,
                label: property.name,
            };
        });
        let propertyLabelName = propertiesOptions.find((property) => property.value === propertyId);

        return propertyLabelName?.label || propertyId
    };


    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            {/* Filter Button - Top Right */}
            <Box sx={{
                display: 'flex',
                justifyContent: 'flex-end',
                mb: 2,
                position: 'relative',
                zIndex: 1
            }}>
                <BtnAtom
                    onClick={handleOpenModal}
                    text={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <FilterListIcon />
                            Filter Results
                        </Box>
                    }
                    startIcon={<FilterListIcon />}
                    sx={{
                        backgroundColor: "primary.main",
                        borderRadius: 2,
                        px: 3,
                        py: 1.5,
                        fontSize: '0.95rem',
                        fontWeight: 'medium',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                        '&:hover': {
                            backgroundColor: 'primary.dark',
                            transform: 'translateY(-2px)',
                            boxShadow: '0 4px 12px rgba(0,0,0,0.2)'
                        },
                        transition: 'all 0.3s ease'
                    }}
                />
            </Box>

            {/* Filter Modal */}
            <Dialog
                open={isModalOpen}
                onClose={handleCloseModal}
                maxWidth="sm"
                fullWidth
                sx={{
                    '& .MuiDialog-paper': {
                        borderRadius: 3,
                        minHeight: '400px'
                    }
                }}
            >
                <DialogTitle sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    backgroundColor: 'primary.main',
                    color: 'white',
                    py: 2
                }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <FilterListIcon sx={{ color: '#ffffff' }} />
                        <TypographyAtom
                            variant="h6"
                            text="Analytics Filters"
                            sx={{ color: '#ffffff', fontWeight: 'bold' }}
                        />
                    </Box>
                    <IconButton
                        onClick={handleCloseModal}
                        sx={{ color: '#ffffff' }}
                    >
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>

                <DialogContent sx={{ p: 4 }}>
                    {/* Collection Selection */}
                    <Box sx={{ mb: 3 }}>
                        <TypographyAtom
                            variant="subtitle1"
                            text="Select Collection"
                            sx={{ mb: 1, fontWeight: 'medium' }}
                        />
                        <DropdownAtom
                            options={collectionOptions}
                            selectedItem={selectedCollection}
                            onChange={handleCollectionChange}
                            label="Choose a collection"
                            name="collection"
                            validation={{ isShowError: false, error: "" }}
                        />
                    </Box>

                    {/* Property Value Input - Show only when collection is selected */}
                    {selectedCollection && selectedCollectionData && (
                        <Box sx={{ mb: 3 }}>
                            <TextField
                                fullWidth
                                label={`Please enter '${generatePropertyName(selectedCollectionData.keyMapping?.propertyId)}' value`}
                                value={propertyValue}
                                onChange={handlePropertyValueChange}
                                variant="outlined"
                                placeholder={`Enter ${generatePropertyName(selectedCollectionData.keyMapping?.propertyId)} value`}
                            />
                        </Box>
                    )}

                    {/* Persistent Variables Selection */}
                    {persistentVariablesOptions.length > 0 && (
                        <Box sx={{ mb: 3 }}>
                            <TypographyAtom
                                variant="subtitle1"
                                text="Select Persistent Variables"
                                sx={{ mb: 1, fontWeight: 'medium' }}
                            />
                            <FormControl fullWidth>
                                <InputLabel>Choose persistent variables</InputLabel>
                                <Select
                                    multiple
                                    value={selectedVariables}
                                    onChange={(event) => setSelectedVariables(event.target.value)}
                                    input={<OutlinedInput label="Choose Persistent Variables" />}
                                    renderValue={(selected) => {
                                        const selectedNames = selected.map(variableId => {
                                            const variable = persistentVariablesOptions.find(opt => opt.value === variableId);
                                            return variable.label;
                                        });
                                        return `[${selectedNames.join(', ')}]`;
                                    }}
                                >
                                    {persistentVariablesOptions.map((option) => (
                                        <MenuItem key={option.value} value={option.value}>
                                            <Checkbox checked={selectedVariables.indexOf(option.value) > -1} />
                                            <ListItemText primary={option.label} />
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Box>
                    )}

                    {/* Initiating Rule Ids Selection */}
                    {ruleOptions.length > 0 && (
                        <Box sx={{ mb: 3 }}>
                            <TypographyAtom
                                variant="subtitle1"
                                text="Select Initiating Rule Ids"
                                sx={{ mb: 1, fontWeight: 'medium' }}
                            />
                            <FormControl fullWidth>
                                <InputLabel>Choose rule ids</InputLabel>
                                <Select
                                    multiple
                                    value={selectedRuleIds}
                                    onChange={(event) => setSelectedRuleIds(event.target.value)}
                                    input={<OutlinedInput label="Choose Rule Ids" />}
                                    renderValue={(selected) => {
                                        const selectedNames = selected.map(ruleId => {
                                            const rule = ruleOptions.find(opt => opt.value === ruleId);
                                            return rule ? `${rule.label}` : 'Unknown';
                                        });
                                        return `[${selectedNames.join(', ')}]`;
                                    }}
                                >
                                    {ruleOptions.map((option) => (
                                        <MenuItem key={option.value} value={option.value}>
                                            <Checkbox checked={selectedRuleIds.indexOf(option.value) > -1} />
                                            <ListItemText primary={option.label} />
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Box>
                    )}
                    {/* Time Bucket Selection */}
                    <Box sx={{ mb: 3 }}>
                        <TypographyAtom
                            variant="subtitle1"
                            text="Select Time Bucket"
                            sx={{ mb: 1, fontWeight: 'medium' }}
                        />
                        <DropdownAtom
                            options={timeBucketOptions}
                            selectedItem={selectedTimeBucket}
                            onChange={handleTimeBucketChange}
                            label="Choose time bucket for data aggregation"
                            name="timeBucket"
                            validation={{ isShowError: false, error: "" }}
                        />
                    </Box>

                    {/* Date Selection */}
                    <Box sx={{ mb: 3 }}>
                        <TypographyAtom
                            variant="subtitle1"
                            text="Select Date Range"
                            sx={{ mb: 2, fontWeight: 'medium' }}
                        />

                        <Box sx={{ display: 'flex', gap: 2, flexDirection: { xs: 'column', sm: 'row' } }}>
                            <DatePicker
                                label="Start Date"
                                value={startDate}
                                onChange={(newValue) => setStartDate(newValue)}
                                slotProps={{
                                    textField: {
                                        fullWidth: true,
                                        error: !!validationErrors.dateRange,
                                        helperText: validationErrors.dateRange || ''
                                    }
                                }}
                            />

                            <DatePicker
                                label="End Date"
                                value={endDate}
                                onChange={(newValue) => setEndDate(newValue)}
                                slotProps={{
                                    textField: {
                                        fullWidth: true,
                                        error: !!validationErrors.dateRange,
                                        helperText: validationErrors.dateRange || ''
                                    }
                                }}
                                minDate={startDate}
                            />
                        </Box>
                    </Box>



                    {/* Filter Status Information */}
                    {!hasAnyFilter && (
                        <Box sx={{ mb: 3, p: 2, backgroundColor: 'info.light', borderRadius: 1, textAlign: 'center' }}>
                            <TypographyAtom
                                variant="body2"
                                text="💡 Select at least one filter option"
                                sx={{ color: 'info.contrastText', fontStyle: 'italic' }}
                            />
                        </Box>
                    )}

                    {/* Show validation errors if any */}
                    {Object.keys(validationErrors).length > 0 && (
                        <Box sx={{ mb: 3, p: 2, backgroundColor: 'warning.light', borderRadius: 1 }}>
                            <TypographyAtom
                                variant="body2"
                                text="⚠️ Please fix the following:"
                                sx={{ color: 'warning.contrastText', fontWeight: 'medium', mb: 1 }}
                            />
                            <Box component="ul" sx={{ margin: 0, paddingLeft: 2 }}>
                                {Object.values(validationErrors).map((error, index) => (
                                    <Box key={index} component="li" sx={{ color: 'warning.contrastText', fontSize: '0.875rem' }}>
                                        {error}
                                    </Box>
                                ))}
                            </Box>
                        </Box>
                    )}
                </DialogContent>

                <DialogActions sx={{
                    p: 2,
                    borderTop: '1px solid',
                    borderColor: 'divider',
                    justifyContent: 'center',
                    gap: 2
                }}>
                    <BtnAtom
                        onClick={handleSubmit}
                        disabled={!hasAnyFilter}
                        text="Apply Filters"
                        sx={{
                            backgroundColor: "primary.secondary",
                            minWidth: '140px',
                            '&:disabled': {
                                backgroundColor: 'primary.secondary',
                                color: '#ffffff',
                                cursor: 'not-allowed',
                                opacity: 0.8
                            }
                        }}
                    />
                    <BtnAtom
                        onClick={handleReset}
                        // disabled={!hasAnyFilter}
                        text="Reset All"
                        sx={{
                            backgroundColor: "warning.main",
                            minWidth: '140px',
                            '&:disabled': {
                                backgroundColor: 'warning.main',
                                color: '#ffffff',
                                opacity: 0.8,
                                cursor: 'not-allowed'
                            }
                        }}
                    />
                </DialogActions>
            </Dialog>
        </LocalizationProvider>
    );
};

export default AnalyticsFilter;