import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { Box, IconButton, Tooltip } from "@mui/material";
import CategoryDropdown from "../../../../../../../../global/atoms/dropdown/CategoryDropdown";
import { Btn as BtnAtom } from "../../../../../../../../global/atoms/buttons/Button";
import DynamicConditionComp from "./DynamicConditionComp";
import { useSelector } from "react-redux";
import DeleteIcon from "@mui/icons-material/Delete";
import DropdownAtom from "../../../../../../../../global/atoms/dropdown/Dropdown";
/**
 * Component rendered inside the CustomFunctionModal for configuring/selecting a function.
 *
 * @param {Object} props - Component props.
 * @param {Array} props.functionOptions - Array of available function options { value, label, description, parameters }.
 * @param {Object} props.initialFunction - The function object initially selected.
 * @param {string} props.parameterId - The ID of the parameter being configured.
 * @param {Function} props.onSave - Callback function to save the selected function.
 * @param {Function} props.onCancel - Callback function to cancel and close the modal.
 * @returns {React.JSX.Element}
 */
const FunctionsComp = ({
  functionOptions,
  choosenFunction,
  setChoosenFunction,
  parameterId,
  onSave,
  onCancel,
  initialParameterData,
  ruleType,
}) => {
  const { receivedData } = useSelector((state) => state.campaign);
  const { functions } = receivedData;

  // State to hold the parameters' values for the selected function
  const [parametersData, setParametersData] = useState({});
  // flag to determine if we're editing an existing function
  const isEditingExistingFunction = Boolean(
    initialParameterData?.value?.functionId
  );

  // State for validationhe variable operations are ##
  const [validation, setValidation] = useState({
    isValid: true,
    error: "",
    isShowError: false,
  });
  // Initialize the parameters from the existing function data, if available
  useEffect(() => {
    if (choosenFunction?.parameters && initialParameterData?.value?.args) {
      const initialData = {};

      // Map args array to parameter objects
      choosenFunction.parameters.forEach((param, index) => {
        if (index < initialParameterData.value.args.length) {
          initialData[param.parameterId] = {
            value: initialParameterData.value.args[index],
            isValid: true,
            error: "",
            isShowError: false,
          };
        }
      });

      setParametersData(initialData);
    }
  }, [choosenFunction, initialParameterData]);

  /**
   * Formats function options for the CategoryDropdown.
   */
  const getFunctionCategories = () => {
    if (!functionOptions || functionOptions.length === 0) {
      return [];
    }
    return [
      {
        category: "Functions",
        options: functionOptions,
      },
    ];
  };
  /**
   * Handles deleting the function and returning to regular input.
   */
  const handleDeleteFunction = () => {
    onSave(parameterId, ""); // Clear the function data
    onCancel(); // Close the modal
  };
  /**
   * Handles changes in the function selection dropdown.
   */
  const handleFunctionChange = (id, value) => {
    const fullOption = functions?.find((opt) => opt.value === value);
    setChoosenFunction(fullOption);
    // Reset parameters data when function changes
    setParametersData({});
    setValidation({ isValid: true, error: "", isShowError: false });
  };

  /**
   * Handles changes in function parameters.
   */
  const handleParameterChange = (paramId, value) => {
    setParametersData((prev) => ({
      ...prev,
      [paramId]: {
        value,
        isValid: true,
        error: "",
        isShowError: false,
      },
    }));
  };

  /**
   * Validates and formats the function data for saving.
   */
  const handleSaveClick = () => {
    if (!choosenFunction?.value) {
      setValidation({
        isValid: false,
        error: "Please select a function.",
        isShowError: true,
      });
      return;
    }

    // Check if all required parameters are filled
    const hasAllParams = choosenFunction.parameters?.every(
      (param) => parametersData[param.parameterId]?.value
    );

    if (!hasAllParams) {
      setValidation({
        isValid: false,
        error: "Please fill all function parameters.",
        isShowError: true,
      });
      return;
    }

    // Format the data in the required structure
    const functionData = {
      functionId: choosenFunction.value,
      args: choosenFunction.parameters.map(
        (param) => parametersData[param.parameterId].value
      ),
    };

    onSave(parameterId, functionData);
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
      {/* Delete button - only shown when editing an existing function */}

      <DropdownAtom
        id={`${parameterId}-function-selector`}
        label="Select Function"
        options={functionOptions}
        selectedItem={choosenFunction?.value || ""}
        name="functionSelector"
        required={true}
        onChange={handleFunctionChange}
        validation={{
          isShowError: validation.isShowError,
          error: validation.error,
        }}
      />

      {/* <CategoryDropdown
        id={`${parameterId}-function-selector`}
        categories={getFunctionCategories()}
        label="Select Function"
        value={{
          value: choosenFunction?.value || "",
          isValid: validation.isValid,
          error: validation.error,
          isShowError: validation.isShowError,
        }}
        name="functionSelector"
        required={true}
        onChange={handleFunctionChange}
      /> */}

      {/* Render parameters if a function is selected */}
      {choosenFunction?.parameters && (
        <Box sx={{ mt: 2 }}>
          <DynamicConditionComp
            parameter={choosenFunction.parameters}
            parameterData={parametersData}
            onChange={handleParameterChange}
            renderWrapper={(children) => <Box sx={{ my: 1 }}>{children}</Box>}
            ruleType={ruleType}
          />
        </Box>
      )}

      {/* Action Buttons */}
      <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 1 }}>
        <BtnAtom
          onClick={onCancel}
          text="Cancel"
          sx={{ backgroundColor: "grey.400" }}
        />
        {isEditingExistingFunction && (
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <BtnAtom
              onClick={handleDeleteFunction}
              text="Delete"
              sx={{
                backgroundColor: "danger.main",
              }}
            />
          </Box>
        )}
        <BtnAtom
          onClick={handleSaveClick}
          text="Save Function"
          sx={{ backgroundColor: "primary.secondary" }}
        />
      </Box>
    </Box>
  );
};

FunctionsComp.propTypes = {
  functionOptions: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      description: PropTypes.string,
      parameters: PropTypes.arrayOf(
        PropTypes.shape({
          parameterId: PropTypes.string.isRequired,
          name: PropTypes.string.isRequired,
          type: PropTypes.string.isRequired,
          description: PropTypes.string,
        })
      ),
    })
  ).isRequired,
  initialFunction: PropTypes.shape({
    value: PropTypes.string,
    label: PropTypes.string,
    description: PropTypes.string,
    parameters: PropTypes.array,
  }),
  parameterId: PropTypes.string.isRequired,
  onSave: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
};

export default FunctionsComp;
