import { <PERSON>, Divider, Grid2 as Grid } from "@mui/material";
import Dropdown<PERSON>tom from "../../../../../../../global/atoms/dropdown/Dropdown";
import { Btn as Btn<PERSON>tom } from "../../../../../../../global/atoms/buttons/Button";
import { useState, useEffect, useCallback } from "react";
import { useSelector } from "react-redux";
import { <PERSON><PERSON>, Helpers } from "../../../../../../../../utils";
import SmartValueHoc from "../../../../../../../global/molecules/smartValueHoc/SmartValueHoc";

/**
import FunctionsComp from "../conditions/conditionTypes/FunctionsComp";
 * WebhooksContentOrganizer component handles all webhooks-related data and functionality,
 * including the internal hierarchy of component data.
 *
 * @component
 * @param {Object} props - Component props
 * @param {Function} props.handleCancel - Callback function to handle cancellation action.
 * @param {Function} props.handleSave - Callback function to handle saving of the updated data.
 * @param {Object} props.tab - Object containing data related to the current tab.
 * @param {{entityId: string, contextId: string}} props.entityAndContext - The entity and context IDs.
 * @returns {JSX.Element} The rendered WebhooksContentOrganizer component.
 */
const WebhookCallsContentOrganizer = ({
  handleCancel,
  handleSave,
  tab,
  entityAndContext,
  ruleType,
}) => {
  // state to handle the data of the variable operation
  const [data, setData] = useState({
    ...Helpers.defaultValues(["webhookId"], { ...tab.data }, []),
  });

  const { campaign, receivedData } = useSelector((state) => state.campaign);
  const { functions } = receivedData;
  const functionTypes = functions.map((func) => func.returnType);
  const [webhookParams, setWebhookParams] = useState([]);
  /**
   This function validates the fields and pass the user inputs to the upper level component for saving.
   */
  const handleSubmit = () => {
    let isAllValid = true;
    let validate = webhookParams.map((param, index) => {
      if (
        param.required &&
        (param.value === undefined ||
          param.value === null ||
          param.value === "")
      ) {
        isAllValid = false;
        return {
          ...param,
          isValid: false,
          isShowError: true,
          error: "This field is required",
        };
      }
      return param;
    });

    if (!isAllValid) {
      setWebhookParams(validate);
      return;
    }
    let params = {};
    webhookParams.forEach((item) => {
      params[item.parameterId] = item.value;
    });
    let obj = {
      ...Helpers.returnValues(data),
      parameters: Object.fromEntries(
        Object.entries(params).filter(([_, value]) => value !== "")
      ),
    };
    handleSave(obj);

    Alert("Webhook saved successfully", "success");
  };

  // Handle the dropdown change event
  //   const handleDropdownChange = Helpers.handleDropdownChangeFactoryFn(setData);
  const handleDropdownChange = (name, value) => {
    setData((prev) => {
      return {
        ...prev,
        [name]: {
          ...prev[name],
          value: value,
        },
      };
    });
  };

  let webhookCalls = tab.webhooks.map((item) => ({
    value: item.webhookId,
    label: item.name,
  }));

  useEffect(() => {
    const generateWebhookParamOptions = () => {
      if (!data.webhookId.value) {
        return;
      }
      let webhookCall = tab.webhooks.find(
        (item) => item.webhookId === data.webhookId.value
      );
      let params = Array.isArray(webhookCall.parameters) ? webhookCall.parameters : [];
      let updatedParams = params.map((par) => {
        let { webhookId, parameters } = tab.data;
        if (
          webhookId === data.webhookId.value &&
          parameters[par.parameterId] != undefined &&
          parameters[par.parameterId] != null
        ) {
          return {
            ...par,
            value: tab.data.parameters[par.parameterId],
          };
        } else {
          return { ...par, value: "" };
        }
      });
      setWebhookParams(updatedParams);
    };

    generateWebhookParamOptions();
  }, [data, tab.data.parameters]);

  /**
   * Handles changes in the parameter dropdown. If the selected value corresponds to a function,
   * it opens the function configuration modal. Otherwise, it updates the webhook parameters state
   * with the new value and its validation status.
   *
   * @param {string} name - The name of the parameter being changed.
   * @param {Object} value - The new value object for the parameter, containing the value, error, and validation status.
   * @param {string} value.value - The actual value selected in the dropdown.
   * @param {string} value.error - The error message, if any, associated with the value.
   * @param {boolean} value.isValid - The validation status of the value.
   */
  const handleParmDropdownChange = (name, value) => {
    setWebhookParams((prev) => {
      return prev.map((item) => {
        if (item.parameterId === name) {
          return {
            ...item,
            value: value,
            isValid: true,
            isShowError: false,
            error: "",
          };
        }
        return item;
      });
    });
  };

  return (
    <Box sx={{ width: "100%", padding: 2 }}>
      <Grid container spacing={2}>
        <Grid size={{ xs: 12, sm: 6, md: 4 }} item md={6}>
          {/* Action Type Field */}
          <DropdownAtom
            label="Webhook Call *"
            options={webhookCalls}
            name={"webhookId"}
            selectedItem={data.webhookId.value}
            onChange={handleDropdownChange}
            validation={{
              isShowError: data.webhookId.isShowError,
              error: data.webhookId.error,
            }}
          />
        </Grid>
        {/* Divider   */}
        <Grid size={12} my={3}>
          <Divider sx={{ bgcolor: "primary.main", height: "2px" }} />
        </Grid>

        <Grid item container size={{ xs: 12, sm: 12, md: 12 }}>
          {Array.isArray(webhookParams) && webhookParams.length > 0 &&
            webhookParams.map((item) => (
              <Grid
                key={item.parameterId}
                size={{ sm: 12, md: 6 }}
                sx={{ mt: 2 }}
              >
                
                <SmartValueHoc
                  smartValueProps={{
                    types: [item.type],
                    allowedRef: ["all"],
                    functionTypes: functionTypes,
                    customValAllowed: true,
                    entityId: entityAndContext.entityId,
                    contextId: entityAndContext.contextId,
                    data: item.value,
                    emptySaveAllowed: item.required ? false : true,
                    ruleType: ruleType,
                    errorDetails: item.required
                      ? {
                          error: item.error,
                          isShowError: item.isShowError,
                        }
                      : undefined,
                  }}
                  label={item.name}
                  onSave={(newValue) => {
                    handleParmDropdownChange(item.parameterId, newValue);
                  }}
                />
              </Grid>
            ))}
        </Grid>

        <Grid>
          <Box
            item
            size={{ xs: 12, sm: 12, md: 12 }}
            sx={{ display: "flex", justifyContent: "flex-start", gap: 2 }}
          >
            <BtnAtom
              sx={{ backgroundColor: "danger.main" }}
              onClick={() => handleCancel(tab.id)}
              text="Cancel"
            />
            <BtnAtom
              onClick={handleSubmit}
              sx={{ backgroundColor: "primary.secondary" }}
              text="Save Webhook"
            />
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default WebhookCallsContentOrganizer;
