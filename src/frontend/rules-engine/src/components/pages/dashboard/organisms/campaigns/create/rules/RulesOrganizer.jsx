import { Box, Grid2 as Grid, Divider } from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../../../global/atoms/typography/Typography";
import { Btn as Btn<PERSON>tom } from "../../../../../../global/atoms/buttons/Button";
import { useEffect, useState } from "react";
import SectionHeaderMolecule from "../../../../molecules/campaigns/create/SubSectionHeader";
import { Alert, campaignValidators, Helpers } from "../../../../../../../utils";
import { InputField } from "../../../../../../global/atoms/inputFields/InputField";
import ReusableSectionHOC from "../../../../atoms/global/reusableSectionHOC";

import SectionHeaderWithButton from "../../../../atoms/global/SectionHeaderWithButton";
import VariableOperationsContentOrganizer from "./variableOperations/VariableOperations";
import PropertiesOperationContentOrganizer from "./propertiesOperations/PropertiesOperations";
import ConditionContentOrganizer from "./conditions/ConditionContent";
import { useSelector } from "react-redux";

import React from "react";
import WebhookCallsContentOrganizer from "./webhooks/Webhooks";
import WebhookService from "../../../../../../../Api/Webhook";
import { setFilteredTransactionVariables } from "../../../../../../../redux/slices/tabs/campaigns/campaign";
import { useDispatch } from "react-redux";

/** Variable columns util */
const variablesColumnsUtil = Helpers.createColumnsUtil([
  {
    label: "Variable Id",
    gridSize: 3,
    content: { key: "variableId", def: "Create Variable operation" },
  },
  {
    label: "Assignment Type",
    gridSize: 3,
    content: {
      key: "assignmentTypeId",
    },
  },
  {
    label: "Value",
    gridSize: 3,
    content: {
      key: (item) => {
        const value = String(
          item.value?.functionId
            ? Helpers.formatFunctionData(item.value)
            : item.value
        ); // Ensure it's a string and handle null/undefined values
        return value;
      },
    },
  },
]);

/** Properties operations columns util */
const propertiesColumnsUtil = Helpers.createColumnsUtil([
  {
    label: "Property Id",
    gridSize: 3,
    content: {
      key: "variableId",
      def: "Create Transaction Context Properties Operation",
    },
  },
  {
    label: "Assignment Type",
    gridSize: 3,
    content: { key: "assignmentTypeId" },
  },
  {
    label: "Value",
    gridSize: 3,
    content: {
      key: (item) => {
        const value = String(
          item.value?.functionId
            ? Helpers.formatFunctionData(item.value)
            : item.value
        ); // Ensure it's a string and handle null/undefined values
        return value.length > 30 ? `${value.slice(0, 30)} ...` : value;
      },
    },
  },
]);
const webhookCallsColumnsUtil = Helpers.createColumnsUtil([
  {
    label: "Webhook Id",
    gridSize: 3,
    content: { key: "webhookId" },
  },
]);

/**
 * RuleContentOrganizer component handles all rules-related data and functionality,
 * including the internal hierarchy of component data.
 *
 * @component
 * @param {Object} props - Component props
 * @param {Function} props.handleCancel - Callback function to handle cancellation action.
 * @param {Function} props.handleSave - Callback function to handle saving of the updated data.
 * @param {Object} props.tab - Object containing data related to the current tab.
 * @param {string} props.ruleType - The type of the rule.
 * @param {{entityId: string, contextId: string}} props.entityAndContext - The entity and context IDs.
 * @param {number} props.lastPriority - The last priority number.
 *
 * @returns {JSX.Element} The rendered RuleContentOrganizer component.
 */
const RuleContentOrganizer = ({
  handleCancel,
  handleSave,
  tab,
  entityAndContext,
  lastPriority,
  ruleType,
}) => {
  const [data, setData] = useState({
    ...Helpers.defaultValues(
      ["ruleId", "name", "description", "priority"],
      { ...tab.data },
      ["ruleId", "priority"]
    ),
  });
  const dispatch = useDispatch();
  const [condition, setCondition] = useState(
    tab.data?.condition || {
      type: "",
      operator: "",
      parameters: {},
    }
  );
  /** @type {import("../../../../../../../../../../jsDocs/redux/store").Store['campaign']} */
  const { campaign, receivedData } = useSelector(
    /** @param {{ campaign: any }} state */ (state) => state.campaign
  );
  const { entityId, contextId } = campaign;

  const [variableOperations, setVariableOperations] = useState([]);
  const [propertiesOperations, setPropertiesOperations] = useState([]);
  const [webhookCalls, setWebhookCalls] = useState([]);
  const [webhooks, setWebhooks] = useState([]);

  // --------------------------------------------------------------

  /**
   This function validates the fields and pass the user inputs to the upper level component for saving.
   */
  const handleSubmit = () => {
    // Check if the condition object has a conditionTypeId
    if (!condition.conditionTypeId) {
      Alert("conditionTypeId is required", "error");
      return;
    }
    // Check for unsaved variable operations
    const unsavedVariableOperation = variableOperations.find(
      (item) => item.id === "new_VARIABLE_OPERATION"
    );
    if (unsavedVariableOperation) {
      Alert(
        "Please save or delete the incomplete variable assignment",
        "error"
      );
      return;
    }

    // Check for unsaved properties operations
    const unsavedPropertiesOperation = propertiesOperations.find(
      (item) => item.id === "new_PROPERTIES_OPERATION"
    );
    if (unsavedPropertiesOperation) {
      Alert(
        "Please save or delete the incomplete properties assignment",
        "error"
      );
      return;
    }

    // Check for unsaved webhook calls
    const unsavedWebhookCall = webhookCalls.find(
      (item) => item.id === "new_WEBHOOK_CALL"
    );
    if (unsavedWebhookCall) {
      Alert("Please save or delete the incomplete webhook call", "error");
      return;
    }

    // Validate all fields using isValidAllFields
    const validationResult = campaignValidators.global.isValidAllFields(data);

    if (validationResult.success) {
      const filteredVariables = [];
      const filteredProperties = [];

      // here we are separating the operations into two arrays, one is filtered and the other is unfiltered
      // filtered operations are the operations that are not new items and already in the format which server accepts it
      // unfiltered operations are the operations that are new items and need to be formatted to the server accepted format
      let unfilteredOperations = [];
      variableOperations.forEach((item) => {
        if (item.mode === "viewing" || item.mode === "editing") {
          unfilteredOperations.push(item);
        } else {
          filteredVariables.push(item);
        }
      });

      propertiesOperations.forEach((item) => {
        if (item.mode === "viewing" || item.mode === "editing") {
          unfilteredOperations.push(item);
        } else {
          filteredProperties.push(item);
        }
      });

      let obj = {
        ...Helpers.returnValues(data),
        ruleId: Helpers.idGenerator(data.name.value, "upper-snake", {
          removeSpecialChars: true,
          maxLength: 50,
        }),
        condition: condition,
        variableAssignments: [
          ...filteredVariables,
          ...filteredProperties,
          ...Helpers.returnArrayValues(unfilteredOperations),
        ],
        webhookCalls: webhookCalls.map((item) => item.data),
      };
      handleSave(obj);
    } else {
      // If there are validation errors, update the inputs state to show errors
      const updatedInputs = { ...data };

      // Iterate over the errors array and update the state
      validationResult.errors.forEach(({ field, error }) => {
        updatedInputs[field] = {
          ...updatedInputs[field],
          isValid: false, // Set field as invalid
          isShowError: true, // Show error message
          error, // Set error message
        };
      });

      // Update the inputs state with the new validation statuses
      setData(updatedInputs);
    }
  };

  // Utils
  const conditionUtil = Helpers.createRowsUtil(
    condition,
    setCondition,
    "condition",
    Alert
  );
  const variableOperationsUtil = Helpers.createRowsUtil(
    variableOperations,
    setVariableOperations,
    "variable operation",
    Alert
  );
  const propertiesOperationsUtil = Helpers.createRowsUtil(
    propertiesOperations,
    setPropertiesOperations,
    "properties operation",
    Alert
  );
  const webhookCallsUtil = Helpers.createRowsUtil(
    webhookCalls,
    setWebhookCalls,
    "webhook call",
    Alert
  );

  // ----------------- Handle save condition -----------------

  const handleSaveCondition = (data) => {
    setCondition(data);
  };

  /**
   * This function save the action and variable operation data in its respective arrays.
   */
  const handleUpdateNewItem = (index, data, type) => {
    const updateItems = (items, setItems, getColumnsFn) => {
      const updatedItems = items.map((item, itemIndex) => {
        // If item not matched
        if (itemIndex != index) return item;
        // Get updated columns
        // let columns = variablesColumnsUtil.generateItemContentColumns(data);
        const columns = getColumnsFn ? getColumnsFn(data) : item.columns;

        // Return updated item
        const objUpdated = {
          ...item,
          id: data.variableId ? data.variableId : Math.random(),
          mode: "viewing",
          data,
          columns,
        };
        return objUpdated;
      });

      setItems(updatedItems);
    };

    if (type === "variableOperation") {
      updateItems(
        variableOperations,
        setVariableOperations,
        variablesColumnsUtil.generateItemContentColumns
      );
    } else if (type === "propertiesOperation") {
      // Update propertiesOperations
      updateItems(
        propertiesOperations,
        setPropertiesOperations,
        propertiesColumnsUtil.generateItemContentColumns
      );

      Alert(`${type} successfully saved`, "success");
    } else if (type === "webhookCall") {
      updateItems(
        webhookCalls,
        setWebhookCalls,
        webhookCallsColumnsUtil.generateItemContentColumns
      );
    }
  };

  /**
   * This function cancel or change the mode from editing to viewing of the action condition and variable operation data in its respective arrays.
   * @param {number} itemIndex - The index of the item in the array.
   * @param {string} type - The type of the item.
   * @param {Object} utilClassObj - The utility class object.
   */
  const handleCancelNewItem = (index, type) => {
    const updateItems = (items, setItems, newItemId) => {
      // Check if the item is a new unsaved item
      const item = items[index];
      if (item && item.id === newItemId) {
        // If it's a new unsaved item, remove it completely
        const updatedItems = items.filter(
          (_, itemIndex) => itemIndex !== index
        );
        setItems(updatedItems);
      } else {
        // For existing items, just switch back to viewing mode
        const updatedItems = items.map((item, itemIndex) =>
          itemIndex === index ? { ...item, mode: "viewing" } : item
        );
        setItems(updatedItems);
      }
    };

    switch (type) {
      case "variableOperation":
        updateItems(
          variableOperations,
          setVariableOperations,
          "new_VARIABLE_OPERATION"
        );
        break;
      case "propertiesOperation":
        updateItems(
          propertiesOperations,
          setPropertiesOperations,
          "new_PROPERTIES_OPERATION"
        );
        break;
      case "webhookCall":
        updateItems(webhookCalls, setWebhookCalls, "new_WEBHOOK_CALL");
        break;
      default:
        break;
    }
  };
  /**
   * Handles the change event of the dropdowns (Entity and Context)
   *
   * It updates the data state with the new value and sets the entityName field
   * based on the selected entity. It also sets the isValid and isShowError fields
   * to true and false respectively.
   *
   * @param {string} name - The name of the dropdown field.
   * @param {string} value - The selected value of the dropdown.
   */
  /**
   * This function handle the input change and update the state based on the value change
   */
  const handleInputChange = (e, status) => {
    let { name, value } = e.target;
    setData((prev) => ({
      ...prev, // Spread the previous state
      [name]: {
        value,
        isValid: status.success,
        error: status.success ? null : status.message,
        isShowError: !status.success,
      },
    }));
  };

  useEffect(() => {
    if (tab.data) {
      // Destructure the actions, conditions, variableOperations and propertiesOperations from tab.data
      const {
        variableAssignments = [],
        webhookCalls: receivedWebhooksCalls = [],
      } = tab.data;
      let receivedCondition = tab.data.condition || {};
      const contextProperties = receivedData.entities
        .find((item) => item.entityId === entityId)
        .transactionContexts.find(
          (item) => item.contextId === contextId
        ).properties;
      let varAssignments = variableAssignments.filter(
        (operation) =>
          // Object.keys(item).includes("variableId")
          // Check if this operation's variableId does NOT exist in contextProperties
          !contextProperties.some(
            (prop) => prop.propertyId === operation.variableId
          )
      );

      let propAssignments = variableAssignments.filter((operation) =>
        // Object.keys(item).includes("propertyId")
        contextProperties.some(
          (prop) => prop.propertyId === operation.variableId
        )
      );

      let varAssignmentsData = varAssignments.map((item) => ({
        id: `${Math.random()}`,
        mode: "viewing",
        columns: variablesColumnsUtil.generateItemContentColumns(item),
        data: item,
      }));

      let propAssignmentsData = propAssignments.map((item) => ({
        id: `${Math.random()}`,
        mode: "viewing",
        columns: propertiesColumnsUtil.generateItemContentColumns(item),
        data: item,
      }));
      let webhookCallsData = receivedWebhooksCalls.map((item) => ({
        id: `${Math.random()}`,
        mode: "viewing",
        columns: webhookCallsColumnsUtil.generateItemContentColumns(item),
        data: item,
      }));

      setVariableOperations(varAssignmentsData);
      setPropertiesOperations(propAssignmentsData);
      setWebhookCalls(webhookCallsData);
      setCondition(receivedCondition);
    }
  }, [tab.data]);

  useEffect(() => {
    /**
     * Fetches the webhooks from the server and stores them in the webhooks state.
     */
    const getWebhooks = async () => {
      try {
        let res = await WebhookService.getWebhooks({});

        if (!res.success) {
          Alert(res.message, "error");
          return;
        }
        setWebhooks(res.data.webhooks);
      } catch (error) {
        Alert(error.message, "error");
        console.log(error);
      }
    };
    getWebhooks();
  }, []);

  // useEffect(() => {
  //   if (ruleType) {
  //     const filteredVariables = transactionVariables.filter(
  //       (variable) =>
  //         variable.availablePhases &&
  //         variable.availablePhases.includes(ruleType.toLowerCase())
  //     );
  //     dispatch(setFilteredTransactionVariables(filteredVariables));
  //   }
  // }, [ruleType, transactionVariables]);
  // ----------- On Condition type and operator change ---------
  return (
    <Box sx={{ width: "100%", padding: 2 }}>
      <Grid container spacing={2}>
        {/* The rule name field input */}
        <Grid size={{ xs: 12, sm: 12, md: 12 }} item md={6}>
          {/* The rule name field input */}
          <InputField
            label="Rule Name"
            type="text"
            placeholder="e.g. Summer rule discount"
            rows={4}
            required={true}
            sx={{ backgroundColor: "bgPrimary.main" }}
            name={"name"}
            validator={campaignValidators.entities.ruleName}
            onChange={handleInputChange}
            validateOn={"change"}
            value={data.name.value}
            validation={{
              isShowError: data.name.isShowError,
              error: data.name.error,
            }}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 12, md: 12 }} item md={6}>
          {/* The rule description field input */}
          <InputField
            label="Description"
            type="text"
            placeholder="The total amount of purchases made by a customer"
            multiline={true}
            rows={4}
            required={true}
            sx={{ backgroundColor: "bgPrimary.main" }}
            name={"description"}
            validator={campaignValidators.entities.ruleDescription}
            onChange={handleInputChange}
            validateOn={"change"}
            value={data.description.value}
            validation={{
              isShowError: data.description.isShowError,
              error: data.description.error,
            }}
          />
        </Grid>

        {/* ============ Condition Type Dropdown ===================  */}
        {/* Conditions   */}
        <Grid size={{ xs: 12, sm: 12, md: 12 }} item md={6}>
          <SectionHeaderWithButton title="Condition" />
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 12 }} item md={12}>
          <Box sx={{ width: "100%", backgroundColor: "bgPrimary.main" }}>
            <ConditionContentOrganizer
              key={"condition_organizer"}
              // handleSave={(inputs) => conditionUtil.handleSaveFunc(tab.id, inputs)}
              handleSave={handleSaveCondition}
              handleCancel={() => {}}
              entityAndContext={entityAndContext}
              tab={{ data: condition }}
              nestedLevel={0} // As it's the main condition so the nested level is 0
              ruleType={ruleType}
            />
          </Box>
        </Grid>

        {/* Divider   */}
        <Grid size={12} my={3}>
          <Divider sx={{ bgcolor: "primary.main", height: "2px" }} />
        </Grid>

        {/* ============ Variables  Operations ===================  */}
        <Grid size={{ xs: 12, sm: 12, md: 12 }} item md={6}>
          <SectionHeaderWithButton
            title="Variable Assignments"
            onButtonClick={() => {
              if (
                [...variableOperations, ...propertiesOperations].length >= 10
              ) {
                Alert(
                  "Cannot add more than 10 Assignments Variables (Variables + Properties). Limit exceeded",
                  "error"
                );
                return;
              }
              variableOperationsUtil.handleAddNewItem();
            }}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 12 }} item md={12}>
          <Box sx={{ mt: 2, width: "100%" }}>
            <SectionHeaderMolecule
              sx={{}}
              columns={variablesColumnsUtil.columnsOfSectionHeader}
            />

            {variableOperations.length > 0 &&
              variableOperations.map((tab, index) => (
                <ReusableSectionHOC
                  key={index}
                  index={index}
                  tab={tab}
                  columns={tab.columns}
                  sxContainer={{ backgroundColor: "bgPrimary.main" }}
                  rows={tab.rows}
                  onEdit={() =>
                    // variableOperationsUtil.handleEditBtnClickById(tab.id)
                    variableOperationsUtil.handleEditBtnClick(index)
                  }
                  onDelete={() =>
                    // variableOperationsUtil.handleDeleteItemById(tab.id)
                    variableOperationsUtil.handleDeleteBtnClick(index)
                  }
                  content={
                    <VariableOperationsContentOrganizer
                      // handleSave={(inputs) =>
                      //   variableOperationsUtil.handleSaveFunc(tab.id, inputs)
                      // }
                      handleSave={(data) =>
                        // handleUpdateNewItem(tab.id, data, "variableOperation")
                        handleUpdateNewItem(index, data, "variableOperation")
                      }
                      handleCancel={() =>
                        handleCancelNewItem(index, "variableOperation")
                      }
                      tab={tab}
                      entityAndContext={entityAndContext}
                      ruleType={ruleType}
                    />
                  }
                />
              ))}
            {/* If there are no transaction contexts, display a message */}
            {variableOperations.length < 1 && (
              <Box sx={{ mt: 2 }}>
                <TypographyAtom
                  sx={{ textAlign: "center", color: "textColor.secondary" }}
                  text="No variable operation added yet."
                  variant="h6"
                />
              </Box>
            )}
          </Box>
        </Grid>
        {/* Divider   */}
        <Grid size={12} my={3}>
          <Divider sx={{ bgcolor: "primary.main", height: "2px" }} />
        </Grid>

        {/* ============  Properties Operations ===================  */}
        <Grid size={{ xs: 12, sm: 12, md: 12 }} item md={6}>
          <SectionHeaderWithButton
            title="Properties Assignments"
            buttonTitle="Add Property"
            onButtonClick={() => {
              if (
                [...variableOperations, ...propertiesOperations].length >= 10
              ) {
                Alert(
                  "Cannot add more than 10 Assignments Variables (Variables + Properties). Limit exceeded",
                  "error"
                );
                return;
              }
              propertiesOperationsUtil.handleAddNewItem();
            }}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 12 }} item md={12}>
          <Box sx={{ mt: 2, width: "100%" }}>
            <SectionHeaderMolecule
              sx={{}}
              columns={propertiesColumnsUtil.columnsOfSectionHeader}
            />
            {propertiesOperations.length > 0 &&
              propertiesOperations.map((tab, index) => {
                return (
                  <ReusableSectionHOC
                    key={index}
                    index={index}
                    tab={tab}
                    columns={tab.columns}
                    sxContainer={{ backgroundColor: "bgPrimary.main" }}
                    rows={tab.rows}
                    onEdit={() =>
                      propertiesOperationsUtil.handleEditBtnClick(index)
                    }
                    onDelete={() =>
                      propertiesOperationsUtil.handleDeleteBtnClick(index)
                    }
                    content={
                      <PropertiesOperationContentOrganizer
                        handleSave={(data) =>
                          handleUpdateNewItem(
                            index,
                            data,
                            "propertiesOperation"
                          )
                        }
                        handleCancel={() =>
                          handleCancelNewItem(index, "propertiesOperation")
                        }
                        tab={tab}
                        entityAndContext={entityAndContext}
                        ruleType={ruleType}
                      />
                    }
                  />
                );
              })}
            {/* If there are no transaction contexts, display a message */}
            {propertiesOperations.length < 1 && (
              <Box sx={{ mt: 2 }}>
                <TypographyAtom
                  sx={{ textAlign: "center", color: "textColor.secondary" }}
                  text="No properties assignments added yet."
                  variant="h6"
                />
              </Box>
            )}
          </Box>
        </Grid>

        {/* Divider   */}
        <Grid size={12} my={3}>
          <Divider sx={{ bgcolor: "primary.main", height: "2px" }} />
        </Grid>

        {/* ============  Webhook Calls ===================  */}
        <Grid size={{ xs: 12, sm: 12, md: 12 }} item md={6}>
          <SectionHeaderWithButton
            title="Webhooks"
            buttonTitle="Add"
            onButtonClick={() => {
              if (webhookCalls.length >= 5) {
                Alert(
                  "Cannot add more than 5 webhooks. Limit exceeded",
                  "error"
                );
                return;
              }
              webhookCallsUtil.handleAddNewItemWebhook({
                webhookId: "",
                parameters: {},
              });
            }}
          />
        </Grid>

        <Grid size={{ xs: 12, sm: 6, md: 12 }} item md={12}>
          <Box sx={{ mt: 2, width: "100%" }}>
            <SectionHeaderMolecule
              sx={{}}
              columns={webhookCallsColumnsUtil.columnsOfSectionHeader}
            />
            {webhookCalls.length > 0 &&
              webhookCalls.map((tab, index) => {
                return (
                  <ReusableSectionHOC
                    key={index}
                    index={index}
                    tab={tab}
                    columns={tab.columns}
                    sxContainer={{ backgroundColor: "bgPrimary.main" }}
                    rows={tab.rows}
                    onEdit={() => webhookCallsUtil.handleEditBtnClick(index)}
                    onDelete={() =>
                      webhookCallsUtil.handleDeleteBtnClick(index)
                    }
                    content={
                      <WebhookCallsContentOrganizer
                        handleSave={(data) =>
                          handleUpdateNewItem(index, data, "webhookCall")
                        }
                        handleCancel={() =>
                          handleCancelNewItem(index, "webhookCall")
                        }
                        tab={{ ...tab, webhooks: webhooks }}
                        entityAndContext={entityAndContext}
                        ruleType={ruleType}
                      />
                    }
                  />
                );
              })}
            {/* If there are no transaction contexts, display a message */}
            {webhookCalls.length < 1 && (
              <Box sx={{ mt: 2 }}>
                <TypographyAtom
                  sx={{ textAlign: "center", color: "textColor.secondary" }}
                  text="No webhook calls added yet."
                  variant="h6"
                />
              </Box>
            )}
          </Box>
        </Grid>

        {/* Divider   */}
        <Grid size={12} my={3}>
          <Divider sx={{ bgcolor: "primary.main", height: "2px" }} />
        </Grid>

        {/* Save and Cancel buttons */}
        <Grid size={{ xs: 12, sm: 12, md: 12 }}>
          <Box
            item
            sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}
          >
            <BtnAtom
              sx={{ backgroundColor: "danger.main" }}
              onClick={() => handleCancel(tab.id)}
              text="Cancel"
            />
            {/* {data.name.isValid && ( */}
            <BtnAtom
              onClick={handleSubmit}
              sx={{ backgroundColor: "primary.secondary" }}
              text="Save Rule"
            />
            {/* )} */}
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};
export default RuleContentOrganizer;
