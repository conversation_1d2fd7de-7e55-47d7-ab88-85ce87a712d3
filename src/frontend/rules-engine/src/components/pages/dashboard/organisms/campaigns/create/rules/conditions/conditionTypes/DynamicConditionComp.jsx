import { useSelector } from "react-redux";
import SmartValueHoc from "../../../../../../../../global/molecules/smartValueHoc/SmartValueHoc";
import { Helpers } from "../../../../../../../../../utils/generalFunctions/index";
import { useEffect, useRef } from "react";
import { Alert } from "../../../../../../../../../utils/alert/alertUtils";
import { useVariableOptions } from "../../../../../../../../../hooks/useVariableOptions";

/**
 * A dynamic component which renders the leftoperand and the right operand
 * Supports 'Any', 'String',"enum","list" and 'Date' type rendering variations.
 *
 * @param {Object} props - The component props
 * @param {Object} props.parameter - The parameter definition with name and type
 * @param {Object} props.parameterData - The parameter data containing value and validation state
 * @param {Function} props.onChange - Callback function when value changes
 * @param {{entityId: string, contextId: string}} props.entityAndContext - The entity and context IDs
 * @param {string} props.currentOperator - The current operator
 * @param {Object} props.allConditionData - The all condition data
 * @param {Object} props.renderWrapper - The render wrapper
 * @param {"evaluation" | "outcome"} props.ruleType - The rule type
 * @returns {React.JSX.Element} The rendered component
 */
const DynamicConditionComp = ({
  parameter,
  parameterData,
  onChange,
  entityAndContext,
  renderWrapper,
  currentOperator,
  ruleType,
  allConditionData,
}) => {
  /** @type {import("../../../../../../../../../../../jsDocs/redux/store").Store['campaign']} */
  const { campaign, receivedData } = useSelector((state) => state.campaign);
  const { entityId, contextId } = campaign;
  const { functions } = receivedData;
  const functionTypes = functions.map((func) => func.returnType);
  
  // Keep track of previous operator to detect changes
  const prevOperatorRef = useRef(currentOperator);

  // Reset parameter data when operator changes
  useEffect(() => {
    // Skip initial render
    if (
      prevOperatorRef.current !== currentOperator &&
      prevOperatorRef.current !== undefined
    ) {
      // Reset the parameter data when operator changes
      if (parameter.parameterId) {
        onChange(parameter.parameterId, "");
      }
    }

    // Update the previous operator reference
    prevOperatorRef.current = currentOperator;
  }, [currentOperator, parameter.parameterId, onChange]);

  // const isFirstRender = useRef(true);

  // If we receive an array of parameters, map through them
  if (Array.isArray(parameter)) {
    return parameter.map((paramDef, index) => {
      const paramKey = paramDef.parameterId || paramDef.name;
      const content = (
        <DynamicConditionComp
          key={`${paramKey}-${index}`}
          parameter={paramDef}
          currentOperator={currentOperator}
          parameterData={
            parameterData[paramKey] || {
              value:
                typeof parameterData[paramKey]?.value === "boolean"
                  ? parameterData[paramKey]?.value
                  : "",
              isValid: false,
              error: "",
              isShowError: false,
            }
          }
          allConditionData={allConditionData}
          onChange={onChange}
          entityAndContext={entityAndContext}
          ruleType={ruleType}
        />
      );

      // Use the wrapper if provided, otherwise return content directly
      return renderWrapper ? renderWrapper(content) : content;
    });
  }

  // get the left operand data
  const leftOperandData = allConditionData?.leftOperand?.value;
  const leftOperandPrevValueRef = useRef(leftOperandData);

  // make sure that the right operand is reset when the left operand changes
  useEffect(() => {
    // check if the right operand is rendering and if the left operand changed
    if (
      parameter.parameterId === "rightOperand" &&
      JSON.stringify(leftOperandPrevValueRef.current) !==
        JSON.stringify(leftOperandData)
    ) {
      // Reset the rightOperand value
      onChange(parameter.parameterId, "");
    }

    // Update the reference for next comparison
    leftOperandPrevValueRef.current = leftOperandData;
  }, [leftOperandData]);

  // Determine if this is a right operand
  const isRightOperand = parameter.parameterId === "rightOperand";

  // check if the left operand is empty or not
  const leftOperandEmpty =
    (leftOperandData !== false &&
      leftOperandData !== true &&
      leftOperandData === null) ||
    (typeof leftOperandData === "string" && leftOperandData.trim() === "") ||
    (Array.isArray(leftOperandData) && leftOperandData.length === 0);

  // get the variable options to compare data with it and get the type
  const { generateVariablesOptions } = useVariableOptions(
    entityId,
    contextId,
    ruleType
  );
  // Get all variable options for checking references
  const allVariableOptions = generateVariablesOptions("all").flat();

  //  get the type of the left operand
  let leftOperandType = !leftOperandEmpty
    ? Helpers.determineValueTypeAndSource(
        leftOperandData,
        allVariableOptions,
        functions
      ).type
    : null;

  // as for now we have only string functions
  if (leftOperandType === "functions") {
    leftOperandType = "string";
  }

  // Get the operand types based on the operator and the left operand type
  const getOperandTypes = () => {
    // Default types from operator
    const defaultTypes =
      ["IN", "NOT_IN"].includes(currentOperator) &&
      parameter.parameterId === "leftOperand"
        ? ["string",]
        : Helpers.getApplicableTypes(currentOperator);

    // If this is not a right operand or left operand is empty, use default types
    if (!isRightOperand || leftOperandEmpty) {
      return defaultTypes;
    }

    // For membership operators, always show list/enum types
    if (["IN", "NOT_IN"].includes(currentOperator)) {
      return ["reference", "array",];// Editied
    }

    // For other operators, match right operand type with left operand type
    // or use compatible types depending on operator
    return [leftOperandType || defaultTypes[0]];
  };

  const handleDisabledClick = () => {
    Alert(
      "Please enter a value in the left Operand before setting this value.",
      "error"
    );
  };


  // ---------- We want to get the enum values, if the left operand is a reference to an enum and this is the right operand

  const leftOperandSourceAndType = Helpers.determineValueTypeAndSource(
    leftOperandData,
    allVariableOptions,
    functions
  );

  const enumValues = isRightOperand && leftOperandSourceAndType.type === "enum" ? leftOperandSourceAndType.values : null;
  
 // --------------------------------------------------------------------------------------------------------



  const props = {
    types: getOperandTypes(),
    enumValues: enumValues,
    allowedRef: ["all"],
    // functionTypes: generateFunctionTypes(),// functionTypes,
    customValAllowed: true,
    entityId: entityId,
    contextId: contextId,
    data: parameterData.value,
    emptySaveAllowed: parameter.required ? false : true,
    ruleType: ruleType,
    disabled: isRightOperand && leftOperandEmpty,
    onDisabledClick: handleDisabledClick,
    errorDetails: parameter.required
      ? {
          error: parameterData.error,
          isShowError: parameterData.isShowError,
        }
      : undefined,
  };
 
 
  return (
    <SmartValueHoc
      label={parameter.name}
      onSave={(newValue) => {
        onChange(parameter.parameterId, newValue);
      }}
      smartValueProps={props}
    />
  );
};

export default DynamicConditionComp;
