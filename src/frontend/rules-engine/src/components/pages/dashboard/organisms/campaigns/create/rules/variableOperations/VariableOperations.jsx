import { Box, Grid2 as Grid } from "@mui/material";
import Dropdown<PERSON>tom from "../../../../../../../global/atoms/dropdown/Dropdown";
import { Btn as BtnAtom } from "../../../../../../../global/atoms/buttons/Button";
import { useState, useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { campaignValidators, Helpers } from "../../../../../../../../utils";
import CategoryDropdown from "../../../../../../../global/atoms/dropdown/CategoryDropdown";
import SmartValueHoc from "../../../../../../../global/molecules/smartValueHoc/SmartValueHoc";
import { useVariableOptions } from "../../../../../../../../hooks/useVariableOptions";
import { Alert } from "../../../../../../../../utils/alert/alertUtils";

import FunctionsComp from "../conditions/conditionTypes/FunctionsComp";
import React from "react";

const columnsOfSectionHeader = [
  { label: "Entity Name", gridSize: 10, sx: {} }, // 6 out of 12 columns
  { label: "Actions", gridSize: 2, sx: {} }, // 6 out of 12 columns
];

/**
 * VariableOperationsContentOrganizer component handles all rules-related data and functionality,
 * including the internal hierarchy of component data.
 *
 * @component
 * @param {Object} props - Component props
 * @param {Function} props.handleCancel - Callback function to handle cancellation action.
 * @param {Function} props.handleSave - Callback function to handle saving of the updated data.
 * @param {Object} props.tab - Object containing data related to the current tab.
 * @param {{entityId: string, contextId: string}} props.entityAndContext - The entity and context IDs.
 * @returns {JSX.Element} The rendered VariableOperationsContentOrganizer component.
 */
const VariableOperationsContentOrganizer = ({
  handleCancel,
  handleSave,
  tab,
  entityAndContext,
  ruleType,
}) => {
  const dispatch = useDispatch();

  // Get the received data from the redux store that is received from the API.
  /** @type {import("../../../../../../../../../jsDocs/redux/store").Store['campaign']} */
  const { campaign, receivedData } = useSelector(
    /** @param {{ campaign: any }} state */ (state) => state.campaign
  );

  const { functions } = receivedData;
  const functionTypes = functions.map((func) => func.returnType);

  // Providing the default value to the data state
  let { variableAssignments } = receivedData;

  // state to handle the data of the variable operation
  const [data, setData] = useState({
    ...Helpers.defaultValues(
      ["variableId", "assignmentTypeId", "value"],
      { ...tab.data },
      []
    ),
  });
  const [selectedVarType, setSelectedVarType] = useState(null);
  const [filteredAssignments, setFilteredAssignments] = useState([]);
  // Initialize selectedVarType and filteredAssignments based on existing data
  useEffect(() => {
    // Check if there's a variable already selected
    if (data.variableId?.value) {
      // Find the selected variable type
      const selectedVar = variableOptions
        ?.flatMap((category) => category.options)
        .find((option) => option.value === data.variableId.value);

      const varType = selectedVar?.type || null;
      setSelectedVarType(varType);

      // Filter variable assignments based on the selected variable type
      if (varType) {
        const filtered = variableAssignments.filter((assignment) =>
          assignment.applicableTypes.includes(varType)
        );
        setFilteredAssignments(filtered);
      }
    }
  }, []);
  /**
   This function validates the fields and pass the user inputs to the upper level component for saving.
   */
  const handleSubmit = () => {
    // Validate all fields using isValidAllFields
    const validationResult = campaignValidators.global.isValidAllFields(data);

    if (validationResult.success) {
      let obj = {
        ...Helpers.returnValues(data),
      };
      handleSave(obj);
    } else {
      // If there are validation errors, update the inputs state to show errors
      const updatedData = { ...data };

      // Iterate over the errors array and update the state
      validationResult.errors.forEach(({ field, error }) => {
        updatedData[field] = {
          ...updatedData[field],
          isValid: false, // Set field as invalid
          isShowError: true, // Show error message
          error, // Set error message
        };
      });

      // Update the inputs state with the new validation statuses
      setData(updatedData);
    }
  };
  // Handle the dropdown change event
  const originalHandleDropdownChange =
    Helpers.handleDropdownChangeFactoryFn(setData);

  /**
   * Enhanced dropdown change handler that extends the original handler with additional functionality.
   * This wrapper maintains the original behavior while adding specific logic for variable selection:
   * - Updates the selected variable type state when a variable is selected
   * - Filters assignment types based on the selected variable's type
   * - Resets dependent fields (assignmentTypeId and value) when the variable changes
   * to maintain data consistency and prevent invalid configurations
   */
  const handleDropdownChange = (name, value) => {
    // Call the original function to maintain existing behavior
    originalHandleDropdownChange(name, value);

    // Add extra functionality for variable selection
    if (name === "variableId") {
      // Find the selected variable type
      const selectedVar = variableOptions
        .flatMap((category) => category.options)
        .find((option) => option.value === value);

      const varType = selectedVar?.type || null;
      setSelectedVarType(varType);

      // Filter variable assignments based on the selected variable type
      if (varType) {
        const filtered = variableAssignments.filter((assignment) =>
          assignment.applicableTypes.includes(varType)
        );
        setFilteredAssignments(filtered);
      } else {
        setFilteredAssignments([]);
      }

      // Reset assignmentTypeId when variable changes
      setData((prev) => ({
        ...prev,
        assignmentTypeId: {
          ...prev.assignmentTypeId,
          value: "",
          isValid: false,
          isShowError: false,
          error: "This field is required",
        },
        value: {
          ...prev.value,
          value: "",
          isValid: false,
          isShowError: false,
          error: "This field is required",
        },
      }));
    }
  };
  /**
   * Retrieves variable options from the useVariableOptions hook and generates
   * formatted option groups for dropdowns based on the specified type.
   *
   * @param {'variables'|'all'} type - The type of options to generate ('variables' for only variables, 'all' for all options)
   * @param {boolean} wipeBrackets - Whether to remove curly brackets from option values
   * @returns {Array} - Array of option groups with categories and their respective options
   */
  const variableOptions = useVariableOptions(
    entityAndContext.entityId,
    entityAndContext.contextId,
    ruleType
  ).generateVariablesOptions("variables", true);

  const props = {
    types: selectedVarType ? [selectedVarType] : [],
    functionTypes: functionTypes,
    allowedRef: ["all"],
    customValAllowed: true,
    entityId: entityAndContext.entityId,
    contextId: entityAndContext.contextId,
    data: data.value.value,
    emptySaveAllowed: false,
    ruleType: ruleType,
    disabled: !selectedVarType,
    onDisabledClick: () => Alert("Please select a variable first", "error"),
    errorDetails: {
      error: data.value.error,
      isShowError: data.value.isShowError,
    },
  };

  return (
    <Box sx={{ width: "100%", padding: 2 }}>
      <Grid container spacing={2}>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          {/* The parameters field input */}
          <CategoryDropdown
            isCustomInputDisabled={true}
            categories={variableOptions}
            label="Select Variable *"
            value={data.variableId}
            name={"variableId"}
            onChange={handleDropdownChange}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }} item md={6}>
          {/* Action Type Field */}
          <DropdownAtom
            label="Assignment Type *"
            options={
              selectedVarType ? filteredAssignments : variableAssignments
            }
            name={"assignmentTypeId"}
            selectedItem={data.assignmentTypeId.value}
            onChange={handleDropdownChange}
            disabled={!selectedVarType}
            onDisabledClick={() =>
              Alert("Please select a variable first", "error")
            }
            validation={{
              isShowError: data.assignmentTypeId.isShowError,
              error: data.assignmentTypeId.error,
            }}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }} item md={6}>
          <SmartValueHoc
            label="Value "
            onSave={(newValue) => {
              handleDropdownChange("value", newValue);
            }}
            smartValueProps={props}
          />
        </Grid>

        <Grid>
          <Box
            item
            size={{ xs: 12, sm: 12, md: 12 }}
            sx={{ display: "flex", justifyContent: "flex-start", gap: 2 }}
          >
            <BtnAtom
              sx={{ backgroundColor: "danger.main" }}
              onClick={() => handleCancel(tab.id)}
              text="Cancel"
            />

            <BtnAtom
              onClick={handleSubmit}
              sx={{ backgroundColor: "primary.secondary" }}
              text="Save Variable Operation"
            />
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default VariableOperationsContentOrganizer;
