import { Box, Divider, List, ListItem } from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../global/atoms/typography/Typography";
import { Btn } from "../../../../global/atoms/buttons/Button";
import DynamicIcon from "../../../../global/atoms/icons/Index";
import { useDispatch, useSelector } from "react-redux";
import {
  resetCampaignsData,
  setCampaignsTab,
} from "../../../../../redux/slices/tabs/campaigns/campaigns";
import zIndex from "@mui/material/styles/zIndex";
import { Alert, Helpers } from "../../../../../utils";
import { logoutSuccess } from "../../../../../redux/slices/global/auth";
import { useNavigate } from "react-router-dom";
import CampaignService from "../../../../../Api/CampaignService";
import { setCampaignTab } from "../../../../../redux/slices/global/dashboard";
import React from "react";
import { registerConfirmation } from "../../../../global/atoms/dialog/Index";
import { openDialog } from "../../../../../redux/slices/global/confirmation";
import Version from "../../../../global/atoms/version/version";
import { setGlobalTab } from "../../../../../redux/slices/global/dashboard";
import { resetData } from "../../../../../redux/slices/tabs/campaigns/campaign";

/**
 * Header component with a logo and two buttons.
 *
 * @param {object} props - Component props.
 * @param {string} props.logoText - Text or logo for the header.
 * @param {string} props.firstButtonLabel - Label for the first button.
 * @param {string} props.secondButtonLabel - Label for the second button.
 * @param {function} props.onFirstButtonClick - Handler for the first button click.
 * @param {function} props.onSecondButtonClick - Handler for the second button click.
 */
const CampaignsDashboardHeader = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  /** @type {import("../../../../../../jsDocs/redux/store").Store['campaign']} */
  const { campaign, receivedData, status } = useSelector(
    (state) => state.campaign
  );
  const { tab } = useSelector((state) => state.dashboard);
  const [isBtnLoading, setIsBtnLoading] = React.useState(false);
  /**
   * @type {[boolean, React.Dispatch<React.SetStateAction<boolean>>]
   * @description This state is used to toggle the menu
   */
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);

  // const {campaigns} = useSelector(state=>state.campaigns);
  const campaignsTab = useSelector((state) => state.campaigns.campaignsTab);
  /** @type {import("../../../../../../jsDocs/redux/store").Store['auth']} */
  const { isAuthenticated, user } = useSelector((state) => state.auth);
  let { campaigns } = useSelector((state) => state.campaigns?.campaignsData);

  /** This function handle the api request when updating or deleting the api
   * @param {string} tab - The tab to find the status of the campaign i.e "creating" or "updating"
   */
  let { username } = user;

  const handleBtnClick = async (tab) => {
    let validateCampaign = Helpers.validateCampaign(campaign);

    if (!validateCampaign.success) {
      const errors = Object.values(validateCampaign.data);
      return Alert(errors[0], "error");
    }
    const goToHomePage = () => {
      dispatch(setCampaignsTab("viewCampaigns"));
    };
    let preparedCampaign = Helpers.prepareCampaign(campaign, status);

    setIsBtnLoading(true);
    try {
      if (tab === "createCampaign") {
        // Check if ruleset ID already exists in campaigns
        const existingRuleset = campaigns.find(
          (camp) => camp && camp.rulesetId == preparedCampaign.rulesetId
        );

        if (existingRuleset) {
          setIsBtnLoading(false);
          return Alert(
            "A ruleset Id with this name already exists. Please choose a different ruleset name.",
            "error"
          );
        }
        let response = await CampaignService.createCampaign(
          JSON.stringify(preparedCampaign)
        );

        if (!response.success) {
          // If data is only the string
          if (response.data && typeof response.data === "string") {
            response.data = {
              errors: [response.data],
            };
          }

          // If have the errors in the response then show the errors
          if (response.data && Array.isArray(response.data.errors)) {
            let errors = response.data.errors;

            // _______ How the error in the modal ________

            const confirmationId = registerConfirmation((confirmed) => {});

            dispatch(
              openDialog({
                title: "Failed to create rule set",
                message: (
                  <ul>
                    {errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                ),
                confirmationId,
              })
            );

            return;

            // ___________________________________________

            // return Alert(`Failed to create campaign \n ${errors}`, "error");
          }

          return Alert("Failed to create rule set", "error");
        }
        Alert("Rule Set created successfully", "success");
        return goToHomePage();
      }
      if (tab === "updateCampaign") {
        let response = await CampaignService.updateCampaign(
          JSON.stringify(preparedCampaign)
        );

        if (!response.success) {
          return Alert("Failed to update rule set", "error");
        }
        Alert("Rule Set updated successfully", "success");
        return goToHomePage();
      }
      return dispatch(setCampaignsTab(tab));
    } catch (error) {
      return Alert(
        `Failed to update ${
          status === "creating" ? "create" : "update"
        } rule set!`,
        "success"
      );
    } finally {
      setIsBtnLoading(false);
    }
  };

  const handleLogout = () => {
    dispatch(logoutSuccess());
    dispatch(setGlobalTab("campaigns"));
    navigate("/login");
  };

  const handleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  // --------- Back to the campagins btn handler ------------

  const handleBackToCampaigns = () => {
    const confirmationId = registerConfirmation((confirmed) => {
      if (confirmed) {
        dispatch(setCampaignsTab("viewCampaigns"));
      }
    });

    dispatch(
      openDialog({
        title: "Navigate to the rule set",
        message:
          "Are you sure you want to navigate to the rule set? Any unsaved changes will be lost.",
        confirmationId,
      })
    );
  };

  // --------------------------------------------

  // ________ List of buttons in the account section ________

  const accountItems = [
    ...(username == "admin"
      ? [
          {
            text: "User Management",
            icon: "ManageAccounts",
            id: "userManagement",
          },
        ]
      : []),
    ...(username == "admin"
      ? [{ text: "API Keys", icon: "PersonAdd", id: "apiKeys" }]
      : []),
    { text: "Profile", icon: "AccountCircle", id: "accountManagement" },
    { text: "Logout", icon: "Logout", id: "logout" },
  ];

  // _______________________________________________________

  const handleMenuBtnClick = (id) => {
    if (id === "logout") {
      handleLogout();
    } else {
      dispatch(setGlobalTab(id));
    }
    setIsMenuOpen(false);
  };

  return (
    <Box
      sx={{
        flexGrow: 1,
        display: "flex",
        justifyContent: "space-between",

        alignItems: "center",
        width: "100%",
      }}
    >
      <Box
        sx={{
          borderRight: "1px solid #ddd",
          borderBottom: "2px solid #ff8860",
          height: "100%",
          py: 1,
          px: 1,

          cursor: "pointer",
          alignItems: "center",
          justifyContent: "flex-center",
          alignContent: "center",
          width: "160px",
          textAlign: "center",
        }}
        onClick={() => dispatch(setCampaignsTab("viewCampaigns"))}
      >
        <TypographyAtom
          text={<strong>Ruleforge</strong>}
          type="heading"
          variant={"body1"}
          sx={{ mb: 1 }}
        />
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "flex-center",
            alignContent: "center",
          }}
        >
          {/* <TypographyAtom
            text="Powered by RuleForge"
            type="paragraph"
            variant={"body2"}
          /> */}
          <Version key={"version"} />
          {/* <DynamicIcon sx={{ ml: 1 }} iconName="ArrowForwardIos" size={10} /> */}
        </Box>
      </Box>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 2, // Add spacing between buttons
          pr: { xs: 2, md: 3 }, // Responsive right padding
        }}
      >
        {campaignsTab === "createCampaign" && (
          <>
            <Btn
              text="Back to Rule Set"
              onClick={handleBackToCampaigns}
              sx={{
                zIndex: 999999,
                backgroundColor: "primary.main",
              }}
            />
            <Btn
              isLoading={isBtnLoading}
              text={status === "creating" ? "Save" : "update"}
              onClick={() =>
                handleBtnClick(
                  status === "creating" ? "createCampaign" : "updateCampaign"
                )
              }
              sx={{ backgroundColor: "primary.secondary", width: "100px" }}
            />
          </>
        )}
        <Box
          sx={{
            position: "relative",
            cursor: "pointer",
            borderRadius: "50%",
            ml: { xs: 1, md: 2 }, // Responsive left margin
          }}
        >
          <Box
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: { xs: 35, md: 40 }, // Responsive width
              height: { xs: 35, md: 40 }, // Responsive height
              borderRadius: "50%",
              backgroundColor: "#f5f5f5",
              "&:hover": {
                backgroundColor: "primary.main",

                "& svg": {
                  // Add this to target the icon
                  color: "mainColor.main",
                },
              },
            }}
          >
            <DynamicIcon
              iconName="Person"
              sx={{
                fontSize: { xs: 20, md: 24 }, // Responsive font size
                color: "primary.main",
                "&:hover": {
                  color: "mainColor.main",
                },
              }}
            />
          </Box>

          {isMenuOpen && (
            <Box
              sx={{
                position: "absolute",
                top: "100%",
                right: 0,
                backgroundColor: "mainColor.main",
                borderRadius: "5px",
                boxShadow: "0px 0px 10px 0px #ddd",
                zIndex: 10000,
              }}
            >
              <List>
                {accountItems.map((item) => (
                  <ListItem
                    key={item.id}
                    component="button"
                    onClick={() => handleMenuBtnClick(item.id)}
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      backgroundColor: "mainColor.main",

                      "&:hover": {
                        backgroundColor: "primary.main",
                        color: "mainColor.main",
                        "& .MuiListItemIcon-root": {
                          color: "mainColor.main",
                        },
                        "& .MuiTypography-root": {
                          color: "mainColor.main",
                        },
                      },
                      padding: "10px 20px",
                      cursor: "pointer",
                      border: "none",
                    }}
                  >
                    <DynamicIcon
                      iconName={item.icon}
                      sx={{ fontSize: 20, marginRight: 2 }}
                    />
                    <TypographyAtom text={item.text} />
                  </ListItem>
                ))}
              </List>
            </Box>
          )}
        </Box>

        {/* {isAuthenticated && campaignsTab === "viewCampaigns" && (
          <Btn
            text="Logout"
            onClick={handleLogout}
            sx={{ zIndex: 999999, ml: 1, backgroundColor: "red" }}
          />
        )} */}
      </Box>
    </Box>
  );
};

export default CampaignsDashboardHeader;
