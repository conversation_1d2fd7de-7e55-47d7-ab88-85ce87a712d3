import { Box } from "@mui/material";
import DropdownAtom from "../../../../../global/atoms/dropdown/Dropdown";
import React, { useState, useEffect, Component } from "react";
import { Grid2 as Grid } from "@mui/material";
import { Helpers } from "../../../../../../utils/generalFunctions/index";
import Chart from "../../../../../global/atoms/charts/Chart";
import TabMolecule from "../../../../../../components/global/molecules/tabs/Tabs";
import { IconBtnAtom } from "../../../../../global/atoms/buttons/IconBtn";
import DynamicIcon from "../../../../../global/atoms/icons/Index";

const timeOptions = [
  { label: "Daily", value: "daily" },
  { label: "Weekly", value: "weekly" },
  { label: "Monthly", value: "monthly" },
  { label: "Yearly", value: "yearly" },
];

const graphOptions = [
  { label: "Bar", value: "Bar" },
  // { label: "Doughnut", value: "Doughnut" },
  // { label: "Polar Area", value: "PolarArea" },
  { label: "Line", value: "Line" },
];

/**
 * The `AnalyticsGraphOrganizer` component organizes and visualizes analytics data in chart form.
 * It allows users to switch between different time periods (e.g., daily, weekly) and chart types (e.g., line, bar).
 *
 * @component
 * @example
 * const data = [
 *   {
 *     name: "Airtime Revenue",
 *     fields: [
 *       { name: "Revenue", prefix: "$" },
 *       { name: "Transactions" },
 *     ],
 *     data: [
 *       { timestamp: "2024-11-01T13:07:12Z", values: [1000, 500] },
 *       { timestamp: "2024-11-02T13:07:12Z", values: [1200, 550] },
 *     ],
 *   },
 * ];
 * return <AnalyticsGraphOrganizer data={data} />;
 *
 * @param {Object} props - The component props.
 * @param {Object[]} props.data - Array of analytics data objects.
 *
 * @param {string} props.data[].name - The name of the analytics metric.
 * @param {Object[]} props.data[].fields - The fields for the metric.
 * @param {string} props.data[].fields[].name - The name of the field.
 * @param {string} [props.data[].fields[].prefix] - Optional prefix for the field (e.g., "$").
 * @param {Object[]} props.data[].data - Array of timestamped data values.
 * @param {string} props.data[].data[].timestamp - The timestamp of the data point (ISO 8601 format).
 * @param {number[]} props.data[].data[].values - Array of values corresponding to the fields.
 * @param {Function} props.onBackBtnClick - The function to handle the back button click.
 *
 * @returns {JSX.Element} The JSX element for the `AnalyticsGraphOrganizer` component.
 */
const AnalyticsGraphOrganizer = ({ data, onBackBtnClick }) => {
  const [selectedTime, setSelectedTime] = useState("daily");
  const [selectedChart, setSelectedChart] = useState("Bar");
  const [chartDataArray, setChartDataArray] = useState([]);
  const [selectedTabChartData, setSelectedTabChartData] = useState(0);

  const onTimeChange = (_, value) => {
    setSelectedTime(value);
  };

  const onChartChange = (_, value) => {
    setSelectedChart(value);
  };

  // Function to aggregate and prepare chart data based on selected time period
  const generateAggregatedChartData = (data, period) => {
    const aggregatedData = Helpers.aggregateData(data, period);
    

    return aggregatedData.map((item) => {
      const dateLabel = item.x_axis; // Use timestamp as the label for daily, weekly, monthly, yearly
      return {
        label: Helpers.convertCase(item.name, "title"),
        // data: `${item.label} ${item.value}` ,
        data: item.value,
        dateLabel,
        prefix: item.prefix,
        valueWithPrefix: item.valueWithPrefix,
      };
    });
  };

  // Function to create the final chart data structure based on the aggregated data
  const generateChartData = (aggregatedData) => {
    const labels = aggregatedData.map((item) => item.dateLabel);
    const datasets = [
      {
        label: aggregatedData[0].label, // Use the name from the first item
        data: aggregatedData.map((item) => item.data),

        // Change the Background color based on Analytics if it goes up i.e green, goes down i.e red, stays the same i.e yellow
        backgroundColor: function (context) {
          const index = context.dataIndex; // Get the index of the current data point
          const dataset = context.dataset.data; // Access the dataset array

          const tolerance = 0.01; // Define tolerance percentage (5% of the previous value)

          // Handle the first data point
          if (index === 0) {
            return "rgba(255, 206, 86, 0.6)"; // Default to yellow for the first value
          }

          const currentValue = dataset[index];
          const previousValue = dataset[index - 1]; // Access the previous value

          const relativeChange =
            Math.abs(currentValue - previousValue) / previousValue;

          // Determine color based on trend with tolerance
          if (relativeChange <= tolerance) {
            return "rgba(255, 206, 86, 0.7)"; // Yellow for steady values
          } else if (currentValue > previousValue) {
            return "rgba(46, 195, 32, 0.7)"; // Green for upward trend
            // return "rgba(75, 192, 192, 0.8)"; // Green for upward trend
          } else {
            return "rgba(255, 99, 132, 0.7)"; // Red for downward trend
          }
        },
        borderColor: function (context) {
          const index = context.dataIndex; // Get the index of the current data point
          const dataset = context.dataset.data; // Access the dataset array

          const tolerance = 0.01; // Define tolerance percentage (5% of the previous value)

          // Handle the first data point
          if (index === 0) {
            return "rgba(255, 206, 86, 1)"; // Default border color for the first value
          }

          const currentValue = dataset[index];
          const previousValue = dataset[index - 1]; // Access the previous value

          const relativeChange =
            Math.abs(currentValue - previousValue) / previousValue;

          // Determine border color based on trend with tolerance
          if (relativeChange <= tolerance) {
            return "rgba(255, 206, 86, 1)"; // Yellow for steady values
          } else if (currentValue > previousValue) {
            return "rgba(75, 192, 192, 1)"; // Green for upward trend
          } else {
            return "rgba(255, 99, 132, 1)"; // Red for downward trend
          }
        },
      },
    ];
    // valueWithPrefix in the tooltip
    const options = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        tooltip: {
          callbacks: {
            label: function (context) {
              const index = context.dataIndex;
              const data = aggregatedData[index];
              return `${data.valueWithPrefix}`;
            },
          },
        },
        legend: {
          position: "top",
        },
        title: {
          display: true,
          text: `${selectedTime}  Data`.toUpperCase(),
        },
      },
      scales: {
        x: {
          grid: {
            display: false, // Removes gridlines for cleaner appearance
          },
        },
        y: {
          grid: {
            color: "#e0e0e0", // Light gray gridlines for subtlety
          },
          ticks: {
            beginAtZero: true,
          },
        },
      },
    };
    return { labels, datasets, options };
  };

  // Update the chart data array whenever the selected time period changes
  useEffect(() => {
    const updatedChartDataArray = data.map((item) => {
      const aggregatedData = generateAggregatedChartData(item, selectedTime);
      return generateChartData(aggregatedData);
    });
    
    setChartDataArray(updatedChartDataArray);
  }, [data, selectedTime]);

  // Format Chart.js options
  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: "top",
      },
      title: {
        display: true,
        text: `${selectedTime} Sales Data`.toUpperCase(),
      },
    },
    scales: {
      x: {
        grid: {
          display: false, // Removes gridlines for cleaner appearance
        },
      },
      y: {
        grid: {
          color: "#e0e0e0", // Light gray gridlines for subtlety
        },
        ticks: {
          beginAtZero: true,
        },
      },
    },
  };

  // Single Chart Template Component @TODO: Move to a  template section
  const SingleChartComponent = ({ cData }) => {
    if (!cData || !cData.labels.length) {
      return <Box>No data available</Box>; // Render placeholder if no data is available
    }
    return (
      <Box
        sx={{
          boxShadow: 2,
          width: "100%",
          p: 2,
          mt: 2,
          minWidth: "500px",
          overflow: "scroll",
        }}
      >
        <Chart data={cData} type={selectedChart} options={cData.options} />
      </Box>
    );
  };

  // Show data in chart based on selected tabs
  // Example of tabs array where each tab has a label and a component
  const tabs = data.map((chartData, index) => {
    // Handle cases where chartDataArray is not ready
    const cData = chartDataArray[index] || { labels: [], datasets: [] };

    return {
      tab: { label: chartData[0].name },
      component: <SingleChartComponent cData={cData} />,
    };
  });

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setSelectedTabChartData(newValue);
  };

  // Return main component
  return (
    <Grid container>
      <Grid container columnSpacing={1} sx={{ width: "100%", display: "flex" }}>
        <Grid size={{ sm: 12, xs: 12 }}>
          <IconBtnAtom
            sx={{ my: 2 }}
            iconParams={{
              iconName: "ArrowBackIos",
              color: "secondary",
              size: 25,
              title: "More Options",
            }}
            onClick={onBackBtnClick}
          />
        </Grid>
        <Grid size={{ sm: 6, xs: 12 }}>
          <DropdownAtom
            options={timeOptions}
            onChange={onTimeChange}
            label="Select Time"
            name="time"
            selectedItem={selectedTime}
            sx={{ width: "100%", mt: { xs: 2, sm: 0 } }}
          />
        </Grid>
        <Grid size={{ sm: 6, xs: 12 }}>
          <DropdownAtom
            options={graphOptions}
            onChange={onChartChange}
            label="Select Chart"
            name="chart"
            selectedItem={selectedChart}
            sx={{ width: "100%", mt: { xs: 2, sm: 0 } }}
          />
        </Grid>
      </Grid>

      <Box sx={{ width: "100%", mt: 3 }}>
        <TabMolecule
          selectedTab={selectedTabChartData} // Currently selected tab
          onTabChange={handleTabChange} // Function to handle tab change
          tabs={tabs} // Array of tabs to be displayed
          orientation="horizontal" // Orientation of the tabs ('horizontal' or 'vertical')
          variant="scrollable" // Variant of the tabs ('scrollable', 'standard', or 'fullWidth')
          indicatorColor="secondary" // Color of the tab indicator
          textColor="inherit" // Color of the tab text
          tabSx={{ minWidth: "150px" }} // Styles for the tab
          tabContentSx={{ minHeight: "200px" }} // Styles for the tab content
        />
      </Box>
    </Grid>
  );
};

export default AnalyticsGraphOrganizer;
