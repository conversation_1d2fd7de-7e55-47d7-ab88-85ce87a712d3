import React, { useState } from "react";
import PropTypes from "prop-types";
import { Box, IconButton, CircularProgress } from "@mui/material";
import { Typography<PERSON>tom } from "../../../../../global/atoms/typography/Typography";
import DynamicIcon from "../../../../../global/atoms/icons/Index";
import CampaignService from "../../../../../../Api/CampaignService";
// import { Alert } from "../../../../../../utils/index";
/**
 * ListManagement Component
 *
 * A React component that displays a single management item in a row format with actions such as delete.
 * Provides a loading effect when performing actions.
 *
 * @component
 * @example
 * const item = { name: "User Management", dataType: "String" };
 * const index = 0;
 * const managementList = [{ name: "User Management", dataType: "String" }];
 * const setManagementList = (updatedList) => console.log(updatedList);
 * return <ListManagement item={item} index={index} managementList={managementList} setManagementList={setManagementList} />;
 *
 * @param {Object} props - Component props.
 * @param {Object} props.item - The management item to display.
 * @param {string} props.item.name - The name of the management item.
 * @param {string} props.item.dataType - The data type of the management item.
 * @param {number} props.index - The index of the item in the list.
 * @param {Array} props.managementList - The current list of management items.
 * @param {Function} props.setManagementList - The function to update the management list.
 *
 * @returns {JSX.Element} The rendered ListManagement component.
 */

const ListManagement = ({ item, index, managementList, setManagementList }) => {
  /**
   * @state {boolean} loading - Tracks whether the delete operation is in progress.
   */
  const [loading, setLoading] = useState(false);

  /**
   * Handles the deletion of the current management item.
   * Simulates a delay to show the loading effect before removing the item from the list.
   */
  const handleDelete = async () => {
    try {
      setLoading(true);
      // Call the API to delete the item
      const response = await CampaignService.deleteCampaignList(item.id, 0);
      if (!response.success) {
        // Alert("Failed to delete management item", "error");
        setLoading(false);
      }
      /**
       * Updates the management list by filtering out the item at the specified index.
       */
      const updatedList = managementList.filter((_, i) => i !== index);
      setManagementList(updatedList);
    } catch (error) {
      console.error("Error deleting management item:", error);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        position: "relative",
        display: "grid",
        gridTemplateColumns: "1fr 1fr 1fr",
        gap: 2,
        textAlign: "left",
        padding: "8px 16px",
        alignItems: "center",
        backgroundColor: index % 2 === 0 ? "#ffffff" : "#f9f9f9", // Alternating row colors
        borderBottom: "1px solid #ddd",
      }}
    >
      {/* Loading Overlay */}
      {loading && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundColor: "rgba(255,255,255,0.8)", // Semi-transparent white overlay
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <CircularProgress color="inherit" />
        </Box>
      )}

      {/* Management Item Name */}
      <TypographyAtom variant="body1" text={item.name} />

      {/* Management Item Data Type */}
      <TypographyAtom variant="body1" text={item.dataType} />

      {/* Delete Button */}
      <IconButton
        sx={{ justifySelf: "start" }}
        color="error"
        onClick={handleDelete}
        aria-label="Delete"
      >
        <DynamicIcon iconName="Delete" size={20} />
      </IconButton>
    </Box>
  );
};

ListManagement.propTypes = {
  /**
   * @prop {Object} item - The management item to display.
   * @prop {string} item.name - The name of the management item.
   * @prop {string} item.dataType - The data type of the management item.
   */
  item: PropTypes.shape({
    name: PropTypes.string.isRequired,
    dataType: PropTypes.string.isRequired,
  }).isRequired,

  /**
   * @prop {number} index - The index of the item in the list.
   */
  index: PropTypes.number.isRequired,

  /**
   * @prop {Array} managementList - The current list of management items.
   */
  managementList: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      dataType: PropTypes.string.isRequired,
    })
  ).isRequired,

  /**
   * @prop {Function} setManagementList - Function to update the management list.
   */
  setManagementList: PropTypes.func.isRequired,
};

export default ListManagement;
