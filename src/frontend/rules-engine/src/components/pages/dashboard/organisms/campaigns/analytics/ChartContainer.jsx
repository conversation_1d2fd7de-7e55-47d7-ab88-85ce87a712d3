// ChartContainer.jsx
import React, { useRef, useEffect, useCallback } from 'react';
import {
    Box,
    Card,
    CardContent,
    Typography,
    CircularProgress,
    Alert
} from '@mui/material';
import { Line, Bar } from 'react-chartjs-2';
import { Typo<PERSON><PERSON>tom } from '../../../../../global/atoms/typography/Typography';
import { Helpers } from '../../../../../../utils/generalFunctions/index';


/**
 * ChartContainer Component
 * 
 * Handles chart rendering and scroll-based pagination
 * 
 * @component
 * @param {Object} props - Component props
 * @param {Object} props.chartData - Chart data for rendering
 * @param {boolean} props.loading - Loading state
 * @param {string} props.error - Error message
 * @param {string} props.chartType - Chart type ('line' or 'bar')
 * @param {Function} props.shouldUseAutoTicks - Tick strategy function
 * @param {Object} props.pagination - Pagination object
 * @param {Object} props.paginationInfo - Pagination info from API
 * @param {Function} props.onLoadMore - Load more data callback
 * @param {Object} props.scrollState - Scroll state
 * @param {Function} props.onScrollStateChange - Scroll state change handler
 * @returns {JSX.Element} The rendered chart container component
 */
const ChartContainer = ({
    chartData,
    loading,
    error,
    chartType,
    shouldUseAutoTicks,
    pagination,
    paginationInfo,
    onLoadMore,
    scrollState,
    onScrollStateChange,
    isLoadingMore,
    lastRequestTimeRef
}) => {
    const chartRef = useRef(null);

    // Generate chart options
    const chartOptions = Helpers.generateChartOptions(chartType, shouldUseAutoTicks, pagination);

    // ✅ Debounced scroll handler
    useEffect(() => {
        const chartContainer = document.querySelector('.chart-scroll-container');
        if (!chartContainer) return;

        let scrollTimeout;

        const handleScroll = () => {
            // ✅ Clear previous timeout to debounce
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }

            // ✅ Debounce scroll events by 200ms
            scrollTimeout = setTimeout(() => {
                const { scrollLeft, scrollWidth, clientWidth } = chartContainer;
                const scrollPercentage = (scrollLeft + clientWidth) / scrollWidth;

                onScrollStateChange(prevState => {
                    const isScrollingRight = scrollLeft > prevState.lastScrollLeft;

                    // ✅ Simple loading condition with better state checks
                    const shouldLoadMore = (
                        isScrollingRight &&
                        scrollPercentage > 0.9 &&
                        !loading &&
                        !isLoadingMore
                    );

                    if (shouldLoadMore) {
                        onLoadMore(chartType);
                    }

                    return {
                        lastScrollLeft: scrollLeft,
                        isScrollingRight: isScrollingRight
                    };
                });
            }, 200); // ✅ 200ms debounce
        };

        chartContainer.addEventListener('scroll', handleScroll, { passive: true });

        return () => {
            chartContainer.removeEventListener('scroll', handleScroll);
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
        };
    }, [onLoadMore, onScrollStateChange, chartType, loading, isLoadingMore]);



    return (
        <Card sx={{ paddingBottom: '40px' }}>
            <CardContent sx={{ position: 'relative' }}>
                {error && (
                    <Alert severity="error" sx={{ mb: 2 }}>
                        {error}
                    </Alert>
                )}

                {chartData && chartData.datasets.length > 0 ? (
                    <Box sx={{
                        height: '500px',
                        position: 'relative',
                        overflowX: 'auto',
                        overflowY: 'hidden',
                        border: '1px solid #ddd',
                        borderRadius: 1,
                        // Add scroll indicators
                        '&::-webkit-scrollbar': {
                            height: 8,
                        },
                        '&::-webkit-scrollbar-track': {
                            background: '#f1f1f1',
                        },
                        '&::-webkit-scrollbar-thumb': {
                            background: '#888',
                            borderRadius: 4,
                        },
                        '&::-webkit-scrollbar-thumb:hover': {
                            background: '#555',
                        }
                    }}
                        className="chart-scroll-container"  // Add class for scroll detection
                    >
                        {/* Dynamic width based on data points for smooth scrolling */}
                        <Box sx={{
                            height: '100%',
                            minWidth: '1200px',
                            width: `${Math.max(1200, (chartData.labels.length * 120))}px`,
                        }}>
                            {chartType === 'line' ? (
                                <Line
                                    key={`line-chart-${chartType}-${chartData?.labels?.length || 0}-${pagination.page}`}
                                    ref={chartRef}
                                    data={chartData}
                                    options={chartOptions}
                                />
                            ) : (
                                <Bar
                                    key={`bar-chart-${chartType}-${chartData?.labels?.length || 0}-${pagination.page}`}
                                    ref={chartRef}
                                    data={chartData}
                                    options={chartOptions}
                                />
                            )}
                        </Box>
                    </Box>
                ) : !loading && (
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                        <TypographyAtom
                            variant="body1"
                            text="No data available, please try different filters"
                            sx={{ color: 'text.secondary' }}
                        />
                    </Box>
                )}

                {/* Scroll instruction */}
                <Box sx={{
                    position: 'absolute',
                    bottom: -25,
                    left: 10,
                    background: 'rgba(0,0,0,0.9)',
                    px: 2,
                    py: 1,
                    borderRadius: 1,
                    fontSize: '0.75rem',
                    // Use MUI theme palette for color to ensure proper application
                    color: (theme) => theme.palette.common.white,
                }}>
                    <TypographyAtom
                        variant="body2"
                        text={`Scroll horizontally for more data • ${pagination.page}/${paginationInfo?.total_pages || 1} pages loaded`}
                        sx={{ color: 'common.white' }}
                    />
                </Box>
            </CardContent>
        </Card>
    );
};

export default ChartContainer;