import { Bar } from 'react-chartjs-2';


const records = [
    {
        "timestamp": "2025-07-02T09:55:00.000Z",
        "variable_id": "per3",
        "value_numeric": 120
    },
    {
        "timestamp": "2025-07-02T09:55:00.000Z",
        "variable_id": "per4",
        "value_numeric": 150
    },
    {
        "timestamp": "2025-07-02T09:55:00.000Z",
        "variable_id": "per2",
        "value_numeric": 110
    },
    {
        "timestamp": "2025-07-02T09:55:00.000Z",
        "variable_id": "per1",
        "value_numeric": 30
    },
    {
        "timestamp": "2025-06-26T13:05:00.000Z",
        "variable_id": "per4",
        "value_numeric": 1800
    },
    {
        "timestamp": "2025-06-26T12:05:00.000Z",
        "variable_id": "per3",
        "value_numeric": 110
    },
    {
        "timestamp": "2025-06-26T12:05:00.000Z",
        "variable_id": "per4",
        "value_numeric": 1500
    },
    {
        "timestamp": "2025-06-26T11:05:00.000Z",
        "variable_id": "per1",
        "value_numeric": 85
    },
    {
        "timestamp": "2025-06-26T11:05:00.000Z",
        "variable_id": "per4",
        "value_numeric": 1000
    },
    {
        "timestamp": "2025-06-26T10:10:00.000Z",
        "variable_id": "per2",
        "value_numeric": 400
    },
    {
        "timestamp": "2025-06-26T10:10:00.000Z",
        "variable_id": "per3",
        "value_numeric": 160
    },
    {
        "timestamp": "2025-06-26T10:05:00.000Z",
        "variable_id": "per1",
        "value_numeric": 75
    },
    {
        "timestamp": "2025-06-26T09:05:00.000Z",
        "variable_id": "per2",
        "value_numeric": 350
    },
    {
        "timestamp": "2025-06-26T08:05:00.000Z",
        "variable_id": "per3",
        "value_numeric": 60
    },
    {
        "timestamp": "2025-06-26T08:05:00.000Z",
        "variable_id": "per2",
        "value_numeric": 100
    },
    {
        "timestamp": "2025-06-26T07:05:00.000Z",
        "variable_id": "per1",
        "value_numeric": 20
    }
];



// ======================


// const records = [];

// const baseRecords = [
//   { variable_id: "per1", value_numeric: 20 },
//   { variable_id: "per2", value_numeric: 100 },
//   { variable_id: "per3", value_numeric: 60 },
//   { variable_id: "per2", value_numeric: 350 },
//   { variable_id: "per1", value_numeric: 75 },
//   { variable_id: "per3", value_numeric: 160 },
//   { variable_id: "per2", value_numeric: 400 },
//   { variable_id: "per4", value_numeric: 1000 },
//   { variable_id: "per1", value_numeric: 85 },
//   { variable_id: "per4", value_numeric: 1500 },
//   { variable_id: "per3", value_numeric: 110 },
//   { variable_id: "per4", value_numeric: 1800 },
//   { variable_id: "per1", value_numeric: 30 },
//   { variable_id: "per2", value_numeric: 110 },
//   { variable_id: "per4", value_numeric: 150 },
//   { variable_id: "per3", value_numeric: 120 },
// ];

// // Create timestamps from 2025-06-20 08:00:00Z, spaced every 10 minutes
// const startTime = new Date("2025-06-20T08:00:00.000Z").getTime();

// for (let i = 0; i < 100; i++) {
//   const timestamp = new Date(startTime + i * 10 * 60 * 1000).toISOString(); // every 10 minutes
//   const set = baseRecords.map((r, idx) => ({
//     timestamp,
//     variable_id: r.variable_id,
//     value_numeric: Math.round(r.value_numeric * (0.8 + Math.random() * 0.4)), // small variation
//   }));

//   records.push(...set);
// }


// =======================

const includeMicroseconds = false; // Toggle this as needed

const colors = [
    'rgba(255, 99, 132, 0.7)',   // red
    'rgba(54, 162, 235, 0.7)',   // blue
    'rgba(255, 206, 86, 0.7)',   // yellow
    'rgba(75, 192, 192, 0.7)',   // green
    'rgba(153, 102, 255, 0.7)',  // purple
    'rgba(255, 159, 64, 0.7)'    // orange
  ];

function formatTimestamp(ts) {
    // Example ts: "2025-07-02T09:55:00.000Z"
    const [datePart, timePartRaw] = ts.split('T');
    const [year, month, day] = datePart.split('-');
    const [timePart, msZ] = timePartRaw.split('.');
    const time = timePart; // "09:55:00"
    const micros = msZ?.replace('Z', '') || '000';

    let formatted = `${month}-${day} ${time}`;
    if (includeMicroseconds) {
        formatted += `.${micros}`;
    }

    return formatted;
}

// 1. Get unique timestamps (as-is, no formatting)
const timestamps = [...new Set(records.map(r => r.timestamp))].sort();


// 2. Get unique variable_ids
const variableIds = [...new Set(records.map(r => r.variable_id))];

// 3. Use raw timestamps for labels
const labels = timestamps.map(formatTimestamp);

// 4. Construct the data object
const dataSets = timestamps.map(ts => {
    const record = records.find(r => r.timestamp === ts);
    return record ? record.value_numeric : 0;
});

console.log("DataSets",dataSets);

// 4. Build datasets per variable
const datasets = variableIds.map((variableId, index) => ({
    label: variableId,
    data: dataSets,
    backgroundColor: colors[index % colors.length], // cycle colors
}));


const data = {
    labels,
    datasets,
};

const options = {
    responsive: true,
    scales: {
      x: {
        ticks: {
          callback: function(value, index, ticks) {
            return this.getLabelForValue(value);
          },
          autoSkip: false,
          maxRotation: 0,
          minRotation: 0,
        }
      },
      y: {
        beginAtZero: true,
      }
    },
    plugins: {
        tooltip: {
            mode: 'index',
            intersect: false,
            filter: function(tooltipItem) {
              return tooltipItem.raw !== 0;
            }
          },
      legend: {
        position: 'top',
      },
    },
  };

export default function BarChart() {
    return <Bar data={data} options={options} />;
}
