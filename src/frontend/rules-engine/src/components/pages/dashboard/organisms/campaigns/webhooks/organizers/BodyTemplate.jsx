import { Box, Grid2 as Grid } from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../../../global/atoms/typography/Typography";
import React, { useState, useEffect } from "react";
import { DropdownAtom } from "../../../../../../global/atoms/dropdown/Dropdown";
import { InputField } from "../../../../../../global/atoms/inputFields/InputField";
import { Btn } from "../../../../../../global/atoms/buttons/Button";
import { campaignValidators } from "../../../../../../../utils/validators/campaign";
import { Helpers } from "../../../../../../../utils/index";
import CategoryDropdown from "../../../../../../global/atoms/dropdown/CategoryDropdown";



const BodyTemplateOrganizer = ({ tab, handleCancel, handleSave }) => {

  const [inputs, setInputs] = useState(tab.data);

  const handleInputChange = (e,validation) => {
    const { name, value } = e.target;
    setInputs((prev) => ({
      ...prev, // Spread the previous state
      [name]: {
        value,
        isValid: validation.success,
        error: validation.success ? '' : validation.message,
        isShowError: !validation.success,
      },
    }));
  };

  const handleCategoryDropdownChange = (name,value) => {
   
   
    setInputs((prev) => ({
      ...prev,
      [name]: {
        ...prev[name],
        isValid: true,
        ...value,
      },
    }));
  };


  const handleSaveBtnClick = () => {
   
    let trimmedFields = Helpers.trimObjectValues(inputs);
    let validationResult = campaignValidators.global.isValidAllFields(trimmedFields);
    if (validationResult.success) {

      let isExist = tab.bodyTemplate.find((item)=>item.data.key.value.toLowerCase() === inputs.key.value.toLowerCase() && item.id !== tab.id);
      //Helpers.isValueExist({value:inputs.key.value,inputsData:tab.bodyTemplate.map((item)=>item.data.key.value)});
      if(isExist){
        return setInputs((prev) => ({
          ...prev, // Spread the previous state
          key: {
            value:inputs.key.value,
            isValid: false,
            error: "Key already exists",
            isShowError: true,
          },
        }));
      }
   
      handleSave(tab.id, trimmedFields);
    } else {
      // If there are validation errors, update the inputs state to show errors
      const updatedInputs = { ...inputs };

      // Iterate over the errors array and update the state
      validationResult.errors.forEach(({ field, error }) => {
        updatedInputs[field] = {
          ...updatedInputs[field],
          isValid: false, // Set field as invalid
          isShowError: true, // Show error message
          error: error.charAt(0).toUpperCase() + error.slice(1), // Set error message with capitalized first letter
        };
      });

      // Update the inputs state with the new validation statuses
      setInputs(updatedInputs);
    }
  };


  return (
    <Grid container spacing={2}>
     
      <Grid item size={{ md: 6, xs: 12 }}>
        <InputField
          label="Key"
          value={inputs.key?.value}
          name="key"
          onChange={handleInputChange}
          type="text"
          validation={{
            isShowError: inputs.key?.isShowError,
            error: inputs.key?.error,
          }}
          validator={campaignValidators.webhooks.bodyTemplate.key}
          placeholder="Enter Key"
          required
          disabled={false}
          fullWidth={true}
          sx={{ width: "100%" }}
        />
      </Grid>
     
      <Grid item size={{ md: 6, xs: 12 }}>
       <CategoryDropdown
       label="Value"
       name="value"
       value={inputs.value}
      //  onChange={handleCategoryDropdownChange}
      //  onClick={handleCategoryDropdownChange}
       onChangeFormated={handleCategoryDropdownChange}
       categories={tab.parameters.length > 0 ? [{category: "Parameters", options: tab.parameters}] : []}
       /> 
      </Grid>


      <Grid item size={{ md: 12, xs: 12 }}>
        <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
          <Btn
            text="Cancel"
            sx={{
              width: "100%",
              maxWidth: "200px",
              backgroundColor: "danger.main",
            }}
            onClick={() => handleCancel(tab.id)}
          />
          <Btn
            text="Save"
            sx={{
              width: "100%",
              maxWidth: "200px",
              backgroundColor: "primary.secondary",
            }}
            onClick={handleSaveBtnClick}
          />
        </Box>
      </Grid>
    </Grid>
  );
};

export default BodyTemplateOrganizer;
