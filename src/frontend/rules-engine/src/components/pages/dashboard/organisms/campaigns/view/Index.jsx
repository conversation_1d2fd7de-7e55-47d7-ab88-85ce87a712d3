import { Box, Container, Typography, useMediaQuery } from "@mui/material";
// import DropdownAtom from "../../../../../global/atoms/dropdown/Dropdown";
// import { useState } from "react";
// import TabMolecule from "../../../../../../global/molecules/Tabs";
// // import CampaignsListOrganizer from "../organisms/CampaignsList";
// import PaginationAtom from "../../../../../global/atoms/pagination/Index";

import PropTypes from "prop-types";
import CampaignItem from "../../../molecules/campaigns/view/SingleCampaign";
import { TypographyAtom } from "../../../../../global/atoms/typography/Typography";
import { useTheme } from "@emotion/react";
import React from "react";

/**
 * Dashboard Organizer component for rendering a list of campaigns.
 *
 * @component
 * @param {Object} param0 - Component props.
 * @param {Object[]} param0.campaigns - Array of campaign objects.
 * @param {Function} param0.getCampaigns - Function to refetch the campaigns.
 * @returns {JSX.Element} The rendered DashboardOrganizer component.
 */
// const CampaignsListOrganizer = ({ campaigns, getCampaigns }) => {
// @ts-ignore
const CampaignsListOrganizer = ({ campaigns, getCampaigns }) => {
  return (
    <Box>
      {campaigns && campaigns.length > 0 ? (
        campaigns.map(
          (campaign, index) =>
            campaign ? ( // Add a condition to render only valid campaigns
              <CampaignItem
                key={index}
                campaign={campaign}
                getCampaigns={getCampaigns}
                // onOptionsClick={() => onOptionsClick(campaign)}
              />
            ) : null // Render nothing if the campaign is invalid
        )
      ) : (
        <TypographyAtom variant="body2" text="No rulesets available" />
      )}
    </Box>
  );
};

CampaignsListOrganizer.propTypes = {
  campaigns: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      entities: PropTypes.arrayOf(PropTypes.object).isRequired,
      startDateTime: PropTypes.string.isRequired, // Assuming date is in string format
      endDateTime: PropTypes.string.isRequired,
      lastModifiedDateTime: PropTypes.string.isRequired,
    })
  ).isRequired,
  // onOptionsClick: PropTypes.func.isRequired, // Callback for the options button click
};

export default CampaignsListOrganizer;
