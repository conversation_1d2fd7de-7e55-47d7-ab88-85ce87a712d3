// AnalyticsChart.jsx (Refactored Main Component)
import React, { useState, useEffect, useCallback } from 'react';
import {
    Box,
    CircularProgress,
    Typography
} from '@mui/material';
import {
    Chart as ChartJS,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    Title,
    Tooltip,
    Legend,
    TimeScale,
    Filler
} from 'chart.js';
import 'chartjs-adapter-date-fns';
import useAnalyticsData from '../../../../../../hooks/useAnalyticsData';
import ChartVariableSection from './ChartVariableSection';
import ChartContainer from './ChartContainer';
import { Helpers } from '../../../../../../utils/generalFunctions/index';

// Register Chart.js components
ChartJS.register(
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    BarElement,
    Title,
    Tooltip,
    Legend,
    TimeScale,
    Filler
);

/**
 * AnalyticsChart Component
 * 
 * A comprehensive time-series chart component that displays analytics data
 * with horizontal scrolling and lazy loading capabilities.
 * 
 * @component
 * @param {Object} props - Component props
 * @param {Object} props.filters - Filter criteria for data query
 * @returns {JSX.Element} The rendered analytics chart component
 */
const AnalyticsChart = ({ filters = {} }) => {
    // Chart type state
    // const [chartType, setChartType] = useState('line'); // 'line' or 'bar'
    const [scrollState, setScrollState] = useState({
        lastScrollLeft: 0,
        isScrollingRight: false
    });



    /**
     * Filter-based tick strategy: Auto vs Exact Backend Sync
     * Returns true for auto-tick selection, false for exact backend timestamps
     */
    const shouldUseAutoTicks = useCallback(() => {
        // Case 1: Empty filters - let chart decide
        if (!filters || Object.keys(filters).length === 0) {
            return true;
        }

        // Case 2: time_bucket is null or 1 - let chart decide  
        if (filters.time_bucket === null || filters.time_bucket === 0) {
            return true;
        }

        // Case 3: time_bucket > 1 - use exact backend timestamps
        return false;
    }, [filters]);


    let includeMicroseconds = shouldUseAutoTicks() ? true : false;
    // Use custom hook for data management
    const {
        chartData,
        aggregationData,
        loading,
        error,
        pagination,
        paginationInfo,
        hasMoreData,
        selectedVariables,
        dateRange,
        isLoadingMore,
        fetchTimeSeriesData,
        loadMoreDataProtected,
        colorPalette,
        campaignData,
        lastRequestTimeRef,
        setChartType,
        chartType
    } = useAnalyticsData(filters, includeMicroseconds);

    /**
     * Handle chart type selection change
     * @param {Object} event - The select change event
     */
    const handleChartTypeChange = (value) => {
        setChartType(value);

    };
    /**
     * Handle loading more data with chart type
     */
    const handleLoadMore = useCallback(() => {
        loadMoreDataProtected();
    }, [loadMoreDataProtected]);

    // Initial data load and filter change handler
    useEffect(() => {
        // Check if filters object has any meaningful filter properties (excluding base campaign filters)
        const hasAppliedFilters = Object.keys(filters).some(key =>
            !['entity_id', 'context_id', 'rule_set_id', 'rule_set_version'].includes(key) &&
            filters[key] !== undefined &&
            filters[key] !== null &&
            filters[key] !== ''
        );

        // Only reset data when actual user filters are applied
        fetchTimeSeriesData(hasAppliedFilters);
    }, [filters]);

    // ✅ Reset scroll tracking when filters change (new data set)
    useEffect(() => {
        setScrollState({
            lastScrollLeft: 0,
            isScrollingRight: false
        });
        lastRequestTimeRef.current = 0;
    }, [filters, lastRequestTimeRef]);




    return (
        <Box sx={{ mt: 4 }}>
            {loading && !chartData ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                    <CircularProgress />
                </Box>
            ) : (
                <Box>
                    {/* Variables Section */}
                    <ChartVariableSection
                        aggregationData={aggregationData}
                        selectedVariables={selectedVariables}
                        chartData={chartData}
                        colorPalette={colorPalette}
                        filters={filters}
                        chartType={chartType}
                        onChartTypeChange={handleChartTypeChange}
                    />

                    {/* Chart Container */}
                    <ChartContainer
                        chartData={chartData}
                        loading={loading}
                        error={error}
                        chartType={chartType}
                        shouldUseAutoTicks={shouldUseAutoTicks}
                        pagination={pagination}
                        paginationInfo={paginationInfo}
                        onLoadMore={handleLoadMore}
                        scrollState={scrollState}
                        onScrollStateChange={setScrollState}
                        isLoadingMore={isLoadingMore}
                        lastRequestTimeRef={lastRequestTimeRef}
                    />

                    {/* Loading indicator for lazy loading - Positioned at top center */}
                    {loading && chartData && (
                        <Box sx={{
                            position: 'absolute',
                            bottom: 50,
                            left: '50%',
                            transform: 'translateX(-50%)',
                            zIndex: 10,
                            background: 'rgba(255,255,255,0.95)',
                            borderRadius: 2,
                            p: 2,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                            border: '1px solid',
                            borderColor: 'primary.main'
                        }}>
                            <CircularProgress size={20} />
                            <Typography variant="body2" sx={{ color: 'primary.main', fontWeight: 'medium' }}>
                                Loading more data...
                            </Typography>
                        </Box>
                    )}

                    {/* Date Range Display */}
                    {dateRange && (
                        <Box sx={{
                            p: 2,
                            backgroundColor: 'info.light',
                            borderRadius: 1,
                            border: '1px solid',
                            borderColor: 'info.main',
                            width: 'max-content',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                            mx: 'auto', // Horizontal margin auto for centering
                            mt: 2,
                        }}>
                            <Typography variant="body2" sx={{ color: 'info.contrastText', fontWeight: 'medium' }}>
                                Start Date: {Helpers.formatDateWithoutTimezone(dateRange.startDate)} - End Date: {Helpers.formatDateWithoutTimezone(dateRange.endDate)}
                            </Typography>
                        </Box>
                    )}
                </Box>
            )}
        </Box>
    );
};

export default AnalyticsChart; 