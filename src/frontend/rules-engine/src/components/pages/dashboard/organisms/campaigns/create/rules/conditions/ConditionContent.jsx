import { Box, Grid2 as Grid } from "@mui/material";
import { Btn as Btn<PERSON>tom } from "../../../../../../../global/atoms/buttons/Button";
import { useState } from "react";
import { useSelector } from "react-redux";
import {
  <PERSON><PERSON>,
  campaignValidators,
  Helpers,
} from "../../../../../../../../utils";
import ConditionTypeDropDown from "../../../../../molecules/campaigns/create/ConditionTypeDropDown";
import React from "react";
import ConditionLogicalComponent from "./conditionTypes/ConditionLogicalComponent";
import DynamicConditionComp from "./conditionTypes/DynamicConditionComp";
import { useEffect, useRef } from "react";

// ---------- Sub content hook maping by the condition type ------------

const subContentMap = {
  LOGICAL: ConditionLogicalComponent,
  DYNAMIC: DynamicConditionComp,
};
// let's create a utility function outside the component for parameter initialization
const initializeParameter = (value = "") => ({
  value,
  isValid: value !== undefined && value !== "" ? true : false,
  error: value !== undefined && value !== "" ? "" : "This field is required",
  isShowError: false,
});

// Create a function to get condition item
const getConditionItem = (conditionTypes, type, operator) => {
  const conditionType = conditionTypes?.find(
    (typeObj) => typeObj.value === type
  );
  if (!conditionType) return null;
  // if its operator matches the requested operator
  if (conditionType.operator === operator) {
    return conditionType;
  }

  // If the operator doesn't match, return a modified copy with the requested operator
  return {
    ...conditionType,
    operator: operator,
  };

  // return conditionType?.items?.find(
  //   (item) => item.type === type && item.operator === operator
  // );
};
// -------------------------------------------------------------------------

/**
 * ConditionContentOrganizer component handles all rules-related data and functionality,
 * including the internal hierarchy of component data.
 
 * @param {Object} props - Component props
 * @param {Function} props.handleCancel - Callback function to handle cancellation action.
 * @param {function({type: string, operator: string, parameters: Array.<Object>}) : void} props.handleSave - Callback function to handle saving of the updated data.
 * @param {number} [props.nestedLevel] - The level of the component in the hierarchy because we are using it as recursive component and it can have inifinite levels.
 * @param {Object} [props.tab] - 
 * Object containing data related to the current tab.
 * @param {{entityId: string, contextId: string}} props.entityAndContext - The entity and context IDs.
 * @param {string} [props.ruleType] - The rule type.
 * @returns {JSX.Element} The rendered ActionContentOrganizer component.
 */
const ConditionContentOrganizer = ({
  handleCancel,
  handleSave,
  tab = {},
  entityAndContext,
  nestedLevel = 0,
  ruleType,
}) => {
  // Get the received data from the redux store that is received from the API.
  /** @type {import("../../../../../../../../../../jsDocs/redux/store").Store['campaign']} */
  const { receivedData } = useSelector((state) => state.campaign);
  // If the tab.data did not have the type and operator then we will set the default value of first type and it's first operator. Because on the dropdown the first values are shown
  const type = tab.data?.conditionTypeId
    ? tab.data.conditionTypeId
    : receivedData.conditionTypes[0].value;
  const operator = tab.data?.operator
    ? tab.data.operator
    : receivedData.conditionTypes[0].operators[0].value;
  // Get the parameters from the tab.data i.e from server
  const parameters = tab.data?.parameters ? tab.data.parameters : {};
  const iTabData = { ...tab.data };

  const [data, setData] = useState({
    ...Helpers.defaultValues(["type", "operator"], {
      ...iTabData,
      type,
      operator,
    }),
  });

  const subContentHelperObj = {};

  // Initialize parameter data with any existing values and keep track of current selected parameters
  const [parameterData, setParameterData] = useState(() => {
    const initialData = {};
    if (parameters) {
      Object.keys(parameters).forEach((key) => {
        initialData[key] = {
          value: parameters[key],
          isValid: true,
          error: "",
          isShowError: false,
        };
      });
    }
    return initialData;
  });
  // State to hold parameter definitions i.e. get the parameters array with it's type and name to make it dynamic
  const [parameterDefinitions, setParameterDefinitions] = useState([]);
  // ----------- On Condition type and operator change ---------

  // Ref to store the previous type value in order to check if the operator changed
  const prevTypeRef = useRef(data.type?.value);
  const isJustOperatorChange = useRef(false);

  // Modify the handleConditionDropdownChange function
  const handleConditionDropdownChange = ({ type, operator }) => {
    // Check if only operator changed
    isJustOperatorChange.current = type.value === prevTypeRef.current;
    prevTypeRef.current = type.value;

    setData((prev) => ({
      ...prev,
      type,
      operator,
    }));
  };
  // Memoize the condition item to prevent unnecessary recalculations
  const conditionItem = React.useMemo(() => {
    if (!data.type?.value || !data.operator?.value) return null;
    return getConditionItem(
      receivedData.conditionTypes,
      data.type.value,
      data.operator.value
    );
  }, [data.type?.value, data.operator?.value, receivedData.conditionTypes]);

  // Effect to update parameter definitions and data
  useEffect(() => {
    if (!conditionItem) {
      setParameterDefinitions([]);
      setParameterData({});
      return;
    }

    const { parameters: conditionParameters } = conditionItem;

    if (!conditionParameters?.length) {
      setParameterDefinitions([]);
      setParameterData({});
      return;
    }

    // Update parameter definitions
    setParameterDefinitions(conditionParameters);
    // don't initialize the parameter data if the operator changed or the parameter data is empty
    if (
      !isJustOperatorChange.current ||
      Object.keys(parameterData).length === 0
    ) {
      // Initialize the parameter data to match our expected structure
      const newParamData = conditionParameters.reduce((acc, param) => {
        const paramKey = param.parameterId || param.name;
        const existingValue = tab.data?.parameters?.[paramKey];
        acc[paramKey] = initializeParameter(existingValue);
        return acc;
      }, {});
      setParameterData(newParamData);
    }
  }, [conditionItem, tab.data?.parameters]);

  // Handle parameter value changes
  const handleParameterChange =
    Helpers.handleDropdownChangeFactoryFn(setParameterData);
  /**
   This function validates the fields and pass the user inputs to the upper level component for saving.
   */
  const handleSubmit = () => {
    // ______ Verify the parameters data object _____
    if (data.type.value === "LOGICAL") {
      // Get the parameters data object from the subcontent hook
      const parameters = subContentHelperObj?.getData();

      // Validate all fields using isValidAllFields
      const paramsValdRslt =
        campaignValidators.global.isValidAllFields(parameters);

      // If the validation failed then set the updated object which will auto update the fields with the error messages
      if (!paramsValdRslt.success) {
        return subContentHelperObj.setData(() => paramsValdRslt.updatedData);
      }

      // Get the json schema compatible raw data
      const parametersRaw = Helpers.returnValues(parameters);

      // ______________________________________________

      let rawObj = {
        conditionTypeId: data.type,
        operator: data.operator,
      };

      // Validate all fields using isValidAllFields
      const validationResult =
        campaignValidators.global.isValidAllFields(rawObj);

      if (validationResult.success) {
        let obj = {
          ...Helpers.returnValues(rawObj),
          parameters: parametersRaw,
        };

        handleSave(obj);

        Alert("Condition saved successfully", "success");
      } else {
        // Update the inputs state with the new validation statuses which contains the error messages so the respective fields can show the error messages
        setData(() => validationResult.updatedData);
      }
    } else {
      // For other conditions, validate the parameter data
      const validationResult =
        campaignValidators.global.isValidAllFields(parameterData);

      if (validationResult.success) {
        // Only include parameters that are defined for this condition type
        // Convert the parameters in a format that is accepted by API
        const validParameters = {};
        parameterDefinitions.forEach((paramDef) => {
          const paramKey = paramDef.parameterId || paramDef.name;
          if (parameterData[paramKey]) {
            validParameters[paramKey] =
              parameterData[paramDef.parameterId].value;
          }
        });

        let obj = {
          conditionTypeId: data.type.value,
          operator: data.operator.value,
          parameters: validParameters,
        };

        handleSave(obj);

        Alert("Condition saved successfully", "success");
      } else {
        // Update parameter data with validation errors
        setParameterData(validationResult.updatedData);
      }
    }
  };
  // ------------------------------------------------------------
  const renderConditionContent = () => {
    if (data.type.value === "LOGICAL") {
      const SubContent = subContentMap.LOGICAL;
      return (
        <SubContent
          helperObj={subContentHelperObj}
          key={`${data.type.value}_condition_sub_content`}
          nestedLevel={nestedLevel}
          parameters={parameters}
          entityAndContext={entityAndContext}
          ruleType={ruleType}
        />
      );
    }
    // For non-logical types, render parameter inputs dynamically
    const SubContent = subContentMap.DYNAMIC;
    return (
      <SubContent
        key={`${data.type.value}_condition_sub_content`}
        parameter={parameterDefinitions}
        parameterData={parameterData}
        onChange={handleParameterChange}
        entityAndContext={entityAndContext}
        currentOperator={data.operator.value}
        ruleType={ruleType}
        allConditionData={parameterData}
        renderWrapper={(children) => (
          <Grid size={{ xs: 12, sm: 6 }} mt={2}>
            {children}
          </Grid>
        )}
      />
    );
  };

  return (
    <Box key={"condition_content"} sx={{ width: "100%", padding: 2 }}>
      <Grid container spacing={2}>
        {/* Common ConditionTypeDropDown */}
        <Grid size={{ xs: 12, sm: 12 }} item>
          <ConditionTypeDropDown
            key={"condition_type_dropdown"}
            type={data.type}
            operator={data.operator}
            conditionTypes={receivedData.conditionTypes}
            onChange={handleConditionDropdownChange}
          />
        </Grid>

        {/* Dynamic SubContent based on condition type */}
        {renderConditionContent()}

        {/* Common Button Section */}
        <Grid size={{ sm: 12 }}>
          <Box
            item
            size={{ xs: 12, sm: 12, md: 12 }}
            sx={{
              display: "flex",
              justifyContent: nestedLevel > 0 ? "center" : "flex-end",
              gap: 2,
            }}
          >
            {nestedLevel > 0 && (
              <BtnAtom
                sx={{ backgroundColor: "danger.main" }}
                onClick={() => handleCancel(tab.id)}
                text="Cancel"
              />
            )}
            <BtnAtom
              onClick={handleSubmit}
              sx={{ backgroundColor: "primary.secondary" }}
              text={nestedLevel > 0 ? "Save" : "Save Condition"}
            />
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ConditionContentOrganizer;
