import { Box, Grid2 as Grid } from "@mui/material";
import Dropdown<PERSON>tom from "../../../../../../global/atoms/dropdown/Dropdown";
import { InputField } from "../../../../../../global/atoms/inputFields/InputField";
import { Btn as Btn<PERSON>tom } from "../../../../../../global/atoms/buttons/Button";
import PropTypes from "prop-types";
import { useState } from "react";
import { Alert, campaignValidators } from "../../../../../../../utils";
import { Helpers } from "../../../../../../../utils";
import DatePickerAtom from "../../../../../../global/atoms/datePicker/Index";
import { useSelector } from "react-redux";
import React from "react";
import EnumManager from "../../../../../../global/atoms/enum/Index";
import SmartValueHoc from "../../../../../../global/molecules/smartValueHoc/SmartValueHoc";
/**
 * @typedef {import('../../../../../../../../jsDocs/components/dashboard').VariablesContentInputsData} InputsState
 */

const VariablesContentOrganizer = ({
  tab,
  handleCancel,
  handleSave,
  tabType = "local",
  collections = [],
}) => {
  /** @type {import("../../../../../../../../jsDocs/redux/store").Store['campaign']} */
  const { campaign, receivedData } = useSelector(
    /** @param {{ campaign: any }} state */ (state) => state.campaign
  );
  const { evaluationRules, outcomeRules } = campaign;

  /** @type {[InputsState,  React.Dispatch<React.SetStateAction<InputsState>>]} */
  const [inputs, setInputs] = useState(tab.inputs);
  const { persistentVariables, localVariables, contextId, entityId } = campaign;
  const { transactionVariables, entities } = receivedData;
  // get the previous inputs data using tab.inputs
  const previousInputs = tab.inputs;

  const [dropdownData, setDropdownData] = useState({
    types: [
      { label: "Number", value: "number" },
      { label: "String", value: "string" },
      { label: "Boolean", value: "boolean" },
      { label: "Date", value: "date" },
      { label: "Array", value: "array" },
    ],
    booleanOptions: [
      { label: "True", value: true },
      { label: "False", value: false },
    ],
    collections: [{ label: "Collection 1", value: "collection1" }],
  });

  const handleInputChange = (e, status) => {
    const type = inputs.type.value; // Get the current type from inputs
    let name = e.target.name;
    let value = e.target.value;
    let isValid = status.success;
    let errorMessage = status.message;

    // Format the value based on the field
    switch (name) {
      // For name field
      case "name":
        {
          let variableId = inputs?.variableId?.value;

          /** Check the variable if it is used in the evaluation rules or outcome rules or not */
          const isVarUsedEvalRules = variableId
            ? Helpers.isVariableUsed(variableId, evaluationRules)
            : false;
          const isVarUsedOutcomeRules = variableId
            ? Helpers.isVariableUsed(variableId, outcomeRules)
            : false;
          if (isVarUsedEvalRules)
            return Alert(
              "Variable is used in the rule set. To delete or edit this variable, please remove it from the evaluation rules section first.",
              "error"
            );
          if (isVarUsedOutcomeRules)
            return Alert(
              "Variable is used in the rule set. To delete or edit this variable, please remove it from the outcome rules section first.",
              "error"
            );
        }
        break;

      // For default value field
      case "defaultValue":
        break;
    }

    // If type is number and field is defaultvalue then conver the value to number
    if (type === "number" && name === "defaultValue") {
      // Convert value to number
      // value = Number(value);
      // // Check if the value is a valid number
      // if (isNaN(value)) {
      //   isValid = false;
      //   errorMessage = "Must be a valid number.";
      // }
    }

    if (type === "integer" && name === "defaultValue") {
      // For integer, remove any decimal points
      // value = Number(value);
      // value = value.replace(/\./g, "");
      // if (!/^\d+$/.test(value)) {
      //   isValid = false;
      //   errorMessage = "Must be a valid integer.";
      // }
    } else if (type === "float" && name === "defaultValue") {
      // For float, if no decimal is added, append .00
      // if (value && !value.includes(".")) {
      //   value = `${value}`;
      // }
      // if (!/^\d+(\.\d+)?$/.test(value)) {
      //   isValid = false;
      //   errorMessage = "Must be a valid float or number.";
      // }
    }

    setInputs((prev) => ({
      ...prev, // Spread the previous state
      [name]: {
        value,
        isValid: isValid,
        error: isValid ? null : errorMessage,
        isShowError: !isValid,
      },
    }));
  };

  // Handle dropdown changes (Entity and Context)
  const handleDropdownChange = (name, value) => {
    let isValid = true;
    if (value === null) isValid = false;

    // Update based on dropdown name
    if (name === "type") {
      // @ts-ignore
      setInputs((prev) => ({
        ...prev,
        type: {
          value,
          isValid: isValid,
          error: isValid ? null : `Invalid ${name}`,
          isShowError: false,
        },
        defaultValue: {
          ...prev.defaultValue,
          value: value === "array" ? [] : "", // Reset default value when type changes
          isValid: false,
          error: `Please select a default value`,
        },
      }));
    } else {
      setInputs((prev) => ({
        ...prev,
        [name]: {
          value,
          isValid: isValid,
          error: isValid ? null : `Invalid ${name}`,
        },
      }));
    }
  };
  const onDateChange = (date, key) => {
    setInputs((prev) => ({
      ...prev,
      [key]: {
        value: date,
        isValid: true,
        error: null,
        isShowError: false,
      },
    }));
  };

  const onBlurHandler = (e) => {
    const type = inputs.type.value; // Get the current type from inputs
    let { name, value } = e.target;

    // If it's number type, convert the value to number
    if (type === "number" && name === "defaultValue") {
      if (value == 0) {
        value = 0;
      } else {
        // Convert value to number
        value = Number(value);
      }

      // Check if the value is a valid number
      if (!isNaN(value)) {
        setInputs((prev) => ({
          ...prev,
          [name]: {
            value,
            isValid: true,
            error: "",
            isShowError: false,
          },
        }));
      }
    }
  };

  const handleSubmit = () => {
    // Validate all fields using isValidAllFields
    const validationResult = campaignValidators.global.isValidAllFields(inputs);

    if (validationResult.success) {
      let defaultValue = inputs.defaultValue.value;

      // Check if the type is float or number and ensure proper formatting
      if (inputs.type.value === "float" || inputs.type.value === "number") {
        // If no decimal is present, append .00
        // if (defaultValue && !defaultValue.includes(".")) {
        //   defaultValue = `${defaultValue}.00`;
        // }
      }
      // Check if the name is same as the previous name
      if (inputs.name.value != previousInputs.name.value) {
        // Validate the variable name against all potential conflicts
        const nameValidationResult = Helpers.validateVariableName(
          inputs.name.value,
          transactionVariables,
          localVariables,
          persistentVariables,
          entities
        );
        if (!nameValidationResult.isValid) {
          Alert(nameValidationResult.message, "error");
          setInputs((prev) => ({
            ...prev,
            name: {
              ...prev.name,
              isValid: false,
              error: nameValidationResult.fieldError,
              isShowError: true,
            },
          }));
          return;
        }
      }

      let obj = {
        ...inputs,
        defaultValue: {
          ...inputs.defaultValue,
          value: defaultValue,
        }, // Use the corrected default value
      };
      handleSave(tab.id, Helpers.returnValues(obj));
    } else {
      // If there are validation errors, update the inputs state to show errors
      const updatedInputs = { ...inputs };

      // Iterate over the errors array and update the state
      validationResult.errors.forEach(({ field, error }) => {
        updatedInputs[field] = {
          ...updatedInputs[field],
          isValid: false, // Set field as invalid
          isShowError: true, // Show error message
          error, // Set error message
        };
      });

      // Update the inputs state with the new validation statuses
      setInputs(updatedInputs);

      // // Display the first error message
      // const firstErrorMessage = validationResult.errors[0].error;
      // Alert(firstErrorMessage, "error");
    }
  };

  const onAddNewValueHandler = (newValue) => {
    // @ts-ignore
    setInputs((prev) => ({
      ...prev,
      defaultValue: {
        value: [...prev.defaultValue.value, newValue],
        isValid: true,
        error: null,
        isShowError: false,
      },
    }));
  };

  const onDeleteValueHandler = (indexToDelete) => {
    setInputs((prev) => ({
      ...prev,
      defaultValue: {
        ...prev.defaultValue,
        value: prev.defaultValue.value.filter(
          (_, index) => index !== indexToDelete
        ),
        isValid: prev.defaultValue.value.length - 1 > 0,
      },
    }));
  };

  return (
    <Box sx={{ width: "100%", padding: 2 }}>
      <Grid container spacing={2}>
        <Grid item size={{ xs: 12, sm: 6, md: 6, order: 1 }}>
          <InputField
            label="Variable name"
            type="text"
            placeholder="e.g Amount"
            name={"name"}
            validator={campaignValidators.variables.name}
            onChange={handleInputChange}
            sx={{ backgroundColor: "bgPrimary.main" }}
            validateOn={"change"}
            value={inputs.name.value}
            validation={{
              isShowError: inputs.name.isShowError,
              error: inputs.name.error,
            }}
            // onMountValidate={true} // if it;s edit screen then provide true else false
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 6 }} sx={{ order: 2 }} item md={6}>
          <DropdownAtom
            label="Type *"
            options={dropdownData.types}
            name={"type"}
            onChange={handleDropdownChange}
            selectedItem={inputs.type.value}
            sx={{ backgroundColor: "bgPrimary.main" }}
            validation={{
              isShowError: inputs.type.isShowError,
              error: inputs.type.error,
            }}
            //  disabled={!inputs.type.value}
          />
        </Grid>
        {/* Conditionally render the collection dropdown if tabType is "persistent" */}
        {tabType === "persistent" && (
          <Grid size={{ xs: 12, sm: 6, md: 6 }} sx={{ order: 3 }} item md={6}>
            <DropdownAtom
              label="Collection Id*"
              options={collections}
              name={"collectionId"}
              sx={{ backgroundColor: "bgPrimary.main" }}
              onChange={handleDropdownChange}
              selectedItem={inputs?.collectionId?.value}
              validation={{
                isShowError: inputs.collectionId.isShowError,
                error: inputs.collectionId.error,
              }}
            />
          </Grid>
        )}

        <Grid
          item
          size={{ xs: 12, sm: 6, md: 6 }}
          sx={{ order: tabType === "local" ? 5 : 4 }}
        >
          {inputs.type.value === "boolean" ? (
            <DropdownAtom
              label="Default value *"
              options={dropdownData.booleanOptions}
              name={"defaultValue"}
              onChange={handleDropdownChange}
              selectedItem={inputs.defaultValue.value}
              sx={{ backgroundColor: "bgPrimary.main" }}
              disabled={!inputs.type.value}
              validation={{
                isShowError: inputs.defaultValue.isShowError,
                error: inputs.defaultValue.error,
              }}
            />
          ) : inputs.type.value === "date" ? (
           
            <SmartValueHoc
              label="Default value "
              onSave={(newValue) => {
                setInputs((prev) => ({
                  ...prev,
                  defaultValue: {
                    value: newValue,
                    isValid: true,
                    error: null,
                    isShowError: false,
                  },
                }));
              }}
              smartValueProps={{
                types: ["dateTime"],
                functionTypes: [],
                allowedRef: [],
                customValAllowed: true,
                entityId: entityId,
                contextId: contextId,
                data: inputs.defaultValue.value,
                emptySaveAllowed: false,
                ruleType: "",
                disabled: !inputs.type.value,
                errorDetails: {
                  error: inputs.defaultValue.error,
                  isShowError: inputs.defaultValue.isShowError,
                },
              }}
            />
          ) : inputs.type.value === "array" ? (
            <SmartValueHoc
            label="Default value "
            onSave={(newValue) => {
              setInputs((prev) => ({
                ...prev,
                defaultValue: {
                  value: newValue,
                  isValid: true,
                  error: null,
                  isShowError: false,
                },
              }));
            }}
            smartValueProps={{
              types: ["array"],
              functionTypes: [],
              allowedRef: [],
              customValAllowed: true,
              entityId: entityId,
              contextId: contextId,
              data: inputs.defaultValue.value,
              emptySaveAllowed: true,
              ruleType: "",
              disabled: !inputs.type.value,
              errorDetails: {
                error: inputs.defaultValue.error,
                isShowError: inputs.defaultValue.isShowError,
              },
            }}
          />
          ) : (
            <InputField
              label="Default value"
              type={
                inputs.type.value === "integer" ||
                inputs.type.value === "float" ||
                inputs.type.value === "number"
                  ? "number"
                  : "text"
              }
              placeholder={`Enter a default value of type ${inputs.type.value}`}
              name={"defaultValue"}
              validator={campaignValidators.variables.defaultValue}
              onChange={handleInputChange}
              validateOn={"change"}
              sx={{ backgroundColor: "bgPrimary.main" }}
              value={
                inputs.type.value === "integer"
                  ? String(inputs.defaultValue.value)
                  : inputs.defaultValue.value
              }
              disabled={!inputs.type.value}
              valueType={inputs.type.value}
              validation={{
                isShowError: inputs.defaultValue.isShowError,
                error: inputs.defaultValue.error,
              }}
              onBlur={onBlurHandler}
              onKeyDown={(e) => {
                // Prevent input of 'e', 'E', '+', and '-'
                if (
                  inputs.type.value === "integer" ||
                  inputs.type.value === "float" ||
                  inputs.type.value === "number"
                ) {
                  if (["e", "E", "+", "-"].includes(e.key)) {
                    e.preventDefault();
                  }
                }
              }}
            />
          )}
        </Grid>
        <Grid
          item
          size={{ xs: 12, sm: 6, md: 6 }}
          sx={{ order: tabType === "local" ? 4 : 5 }}
        >
          <InputField
            label="Description "
            type="text"
            placeholder="The total amount of purchases made by a customer"
            multiline={true}
            rows={4}
            required={true}
            sx={{ backgroundColor: "bgPrimary.main" }}
            name={"description"}
            validator={campaignValidators.variables.description}
            onChange={handleInputChange}
            validateOn={"change"}
            value={inputs.description.value}
            validation={{
              isShowError: inputs.description.isShowError,
              error: inputs.description.error,
            }}
          />
        </Grid>

        <Grid
          item
          size={{ xs: 12, sm: 12, md: 12 }}
          sx={{ display: "flex", justifyContent: "flex-end", gap: 2, order: 6 }}
        >
          <BtnAtom
            sx={{ backgroundColor: "danger.main" }}
            onClick={() => handleCancel(tab.id)}
            text="Cancel"
          />
          <BtnAtom
            onClick={handleSubmit}
            text="Save"
            sx={{ backgroundColor: "primary.secondary" }}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default VariablesContentOrganizer;
VariablesContentOrganizer.propTypes = {
  tab: PropTypes.object,
  handleCancel: PropTypes.func,
  handleSubmit: PropTypes.func,
  setInputs: PropTypes.func,
  dropdownData: PropTypes.object,
  handleDropdownChange: PropTypes.func,
  handleInputChange: PropTypes.func,
};
