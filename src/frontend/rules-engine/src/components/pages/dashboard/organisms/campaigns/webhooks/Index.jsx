import { Box, Grid2 as Grid } from "@mui/material";
import { InputField } from "../../../../../../components/global/atoms/inputFields/InputField";
import { Typography<PERSON>tom } from "../../../../../../components/global/atoms/typography/Typography";
import React, { useEffect, useState } from "react";
import DropdownAtom from "../../../../../../components/global/atoms/dropdown/Dropdown";
import { IconBtnAtom } from "../../../../../../components/global/atoms/buttons/IconBtn";
import { Btn } from "../../../../../../components/global/atoms/buttons/Button";
import SectionHeaderMolecule from "../../../../../../components/pages/dashboard/molecules/campaigns/create/SubSectionHeader";
import ReusableSectionHOC from "../../../../../../components/pages/dashboard/atoms/global/reusableSectionHOC";
import { collectionMappingTabData } from "../../../../../../utils/dummyData/Campaigns";
import ParamContentOrganizer from "./organizers/Params";
import HeaderContentOrganizer from "./organizers/Headers";
import {
  campaignValidators,
  Helpers,
  Alert,
  Data,
} from "../../../../../../utils/index";
import { useSelector } from "react-redux";
import { useDispatch } from "react-redux";
import LoadingComponent from "../../../../../../components/global/molecules/LoadingComponent";
import BodyTemplateOrganizer from "./organizers/BodyTemplate";
import WebhookService from "../../../../../../Api/Webhook";

let { inputKeys, sectionHeaders } = Data.services.webhook;
let { detailsKeys, parametersKeys, headersKeys, bodyTemplateKeys } = inputKeys; 
let { parametersColumns, headersColumns, bodyTemplateColumns } = sectionHeaders;

const WebhookOrganizers = ({ handleCancel, handleSave, tab }) => {
  /** @type {import("../../../../../../redux/store").Store['campaign']} */
  const { campaign, receivedData } = useSelector((state) => state.campaign);
  const { webhooksAllowedUrls } = receivedData;
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  const { rules } = campaign;
  const [isLoading, setIsLoading] = useState(true);
  const dispatch = useDispatch();

  const [inputs, setInputs] = useState({});


  // _______________ URL Options _______________
  const urlOptions = webhooksAllowedUrls.map((item) => ({
    label: item,
    value: item,
  }));

  // _______________ Input Change Handler _______________
  /**
   * This Function will handle the input change
   */
  const handleInputChange = (event, validation) => {
    const { name, value } = event.target;

    setInputs({
      ...inputs,
      [name]: {
        value: value,
        isShowError: validation.success === false,
        error: validation.message,
        isValid: validation.success,
      },
    });
  };
  /**
   * This Function will handle the dropdown change
   */
  const handleDropdownChange = (name, value) => {
    setInputs({
      ...inputs,
      [name]: {
        value: value,
        validation: {
          isShowError: false,
          error: "",
        },
        isValid: true,
      },
    });
  };
  /**
   * This Function will create or update the webhook
   */
  const handleSaveBtnClick = async () => {
    try {
      setIsBtnLoading(true);
      let {
        name,
        description,
        url,
        method,
        timeout,
        maxRetries,
        initialDelaySeconds,
        backoffMultiplier,
        securityLevel,
      } = inputs;

      let fields = {
        name,
        description,
        url,
        method,
        timeout,
        maxRetries,
        initialDelaySeconds,
        backoffMultiplier,
        securityLevel,
      };
      // This will remove all the spaces from the strings and set the validation status
      let trimmedFields = Helpers.trimObjectValues(fields);
      // This will check if all the fields are valid
      let validationResult = campaignValidators.global.isValidAllFields(trimmedFields);
      // If there are validation errors, update the inputs state to show errors
      if (!validationResult.success) {
        const updatedInputs = { ...inputs };
        validationResult.errors.forEach(({ field, error }) => {
          updatedInputs[field] = {
            ...updatedInputs[field],
            isValid: false,
            isShowError: true,
            error,
          };
        });

        // Update the inputs state with the new validation statuses
        setInputs(updatedInputs);
        return;
      }

      let isExist = tab.webhooks.find(
        (item) =>
          item.data.name.toLowerCase() ===
            inputs.name.value.trim().toLowerCase() && item.id !== tab.id
      );
      if (isExist) {
        setInputs({
          ...inputs,
          name: {
            ...inputs.name,
            isValid: false,
            isShowError: true,
            error: "Parameter name already exists",
          },
        });
        return;
      }
      // Convert the inputs to a formatted object to send to the server
      let obj = Helpers.generateFormattedWebhookData(
        { ...inputs, ...trimmedFields },
        tab
      );

      if (tab.id === "newWebhook") {
        let res = await WebhookService.createWebhook(obj);

        if (!res.success) {
          Alert(res?.data, "error");
          return;
        }
        Alert(res?.data, "success");
      } else {
        const res = await WebhookService.updateWebhook(tab.id, obj);
        if (!res.success) {
          Alert(res?.data, "error");
          return;
        }
        Alert(res?.data, "success");
      }

      handleSave(tab.id, obj);
    } catch (error) {
      console.log("error", error);
      Alert(error.toString(), "error");
    } finally {
      setIsBtnLoading(false);
    }
  };

  useEffect(() => {
    /**
     * This Function will set the received data to the inputs state
     */
    const handleReceivedData = () => {
      let {
        name,
        description,
        url,
        method,
        timeout,
        retryPolicy,
        securityLevel,
        parameters,
        headers,
        bodyTemplate,
      } = tab.data;
      let { initialDelaySeconds, backoffMultiplier, maxRetries } = retryPolicy;
      let receivedData = {
        name,
        description,
        url,
        method,
        headers,
        timeout,
        securityLevel,
        maxRetries,
        initialDelaySeconds,
        backoffMultiplier,
      };

      let val = Helpers.defaultValues(
        detailsKeys.keys,
        receivedData,
        detailsKeys.optionalKeys
      );

      let headersArray = Object.entries(headers).map(([key, value]) => {
        return {
          id: key,
          mode: "viewing", // ["viewing", "editing"]
          columns: [
            { content: key, gridSize: 5 },
            { content: value, gridSize: 5 },
          ],
          validations: {},
          data: Helpers.defaultValues(
            headersKeys.keys,
            { key: key, value: value },
            headersKeys.optionalKeys
          ),
        };
      });
      let parametersArray = Array.isArray(parameters)? parameters.map((item) => {
        return {
          id: item.parameterId,
          mode: "viewing", // ["viewing", "editing"]
          columns: [
            { content: item.name, gridSize: 5 },
            { content: item.type, gridSize: 5 },
          ],
          validations: {},
          data: Helpers.defaultValues(
            parametersKeys.keys,
            item,
            parametersKeys.optionalKeys
          ),
        };
      }):[];

      let bodyTemplateArray = Object.entries(bodyTemplate).map(
        ([key, value]) => {
          return {
            id: key,
            mode: "viewing", // ["viewing", "editing"]
            columns: [
              { content: key, gridSize: 5 },
              { content: value, gridSize: 5 },
            ],
            data: Helpers.defaultValues(
              bodyTemplateKeys.keys,
              { key: key, value: value },
              bodyTemplateKeys.optionalKeys
            ),
          };
        }
      );

      setInputs({
        ...val,
        headers: headersArray,
        parameters: parametersArray,
        bodyTemplate: bodyTemplateArray,
      });
      setIsLoading(false);
    };
    handleReceivedData();
  }, [tab.data]);

  // __________ Parameter Section __________

  const handleAddNewParam = () => {
    let isIncompleteVar = inputs.parameters.find((item) => {
      return item.id === "newParameter";
    });
    if (isIncompleteVar)
      return Alert("Please fill the incomplete parameter", "error");

    const newItem = {
      id: "newParameter",
      mode: "editing", // ["viewing", "editing"]
      columns: [{ content: "Create Parameter", gridSize: 6 }],
      isEditable: true,
      isDeletable: true,
      isAllFieldsValid: false,
      validations: {},
      data: Helpers.defaultValues(
        parametersKeys.keys,
        {},
        parametersKeys.optionalKeys
      ),
    };

    setInputs({ ...inputs, parameters: [...inputs.parameters, newItem] });
  };
  // This will map over the parameters array and return an array of objects for the body template
  const paramsArray = inputs?.parameters?.map((item) => ({
    value: "{" + item.data.parameterId.value + "}",
    label: item.data.name.value,
    description: item?.data?.description
      ? item.data.description.value
      : item.data.name.value,
  }));

  /**
   * The function that puts a local variable in edit mode.
   * It takes the id of the local variable as an argument.
   * It iterates through the variableDefinitions state and sets the mode of the
   * local variable with the given id to "editing".
   * The updated state is then saved to the variableDefinitions state.
   * @param {string} id The id of the local variable to be edited.
   */
  const handleEditParam = (id) => {
    const updatedData = inputs.parameters.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          mode: "editing",
        };
      }
      return item;
    });
    setInputs({ ...inputs, parameters: updatedData });
  };
  /**
   * The function that handles deleting a local variable.
   * It takes the id of the local variable as an argument.
   * It iterates through the variableDefinitions state and removes the local
   * variable with the given id from the state.
   * Then it updates the campaign state by removing the deleted local variable
   * from the localVariableDefinitions array.
   * @param {string} id The id of the local variable to be deleted.
   */
  const handleDeleteParam = (id) => {
    let isParamUsedInBodyTemplate = inputs.bodyTemplate.find((item) => {
      // here i want that the value is {userId} and the key is userId
      return item.data.value.value === `{${id}}`;
    });

    if (isParamUsedInBodyTemplate) {
      return Alert("Parameter is used in the body template", "error");
    }
    // Filter out the local variable with the given id from the state
    const updatedData = inputs.parameters.filter((item) => item.id !== id);

    // // Update the variableDefinitions state with the new array
    setInputs({ ...inputs, parameters: updatedData });

  };
  // Function to handle cancel action for a specific item
  const handleCancelParam = (id) => {
    // Map over the variableDefinitions array and update the mode of the specific item to "viewing"
    let updatedData = inputs.parameters.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          mode: "viewing",
        };
      }
      return item;
    });
    // Update the variableDefinitions state with the modified data
    setInputs({ ...inputs, parameters: updatedData });
  };
  /**
   * Handles the save action for a specific local variable.
   * @param {string} id The id of the local variable to be saved.
   * @param {object} inputs The input values to be saved.
   * It first checks if the variable name already exists in the local variables
   * or in the persistent variables. If it does, it displays an error alert.
   * If it doesn't, it updates the local variable definitions with the new input values
   * and dispatches an action to update the campaign state with the new
   * localVariableDefinitions array. If the save is successful, it displays a success alert.
   */
  const handleSaveParam = (id, inputsData) => {
    let parameterId = Helpers.generateUniqueId({
      name: inputsData.name.value,
      existingIds: inputs.parameters.map((item) => item.data.parameterId.value),
      maxLength: 40,
    });

    //  let isExist = isIdExist({id:parameterId,key:"parameterId"},inputs.parameters);
    //  if(isExist){
    //   return Alert("Parameter already exists", "error");
    //  }

    // @todo: Check the id that not match before saving like id.slice(0,50)
    const updatedData = inputs.parameters.map((item) => {
      return item.id === id
        ? {
            ...item,
            mode: "viewing",
            id: parameterId,
            columns: [
              { content: inputsData.name.value, gridSize: 5 },
              { content: inputsData.type.value, gridSize: 5 },
            ],
            data: {
              ...inputsData,
              parameterId: {
                ...inputsData.parameterId,
                value: parameterId,
              },
            },
          }
        : item;
    });

    setInputs({ ...inputs, parameters: updatedData });

    // Set the alert here if successfully
    Alert("Variable created successfully", "success");
  };

  // ________ Header Section ________

  const handleAddNewHeader = () => {
    let isIncompleteVar = inputs.headers.find((item) => {
      return item.id === "newHeader";
    });
    if (isIncompleteVar)
      return Alert("Please fill the incomplete header", "error");

    const newItem = {
      id: "newHeader",
      mode: "editing", // ["viewing", "editing"]
      columns: [{ content: "Create Header", gridSize: 6 }],
      isEditable: true,
      isDeletable: true,
      isAllFieldsValid: false,
      validations: {},
      data: Helpers.defaultValues(
        headersKeys.keys,
        {},
        headersKeys.optionalKeys
      ),
    };

    setInputs({ ...inputs, headers: [...inputs.headers, newItem] });
  };
  const handleDeleteHeader = (id) => {
    // Filter out the local variable with the given id from the state
    const updatedData = inputs.headers.filter((item) => item.id !== id);

    // Update the variableDefinitions state with the new array
    setInputs({ ...inputs, headers: updatedData });
  };
  const handleEditHeader = (id) => {
    const updatedData = inputs.headers.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          mode: "editing",
        };
      }
      return item;
    });
    setInputs({ ...inputs, headers: updatedData });
  };
  const handleCancelHeader = (id) => {
    // Map over the variableDefinitions array and update the mode of the specific item to "viewing"
    let updatedData = inputs.headers.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          mode: "viewing",
        };
      }
      return item;
    });
    // Update the variableDefinitions state with the modified data
    setInputs({ ...inputs, headers: updatedData });
  };
  const handleSaveHeader = (id, inputsData) => {
    const updatedData = inputs.headers.map((item) => {
      return item.id === id
        ? {
            ...item,
            mode: "viewing",
            id: inputsData.key,
            columns: [
              { content: inputsData.key.value, gridSize: 5 },
              { content: inputsData.value.value, gridSize: 5 },
            ],
            data: inputsData,
          }
        : item;
    });
    setInputs({ ...inputs, headers: updatedData });
  };

  // ________ Body Template Section ________

  const handleAddNewBodyTemplate = () => {
    let isIncompleteVar = inputs.bodyTemplate.find((item) => {
      return item.id === "newBodyProperty";
    });
    if (isIncompleteVar)
      return Alert("Please fill the incomplete body template", "error");

    const newItem = {
      id: "newBodyProperty",
      mode: "editing", // ["viewing", "editing"]
      columns: [{ content: "Create Body Property", gridSize: 6 }],
      isEditable: true,
      isDeletable: true,
      isAllFieldsValid: false,
      validations: {},
      data: Helpers.defaultValues(
        bodyTemplateKeys.keys,
        {},
        bodyTemplateKeys.optionalKeys
      ),
    };

    setInputs({ ...inputs, bodyTemplate: [...inputs.bodyTemplate, newItem] });
  };
  const handleDeleteBodyTemplate = (id) => {
    // Filter out the local variable with the given id from the state
    const updatedData = inputs.bodyTemplate.filter((item) => item.id !== id);

    // Update the variableDefinitions state with the new array
    setInputs({ ...inputs, bodyTemplate: updatedData });
  };
  const handleEditBodyTemplate = (id) => {
    const updatedData = inputs.bodyTemplate.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          mode: "editing",
        };
      }
      return item;
    });
    setInputs({ ...inputs, bodyTemplate: updatedData });
  };
  const handleCancelBodyTemplate = (id) => {
    // Map over the variableDefinitions array and update the mode of the specific item to "viewing"
    let updatedData = inputs.bodyTemplate.map((item) => {
      if (item.id === id) {
        return {
          ...item,
          mode: "viewing",
        };
      }
      return item;
    });
    // Update the variableDefinitions state with the modified data
    setInputs({ ...inputs, bodyTemplate: updatedData });
  };
  const handleSaveBodyTemplate = (id, inputsData) => {
    const updatedData = inputs.bodyTemplate.map((item) => {
      return item.id === id
        ? {
            ...item,
            mode: "viewing",
            id: inputsData.key.value,
            columns: [
              { content: inputsData.key.value, gridSize: 5 },
              { content: inputsData.value.value, gridSize: 5 },
            ],
            data: inputsData,
          }
        : item;
    });
    setInputs({ ...inputs, bodyTemplate: updatedData });
  };

  if (isLoading) {
    return <LoadingComponent sx={{}} fullScreen={true} />;
  }
  return (
    <Box>
      <Grid container spacing={2}>
        {/*  Webhook Details Section  */}
        {/*  Webhook Name  */}
        <Grid item size={{ md: 6, xs: 12 }}>
          <InputField
            label="Name"
            value={inputs.name?.value}
            onChange={handleInputChange}
            validator={campaignValidators.webhooks.name}
            name="name"
            type="text"
            validation={{
              isShowError: inputs.name?.isShowError,
              error: inputs.name?.error,
            }}
            placeholder="Enter Webhook Name"
            required
            disabled={false}
            fullWidth={true}
            sx={{ width: "100%" }}
          />
        </Grid>

        {/*  Webhook Description  */}
        <Grid item size={{ md: 6, xs: 12 }}>
          <InputField
            label="Description"
            value={inputs.description?.value}
            onChange={handleInputChange}
            name="description"
            validator={campaignValidators.webhooks.description}
            validation={{
              isShowError: inputs.description?.isShowError,
              error: inputs.description?.error,
            }}
            type="text"
            placeholder="Description"
            required
            disabled={false}
            fullWidth={true}
            sx={{ width: "100%" }}
          />
        </Grid>

        <Grid item size={{ md: 6, xs: 12 }}>

        <DropdownAtom
            label="URL"
            selectedItem={inputs.url?.value}
            onChange={handleDropdownChange}
            name="url"
            validation={{
              isShowError: inputs.url?.isShowError,
              error: inputs.url?.error,
            }}
            options={urlOptions}
          />

          {/* <InputField
            label="URL"
            value={inputs.url?.value}
            onChange={handleInputChange}
            validator={campaignValidators.webhooks.url}
            name="url"
            type="text"
            validation={{
              isShowError: inputs.url?.isShowError,
              error: inputs.url?.error,
            }}
            placeholder="Enter Webhook URL"
            required
            disabled={false}
            fullWidth={true}
            sx={{ width: "100%" }}
          /> */}
        </Grid>

        {/*  Webhook Method  */}
        <Grid item size={{ md: 6, xs: 12 }}>
          <DropdownAtom
            label="Method"
            selectedItem={inputs.method?.value}
            onChange={handleDropdownChange}
            name="method"
            validation={{
              isShowError: inputs.method?.isShowError,
              error: inputs.method?.error,
            }}
            options={[
              { label: "GET", value: "GET" },
              { label: "POST", value: "POST" },
              { label: "PUT", value: "PUT" },
              { label: "DELETE", value: "DELETE" },
              { label: "PATCH", value: "PATCH" },
            ]}
          />
        </Grid>

        {/*  Webhook Timeout  */}
        <Grid item size={{ md: 6, xs: 12 }}>
          <InputField
            label="Timeout"
            value={inputs.timeout?.value}
            onChange={(e, validation) => {
              // Remove all non-digit characters (including dot)
              const cleanedValue = e.target.value.replace(/\D/g, "");
              e.target.value = cleanedValue;
              handleInputChange(e, validation);
            }}
            onKeyDown={(e) => {
              if (e.key === "." || e.key === "e") {
                e.preventDefault(); // prevent typing dot or scientific notation
              }
            }}
            validator={campaignValidators.webhooks.timeout}
            name="timeout"
            type="number"
            validation={{
              isShowError: inputs.timeout?.isShowError,
              error: inputs.timeout?.error,
            }}
            placeholder="Enter Webhook Timeout"
            required
            disabled={false}
            fullWidth={true}
            sx={{ width: "100%" }}
            inputProps={{
              inputMode: "numeric", // shows number pad on mobile
              pattern: "[0-9]*", // helps limit input
            }}
          />
        </Grid>

        {/*  Webhook Security Level  */}
        <Grid item size={{ md: 6, xs: 12 }}>
          <DropdownAtom
            label="Security Level"
            selectedItem={inputs.securityLevel?.value}
            name="securityLevel"
            validation={{
              isShowError: inputs.securityLevel?.isShowError,
              error: inputs.securityLevel?.error,
            }}
            onChange={handleDropdownChange}
            options={[
              { label: "SYSTEM", value: "SYSTEM" },
              { label: "ORGANIZATION", value: "ORGANIZATION" },
              { label: "ENTITY", value: "ENTITY" },
            ]}
          />
        </Grid>

        {/*  Retry Policy Section  */}
        <Grid container item size={{ md: 12, xs: 12 }}>
          <Grid item size={{ md: 12, xs: 12 }}>
            <TypographyAtom text="Retry Policy" variant="h6" />
          </Grid>
          <Grid item size={{ md: 6, xs: 12 }}>
            <InputField
              label="Max Retries"
              value={inputs.maxRetries?.value}
              validation={{
                isShowError: inputs.maxRetries?.isShowError,
                error: inputs.maxRetries?.error,
              }}
              onChange={(e, validation) => {
                // Remove all non-digit characters (including dot)
                const cleanedValue = e.target.value.replace(/\D/g, "");

                // Update the event's value (optional, if you need to modify the original event)
                e.target.value = cleanedValue;

                handleInputChange(e, validation);
              }}
              onKeyDown={(e) => {
                if (e.key === "." || e.key === "e") {
                  e.preventDefault(); // prevent typing dot or scientific notation
                }
              }}
              validator={campaignValidators.webhooks.retryPolicy.maxRetries}
              name="maxRetries"
              type="number"
              placeholder="Enter Max Retries"
              required
            />
          </Grid>
          <Grid item size={{ md: 6, xs: 12 }}>
            <InputField
              label="Initial Delay"
              value={inputs.initialDelaySeconds?.value}
              validation={{
                isShowError: inputs.initialDelaySeconds?.isShowError,
                error: inputs.initialDelaySeconds?.error,
              }}
              onChange={(e, validation) => {
                // Remove all non-digit characters (including dot)
                const cleanedValue = e.target.value.replace(/\D/g, "");
                e.target.value = cleanedValue;
                handleInputChange(e, validation);
              }}
              onKeyDown={(e) => {
                if (e.key === "." || e.key === "e") {
                  e.preventDefault(); // prevent typing dot or scientific notation
                }
              }}
              validator={
                campaignValidators.webhooks.retryPolicy.initialDelaySeconds
              }
              name="initialDelaySeconds"
              type="number"
              placeholder="Enter Initial Delay (Milliseconds)"
              required
            />
          </Grid>
          <Grid item size={{ md: 6, xs: 12 }}>
            <InputField
              label="Backoff Multiplier"
              value={inputs.backoffMultiplier?.value}
              validation={{
                isShowError: inputs.backoffMultiplier?.isShowError,
                error: inputs.backoffMultiplier?.error,
              }}
              onChange={handleInputChange}
              validator={
                campaignValidators.webhooks.retryPolicy.backoffMultiplier
              }
              name="backoffMultiplier"
              type="number"
              placeholder="Enter Backoff Multiplier"
              required
            />
          </Grid>
        </Grid>

        {/*  Parameters Section  */}
        <Grid item size={{ md: 12, xs: 12 }}>
          <Box
            sx={{
              mt: 2,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <TypographyAtom text="Parameters" variant="h6" sx={{}} />
            <Btn
              onClick={handleAddNewParam}
              sx={{ backgroundColor: "primary.secondary" }}
              text="Add"
            />
          </Box>

          <Box sx={{ mt: 2, width: "100%" }}>
            {/* {variableDefinitions.length > 0 && ( */}
            <SectionHeaderMolecule sx={{}} columns={parametersColumns} />
            {/* )} */}
            {/*  Here we are mapping over the variableDefinitions array to render the sub sections of the  variable */}
            {inputs.parameters.length > 0 &&
              inputs.parameters.map((tab, index) => (
                <ReusableSectionHOC
                  key={tab.id}
                  index={index}
                  tab={tab}
                  columns={parametersColumns}
                  rows={[]}
                  onEdit={() => handleEditParam(tab.id)}
                  onDelete={() => handleDeleteParam(tab.id)}
                  sxContainer={{ backgroundColor: "bgPrimary.main" }}
                  sxHeaderRow={{ backgroundColor: "bgPrimary.main" }}
                  content={
                    <ParamContentOrganizer
                      handleCancel={handleCancelParam}
                      handleSave={handleSaveParam}
                      key={index}
                      tab={{ ...tab, parameters: inputs.parameters }}
                    />
                  }
                />
              ))}
            {/* If the variableDefinitions array is empty, we display a message */}
            {inputs.parameters.length < 1 && (
              <Box sx={{ mt: 2 }}>
                <TypographyAtom
                  sx={{ textAlign: "center", color: "textColor.secondary" }}
                  text="No parameters added yet"
                  variant="h6"
                />
              </Box>
            )}
          </Box>
        </Grid>

        {/*  Headers Section  */}
        <Grid item size={{ md: 12, xs: 12 }}>
          <Box
            sx={{
              mt: 2,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <TypographyAtom text="Headers" variant="h6" sx={{}} />
            <Btn
              onClick={handleAddNewHeader}
              sx={{ backgroundColor: "primary.secondary" }}
              text="Add"
            />
          </Box>

          <Box sx={{ mt: 2, width: "100%" }}>
            {/* {variableDefinitions.length > 0 && ( */}
            <SectionHeaderMolecule sx={{}} columns={headersColumns} />
            {/* )} */}
            {/*  Here we are mapping over the variableDefinitions array to render the sub sections of the  variable */}
            {inputs.headers.length > 0 &&
              inputs.headers.map((tab, index) => (
                <ReusableSectionHOC
                  key={tab.id}
                  index={index}
                  tab={tab}
                  columns={headersColumns}
                  rows={[]}
                  onEdit={() => handleEditHeader(tab.id)}
                  onDelete={() => handleDeleteHeader(tab.id)}
                  sxContainer={{ backgroundColor: "bgPrimary.main" }}
                  sxHeaderRow={{ backgroundColor: "bgPrimary.main" }}
                  content={
                    <HeaderContentOrganizer
                      handleCancel={handleCancelHeader}
                      handleSave={handleSaveHeader}
                      key={index}
                      tab={{ ...tab, headers: inputs.headers }}
                    />
                  }
                />
              ))}
            {/* If the variableDefinitions array is empty, we display a message */}
            {inputs.headers.length < 1 && (
              <Box sx={{ mt: 2 }}>
                <TypographyAtom
                  sx={{ textAlign: "center", color: "textColor.secondary" }}
                  text="No headers added yet"
                  variant="h6"
                />
              </Box>
            )}
          </Box>
        </Grid>
        {/*  Body Template Section  */}
        <Grid item size={{ md: 12, xs: 12 }}>
          <Box
            sx={{
              mt: 2,
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <TypographyAtom
              text="Body Template Properties"
              variant="h6"
              sx={{}}
            />
            <Btn
              onClick={handleAddNewBodyTemplate}
              sx={{ backgroundColor: "primary.secondary" }}
              text="Add"
            />
          </Box>

          <Box sx={{ mt: 2, width: "100%" }}>
            {/* {variableDefinitions.length > 0 && ( */}
            <SectionHeaderMolecule sx={{}} columns={bodyTemplateColumns} />
            {/* )} */}
            {/*  Here we are mapping over the variableDefinitions array to render the sub sections of the  variable */}
            {inputs.bodyTemplate.length > 0 &&
              inputs.bodyTemplate.map((tab, index) => (
                <ReusableSectionHOC
                  key={tab.id}
                  index={index}
                  tab={tab}
                  columns={bodyTemplateColumns}
                  rows={[]}
                  onEdit={() => handleEditBodyTemplate(tab.id)}
                  onDelete={() => handleDeleteBodyTemplate(tab.id)}
                  sxContainer={{ backgroundColor: "bgPrimary.main" }}
                  sxHeaderRow={{ backgroundColor: "bgPrimary.main" }}
                  content={
                    <BodyTemplateOrganizer
                      handleCancel={handleCancelBodyTemplate}
                      handleSave={handleSaveBodyTemplate}
                      key={index}
                      tab={{
                        ...tab,
                        parameters: paramsArray,
                        bodyTemplate: inputs.bodyTemplate,
                      }}
                    />
                  }
                />
              ))}
            {/* If the variableDefinitions array is empty, we display a message */}
            {inputs.bodyTemplate.length < 1 && (
              <Box sx={{ mt: 2 }}>
                <TypographyAtom
                  sx={{ textAlign: "center", color: "textColor.secondary" }}
                  text="No body properties added yet"
                  variant="h6"
                />
              </Box>
            )}
          </Box>
        </Grid>
        <Grid item size={{ md: 12, xs: 12 }}>
          <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
            <Btn
              text="Cancel"
              sx={{
                width: "100%",
                maxWidth: "200px",
                backgroundColor: "danger.main",
              }}
              onClick={() => handleCancel(tab.id)}
            />

            <Btn
              text="Save"
              isLoading={isBtnLoading}
              sx={{
                width: "100%",
                maxWidth: "200px",
                backgroundColor: "primary.secondary",
              }}
              onClick={handleSaveBtnClick}
            />
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default WebhookOrganizers;
