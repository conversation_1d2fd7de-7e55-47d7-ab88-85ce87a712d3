import { Box, Grid2 as Grid } from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../../../global/atoms/typography/Typography";
import React, { useState, useEffect } from "react";
import { DropdownAtom } from "../../../../../../global/atoms/dropdown/Dropdown";
import { InputField } from "../../../../../../global/atoms/inputFields/InputField";
import { Btn } from "../../../../../../global/atoms/buttons/Button";
import { campaignValidators } from "../../../../../../../utils/validators/campaign";
import { Helpers, Alert } from "../../../../../../../utils/index";

const types = [
  { label: "String", value: "string" },
  { label: "Number", value: "number" },
  { label: "Boolean", value: "boolean" },
  { label: "Object", value: "object" },
  { label: "Array", value: "array" },
  { label: "Date", value: "date" },
]

const ParamContentOrganizer = ({ tab, handleCancel, handleSave }) => {
  const [inputs, setInputs] = useState(tab.data);

  const handleInputChange = (e, validation) => {
    const { name, value } = e.target;
    const isValid = true; // Replace with actual validation logic
    const errorMessage = "Invalid input"; // Replace with actual error message

    setInputs((prev) => ({
      ...prev, // Spread the previous state
      [name]: {
        value,
        isValid: validation.success,
        error: validation.message,
        isShowError: !validation.success,
      },
    }));
  };
  const handleDropdownChange = (name, value) => {
    const isValid = true; // Replace with actual validation logic
    const errorMessage = "Invalid input"; // Replace with actual error message
    setInputs((prev) => ({
      ...prev, // Spread the previous state
      [name]: {
        value,
        isValid: isValid,
        error: isValid ? null : errorMessage,
        isShowError: !isValid,
      },
    }));
  };

  const handleSaveBtnClick = () => {
    let inputsWithoutParameterId = { ...inputs };
    delete inputsWithoutParameterId.parameterId;
    let trimmedFields = Helpers.trimObjectValues(inputsWithoutParameterId);
    let validationResult = campaignValidators.global.isValidAllFields(trimmedFields);
  

    if (validationResult.success ) {
      let isExist = tab.parameters.find((item)=>item.data.name.value.toLowerCase() === inputs.name.value.toLowerCase() && item.id !== tab.id);
      if(isExist){
       setInputs({...inputs,name:{...inputs.name,isValid:false,isShowError:true,error:"Parameter name already exists"}})
        return;
      }
      handleSave(tab.id, trimmedFields);
    } else {
      // If there are validation errors, update the inputs state to show errors
      const updatedInputs = { ...inputs };

      // Iterate over the errors array and update the state
      validationResult.errors.forEach(({ field, error }) => {
        updatedInputs[field] = {
          ...updatedInputs[field],
          isValid: false, // Set field as invalid
          isShowError: true, // Show error message
          error: error.charAt(0).toUpperCase() + error.slice(1),
        };
      });

      // Update the inputs state with the new validation statuses
      setInputs(updatedInputs);
    }
  };

 

  return (
    <Grid container spacing={2}>
     
      <Grid item size={{ md: 6, xs: 12 }}>
        <InputField
          label="Name"
          value={inputs.name?.value}
          name="name"
          onChange={handleInputChange}
          type="text"
          validator={campaignValidators.webhooks.parameters.name}
          validation={{
            isShowError: inputs.name?.isShowError,
            error: inputs.name?.error,
          }}
          placeholder="Name"
          required
          disabled={false}
          fullWidth={true}
          sx={{ width: "100%" }}
        />
      </Grid>
      <Grid item size={{ md: 6, xs: 12 }}>
        <InputField
          label="Description"
          value={inputs.description?.value}
         
          name="description"
          onChange={handleInputChange}
          type="text"
          validator={campaignValidators.webhooks.parameters.description}
          validation={{
            isShowError: inputs.description?.isShowError,
            error: inputs.description?.error,
          }}
          placeholder="Enter Description"
          // required
          disabled={false}
          fullWidth={true}
          sx={{ width: "100%" }}
        />
      </Grid>
      <Grid item size={{ md: 6, xs: 12 }}>
        <DropdownAtom
          label="Type *"
          selectedItem={inputs.type?.value}
          name="type"
          onChange={(name, value) => handleDropdownChange(name, value)}
          options={types}
          validation={{
            isShowError: inputs.type?.isShowError,
            error: inputs.type?.error,
          }}
        />
      </Grid>
      <Grid item size={{ md: 6, xs: 12 }}>
        <DropdownAtom
          label="Required *"
          selectedItem={inputs.required?.value}
          name="required"
          onChange={(name, value) => handleDropdownChange(name, value)}
          validation={{
            isShowError: inputs.required?.isShowError,
            error: inputs.required?.error,
          }}
          options={[
            { label: "True", value: "true" },
            { label: "False", value: "false" },
          ]}
        />
      </Grid>

      <Grid item size={{ md: 12, xs: 12 }}>
        <Box sx={{ display: "flex", gap: 2, justifyContent: "flex-end" }}>
          <Btn
            text="Cancel"
            sx={{
              width: "100%",
              maxWidth: "200px",
              backgroundColor: "danger.main",
            }}
            onClick={() => handleCancel(tab.id)}
          />
          <Btn
            text="Save"
            sx={{
              width: "100%",
              maxWidth: "200px",
              backgroundColor: "primary.secondary",
            }}
            onClick={handleSaveBtnClick}
          />
        </Box>
      </Grid>
    </Grid>
  );
};

export default ParamContentOrganizer;
