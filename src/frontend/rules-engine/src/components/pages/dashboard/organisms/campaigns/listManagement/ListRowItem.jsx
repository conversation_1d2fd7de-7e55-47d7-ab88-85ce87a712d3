import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Box, IconButton, CircularProgress, Tooltip } from "@mui/material";
import { Typography<PERSON>tom } from "../../../../../global/atoms/typography/Typography";
import DynamicIcon from "../../../../../global/atoms/icons/Index";
import ListService from "../../../../../../Api/ListService";
import { Helpers } from "../../../../../../utils/generalFunctions/index";
import { Alert } from "../../../../../../utils/index";
import { useDispatch } from "react-redux";
import { registerConfirmation } from "../../../../../global/atoms/dialog/Index";
import { openDialog } from "../../../../../../redux/slices/global/confirmation";

/**
 * ListRowItem Component
 *
 * A React component that displays a single management item in a row format with actions such as delete.
 * Provides a loading effect when performing actions.
 *
 * @component
 * @example
 * const item = { name: "User Management", dataType: "String", text: "Description" };
 * const index = 0;
 * const managementList = [{ name: "User Management", dataType: "String", text: "Description" }];
 * const setManagementList = (updatedList) => console.log(updatedList);
 * return <ListManagement item={item} index={index} managementList={managementList} setManagementList={setManagementList} dynamicFields={["text"]} />;
 *
 * @param {Object} props - Component props.
 * @param {Object} props.item - The management item to display.
 * @param {string} props.item.listId- The id of the management item.
 * @param {string} props.item.name - The name of the management item.
 * @param {string} props.item.type - The data type of the management item.
 * @param {{label: string, id: string}[]} props.headers - Column headers for the list.
 * @param {import ("../../../../../../Api/ListService")["default"]["downloadList"]} props.apiDownloadListFunction - A function that downloads the list CSV by ID first param id, second param success callback, third param error callback
 * @param {import ("../../../../../../Api/ListService")["default"]["deleteList"]} props.apiDeleteFunction - A function that downloads the list CSV by ID first param id, second param success callback, third param error callback
 * @param {import ("../../../../../../Api/ListService")["default"]["sendBulkSms"]} props.apiSendBulkSmsFunction - A function that sends bulk sms to the list by ID first param id, second param success callback, third param error callback
 * @param {Array} props.fields - Additional dynamic fields to render in the row.
 * @param {number} props.index - The index of the item in the list.
 * @param {Function} props.fetchList - The function to fetch the list from the API.
 *
 * @returns {JSX.Element} The rendered ListManagement component.
 */
const ListRowItem = ({
  item,
  index,
  fields = [],
  headers,
  apiDownloadListFunction,
  apiDeleteFunction,
  apiSendBulkSmsFunction,
  fetchList,
}) => {
  /**
   * @state {boolean} loading - Tracks whether the delete operation is in progress.
   */
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  /**
   * Handles the deletion of the current management item.
   */
  const handleDelete = async () => {
    try {
      const deleteIem = async () => {
        setLoading(true);

        // Call the API to delete the item
        const response = await apiDeleteFunction(item.listId?item.listId:item.id);

        // If error
        if (!response.success) {
          Alert(response.data, "error");
          setLoading(false);
          return;
        }

        // We are here it means success

        Alert("List deleted successfully", "success");

        // Stop the loader
        setLoading(false);

        // Refetch the list
        fetchList();
      };

      // If the confirmation dialog is confirmed then delete the item
      const confirmationId = registerConfirmation((confirmed) => {
        if (confirmed) {
          deleteIem();
        }
      });

      dispatch(
        openDialog({
          title: "Do you want to delete the list?",
          message:
            "Do you want to delete the list? As if any campaign is using this list, it will break the campaign.",
          confirmationId,
        })
      );
    } catch (error) {
      console.error("Error deleting management item:", error);
    }
  };

  /**
   * Download the list
   */
  const downloadList = async () => {
    try {
      // Enable loader
      setLoading(true);

      const response = await apiDownloadListFunction(item.listId?item.listId:item.id);

      if (response.success) {
        Helpers.downloadCsvFileByBlob(
          response.data.blob,
          response.data.filename
        );
      }
    } catch (error) {
      alert(error);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handles the send bulk sms api for the bulk notification list
   */
  const sendBulkSms = async () => {
    try {
      const sendSms = async () => {
        setLoading(true);

        // Call the API to send the bulk sms
        const response = await apiSendBulkSmsFunction(item.listId?item.listId:item.id);

        // If error
        if (!response.success) {
          Alert(response.data, "error");
          setLoading(false);
          return;
        }

        // We are here it means success

        Alert("Notification sent to all contacts successfully!", "success");

        // Stop the loader
        setLoading(false);

        // Refetch the list
        fetchList();
      };

      // If the confirmation dialog is confirmed then sendSms
      const confirmationId = registerConfirmation((confirmed) => {
        if (confirmed) {
          sendSms();
        }
      });

      dispatch(
        openDialog({
          title: "Are you sure?",
          message:
            "Do you want to send bulk SMS to the list? It may take some time depending on the size of the contact list!",
          confirmationId,
        })
      );
    } catch (error) {
      console.error("Error sending bulk sms to the list item:", error);
    }
  };

  return (
    <Box
      sx={{
        position: "relative",
        display: "grid",
        gridTemplateColumns: `repeat(${1 + fields.length}, 1fr)`, // Adjust columns dynamically
        gap: 2,
        textAlign: "left",
        padding: "8px 16px",
        alignItems: "center",
        backgroundColor: index % 2 === 0 ? "#ffffff" : "#f9f9f9", // Alternating row colors
        borderBottom: "1px solid #ddd",
      }}
    >
      {/* Loading Overlay */}
      {loading && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            backgroundColor: "rgba(255,255,255,0.8)", // Semi-transparent white overlay
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            zIndex: 1, // Place above other elements
          }}
        >
          <CircularProgress color="inherit" />
        </Box>
      )}

      {/* Management Item Name */}
      {/* <TypographyAtom variant="body1" text={item.name} /> */}

      {/* Management Item Data Type */}
      {/* <TypographyAtom variant="body1" text={item.dataType} /> */}

      {/* Render Dynamic Fields */}
      {headers
        .filter((header) => header.id !== "action") // Skip headers with id === "action"
        .map((header) => {
          const valueT = item[header.id] || "N/A"; // Find matching value in item

          if (header.id === "name") {
            return (
              <TypographyAtom
                key={header.id} // Use header.id as the unique key
                variant="body1"
                text={valueT}
              />
            );
          }

          return (
            <TypographyAtom
              key={header.id} // Use header.id as the unique key
              variant="body1"
              text={valueT}
            />
          );
        })}

      <Box
        sx={{
          display: "flex",
          flexDirection: "row",
          gap: 1, // Add spacing between rows
          alignItems: "flex-start", // Align items to the start of the container
        }}
      >
        {/* Delete Button */}
        {apiDeleteFunction && (
          <Tooltip title="Delete">
            <IconButton
              sx={{ justifySelf: "start" }}
              color="error"
              onClick={handleDelete}
            >
              <DynamicIcon iconName="Delete" size={20} />
            </IconButton>
          </Tooltip>
        )}

        {/* Download Button */}
        <Tooltip title="Download list csv">
          <IconButton
            sx={{ justifySelf: "start" }}
            color="primary"
            onClick={downloadList}
          >
            <DynamicIcon iconName="Download" size={20} />
          </IconButton>
        </Tooltip>

        {/* Send Bulk Sms Button */}
        {apiSendBulkSmsFunction && (
          <Tooltip title="Send Bulk Sms">
            <IconButton
              sx={{ justifySelf: "start" }}
              color="primary"
              onClick={sendBulkSms}
            >
              <DynamicIcon iconName="Send" size={20} />
            </IconButton>
          </Tooltip>
        )}
      </Box>
    </Box>
  );
};

ListRowItem.propTypes = {
  /**
   * @prop {Object} item - The management item to display.
   * @prop {string} item.name - The name of the management item.
   * @prop {string} item.dataType - The data type of the management item.
   * @prop {Object} [item.fields] - Additional dynamic fields to display in the row.
   */
  item: PropTypes.shape({
    name: PropTypes.string.isRequired,
    dataType: PropTypes.string,
    text: PropTypes.string, // Example dynamic field
  }).isRequired,

  /**
   * @prop {number} index - The index of the item in the list.
   */
  index: PropTypes.number.isRequired,

  /**
   * @prop {Array} fields - Array of additional field names to render dynamically.
   */
  fields: PropTypes.arrayOf(PropTypes.string),
};

export default ListRowItem;
