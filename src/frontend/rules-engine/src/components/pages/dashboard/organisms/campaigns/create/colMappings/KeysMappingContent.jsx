import { Box, Typography, Grid2 as Grid } from "@mui/material";
import DropdownAtom from "../../../../../../global/atoms/dropdown/Dropdown";
import { Typography<PERSON>tom } from "../../../../../../global/atoms/typography/Typography";
import { Btn as Btn<PERSON>tom } from "../../../../../../global/atoms/buttons/Button";
import { useEffect, useId, useState } from "react";
import SectionHeaderMolecule from "../../../../molecules/campaigns/create/SubSectionHeader";
import SectionTabHOC from "../../../../molecules/campaigns/create/SectionTabHOC";
import PropTypes from "prop-types";
import {
  collectionDropdown,
  collectionMappingTabData,
} from "../../../../../../../utils/dummyData/Campaigns";
import SubSectionHOC from "../../../../molecules/campaigns/create/SubSectionHOC";
import SubSectionRowMolecule from "../../../../molecules/campaigns/create/SubSectionRow";
import KeyMappingContentOrganizer from "../../../../organisms/campaigns/create/colMappings/CollectionMappingContent";
import { useDispatch, useSelector } from "react-redux";
import { Alert, campaignValidators, Helpers } from "../../../../../../../utils";
import { setCampaign } from "../../../../../../../redux/slices/tabs/campaigns/campaign";
import { InputField } from "../../../../../../global/atoms/inputFields/InputField";
import React from "react";
// const keys = ["entityId", "contextId", "propertyId"];
const keys = ["propertyId"];

const generateEntitiesOptions = (entities) => {
  let options = entities.map((entity) => {
    return {
      value: entity.entityId,
      label: entity.entityName,
    };
  });

  return options;
};
const generateContextOptions = (entity) => {
  if (!entity) {
    return [];
  }
  let contextOptions = entity.transactionContexts.map((context) => {
    return {
      value: context.contextId,
      label: context.contextName,
    };
  });

  // let propertyOptions = entity.properties.map((property) => {
  //   return {
  //     value: property.name,
  //     label: property.name,
  //   };
  // })

  return contextOptions;
};
/**
 *
 * @param {object} entities The entities array
 * @param {string} entityId The entity id
 * @param {string} contextId The context id
 * @returns {{ value:string,label:string }[]} The properties options
 */
const generatePropertiesOptions = (entities, entityId, contextId) => {
  if (!entityId || !contextId) {
    return [];
  }
  let entity = entities.find((entity) => entity.entityId === entityId);
  let context = entity.transactionContexts.find(
    (context) => context.contextId === contextId
  );
  let propertiesOptions = context.properties.map((property) => {
    return {
      value: property.propertyId,
      label: property.name,
    };
  });

  return propertiesOptions;
};
/**
 * @typedef {import('../../../../../../../../jsDocs/components/dashboard').KeyMappingContentInputsData} InputsState
 * * @typedef {import('../../../../../../../../jsDocs/components/dashboard').InputField} InputField
 */

/**
 * This component is used to render the content of the collection mapping which contain the entityId, contextId and property inputs
 *
 * It renders three input fields, one for the entityId, one for the contextId and one for the property
 * It also renders a cancel and a save button
 * The cancel button will cancel the current mapping and go back to the previous tab
 * The save button will validate the inputs and if they are valid, it will call the handleSaveKeyMapping function with the form data
 * If there are validation errors, it will update the inputs state to show errors
 * And displays the first error message
 */
const KeyMappings = ({ handleCancel, tab, setTab }) => {
  const dispatch = useDispatch();
  const { campaign, receivedData } = useSelector((state) => state.campaign);
  const { entityId, contextId } = campaign;
  let { entities } = receivedData;
  /** @type {[InputsState,  React.Dispatch<React.SetStateAction<InputsState>>]} */
  const [inputs, setInputs] = useState(
    // Helpers.defaultValues(keys, tab.inputs, [])
    Helpers.defaultValues(keys, tab.inputs, [])
  );

  /**
   * Handles the submit event of the form
   *
   * It validates all fields using isValidAllFields
   * And if the form is valid, it calls the handleSaveKeyMapping function with the form data
   * If there are validation errors, it updates the inputs state to show errors
   * And displays the first error message
   */
  const handleSubmit = () => {
    // Validate all fields using isValidAllFields
    const validationResult = campaignValidators.global.isValidAllFields(inputs);

    if (validationResult.success) {
      let obj = {
        ...Helpers.returnValues(inputs),
      };
      setTab({
        propertyId: Math.random(), // Generate a new random id
        inputs: { ...obj }, // Store the inputs in the `values` field
        mode: "editing", // Switch back to viewing mode after save
        columns: [{ content: obj.propertyId, gridSize: 6 }], // Set the content property
      });
    } else {
      // If there are validation errors, update the inputs state to show errors
      const updatedInputs = { ...inputs };

      // Iterate over the errors array and update the state
      validationResult.errors.forEach(({ field, error }) => {
        updatedInputs[field] = {
          ...updatedInputs[field],
          isValid: false, // Set field as invalid
          isShowError: true, // Show error message
          error, // Set error message
        };
      });

      // Update the inputs state with the new validation statuses
      setInputs(updatedInputs);

      // // Display the first error message
      // const firstErrorMessage = validationResult.errors[0].error;
      // Alert(firstErrorMessage, "error");
    }
  };

  /**
   * Handles the change event of the dropdowns (Entity and Context)
   *
   * It updates the inputs state with the new value and sets the entityTransactions state
   * if the name is "entityId". It also generates the context options based on the selected entity
   */
  const handleDropdownChange = (name, value) => {
    let obj = Helpers.updateInputField(inputs, { name: name, value: value });
    // TODO: here check the contexid
    setInputs(obj);
    setTab({
      propertyId: obj.propertyId.value || Math.random(), // Generate a new random id
      inputs: { ...obj }, // Store the inputs in the `values` field
      mode: "editing", // Switch back to viewing mode after save
      columns: [{ content: obj.propertyId.value, gridSize: 6 }], // Set the content property
    });

    if (name === "entityId") {
      let entityObj = entities.find((entity) => entity.entityId === value);

      let contextOptions = Helpers.generateContextOptions(entityObj);
      setEntityTransactions(contextOptions);
    }

    if (name === "contextId") {
      let propertiesOptions = Helpers.generateTransContextOptions(
        entities,
        inputs.entityId.value,
        value
      );

      setPropertiesOptions(propertiesOptions);
    }
  };
  /**
   * Handles the change event of the input fields (Entity, Context, Property)
   *
   * It updates the inputs state with the new value and the status of the input field
   * @param {object} e - The event object
   * @param {object} status - Validation status of the input field
   */
  const handleInputChange = (e, status) => {
    const { name, value } = e.target;
    let obj = Helpers.updateInputField(inputs, {
      name: name,
      value: value,
      status,
    });
    setInputs(obj);
  };

  // Generate the context options based on the selected entity
  useEffect(() => {
    let { entityId, contextId } = tab.inputs;
    if (entityId && contextId) {
      let obj = entities.find((entity) => entity.entityId === entityId);
      let contextOptions = generateContextOptions(obj);
      setEntityTransactions(contextOptions);
      let propertiesOptions = Helpers.generateTransContextOptions(
        entities,
        entityId,
        contextId
      );
      setPropertiesOptions(propertiesOptions);
    }
  }, [tab.inputs]);

  return (
    <Box sx={{ width: "100%", padding: 2 }}>
      <Grid container spacing={2}>
        <Grid item size={{ xs: 12, sm: 6, md: 6 }}>
          <DropdownAtom
            label="Property name *"
            name={"propertyId"}
            key={"propertyId"}
            onChange={handleDropdownChange}
            selectedItem={inputs.propertyId.value}
            disabled={!entityId || !contextId}
            options={generatePropertiesOptions(entities, entityId, contextId)}
            sx={{ backgroundColor: "bgPrimary.main" }}
            validation={{
              isShowError: inputs.propertyId.isShowError,
              error: inputs.propertyId.error,
            }}
          />
          {!contextId && (
            <TypographyAtom
              text="Please select context id first"
              variant="caption"
              sx={{ color: "error.main", mt: 1 }}
            />
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default KeyMappings;
