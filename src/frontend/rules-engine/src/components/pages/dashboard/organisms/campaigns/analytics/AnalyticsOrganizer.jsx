import { Box, Grid2 as Grid } from "@mui/material";
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../../global/atoms/typography/Typography";
import React, { memo, useState } from "react";
import AnalyticsTileCard from "../../../atoms/pages/campaign/analytics/TitleCard";
import { Btn as BtnAtom } from "../../../../..//global/atoms/buttons/Button";
import { Helpers } from "../../../../../../utils/generalFunctions/index";
/** parameter) item: {
    name: string;
    fields: ({
        name: string;
        prefix: string;
    } | {
        name: string;
        prefix?: undefined;
    })[];
    data: {
        timestamp: string;
        values: number[];
    }[]; */

/** 
     * The AnalyticsOrganizer component is a molecule that organizes the analytics data into a tile card.
     * @param {Object} props - The props of the component.
     * @param {string} props.name - The name of the campaign.
     * @param {Object[]} props.fields - The fields of the campaign.
     * @param {string} props.fields.name - The name of the field.
     * @param {string} props.fields.prefix - The prefix of the field.
     * @param {Object[]} props.data - The data of the campaign.
     * @param {string} props.data.timestamp - The timestamp of the data.
     * @param {number[]} props.data.values - The values of the
     * @param {string} props.id - The ID of the campaign.
     * @param {Function} props.handleDetailsBtnClick - The function to handle the details button click.
    @returns {JSX.Element} The JSX element of the component.
     */
const AnalyticsOrganizer = ({
  name,
  id,
  fields,
  data,
  handleDetailsBtnClick,
}) => {
  let res = Helpers.prepareAnalyticsFieldsData(fields, data);

  let [boxes] = useState(res);


  // Check if the all boxes item.value is empty or zero
  const isAllEmpty = boxes[0].values.every((item) => !item.value);



// Function to get a random color from the colors array
const getRandomColor = () => {
  return colors[Math.floor(Math.random() * colors.length)];
};

const getColorByIndex = (index) => {

  const indexT = index > colors.length ? index % colors.length : index;
  
  return colors[indexT];

}

  return (
    <Box sx={{ mb: 2,boxShadow:2,px:2,py:2 }}>
      {/* Display the campaign name */}
      <TypographyAtom variant="h4" sx={{ fontWeight: "bold" }} text={name} />
      {/* Display analytics metrics in tiles */}
      <Grid
        container
        rowSpacing={2}
        columnSpacing={2}
        sx={{
          display: "flex",
          mt: 2,
          justifyContent: { md: "flex-start", xs: "center" },
        }}
      >
        {boxes[0]?.values.map((item,index) => {
           const color = colors[index % colors.length]; // Cycle through colors
          return (
            <AnalyticsTileCard key={index} size={{ md: 2, sm: 4, xs: 6 }} data={item} 
            sx={{backgroundColor: color, color:'mainColor.main',}} />
          );
        })}
      </Grid>
      {/* "View Chart" button, disable if no data */}
      {
          <BtnAtom
            disabled={isAllEmpty}
            text="View Chart"
            sx={{ mt: 2,color:'mainColor.main',borderRadius:'5px' }}
            onClick={() => handleDetailsBtnClick(id)}
          />
      }

    </Box>
  );
};

export default memo(AnalyticsOrganizer);


// Define an array of beautiful colors
const colors = [
  "#7E5109", // Golden Brown
  "#6C3483", // Purple Plum
  "#E67E22", // Pumpkin Orange
  "#16A085", // Teal Green
  "#581845", // Deep Purple
  "#B9770E", // Gold Ochre
  "#6E2C00", // Deep Ochre
  "#D5DBDB", // Light Gray
  "#9B59B6", // Orchid Purple
  "#1F618D", // Deep Sea Blue
  "#E59866", // Peach
  "#2C3E50", // Midnight Blue
  "#7D3C98", // Royal Purple
  "#1ABC9C", // Bright Aqua
  "#F7DC6F", // Soft Yellow
  "#3357FF", // Bright Blue
  "#A569BD", // Violet Lavender
  "#B03A2E", // Rust Red
  "#148F77", // Aqua Deep
  "#FF5733", // Vibrant Red Orange
  "#117A65", // Jungle Green
  "#C70039", // Crimson Red
  "#F1C40F", // Golden Yellow
  "#2980B9", // Ocean Blue
  "#900C3F", // Dark Magenta
  "#5DADE2", // Soft Blue
  "#DAF7A6", // Soft Mint Green
  "#AF601A", // Burnt Sienna
  "#D35400", // Burnt Orange
  "#2ECC71", // Spring Green
  "#FFC300", // Bright Yellow Gold
  "#3498DB", // Soft Sky Blue
  "#8E44AD", // Amethyst
  "#2874A6", // Arctic Blue
  "#E74C3C", // Bright Red
  "#5B2C6F", // Rich Violet
  "#76448A", // Lavender Purple
  "#2471A3", // Cerulean Blue
  "#F39C12", // Orange Sunset
  "#33FF57", // Bright Lime Green
  "#7FB3D5", // Light Sky Blue
  "#4A235A", // Royal Violet
  "#196F3D", // Pine Green
  "#1C2833", // Charcoal Blue
  "#16A085", // Teal Green
  "#34495E", // Steel Blue Gray
  "#283747", // Navy Gray
  "#27AE60", // Forest Green
  "#512E5F", // Dark Purple
];