import React, { useEffect } from "react";
import { useState } from "react";
import { <PERSON><PERSON>, Helpers } from "../../../../../../../../../utils";
import { useSelector } from "react-redux";
import { Box, Grid2 as Grid } from "@mui/material";
import SectionHeaderWithButton from "../../../../../../atoms/global/SectionHeaderWithButton";
import SectionHeaderMolecule from "../../../../../../molecules/campaigns/create/SubSectionHeader";
import ReusableSectionHOC from "../../../../../../atoms/global/reusableSectionHOC";
import ConditionContentOrganizer from "../ConditionContent";
import { TypographyAtom } from "../../../../../../../../global/atoms/typography/Typography";

/**
 * Sub component for the condition type 'LOGICAL'. This component is responsible for rendering the components for the condition type 'LOGICAL'.
 * @param {Object} param0 - The object.
 * @param {{conditions: Array.<any>}} param0.parameters - The parameters data object containing the condition data.
 * @param {{entityId: string, contextId: string}} param0.entityAndContext - The entity and context IDs of the current rule.
 * @param {Function} [param0.onDataChange] - The callback function to be called when any field data changes.
 * @returns {{
 *   helperObj: {getData: () => {value: any[], isValid: boolean, error: string, isShowError: boolean}, setData: Function}, // Returns the helper object containing the getData and setData functions.
 *   ConditionComparisonComp: React.JSX.Element // React component responsible for rendering the comparison input fields.
 * }} - Returns an object containing utility functions and the React component
 */

/**
 * Sub component for the condition type 'LOGICAL'. This component is responsible for rendering the components for the condition type 'LOGICAL'.
 * @param {Object} param0 - The object.
 * @param {{conditions: Array.<any>}} param0.parameters - The parameters data object containing the condition data.
 * @param {{entityId: string, contextId: string}} param0.entityAndContext - The entity and context IDs of the current rule.
 * @param {Function} [param0.onDataChange] - The callback function to be called when any field data changes.
 * @param {number} param0.nestedLevel - The level of the condition component in the hierarchy.
 * @returns {React.JSX.Element}
 */
const ConditionLogicalComponent = ({
  parameters = {},
  onDataChange,
  entityAndContext,
  nestedLevel,
  helperObj,
  ruleType,
}) => {
  const [conditions, setConditions] = useState([]);

  // Get the received data from the redux store that is received from the API.
  /** @type {import("../../../../../../../../../../../jsDocs/redux/store").Store['campaign']} */
  const { campaign, receivedData } = useSelector((state) => state.campaign);

  // Get the data
  helperObj.getData = () => {
    return {
      conditions: {
        value: Helpers.returnArrayValues(conditions),
        isValid: true,
        error: "",
        isShowError: false,
      },
    };
  };
  // Set the data
  helperObj.setData = setConditions;

  // Conditions util
  const conditionsUtil = Helpers.createRowsUtil(
    conditions,
    setConditions,
    "condition",
    Alert
  );

  /** Conditions columns util */
  const conditionsColumnsUtil = Helpers.createColumnsUtil([
    {
      label: "Type",
      gridSize: 4,
      content: { key: "conditionTypeId", def: "Create condition" },
    },
    { label: "Operator", gridSize: 4, content: { key: "operator" } },
  ]);

  const localHandleCancel = (index) => {
    setConditions((itemsArray) => {
      // Check if the item is a new condition that needs to be removed
      const item = itemsArray[index];
      if (item && item.id === "new_CONDITION") {
        // If it's a new unsaved condition, remove it completely
        return itemsArray.filter((_, iIndex) => iIndex !== index);
      } else {
        // For existing conditions, just switch back to viewing mode
        return itemsArray.map((item, iIndex) => {
          return iIndex === index ? { ...item, mode: "viewing" } : item;
        });
      }
    });
  };

  // ----------- Format and add conditions item ------------

  /* ConditionContentOrganizer handleSave function returns the json schema formated data and it is recursive compoment and here
       we are using as the row of the conditions. So for that we need the proper formated object to be added in the conditions array.
       @param {string} index - The tab index
       @param {object} data - The data received from the ConditionContentOrganizer handleSave function
       */
  const formatAndAddConditionsItem = (index, data) => {
    setConditions((itemsArray) => {
      const updatedData = itemsArray.map((item, iIndex) => {
        return iIndex == index
          ? {
              ...item,
              data: data,
              columns: conditionsColumnsUtil.generateItemContentColumns(data),
              mode: "viewing",
              id: Math.random(),
            }
          : item;
      });

      return updatedData;
    });
  };

  // -------------------------------------------------------

  // ------------ On mount parameters changes then set the data ------------

  useEffect(() => {
    const { conditions } = parameters;

    // If there are no conditions then return
    if (!conditions || conditions.length == 0) return;

    // Update states using the received data
    setConditions(
      conditions.map((item) => ({
        id: Math.random(),
        columns: conditionsColumnsUtil.generateItemContentColumns(item),
        mode: "viewing",
        data: item,
      }))
    );
  }, []);

  // ---------------------------------------------------------------------------

  return (
    <>
      <Grid my={4} size={{ xs: 12, sm: 12, md: 12 }} item md={6}>
        <SectionHeaderWithButton
          title="Conditions"
          onButtonClick={() => {
            if (conditions.length >= 10) {
              Alert(
                "Cannot add more than 10 conditions. Limit exceeded",
                "error"
              );
              return;
            }
            conditionsUtil.handleAddNewItem();
          }}
        />
      </Grid>

      <Grid key="condt_wrap" size={{ xs: 12, sm: 6, md: 12 }} item md={12}>
        <Box key="condtn_wrp_box" sx={{ mt: 2, width: "100%" }}>
          <SectionHeaderMolecule
            sx={{}}
            columns={conditionsColumnsUtil.columnsOfSectionHeader}
          />
          {conditions.length > 0 &&
            conditions.map((tab, index) => (
              <ReusableSectionHOC
                key={index}
                index={index}
                tab={tab}
                columns={tab.columns}
                rows={tab.rows}
                onEdit={() => conditionsUtil.handleEditBtnClick(index)}
                onDelete={() => conditionsUtil.handleDeleteItem(index)}
                content={
                  <ConditionContentOrganizer
                    key={tab.id}
                    // handleSave={(inputs) =>
                    //   actionsUtil.handleSaveFunc(tab.id, inputs)
                    // }
                    handleSave={(data) =>
                      formatAndAddConditionsItem(index, data)
                    }
                    handleCancel={() => localHandleCancel(index)}
                    tab={tab}
                    entityAndContext={entityAndContext}
                    nestedLevel={nestedLevel + 1} // Plus 1 to the nested level
                    ruleType={ruleType}
                  />
                }
              />
            ))}
          {/* If there are no condition, display a message */}
          {conditions.length < 1 && (
            <Box sx={{ mt: 2 }}>
              <TypographyAtom
                sx={{ textAlign: "center", color: "textColor.secondary" }}
                text="No conditions added yet."
                variant="h6"
              />
            </Box>
          )}
        </Box>
      </Grid>
    </>
  );
};

export default ConditionLogicalComponent;
