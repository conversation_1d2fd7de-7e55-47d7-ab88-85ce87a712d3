import { Box, Grid2 as Grid, Typography } from "@mui/material";
import { <PERSON>po<PERSON><PERSON><PERSON> } from "../../../../../../global/atoms/typography/Typography";
import React, { useEffect, useState } from "react";
import { InputField } from "../../../../../../global/atoms/inputFields/InputField";
import DropdownAtom from "../../../../../../global/atoms/dropdown/Dropdown";
import { Btn } from "../../../../../../global/atoms/buttons/Button";
import { authValidators } from "../../../../../../../utils/validators/auth";
import { Alert } from "../../../../../../../utils";
import { Checkbox } from "../../../../../../global/atoms/checkboxs/Checkbox";
import PropTypes from 'prop-types';

const AddUser = ({
  status = "creating",
  data,
  onSubmit,
  isLoading,
  setIsLoading,
}) => {
  /**
   * This is the state for the roles
   * @type {[object, React.Dispatch<React.SetStateAction<object>>]}
   */
  const [roles, setRoles] = React.useState([
    {
      label: "User Admin",
      value: 1,
    },
    {
      label: "Campaign Admin",
      value: 2,
    },
    // {
    //   label: "API",
    //   value: 3,
    // },
  ]);
  //   const ROLES = {
  //     ADMIN:
  //     {
  //         id: 1,
  //         slug: 'user-admin',
  //         label: 'User Administrator'
  //     },
  //     USER:
  //     {
  //         id: 2,
  //         slug: 'campaign-admin',
  //         label: 'Campaign Administrator'
  //     },
  //     API:
  //     {
  //         id: 3,
  //         slug: 'api',
  //         label: 'API User'}
  // }
  /**
   * This is the state for the inputs
   * @type {[object, React.Dispatch<React.SetStateAction<object>>]}
   */
  const [inputs, setInputs] = useState({
    // name: "",
    username: "",
    password: "",
    roleId: roles[1].value,
    confirmPassword: "",
    // sendEmail: false,
  });
  /**
   * This is the function to handle the change of the inputs
   * @param {React.ChangeEvent<HTMLInputElement>} e - The change event
   */
  const handleChange = (e) => {
    const { name, value } = e.target;
    setInputs({
      ...inputs,
      [name]: value,
    });
  };
  /**
   * This is the function to handle the submit of the form
   */
  const handleSubmit = () => {

    try {
      const isCreateNew = status === "creating";
      
      // Validate the form data
      let isFormValid = authValidators.isUserRegisterFormValid(
        isCreateNew,
        inputs
      );
      
      if (!isFormValid.success) {
        // If the form is not valid, display an alert with the error message
        return Alert(isFormValid.message, "error");
      }

      // Prepare request data
      const requestData = {
        username: inputs.username,
        password: inputs.password,
        // roleId: inputs.roleId,
        // sendEmail: inputs.sendEmail,
        // profile_data: {
        //   // full_name: inputs.name
        // }
      };

      

      onSubmit(requestData);
      
    } catch (error) {
      Alert("Error submitting form", "error");
    }
  };
  /**
   * This is the function to handle the useEffect
   */
  useEffect(() => {
    if (status == "editing") {
      setInputs({
        ...inputs,
        ...data,
      });
    }
  }, []);
  /**
   * This is the function to render the form
   * @returns {JSX.Element} The form
   */
  return (
    <Grid container spacing={2}>
      <Grid size={12}>
        <TypographyAtom
          variant="h5"
          sx={{ fontWeight: "bold" }}
          text={status === "creating" ? "Add New User" : "Edit User"}
        />
      </Grid>
      {/* <Grid size={12}>
        <InputField
          label="Name"
          variant="outlined"
          name="name"
          value={inputs.name}
          onChange={handleChange}
          required
        />
      </Grid> */}
      <Grid size={12}>
        <InputField
          label="Username"
          variant="outlined"
          name="username"
          type="text"
          value={inputs.username}
          onChange={handleChange}
          required
        />
      </Grid>

      <Grid size={12}>
        <InputField
          label="Password"
          variant="outlined"
          name="password"
          type="password"
          value={inputs.password}
          onChange={handleChange}
          required
          validation={{ isShowError: false, error: "" }}
        />
      </Grid>
      <Grid size={12}>
        <InputField
          label="Confirm Password"
          variant="outlined"
          name="confirmPassword"
          type="password"
          value={inputs.confirmPassword}
          onChange={handleChange}
          required
        />
      </Grid>
      {/* <Grid size={12}>
        <DropdownAtom
          name="roleId"
          selectedItem={inputs.roleId}
          label="Role"
          onChange={(name, value) => setInputs({ ...inputs, [name]: value })}
          options={roles}
          validation={{ isShowError: false, error: "" }}
        />
      </Grid> */}

      <Grid size={12} display={"none"}>
        <Checkbox
          checked={inputs.sendEmail}
          onChange={(e) => setInputs({ ...inputs, sendEmail: e.target.checked })}
          label="Send user details to email"
          size="medium"
          color="primary"
        />
      </Grid>

      <Grid size={12}>
        <Btn
          onClick={handleSubmit}
          isLoading={isLoading}
          sx={{ width: "100%" }}
          text="Submit"
        />
      </Grid>
    </Grid>
  );
};

export default AddUser;
