import { Box, List, ListItem, Divider } from "@mui/material";
import PropTypes from "prop-types";
import { Typography<PERSON>tom } from "../../../../global/atoms/typography/Typography";
import DynamicIcon from "../../../../global/atoms/icons/Index";
import { useDispatch, useSelector } from "react-redux";
import { setGlobalTab } from "../../../../../redux/slices/global/dashboard";
import React from "react";

const commonSX = {
  listItem: (isSidebarOpen, isSelected, sectionColor) => ({
    cursor: "pointer",
    backgroundColor: isSelected ? sectionColor : null,
    color: isSelected ? "mainColor.main" : "textColor.main",
    display: "flex",
    "& .MuiTypography-root": {
      color: isSelected ? "mainColor.main" : "textColor.main",
    },
    alignItems: "center",
    justifyContent: isSidebarOpen ? "start" : "center",
    width: {
      md: !isSidebarOpen ? "45px" : "100%",
    },
    height: {
      md: !isSidebarOpen ? "45px" : null,
    },
    borderRadius: {
      md: !isSidebarOpen ? "50px" : null,
    },
    mt: {
      md: !isSidebarOpen ? 1 : null,
    },
    "&:hover": {
      backgroundColor: sectionColor,
      color: "mainColor.main",
      "& .MuiListItemIcon-root": {
        color: "mainColor.main",
      },
      "& .MuiTypography-root": {
        color: "mainColor.main",
      },
    },
  }),
  icon: (isSidebarOpen, size) => ({
    fontSize: isSidebarOpen ? size : size + 10,
  }),
};

/**
 * Renders the list of menu items.
 *
 * @param {Array} items - The list of menu items.
 * @param {boolean} isSidebarOpen - If the sidebar is open.
 * @param {string} selectedTab - Currently selected tab.
 * @param {function} handleClick - Function to handle menu item clicks.
 * @param {string} sectionColor - Color for the background (based on section).
 * @returns {JSX.Element} - Rendered list items.
 */
const renderMenuItems = (
  items,
  isSidebarOpen,
  selectedTab,
  handleClick,
  sectionColor
) => {
  return (
    <React.Fragment>
      {items.map((item) => (
        <ListItem
          key={item.id}
          disableGutters
          onClick={() => handleClick(item.id)}
          sx={commonSX.listItem(
            isSidebarOpen,
            item.id === selectedTab,
            sectionColor
          )}
        >
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              minWidth: "200px",
              justifyContent: isSidebarOpen ? "start" : "center",
            }}
          >
            <DynamicIcon
              sx={commonSX.icon(isSidebarOpen, 20)}
              iconName={item.icon}
            />
            {isSidebarOpen && <TypographyAtom sx={{ ml: 2 }} text={item.text} />}
          </Box>
        </ListItem>
      ))}
    </React.Fragment>
  );
};

/**
 * Custom Sidebar component with Account and Support sections.
 *
 * @param {Object} props - Component props
 * @param {boolean} props.isSidebarOpen - Whether the sidebar is open.
 * @returns {JSX.Element} - The rendered custom sidebar component.
 */
const CampDashboardSidebar = ({ isSidebarOpen }) => {
  const dispatch = useDispatch();
  const { tab } = useSelector((state) => state.dashboard);
  const { user } = useSelector((state) => state.auth);
  const handleMenuItemClick = (id) => {
    dispatch(setGlobalTab(id));
  }; 

  const accountItems = [
    { text: "Rule Sets", icon: "CampaignOutlined", id: "campaigns" },
    { text: "List Management", icon: "ListAlt", id: "listManagement" },
    { text: "Webhooks", icon: "CallMade", id: "webhooks" },
    {
      text: "Bulk Notification List",
      icon: "CampaignOutlined",
      id: "bulknotificationList",
    },
    ...(user?.username === "admin"
      ? [
          { text: "User Management", icon: "ManageAccounts", id: "userManagement" },
          { text: "API Keys", icon: "PersonAdd", id: "apiKeys" },
        ]
      : []),
    { text: "Profile", icon: "AccountCircle", id: "accountManagement" },
    // { text: "Overview", icon: "Equalizer", id: "overview" },
    // { text: "Settings", icon: "Settings", id: "settings" },
  ];

  const supportItems = [
    // { text: "Help", icon: "HelpOutlineOutlined", id: "help" },
    // { text: "Documentation", icon: "CommentOutlined", id: "documentation" },
  ];

  return (
    <Box
      sx={{
        width: "100%", // Custom sidebar width
        flexShrink: 0,
        backgroundColor: "white",
        boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
        height: "100vh",
        pt: 3,
        pb: 3,
      }}
    >
      {isSidebarOpen && (
        <TypographyAtom
          text="Admin Panel"
          sx={{
            textAlign: "start",
            ml: 2,
            mt: 4,
          }}
          variant="h6"
          fontWeight="bold"
        />
      )}
      {/* Account Section */}
      <List
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        {renderMenuItems(
          accountItems,
          isSidebarOpen,
          tab,
          handleMenuItemClick,
          "primary.main"
        )}
      </List>

      <Divider />
      {/* 
      {isSidebarOpen && (
        <TypographyAtom
          text="Support"
          variant="h6"
          sx={{
            textAlign: "start",
            ml: 2,
            mt: 2,
          }}
        />
      )} */}

      {/* Support Section */}
      {/* <List
        sx={{ display: "flex", flexDirection: "column", alignItems: "center" }}
      >
        {renderMenuItems(
          supportItems,
          isSidebarOpen,
          tab,
          handleMenuItemClick,
          "secondary.main"
        )}
      </List> */}
    </Box>
  );
};

CampDashboardSidebar.propTypes = {
  isSidebarOpen: PropTypes.bool.isRequired, // Boolean to determine if the sidebar is open
};

export default CampDashboardSidebar;
