import React, { useState } from "react";
import PropTypes from "prop-types";
import <PERSON><PERSON><PERSON><PERSON> from "../../../../global/atoms/modal/Index";
import { Btn } from "../../../../global/atoms/buttons/Button";
import {
  Box,
  Button,
  Modal,
  TextField,
  MenuItem,
  Typography,
  CircularProgress,
} from "@mui/material";
import { Alert } from "../../../../../utils/index";

/**
 * Modal for Adding a New List Item with Dynamic Fields.
 *
 * @component
 * @example
 * const additionalFields = [
 *   { name: "text", label: "Text", type: "string", required: true },
 * ];
 * return (
 *   <AddListModal
 *     open={true}
 *     onClose={() => {}}
 *     onSubmit={(data) => console.log(data)}
 *     additionalFields={additionalFields}
 *   />
 * );
 *
 * @param {boolean} open - Whether the modal is open.
 * @param {Function} onClose - Function to close the modal.
 * @param {Function} onSubmit - Function to submit the form data.
 * @param {Function} fetchList - Function to fetch the list from the API.
 * @param {Array} fields - Array of additional field configurations.
 */
const AddListModal = ({ open, onClose, onSubmit, fields, id, fetchList }) => {
  const [loading, setLoading] = useState(false);

  const [formData, setFormData] = useState({
    ...Object.fromEntries(fields.map((field) => [field.name, ""])), // Initialize additional fields
  });

  // Handle form field change
  const handleChange = (e) => {
    const { name, value } = e.target;

    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  // Handle file selection
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.type === "text/csv") {
      setFormData((prevData) => ({ ...prevData, file }));
    } else {
      alert("Please select a valid CSV file.");
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    const requiredFields = [
      ...fields
        .filter((field) => field.required) // Filter fields where required is true
        .map((field) => field.name), // Map to their names
    ];
    const missingFields = requiredFields.filter((field) => !formData[field]);

    if (missingFields.length) {
      alert(`The following fields are required: ${missingFields.join(", ")}`);
      return;
    }

    // Add the default value to the form data
    const iformData = { ...formData, dataType: "string" };

    // If the field.name value have the space then alert error
    if (iformData["name"].includes(" ")) {
      return Alert("Spaces are not allowed in the name field!", "error");
    }

    setLoading(true); // Start loading
    try {
      const newItem = { ...iformData }; // Omit the file object if needed

      // On success callback
      const onSuccessCallback = (message) => {
        Alert("Item added successfully!", "success");

        setFormData({
          ...Object.fromEntries(fields.map((field) => [field.name, ""])),
        });

        // alert("Item added successfully");
      };

      const onFailCallback = (error) => {
        Alert(error.message, "error");
      };

      onSubmit(newItem, onSuccessCallback, onFailCallback); // Pass the new item to the parent component
    } catch (error) {
      Alert("Error submitting form:", error);
      //console.error("Error submitting form:", error);
    } finally {
      setLoading(false); // End loading
    }
  };
  const sxModal = {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)", // Adds a semi-transparent background
    // border: "5px solid blue",
  };

  const sxMainBox = {
    position: "absolute",

    // position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: "400px",
    bgcolor: "background.paper",
    // boxShadow: 24,
    borderRadius: 2,
    p: 1,
    outline: "none",
    // border: "5px solid yellow",
  };

  const sxContentBox = {
    mt: 2,
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    gap: "16px", // Space between form elements
    // border: "5px solid red",
  };

  const modalContent = (
    <Box
      sx={{
        width: 400,
        bgcolor: "background.paper",
        p: 4,
        borderRadius: 2,
      }}
    >
      <Typography variant="h6" sx={{ mb: 2 }}>
        Add New {id}
      </Typography>
      <form onSubmit={handleSubmit}>
        {fields.map((field) => {
          switch (field.name) {
            case "dataType":
              return (
                <TextField
                  autoComplete="off"
                  key={field.name}
                  select
                  label="Data Type"
                  name="dataType"
                  value={formData.dataType}
                  onChange={handleChange}
                  fullWidth
                  disabled={loading}
                  sx={{ mb: 2, display: "none" }}
                >
                  <MenuItem value="string">String</MenuItem>
                  <MenuItem value="number">Number</MenuItem>
                </TextField>
              );

            default:
              return (
                <TextField
                  autoComplete="off"
                  key={field.name}
                  label={field.label}
                  name={field.name}
                  value={formData[field.name]}
                  onChange={handleChange}
                  multiline={field.type === "textarea"} // Enables the textarea mode
                  rows={4} // Number of rows for the textarea
                  fullWidth
                  inputProps={{ maxLength: field.maxLength || 255 }}
                  sx={{ mb: 2 }}
                  disabled={loading}
                  required={field.required}
                />
              );
          }
        })}
        <Button
          variant="contained"
          component="label"
          fullWidth
          sx={{
            mb: 2,
            backgroundColor: "primary.main",
            color: "white.main",
          }}
          disabled={loading}
        >
          Select CSV File
          <input type="file" accept=".csv" hidden onChange={handleFileChange} />
        </Button>

        {/* Display the uploaded file name */}
        {formData.file && (
          <Typography
            variant="body2"
            sx={{ color: "gray", mb: 2, textAlign: "left" }}
          >
            {`Uploaded File: ${formData.file.name}`}
          </Typography>
        )}
        <Box sx={{ position: "relative" }}>
          {/* Submit Button */}
          <Btn
            type="submit"
            variant="contained"
            sx={{ width: "100%", borderRadius: 2 }}
            // disabled={loading} // Disable button while loading
            text={loading ? "Submitting..." : "Submit"}
            isLoading={loading}
          />
        </Box>
      </form>
    </Box>
  );
  return (
    <ModalAtom
      // title={"Add New List"}
      open={open}
      content={modalContent}
      onClose={() => {
        setFormData({
          name: "",
          dataType: "string",
          file: null,
          ...Object.fromEntries(fields.map((field) => [field.name, ""])),
        }),
          onClose();
      }}
      sxModal={sxModal}
      sxMainBox={sxMainBox}
      sxContentBox={sxContentBox}
    />
  );
};

AddListModal.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func.isRequired,
  fields: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
      type: PropTypes.string.isRequired,
      required: PropTypes.bool,
    })
  ).isRequired,
};

export default AddListModal;
