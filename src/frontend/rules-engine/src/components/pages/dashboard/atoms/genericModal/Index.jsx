import React from "react";
import PropTypes from "prop-types";
import { Box, Typography } from "@mui/material";
import { Btn } from "../../../../global/atoms/buttons/Button";
import <PERSON><PERSON><PERSON>tom from "../../../../global/atoms/modal/Index";

/**
 * A generic modal HOC that can be used to display any content with a title and close button.
 *
 * @component
 * @example
 * return (
 *   <GenericModal
 *     open={true}
 *     title="My Modal"
 *     onClose={() => {}}
 *   >
 *     <div>My modal content</div>
 *   </GenericModal>
 * );
 *
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether the modal is open
 * @param {string | JSX.Element} props.title - The title of the modal
 * @param {Function} props.onClose - Function to close the modal
 * @param {React.ReactNode} props.children - The content to display in the modal
 * @param {Object} [props.sx] - Custom styles for different parts of the modal
 * @param {boolean} [props.showCloseButton=true] - Whether to show the close button
 * @param {string} [props.closeButtonText="Close"] - Text for the close button
 * @returns {JSX.Element} The rendered modal component
 */
const GenericModal = ({
  open,
  title,
  onClose,
  children,
  sx = {},
  showCloseButton = true,
  closeButtonText = "Close"
}) => {
  const modalContent = (
    <Box
      sx={{
        bgcolor: "background.paper",
        p: 4,
        borderRadius: 2,
        ...sx.content
      }}
    >
      <Typography variant="h6" sx={{ mb: 3, ...sx.title }}>
        {title}
      </Typography>

      <Box sx={{ mb: 3 }}>
        {children}
      </Box>

      {showCloseButton && (
        <Box sx={{ display: 'flex', justifyContent: 'flex-center', mt: 2 }}>
          <Btn
            onClick={onClose}
            text={closeButtonText}
            variant="contained"
            sx={{ 
              minWidth: '100px',
              ...sx.closeButton 
            }}
          />
        </Box>
      )}
    </Box>
  );


  return (
    <ModalAtom
      open={open}
      content={modalContent}
      onClose={onClose}
      sxModal={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        ...sx.modal
      }}
      sxMainBox={{
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        outline: "none",
        ...sx.mainBox
      }}
      sxContentBox={{
        width: "100%",
        display: "flex",
        flexDirection: "column",
        gap: 2,
        ...sx.contentBox
      }}
    />
  );
};

GenericModal.propTypes = {
  open: PropTypes.bool.isRequired,
  title: PropTypes.string.isRequired,
  onClose: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  sx: PropTypes.shape({
    modal: PropTypes.object,
    mainBox: PropTypes.object,
    contentBox: PropTypes.object,
    content: PropTypes.object,
    title: PropTypes.object,
    closeButton: PropTypes.object,
    width: PropTypes.number
  }),
  showCloseButton: PropTypes.bool,
  closeButtonText: PropTypes.string
};

export default GenericModal; 