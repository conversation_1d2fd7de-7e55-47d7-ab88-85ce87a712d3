import React from "react";
import SubSectionHOC from "../../molecules/campaigns/create/SubSectionHOC";
import SubSectionRowMolecule from "../../molecules/campaigns/create/SubSectionRow";
import PropTypes from "prop-types";

/**
 * Higher-order component for rendering sections within a dashboard. This component
 * utilizes a consistent layout and structure, leveraging `SubSectionHOC` and
 * `SubSectionRowMolecule` to organize and display content with associated actions.
 *
 * @param {object} props - Component props.
 * @param {number} props.index - The unique index of the section, used as a key.
 * @param {Object} props.tab - Object containing properties related to the tab, including columns and mode.
 * @param {Array} props.columns - Array of column definitions that define the structure of the section.
 * @param {Array} props.rows - Array of row data to be displayed within the section.
 * @param {function} props.onEdit - Callback function to handle the edit action for a section.
 * @param {function} props.onDelete - Callback function to handle the delete action for a section.
 * @param {Object} [props.sxContainer={}] - Custom styles to be applied to the container of the section.
 * @param {JSX.Element} props.content - The content to be rendered inside the section.
 * @returns {JSX.Element} A rendered section with organized content and actions.
 */
const ReusableSectionHOC = ({
  index, // The unique index of the section
  tab, // Object containing tab-related properties
  columns, // Array of column definitions
  rows, // Array of row data
  onEdit, // Function to handle edit action
  onDelete, // Function to handle delete action
  sxContainer = {}, // Custom styles for the container
  content, // The content to be rendered inside the section
  dialogData,
  sxHeaderRow,
}) => {
  return (
    <SubSectionHOC
      key={index} // Unique key for the section
      columns={columns} // Pass the columns to the sub-section
      rows={rows} // Pass the rows to the sub-section
      HeaderComponent={
        <SubSectionRowMolecule
          key={index} // Unique key for the row molecule
          index={index} // Pass the index to the row molecule
          onEdit={onEdit} // Pass the edit handler function
          onDelete={onDelete} // Pass the delete handler function
          columns={tab.columns} // Use columns from the tab object
          mode={tab.mode} // Use mode from the tab object
          sxHeaderRow={sxHeaderRow}
          dialogData={dialogData}
          tab={tab}
        />
      }
      {...tab} // Spread tab properties into the sub-section
      mode={tab.mode} // Set the mode of the sub-section
      sx={{ sxContainer }} // Apply custom container styles
    >
      {content}
    </SubSectionHOC>
  );
};

ReusableSectionHOC.propTypes = {
  index: PropTypes.number.isRequired,
  tab: PropTypes.object.isRequired,
  columns: PropTypes.array.isRequired,
  rows: PropTypes.array.isRequired,
  onEdit: PropTypes.func.isRequired,
  onDelete: PropTypes.func.isRequired,
  sxContainer: PropTypes.object,
  content: PropTypes.node.isRequired,
};

export default ReusableSectionHOC;
