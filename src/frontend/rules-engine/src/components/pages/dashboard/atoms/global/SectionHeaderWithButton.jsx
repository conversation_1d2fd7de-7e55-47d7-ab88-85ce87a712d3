import React from "react";
import { Box } from "@mui/material";
import { Typo<PERSON><PERSON><PERSON> } from "../../../../global/atoms/typography/Typography";
import { Btn as BtnAtom } from "../../../../global/atoms/buttons/Button";

/**
 * SectionHeaderWithButton component renders a title and an associated button.
 *
 * @component
 * @param {Object} props - Component props
 * @param {string} props.title - Title text to display.
 * @param {string} [props.buttonTitle] - Button text to display.
 * @param {Function} [props.onButtonClick] - Callback function for button click. If not provided, button will not be rendered.
 * @param {import('@mui/material').BoxProps["sx"]} [props.sx] - Box component sx.
 * @returns {JSX.Element} The rendered SectionHeaderWithButton component.
 */
const SectionHeaderWithButton = ({
  title,
  buttonTitle,
  onButtonClick,
  sx = {},
}) => {
  return (
    <Box
      sx={{
        mt: 2,
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        ...sx,
      }}
    >
      <TypographyAtom text={title} variant="h6" />
      {onButtonClick && (
        <BtnAtom
          onClick={onButtonClick}
          sx={{ backgroundColor: "primary.secondary" }}
          text={buttonTitle ? buttonTitle : `Add ${title}`}
        />
      )}
    </Box>
  );
};

export default SectionHeaderWithButton;
