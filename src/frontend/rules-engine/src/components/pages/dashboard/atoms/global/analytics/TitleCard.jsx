import React from 'react';
import PropTypes from 'prop-types';
import { Box, Typography } from '@mui/material';

/**
 * A reusable component that displays a title and a value.
 * It allows custom styling to be passed in.
 *
 * @component
 * @example
 * return (
 *   <TileCard
 *     title="Agent Count"
 *     value={30}
 *     style={{ width: '150px', backgroundColor: '#f0f0f0' }}
 *   />
 * )
 *
 * @param {Object} props - Component props
 * @param {string} props.title - The title to display on the card
 * @param {number|string} props.value - The value to display on the card
 * @param {Object} [props.style] - Custom styles to apply to the card
 * @returns {JSX.Element} A TileCard component
 */
const AnalyticsTileCard = ({ title, value, style }) => {
  return (
    <Box
      sx={{
        border: '1px solid #ddd',
        borderRadius: '4px',
        padding: '16px',
        textAlign: 'center',
        ...style,
      }}
    >
      <Typography variant="subtitle1" fontWeight="bold">
        {title}
      </Typography>
      <Typography variant="h4">
        {value}
      </Typography>
    </Box>
  );
};

AnalyticsTileCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  style: PropTypes.object,
};

AnalyticsTileCard.defaultProps = {
  style: {},
};

export default AnalyticsTileCard;
