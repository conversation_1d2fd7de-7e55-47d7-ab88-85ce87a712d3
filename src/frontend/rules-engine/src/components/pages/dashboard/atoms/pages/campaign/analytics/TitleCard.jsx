import React from "react";
import PropTypes from "prop-types";
import { Box, Typography, Grid2 as Grid } from "@mui/material";
import { TypographyAtom } from "../../../../../../global/atoms/typography/Typography";
/**
 * A reusable component that displays an analytics data with a name, optional prefix, and value.
 * It allows custom styling to be applied to the card.
 *
 * @component
 * @example
 * return (
 *   <AnalyticsTileCard
 *     data={{ name: "Agent Count", prefix: "$", value: 30 }}
 *     style={{ width: '150px', backgroundColor: '#f0f0f0' }}
 *   />
 * )
 *
 * @param {Object} props - Component props
 * @param {Object} props.data - The data object to display on the card
 * @param {string} props.data.name - The name/title to display on the card
 * @param {string} [props.data.prefix] - An optional prefix to display before the value
 * @param {number|string} props.data.value - The value to display on the card
 * @param {Object} [props.sx] - Custom styles to apply to the card
 * @param {Object} [props.size] - The size of the card
 * @returns {JSX.Element} An AnalyticsTileCard component
 */
const AnalyticsTileCard = ({ data, sx, size }) => {
  return (
    <Grid
      item
      size={size}
      sx={{
        border: "1px solid #ddd",
        borderRadius: "4px",
        padding: "16px",
        textAlign: "center",
        width: "100%",
        maxWidth: "200px",
        boxShadow: "0px 4px 8px primary.secondary",
        // boxShadow: 1,
        ...sx,
      }}
    >
      <TypographyAtom
        variant="body1"
        sx={{ fontWeight: "bold", color: "mainColor.main" }}
        text={`${data?.prefix || ""}${data?.value}`}
      />
      <TypographyAtom
        variant={"body2"}
        sx={{ color: "mainColor.main" }}
        text={data.name}
      />
    </Grid>
  );
};

AnalyticsTileCard.propTypes = {
  title: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  style: PropTypes.object,
};

AnalyticsTileCard.defaultProps = {
  style: {},
};

export default AnalyticsTileCard;
