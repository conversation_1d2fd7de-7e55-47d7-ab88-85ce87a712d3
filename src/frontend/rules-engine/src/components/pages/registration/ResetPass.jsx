import React, { useState } from "react";
import { Box, Grid, Tabs, Tab, Typography } from "@mui/material";
import LoginTemplate from "./templates/LogIn";
import SignUpTemplate from "./templates/SignUp";
import { TypographyAtom } from "../../global/atoms/typography/Typography";

/**
 * ResetPassPage component renders a page with a login and sign up form.
 *
 * @component
 * @returns {JSX.Element} The rendered ResetPassPage component.
 *
 * @description
 * This component renders a page with a login and sign up form. It uses the
 * `Tabs` component from material-ui to switch between the login and sign up
 * forms. The login form is rendered using the `LoginTemplate` component and the
 * sign up form is rendered using the `SignUpTemplate` component.
 */
const ResetPassPage = () => {
  const [activeTab, setActiveTab] = useState(0);

  /**
   * Handles tab change event.
   * @description
   * This function is called when the active tab changes. It updates the
   * `activeTab` state with the new value. It is used by the `Tabs` component
   * to switch between the login and sign up forms.
   */
  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#f5f5f5",
        padding: "2rem",
      }}
    >
      <Box
        sx={{
          width: { xs: "100%", sm: "400px" },
          backgroundColor: "white",
          padding: "2rem",
          borderRadius: "8px",
          boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
        }}
      >
        {/* Heading */}
        <TypographyAtom variant="h4" text="Welcome!" sx={{ textAlign: "center", marginBottom: "2rem" }} />

        {/* Tabs for switching between Login and Sign Up */}
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          textColor="primary"
          indicatorColor="primary"
          sx={{ marginBottom: "2rem" }}
        >
          <Tab label="Log In" />
          <Tab label="Sign Up" />
        </Tabs>

        {/* Tab Panels */}
        {activeTab === 0 && <LoginTemplate />}
        {activeTab === 1 && <SignUpTemplate />}
      </Box>
    </Box>
  );
};

export default ResetPassPage;
