import React, { useEffect, useState } from "react";
import { Box } from "@mui/material";
import LoginTemplate from "./templates/LogIn";
import SignUpTemplate from "./templates/SignUp";
import { Typo<PERSON><PERSON>tom } from "../../global/atoms/typography/Typography";
import ForgotPassTemplate from "./templates/ForgotPass";
import VerifyUserTemplate from "./templates/VerifyUser";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import ResetPassTemplate from "./templates/ResetPass";
import { Btn as BtnAtom } from "../../global/atoms/buttons/Button";
import AuthService from "../../../Api/AuthService";
import { Alert } from "../../../utils/alert/alertUtils";
/**
 * RegistrationPage component renders a page with a login form,
 * sign up form, forgot password form, and verify user form.
 * It uses the state to determine which form to render and
 * passes the state to the form component.
 *
 * @component
 * @returns {JSX.Element} The rendered RegistrationPage component.
 *
 * @description
 * This component renders a page with a login form, sign up form, forgot password form, and verify user form.
 * It uses the state to determine which form to render and passes the state to the form component.
 */
const RegistrationPage = () => {
  const [page, setPage] = useState("login"); // ["login","register","forgotPassword","verifyUser","resetPassword"]
  const navigate = useNavigate();
  const [inputs, setInputs] = useState({});
  const { isAuthenticated } = useSelector((state) => state.auth);
  const [forgotPasswordMail, setForgotPasswordMail] = useState("");
  const [isBtnLoading, setIsBtnLoading] = useState(false);
  /**
   * If the user is logged in, navigate to the home page.
   */
  useEffect(() => {
    if (isAuthenticated) {
      navigate("/");
    }
  }, []);

  /**
   * Handle the forgot password submit.
   * @param {string} email - The email of the user.
   */
  const handleForgotPasswordSubmit = async (email) => {
    try {
      setIsBtnLoading(true)
      const response = await AuthService.forgotPassword(email);
      if (!response.success) {
        Alert(response.data.message);
      }
      setForgotPasswordMail(email);
      Alert("Email sent successfully", "success");
      // setPage("resetPassword");
    } catch (error) {
      Alert("Error sending email", "error");
    } finally {
      setIsBtnLoading(false)
    }
  };

  /**
   * Handle the reset password submit.
   * @param {object} inputs - The inputs of the user.
   * @param {string} inputs.password - The password of the user.
   * @param {string} inputs.cPassword - The confirm password of the user.
   * @param {string} inputs.verificationCode - The verification code of the user.
   */
  const handleResetPasswordSubmit = async (inputs) => {
    try {
      setIsBtnLoading(true)
      const response = await AuthService.resetPassword({
        email: forgotPasswordMail,
        password: inputs.password,
        verificationCode: inputs.verificationCode,
      });
      
      if (!response.success) {
        return Alert(response.data);
      }
      Alert("Password reset successfully", "success");
      // setPage("login");
    } catch (error) {
      console.log("error", error);
      Alert("Error resetting password", "error");
    } finally {
      setIsBtnLoading(false)
    }
  };

  /**
   * An object that maps the state to the form components.
   * The keys are the state values and the values are the
   * form components.
   */
  let Pages = {
    login: <LoginTemplate setPage={setPage} isBtnLoading={isBtnLoading} />,
    // register: SignUpTemplate,
    // forgotPassword: (
    //   <ForgotPassTemplate isBtnLoading={isBtnLoading}  handleSubmit={handleForgotPasswordSubmit} />
    // ),
    // // verifyUser: VerifyUserTemplate,
    // resetPassword: (
    //   <ResetPassTemplate isBtnLoading={isBtnLoading} handleSubmit={handleResetPasswordSubmit} />
    // ),
  };
  const Page = Pages[page];

  return (
    <Box
      sx={{
        width: "100%",
        height: "100%",
        minHeight: "100vh",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        backgroundColor: "#f5f5f5",
        padding: "2rem",
      }}
    >
      <Box
        sx={{
          width: { xs: "100%", sm: "400px" },
          backgroundColor: "white",
          padding: "2rem",
          borderRadius: "8px",
          boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
        }}
      >
        {/* Heading */}
        <TypographyAtom
          variant="h4"
          text={
            page === "resetPassword"
              ? "Reset Password"
              : page === "forgotPassword"
              ? "Forgot Password"
              : "Login"
          }
          sx={{ textAlign: "center", marginBottom: "2rem" }}
        />

        {/* Render the form component based on the state. */}
        {Page}
        {/* {page === "resetPassword" && (
          <BtnAtom
            sx={{
              marginTop: "1rem",
              backgroundColor: "transparent",
              color: "primary.main",
              width: "100%",
            }}
            text="Back to Login"
            onClick={() => setPage("login")}
          />
        )}
        {page === "forgotPassword" && (
          <BtnAtom
            sx={{
              marginTop: "1rem",
              backgroundColor: "transparent",
              color: "primary.main",
              width: "100%",
            }}
            text="Back to Login"
            onClick={() => setPage("login")}
          />
        )}
        {page === "login" && (
          <BtnAtom
            sx={{
              marginTop: "1rem",
              backgroundColor: "transparent",
              color: "primary.main",
              width: "100%",
            }}
            text="Forgot Password?"
            onClick={() => setPage("forgotPassword")}
          />
        )} */}
      </Box>
    </Box>
  );
};

export default RegistrationPage;
