import React, { useState } from "react";
import {  Box, Button, TextField, Typography } from "@mui/material";
import { InputField } from "../../../global/atoms/inputFields/InputField";
import { Btn as Btn<PERSON>tom } from "../../../global/atoms/buttons/Button";
import { useNavigate } from "react-router-dom"; // Import useHistory from react-router-dom
import { authValidators } from "../../../../utils/validators/auth";
import { Alert } from "../../../../utils/alert/alertUtils";

/**
 * ResetPassTemplate is a functional component that renders a form to reset a password.
 * The form has two fields: a password field and a conform password field.
 * The password field is validated against the `authValidators.isResetPasswordFormValid` function.
 * The conform password field is validated against the same function.
 * The form is handled by the `handleResetPassword` function which is called when the form is submitted.
 * The `handleResetPassword` function validates the form data and displays an alert if the form is not valid.
 * The form also has a button to navigate to the login page.
 * The component uses the `useState` hook to store the form data in the component's state.
 * The component uses the `useNavigate` hook to navigate to the login page.
 */
const ResetPassTemplate = ({handleSubmit, isBtnLoading}) => {
  // Get the navigate function from the useNavigate hook
  const navigate = useNavigate();

  // Initialize the component's state with an empty object
  const [inputs, setInputs] = useState({ password: "", cPassword: "", verificationCode: "" });
  

  // Function to handle the submit event of the form
  const handleResetPassword = () => {
    // Validate the form data using the authValidators.isResetPasswordFormValid function
    const isResetPasswordValid =
      authValidators.isResetPasswordFormValid(inputs);
    // If the form is not valid, display an alert with the error message
    if (!isResetPasswordValid.success) {
      return Alert(isResetPasswordValid.message);
    }

    handleSubmit(inputs)
  };

  // Function to handle the change event of any of the form fields
  const handleInputChange = (event) => {
    // Destructure the name and value from the event target
    const { name, value } = event.target;

    // Update the component's state with the new value for the corresponding form field
    setInputs({
      ...inputs,
      [name]: value,
    });
  };

  return (
    // Render a form with two fields: a password field and a conform password field
    <Box
      component="form"
      sx={{ display: "flex", flexDirection: "column", gap: 2 }}
    >
      <InputField
        label="Verification Code"
        type="text"
        variant="outlined"
        onChange={handleInputChange}
        name="verificationCode"
        fullWidth
        required
      />
      <InputField
        label="Password"
        type="password"
        variant="outlined"
        onChange={handleInputChange}
        name="password"
        fullWidth
        required
      />
      <InputField
        label="Conform password"
        type="password"
        name="cPassword"
        onChange={handleInputChange}
        variant="outlined"
        fullWidth
        required
      />
      <BtnAtom isLoading={isBtnLoading} onClick={handleResetPassword}  text="Submit" /> 
     
      {/* <Typography variant="body2" sx={{ textAlign: "center", marginTop: 2 }}>
        Do you have an account? 
        <Button 
          onClick={handleRegister} 
          sx={{ marginLeft: 1, color: 'primary.main', textTransform: 'none' }}
        >
          Sign Up
        </Button>
      </Typography> */}
    </Box>
  );
};

export default ResetPassTemplate;
