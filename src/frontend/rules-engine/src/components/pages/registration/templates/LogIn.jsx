import React, { useEffect, useState } from "react";
import { Box, Button, TextField, Typography } from "@mui/material";
import { InputField } from "../../../global/atoms/inputFields/InputField";
import { Btn, Btn as Btn<PERSON>tom } from "../../../global/atoms/buttons/Button";
import { useNavigate } from "react-router-dom"; // Import useHistory from react-router-dom
import {Alert} from '../../../../utils/alert/alertUtils'
import {Helpers} from '../../../../utils/generalFunctions/index';

import { TypographyAtom } from "../../../global/atoms/typography/Typography";
import { authValidators } from "../../../../utils/validators/auth";
import { useDispatch } from "react-redux";
import { loginSuccess, setIsTokenRefreshing } from "../../../../redux/slices/global/auth";
import LoadingBackdrop<PERSON>tom from "../../../global/atoms/loading/LoadingBackdrop";
import AuthService from "../../../../Api/AuthService";
import { VITE_REFRESH_TOKEN_INTERVAL } from "../../../../config/env";
import { setCampaignsTab } from "../../../../redux/slices/tabs/campaigns/campaigns";
import { setGlobalTab } from "../../../../redux/slices/global/dashboard";
// import { resetCampaignsData } from "../../../../redux/slices/tabs/campaigns/campaigns";
/** 
 * This component renders a login form with a username and password input fields.
 * It also renders a button to submit the form, which will call the handleSubmit function.
 * The handleSubmit function will validate the form data, check if the login credentials are correct,
 * and if so, log the user in and redirect them to the home page.
 * If the login credentials are incorrect, it will display an error message.
 */
const LoginTemplate = ({ setPage,}) => {
  const navigate = useNavigate();
  const dispatch = useDispatch()
  const [inputs, setInputs] = useState({username: "", password: ""});
  const [isLoading, setIsLoading] = useState(false);
  let [isBtnLoading, setIsBtnLoading] = useState(false);
 

  /**
   * This function is called when the user submits the login form.
   * It will validate the form data, check if the login credentials are correct,
   * and if so, log the user in and redirect them to the home page.
   * If the login credentials are incorrect, it will display an error message.
   */
  const handleSubmit = async () => {
   
    try {
      /**
       * Create an object with the username and password.
       * Trim the username and password to remove any leading or trailing whitespace.
       * Convert the username to lowercase so that the login is case-insensitive.
       */
      let obj = {
        username: inputs.username.toLowerCase().trim(),
        password: inputs.password.trim(),
      }
      
      /**
       * Validate the form data.
       * If the form data is not valid, display an error message.
       */
      let isFormValid = authValidators.isLoginFormValid(obj);
   
      if(!isFormValid.success){
       return Alert(isFormValid.message);
      }
      
      setIsLoading(true)
      setIsBtnLoading(true)
      /**
       * Call the handleLogin function to check if the login credentials are correct.
       * If the login credentials are not correct, display an error message.
       * If the login credentials are correct, log the user in and redirect them to the home page.
       */
      let response = await AuthService.login(obj);
    
      if(!response.success){
        setIsLoading(false)
        return Alert(response.data);
      }
 // Calculate refresh time (70% of original refresh time)
 const adjustedRefreshTime =  Math.floor((response.data.refresh_token_t * 1000) * 0.7);
     
      /**
       * Dispatch the setUser action to log the user in.
       * The setUser action will update the user state in the Redux store.
       */
      dispatch(loginSuccess({user: response.data.user, token: response.data.token, isAuthenticated: true, refresh_token_t: adjustedRefreshTime}));
      dispatch(setIsTokenRefreshing(true));
      /**
       * Refresh the token every in the specified interval.
       */
      setTimeout(()=>{ 
        
        AuthService.refreshToken(adjustedRefreshTime);
        
      }, adjustedRefreshTime)
      /**
       * Display a success message to the user.
       * After 1 second, redirect the user to the home page.
       */
    
      setTimeout(()=>{
        // This will call the refreshToken function to refresh the token
       
        setIsLoading(false)
        Alert("Login successful","success");
        if(response.data.user.role_id === 1){
          dispatch(setGlobalTab("userManagement"))
          // dispatch(setCampaignsTab("userManagement"))
          navigate("/") 
        }else{
          dispatch(setGlobalTab("campaigns"))
          // dispatch(setCampaignsTab("viewCampaigns"))
          navigate("/")
        }

      },1000)
    
    } catch (error) {
      console.log(error,"#48494")
      Alert("Error logging in", "error");
    } finally {
      setIsBtnLoading(false)
    }
  }

  /**
   * This function is called when the user types in one of the input fields.
   * It will update the inputs state with the new value of the corresponding input field.
   */
  const handleInputChange = (event) =>{
    let {name, value} = event.target;
    setInputs({
      ...inputs,
      [name]:value,
    })
  }

  return (
    <Box
      component="form"
      sx={{ display: "flex", flexDirection: "column", gap: 2 }}
    >
      <LoadingBackdropAtom message={"Logging in..."}  open={isLoading} />
      <InputField
        label="Username"
        type="text"
        variant="outlined"
        name="username"
        fullWidth
        validation={{ isShowError: false, error: null }}
        validateOn="submit"
        required
        onChange={handleInputChange}
      />
      <InputField
        label="Password"
        type="password"
        variant="outlined"
        name="password"
        validateOn="submit"
        fullWidth
        required
        onChange={handleInputChange}
      />
      <BtnAtom isLoading={isBtnLoading} text="Submit" onClick={handleSubmit} /> {/* Handle your login logic here */}
     
    
    </Box>
  );
};

export default LoginTemplate;
