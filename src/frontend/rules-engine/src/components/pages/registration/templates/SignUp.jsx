import React, { useEffect, useState } from "react";
import { Box, Button, TextField, Typography } from "@mui/material";
import { InputField } from "../../../global/atoms/inputFields/InputField";
import { Btn as Btn<PERSON>tom } from "../../../global/atoms/buttons/Button";

import { Typography<PERSON>tom } from "../../../global/atoms/typography/Typography";
import { authValidators } from "../../../../utils/validators/auth";
/**
 * SignUpTemplate is a functional component that renders a signup form.
 * It uses the InputField component from the global/atoms/inputFields directory
 * to render the form fields. The form fields are name, email, password.
 * The form is validated using the isRegisterFormValid() function from the
 * authValidators module. If the form is valid, it displays an alert with the
 * message "API is not integrated". If the form is not valid, it displays an
 * alert with the error message.
 *
 * The form is handled by the handleSubmit() function which is called when
 * the form is submitted. The handleSubmit() function validates the form and
 * displays an alert if the form is not valid.
 *
 * The handleInputChange() function is called when the user types in any of
 * the form fields. It updates the inputs state with the new value of the
 * corresponding form field.
 *
 * The component also renders a button to switch back to the login page.
 */
const SignUpTemplate = ({ setPage, inputs, setInputs }) => {


  useEffect(() => {
    // Initialize the inputs state with empty strings
    setInputs({ name: "", email: "", password: "" });
  }, []);

  const handleSubmit = () => {
    try {
      // Validate the form data
      let isFormValid = authValidators.isRegisterFormValid(inputs);
      if (!isFormValid.success) {
        // If the form is not valid, display an alert with the error message
        return Alert(isFormValid.message);
      }
      // If the form is valid, display an alert with the message "API is not integrated"
      Alert("API is not integrated", "info");
    } catch (error) {}
  };

  const handleInputChange = (event) => {
    // Destructure the name and value from the event target
    let { name, value } = event.target;
    // Update the inputs state with the new value of the corresponding form field
    setInputs({
      ...inputs,
      [name]: value,
    });
  };

  return (
    <Box
      component="form"
      sx={{ display: "flex", flexDirection: "column", gap: 2 }}
    >
      {/* Render the name field */}
      <InputField
        onChange={handleInputChange}
        label="Full Name"
        variant="outlined"
        name="name"
        fullWidth
        required
        validateOn="submit"
      />
      {/* Render the email field */}
      <InputField
        label="Email"
        type="email"
        variant="outlined"
        name="email"
        validateOn="submit"
        fullWidth
        onChange={handleInputChange}
        required
      />
      {/* Render the password field */}
      <InputField
        label="Password"
        type="password"
        name="password"
        variant="outlined"
        validateOn="submit"
        onChange={handleInputChange}
        fullWidth
        required
      />

      {/* Render the signup button */}
      <BtnAtom text="Sign Up" onClick={handleSubmit} />{" "}
      {/* Handle your signup logic here */}
      <TypographyAtom text="Or" variant="body2" sx={{ textAlign: "center" }} />
      {/* Render the button to switch back to the login page */}
      <Typography variant="body2" sx={{ textAlign: "center" }}>
        Already have an account?
        <Button
          onClick={() => setPage("login")} // Switch back to Login page
          sx={{ marginLeft: 1, color: "primary.main", textTransform: "none" }}
        >
          Sign In
        </Button>
      </Typography>
    </Box>
  );
};

export default SignUpTemplate;
