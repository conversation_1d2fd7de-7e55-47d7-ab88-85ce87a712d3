import React, { useEffect, useState } from "react";
import { Box, Button, TextField, Typography } from "@mui/material";
import { InputField } from "../../../global/atoms/inputFields/InputField";
import { Btn as Btn<PERSON>tom } from "../../../global/atoms/buttons/Button";
import { useNavigate } from "react-router-dom"; // Import useHistory from react-router-dom
import { authValidators } from "../../../../utils/validators/auth";
import { Alert } from "../../../../utils/alert/alertUtils";

/**
 * ForgotPassTemplate is a functional component that renders a form to reset a password.
 * It receives setPage (a function to set the current page), inputs (an object with the current form values), and setInputs (a function to update the current form values) as props.
 * The form has one field: an email field.
 * The form is validated using the authValidators.isForgotPasswordFormValid function.
 * The form is handled by the handleSubmit function which is called when the form is submitted.
 * The handleSubmit function validates the form data and displays an alert if the form is not valid.
 * The component also renders a button to navigate to the login page.
 */
const ForgotPassTemplate = ({ handleSubmit, isBtnLoading }) => {
  const navigate = useNavigate();
const [email,setEmail] = useState("")


 

  // Function to handle the submit event of the form
  const handleForgotPasswordSubmit = () => {
    // Validate the form data using the authValidators.isForgotPasswordFormValid function
    const isForgotPasswordFormValid = authValidators.isForgotPasswordFormValid({
      email:email,
    });

    // If the form is not valid, display an alert with the error message
    if (!isForgotPasswordFormValid.success) {
      return Alert(isForgotPasswordFormValid.message);
    }

   handleSubmit(email)
  };

  // Function to handle the change event of the email field
  const handleInputChange = (event) => {
    // Destructure the name and value from the event target
    let { name, value } = event.target;
    // Update the email state with the new value
    setEmail(value);
  };

  return (
    // Render a form with an email field
    <Box
      component="form"
      sx={{ display: "flex", flexDirection: "column", gap: 2 }}
    >
      <InputField
        label="Email"
        type="email"
        variant="outlined"
        onChange={handleInputChange}
        validateOn="submit"
        name="email"
        fullWidth
        required
      />
      <BtnAtom isLoading={isBtnLoading} text="Submit" onClick={handleForgotPasswordSubmit} />
      {/* Changed from "Sign Up" to "Sign In" */}
      {/* <Button
        onClick={() => setPage("login")}
        sx={{ marginTop: 2, color: "primary.main", textTransform: "none" }}
      >
        LogIn
      </Button> */}
    </Box>
  );
};

export default ForgotPassTemplate;
