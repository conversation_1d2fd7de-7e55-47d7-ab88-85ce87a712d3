import React, { useEffect, useState } from "react";
import { Box, Button, TextField, Typography } from "@mui/material";
import { InputField } from "../../../global/atoms/inputFields/InputField";
import { Btn as BtnAtom } from "../../../global/atoms/buttons/Button";
import { useNavigate } from "react-router-dom"; // Import useHistory from react-router-dom
import { authValidators } from "../../../../utils/validators/auth";
import { Alert } from "../../../../utils/alert/alertUtils";

// Define a functional component VerifyUserTemplate that takes setPage, setInputs, and inputs as props
const VerifyUserTemplate = ({ setPage, setInputs, inputs }) => {
    
    // Import the useNavigate hook from react-router-dom
    const navigate = useNavigate();
    
    // Set the initial values of email and otp in the inputs state when the component mounts
    useEffect(() => {
        setInputs({ email: "", otp: "" });
    }, []);
    
    // Define a function handleSubmit to handle form submission
    const handleSubmit = () => {
        // Validate the form data using authValidators.isForgotPasswordFormValid
        const isForgotPasswordFormValid = authValidators.isForgotPasswordFormValid({
            email,
        });

        // If the form is not valid, display an alert with the error message
        if (!isForgotPasswordFormValid.success) {
            return Alert(isForgotPasswordFormValid.message);
        }

        // Display a message that the API is not integrated
        Alert("API is not integrated", "info");
    };

    // Define a function handleInputChange to update the input fields in the state when the user types
    const handleInputChange = (event) => {
        // Destructure the name and value from the event target
        let { name, value } = event.target;
        
        // Update the inputs state with the new value for the corresponding input field
        setInputs({
            ...inputs,
            [name]: value
        });
    };

    // Render a form with an input field for OTP, a submit button, and a login option
    return (
        <Box
            component="form"
            sx={{ display: "flex", flexDirection: "column", gap: 2 }}
        >
            <InputField
                label="Otp"
                type="email"
                variant="outlined"
                placeholder="e.g. 574895"
                onChange={handleInputChange}
                validateOn="submit"
                name="email"
                fullWidth
                required
            />
            <BtnAtom text="Submit" onClick={handleSubmit} />
            {/* Changed from "Sign Up" to "Sign In" */}
            <Button
                onClick={() => setPage("login")}
                sx={{ marginTop: 2, color: "primary.main", textTransform: "none" }}
            >
                LogIn
            </Button>
        </Box>
    );
};

export default VerifyUserTemplate;
