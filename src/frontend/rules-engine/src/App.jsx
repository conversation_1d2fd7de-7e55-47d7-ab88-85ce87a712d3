// src/components/MyComponent.js

//import Footer from "./components/global/organisms/footer/Footer";
import { Box, listClasses } from "@mui/material";
import { Route, Routes, useNavigate } from "react-router-dom";
import DashboardPage from "./components/pages/dashboard/Index";
import RegistrationPage from "./components/pages/registration/Index";
import APIService from "./Api/APIService";
import { useDispatch, useSelector } from "react-redux";
import AuthService from "./Api/AuthService";
import { useEffect } from "react";
import React from "react";
import CampaignService from "./Api/CampaignService";
import GlobalService from "./Api/GlobalService";
import { Helpers } from "./utils/generalFunctions";
import { setReceivedData } from "./redux/slices/tabs/campaigns/campaign";
import ListServiceClass from "./Api/ListService";
import endPoints from "./Api/EndPoints";
import LoadingBackdrop<PERSON>tom from "./components/global/atoms/loading/LoadingBackdrop";
const LookupListService = new ListServiceClass(endPoints.list);
import { useState } from "react";
import { ErrorMessage } from "./components/global/atoms/errors/Errors";
/* Password
testuser
SecurePass123!



*/

/**
 * The main app component. This is the root of the react component tree.
 *
 * It renders a Box with a single child, a Routes component. The Routes component
 * renders a DashboardPage component when the path is "/".
 *
 * @returns {React.ReactElement} The main app component.
 */

const App = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [appLoading, setAppLoading] = useState(false);
const [loadingMessage, setLoadingMessage] = useState("");
const [appError, setAppError] = useState(false);

  const { isAuthenticated } = useSelector((state) => state.auth);

  const apiService = APIService.getApiService();
  apiService.setupInterceptors(navigate, dispatch); // Setting up interceptors with navigation and dispatch


  useEffect(() => {
    /**
     * Sequential data loading function
     * 1. Load config data
     * 2. Load entities  
     * 3. Load lists
     */
    const loadAppData = async () => {
      if (!isAuthenticated) return;
      
      setAppLoading(true);
      
      try {
        // Step 1: Load Config Data
        setLoadingMessage("Loading Data...");
        
        let configData = {
          conditionTypes: [],
          variableAssignments: [],
          functions: [],
          standardProperties: [],
          webhooksAllowedUrls: [],
        };
  
        try {
          let res = await GlobalService.getConfig();
          if (!res.success) {
            setAppError(res.data || "Error:config data is not loaded properly")
            return;
          }
          configData = res.data
        } catch (error) {
          console.log("Config data error:", error);
          setAppError(error.message)
        }
  
        // Process config data
        const transactionVariables = configData.standardProperties
          ? Helpers.transactionVarsFormatter(configData.standardProperties)
          : [];
  
        const formattedConditionAndActionVars =
          Helpers.aggregateConditionAndActionsVarData(configData);
  
        // Step 2: Load Entities
        
        let entitiesData = [];
        try {
          let entitiesRes = await CampaignService.getEntities();
          if (!entitiesRes.success) {
            setAppError(entitiesRes.data || "Error:entities are not loaded properly")
            return;
          } 
          entitiesData = entitiesRes.data;
        } catch (error) {
          console.log("Entities error:", error);
          setAppError(error.message)
        }
  
        // Step 3: Load Lists
        // setLoadingMessage("Loading lookup lists...");
       
        
        let listsData = [];
        try {
          let listsRes = await LookupListService.getAllList();
          if (!listsRes.success) {
            setAppError(listsRes.data || "Error:lists are not loaded properly")
            return;
          }
          listsData = listsRes.data;
        } catch (error) {
          console.log("Lists error:", error);
          setAppError(error.message)
        }
  
        // Step 4: Dispatch all data to Redux store
        // setLoadingMessage("Finalizing data...");
        
        dispatch(
          setReceivedData({
            ...formattedConditionAndActionVars,
            webhooksAllowedUrls: configData.webhooksAllowedUrls,
            transactionVariables: transactionVariables,
            entities: entitiesData,
            lists: listsData,
          })
        );
  
        
      } catch (error) {
        console.log("App data loading error:", error);
      } finally {
        // Remove loader after all operations complete
        setAppLoading(false);
        setLoadingMessage("");
      }
    };
  
    loadAppData();
  }, [dispatch, isAuthenticated]);
  return (
    <>
      <LoadingBackdropAtom
      message={loadingMessage}
      open={appLoading}
    />
      {/* Show error message if any service fails */}
      {appError && !appLoading && (
      <ErrorMessage errorMessage={appError} />
    )}
    {
      !appError && !appLoading && (
        <Box
        maxWidth={"xl"}
        sx={{
          flexGrow: 1,
          width: "100%",
          maxWidth: "1600px",
          margin: "0 auto",
          position: "relative",
        }}
      >
        <Routes>
          <Route path="/" element={<DashboardPage />} />
          <Route path="/login" element={<RegistrationPage />} />
        </Routes>
        {/* <Footer /> */}
      </Box>
      )
    }
    </>
   
  );
};

export default App;
