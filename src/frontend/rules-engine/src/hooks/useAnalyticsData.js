// useAnalyticsData.js
import { useState, useEffect, useRef, useCallback } from 'react';
import { useSelector } from 'react-redux';
import AnalyticsService from '../Api/AnalyticsService';
import { Helpers } from '../utils/generalFunctions/index';

/**
 * Custom hook for managing analytics data fetching and processing
 * @param {Object} filters - Filter criteria for data query
 * @returns {Object} - Data state and functions
 */
const useAnalyticsData = (filters, includeMicroseconds = true) => {
    // State management
    const [chartData, setChartData] = useState(null);
    const [aggregationData, setAggregationData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [pagination, setPagination] = useState({ page: 1, limit: 100 });
    const [paginationInfo, setPaginationInfo] = useState(null);
    const [hasMoreData, setHasMoreData] = useState(true);
    const [selectedVariables, setSelectedVariables] = useState(new Set());
    const [dateRange, setDateRange] = useState(null);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const lastRequestTimeRef = useRef(0);
    const [chartType, setChartType] = useState('line');

    // Redux data
    const { analyticsData } = useSelector((state) => state.campaigns);
    const { campaignData } = analyticsData;
    const analyticsService = new AnalyticsService({});

    /**
     * Color palette for different variables
     */
    const colorPalette = [
        'rgba(75, 192, 192, 1)',    // Teal
        'rgba(255, 99, 132, 1)',    // Red
        'rgba(54, 162, 235, 1)',    // Blue
        'rgba(255, 205, 86, 1)',    // Yellow
        'rgba(153, 102, 255, 1)',   // Purple
        'rgba(255, 159, 64, 1)',    // Orange
        'rgba(199, 199, 199, 1)',   // Gray
        'rgba(83, 102, 255, 1)',    // Indigo
    ];

    /**
     * Helper function to get persistent variable name from campaign data
     */
    const getVariableName = (variableId) => {
        if (!campaignData?.persistentVariables) return variableId;
        const variable = campaignData.persistentVariables.find(v => v.variableId === variableId);
        return variable ? Helpers.capitalizeFirstLetter(variable.name) : Helpers.capitalizeFirstLetter(variableId);
    };

    /**
     * Enrich aggregation data with persistent variable names from campaign data
     */
    const enrichAggregationWithNames = (aggregation) => {
        if (!aggregation?.variables) return aggregation;

        const enrichedVariables = aggregation.variables.map(variable => ({
            ...variable,
            variable_name: getVariableName(variable.variable_id)
        }));

        return {
            ...aggregation,
            variables: enrichedVariables
        };
    };

    /**
     * Create base filters from campaign data
     */
    const createBaseFilters = useCallback(() => {
        if (!campaignData) return {};

        return {
            entity_id: campaignData.entityId,
            context_id: campaignData.contextId,
            rule_set_id: campaignData.rulesetId,
            rule_set_version: campaignData.version
        };
    }, [campaignData]);

    const formatTimestamp = (ts) => {
        // Example ts: "2025-07-02T09:55:00.000Z"
        const [datePart, timePartRaw] = ts.split('T');
        const [, month, day] = datePart.split('-');
        const [timePart, msZ] = timePartRaw.split('.');
        const time = timePart; // "09:55:00"
        const micros = msZ?.replace('Z', '') || '000';

        let formatted = `${month}-${day} ${time}`;
        if (includeMicroseconds) {
            formatted += `.${micros}`;
        }

        return formatted;
    }


    /**
     * Process raw records into Chart.js format
     * FIXED: Preserve original timestamp without timezone conversion
     */
    // A ref to hold every record ever fetched
    const recordsRef = useRef([]);

    const processChartData = useCallback((newRecords, resetData, useType) => {


        // 1) Merge into our master list
        if (resetData) {
            recordsRef.current = newRecords;
        } else {
            recordsRef.current = [...recordsRef.current, ...newRecords];
        }

        // 2) Base everything off of recordsRef.current
        const allRecords = recordsRef.current;

        if (!allRecords.length) {
            if (resetData) setChartData(null);
            return;
        }

        // 3) Build your labels & datasets just like before, but from allRecords
        const timestamps = Array.from(new Set(allRecords.map(r => r.timestamp))).sort();
        const variableIds = Array.from(new Set(allRecords.map(r => r.variable_id)));
        const labels = timestamps.map(formatTimestamp);

        const datasets = variableIds.map((variableId, idx) => {
            const data = timestamps.map(ts => {
                const rec = allRecords.find(r => r.timestamp === ts && r.variable_id === variableId);
                return rec ? rec.value_numeric : (useType === 'line' ? null : 0);
            });

            // Chart type-specific configuration with proper current chartType value
            if (useType === 'line') {
                return {
                    label: getVariableName(variableId),
                    data,
                    backgroundColor: 'transparent', // Transparent for line charts
                    borderColor: colorPalette[idx % colorPalette.length], // Colored border for lines
                    borderWidth: 2, // Thicker border for line charts
                    fill: false, // No fill for line charts
                    tension: 0.1,
                    pointRadius: 4,
                    pointHoverRadius: 6,
                    spanGaps: true,
                    pointBackgroundColor: colorPalette[idx % colorPalette.length],
                    pointBorderColor: colorPalette[idx % colorPalette.length],
                };
            } else {
                // Bar chart configuration
                return {
                    label: getVariableName(variableId),
                    data,
                    backgroundColor: colorPalette[idx % colorPalette.length].replace('1)', '0.8)'), // Opaque for bars
                    borderColor: colorPalette[idx % colorPalette.length],
                    borderWidth: 1, // Thinner border for bars
                    fill: true, // Fill for bar charts
                    borderRadius: 4,
                };
            }
        });

        setChartData({ labels, datasets });
    }, [chartType, includeMicroseconds]);



    /**
     * Fetch time-series data from the analytics service
     */
    const fetchTimeSeriesData = useCallback(async (resetData = false, explicitPagination = null) => {
        try {
            setLoading(true);
            setError(null);


            // here to sync chartType state value bcz here async it get's stored previous chartValue which causes ui issue
            const useType = (!filters.time_bucket || filters.time_bucket == 0)
                ? 'line'
                : chartType;
            // Determine which pagination to use
            let currentPagination = explicitPagination || pagination;
            if (resetData) {
                setChartData(null);
                setPaginationInfo(null);
                // Use reset pagination values immediately
                currentPagination = { page: 1, limit: paginationInfo?.records_per_page || 100 };
                setPagination(currentPagination);
            }

            // Don't fetch if campaign data is not available
            if (!campaignData?.entityId || !campaignData?.contextId || !campaignData?.rulesetId) {
                setError('Campaign data is not available');
                return;
            }

            // Create base filters from campaign data and merge with user filters
            const baseFilters = createBaseFilters();
            const mergedFilters = { ...baseFilters, ...filters };

            // Always pass pagination when we have explicit pagination or when resetData is true
            const shouldPassPagination = resetData || explicitPagination;
            const response = shouldPassPagination
                ? await analyticsService.queryTimeSeriesData(mergedFilters, currentPagination)
                : await analyticsService.queryTimeSeriesData(mergedFilters);

            if (response.success) {
                const { records, aggregation, pagination: paginationInfo, dateRange } = response.data;
                processChartData(records, resetData, useType);

                // Enrich aggregation data with variable names
                const enrichedAggregation = enrichAggregationWithNames(aggregation);
                setAggregationData(enrichedAggregation);

                // Set the date range from API response
                setDateRange(dateRange);

                // Store pagination info for display
                setPaginationInfo(paginationInfo);

                setHasMoreData(paginationInfo.has_next_page);

                // Initialize selected variables with all available variables
                if (enrichedAggregation?.variables) {
                    const availableVars = new Set(enrichedAggregation.variables.map(v => v.variable_id));
                    setSelectedVariables(availableVars);
                }
            } else {
                setError(response.data);
            }
        } catch (err) {
            setError('Failed to load analytics data');
            console.error('Analytics chart error:', err);
        } finally {
            setLoading(false);
        }
    }, [filters, pagination, createBaseFilters, chartType, processChartData]);
    /**
   * Auto-reset chart type to 'line' when selector conditions are not met
   */
    useEffect(() => {
        const shouldShowSelector = filters &&
            Object.keys(filters).length > 0 &&
            filters.time_bucket !== null &&
            filters.time_bucket !== 0;

        // If selector should be hidden, force chart type to 'line'
        if (!shouldShowSelector && chartType !== 'line') {
            setChartType('line');
        }
    }, [filters]);
    /**
     * Re-process existing data when chart type changes
     * This ensures immediate re-styling without re-fetching data
     */
    useEffect(() => {
        // Only re-process if we have existing data in recordsRef
        if (recordsRef.current && recordsRef.current.length > 0) {
            processChartData(recordsRef.current, true, chartType); // Use existing data, treat as reset to force re-render
        }
    }, [chartType, processChartData]);

    /**
     * ✅ Protected load more data function
     */
    const loadMoreDataProtected = useCallback(() => {
        const now = Date.now();
        const timeSinceLastRequest = now - lastRequestTimeRef.current;

        // ✅ Prevent requests if:
        // 1. Already loading
        // 2. Another request in progress  
        // 3. Less than 1 second since last request
        if (loading || isLoadingMore || timeSinceLastRequest < 1000) {
            return;
        }

        if (!hasMoreData) {
            return;
        }

        const currentPage = pagination.page;
        const totalPages = paginationInfo?.total_pages || 1;

        if (currentPage >= totalPages) {
            return;
        }

        // ✅ Set locks
        setIsLoadingMore(true);
        lastRequestTimeRef.current = now;
        const nextPagePagination = { page: pagination.page + 1, limit: paginationInfo.records_per_page };
        setPagination(nextPagePagination);
        fetchTimeSeriesData(false, nextPagePagination);
    }, [loading, isLoadingMore, hasMoreData, pagination.page, paginationInfo?.total_pages, chartType]);

    // ✅ Release lock when API call completes
    useEffect(() => {
        if (!loading && isLoadingMore) {
            setIsLoadingMore(false);
        }
    }, [loading, isLoadingMore]);

    return {
        // State
        chartData,
        aggregationData,
        loading,
        error,
        pagination,
        paginationInfo,
        hasMoreData,
        selectedVariables,
        dateRange,
        isLoadingMore,

        // Functions
        fetchTimeSeriesData,
        loadMoreDataProtected,
        getVariableName,
        colorPalette,

        // Redux data
        campaignData,

        // Setters for components
        setError,
        setPagination,
        setSelectedVariables,
        setIsLoadingMore,
        lastRequestTimeRef,
        setChartType,
        chartType
    };
};

export default useAnalyticsData;