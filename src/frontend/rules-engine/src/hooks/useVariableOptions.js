import { useSelector } from "react-redux";
import { Helpers } from "../utils/generalFunctions/index";

/**
 * Hook: useVariableOptions
 * Provides grouped dropdown options for variables, transaction variables,
 * context properties, and functions.
 *
 * @param {string} entityId
 * @param {string} contextId
 * @param {string} ruleType
 * @returns {{
 *   localVariablesOptions: Array,
 *   persistentVariablesOptions: Array,
 *   transactionVariableOptions: Array,
 *   transactionContextProperties: Array,
 *   functionOptions: Array,
 *   generateVariablesOptions: (type: 'variables'|'all', wipeBrackets?: boolean) => Array
 * }}
 */
export function useVariableOptions(entityId, contextId, ruleType) {
  const { campaign, receivedData } = useSelector((s) => s.campaign);
  const { persistentVariables, localVariables } = campaign;
  const { transactionVariables, entities, functions } = receivedData;

  const generateOption = (type) => {
    switch (type) {
      case "persistentVariables":
        return persistentVariables.map((item) => ({
          label: item.name,
          value: `{${item.variableId}}`,
          type: item.type,
          description: item.description,
        }));
      case "localVariables":
        return localVariables.map((item) => ({
          label: item.name,
          value: `{${item.variableId}}`,
          type: item.type,
          description: item.description,
        }));
      case "transactionVariables":
        return ruleType
          ? transactionVariables
              .filter(
                (variable) =>
                  variable.availablePhases &&
                  variable.availablePhases.includes(ruleType.toLowerCase())
              )
              .map((item) => ({
                value: `{${item.value}}`,
                label: item.label,
                type: item.type,
                description: item.description,
                values: item.values,
              }))
          : transactionVariables.map((item) => ({
              value: `{${item.value}}`,
              label: item.label,
              type: item.type,
              description: item.description,
              values: item.values,
            }));
      case "transactionContextProperties":
        return Helpers.generateTransContextOptions(
          entities,
          entityId,
          contextId,
          true
        );
      case "functions":
        return functions.map((fn) => ({
          value: fn.value,
          label: fn.label,
          description: fn.description,
          type: fn.returnType,
        }));
      default:
        return [];
    }
  };

  const localVariablesOptions = generateOption("localVariables");
  const persistentVariablesOptions = generateOption("persistentVariables");
  const transactionVariableOptions = generateOption("transactionVariables");
  const transactionContextProperties = generateOption(
    "transactionContextProperties"
  );

  const functionOptions = generateOption("functions");

  /**
   * @param {'variables'|'all'} type
   * @param {boolean} wipeBrackets
   */
  function generateVariablesOptions(type, wipeBrackets = false) {
    const groups = [];

    if (type === "variables" || type === "all") {
      if (persistentVariablesOptions.length)
        groups.push({
          category: "Persistent Variables",
          options: persistentVariablesOptions,
        });
      if (localVariablesOptions.length)
        groups.push({
          category: "Local Variables",
          options: localVariablesOptions,
        });
      // if (transactionVariableOptions.length)
      //   groups.push({
      //     category: "Transaction Variables",
      //     options: transactionVariableOptions,
      //   });
    }

    if (type === "all") {
      //   if (functionOptions.length)
      //     groups.push({ category: "Functions", options: functionOptions });
      if (transactionVariableOptions.length)
        groups.push({
          category: "Transaction Variables",
          options: transactionVariableOptions,
        });
      if (transactionContextProperties.length)
        groups.push({
          category: "Transaction Context Properties",
          options: transactionContextProperties,
        });
    }

    return wipeBrackets
      ? Helpers.wipeTheBracketsFromTheOptionsValue(groups)
      : groups;
  }

  return {
    localVariablesOptions,
    persistentVariablesOptions,
    transactionVariableOptions,
    transactionContextProperties,
    functionOptions,
    generateVariablesOptions,
  };
}
