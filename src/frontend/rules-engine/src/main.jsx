import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import App from "./App.jsx";
import "./index.css";
import { store } from "./redux/store";
import { Provider } from "react-redux";
import AlertStack from "./components/global/atoms/alert/Alert.jsx";
import { MuiTheme } from "./utils/styles/index.js";
import { ThemeProvider } from "@mui/material/styles";
import { BrowserRouter as Router } from "react-router-dom";
import ConfirmationDialogAtom from "./components/global/atoms/dialog/Index.jsx";

createRoot(document.getElementById("root")).render(

    <ThemeProvider theme={MuiTheme}>
      <Router>
        <Provider store={store}>
          <AlertStack />
          <ConfirmationDialogAtom />
          <App />
        </Provider>
      </Router>
    </ThemeProvider>

);
