export const dummyCampaignsData = [
  {
    campaignId: "SUMMER_PROMO_2024",
    name: "Summer Promo",
    description: "Special summer promotion with discounts and rewards.",
    startDate: "2024-06-01T10:30:00Z",
    endDate: "2024-08-31T23:59:59Z",
    entities: [
      {
        entityId: "Entity_A",
        entityName: "Entity A",
        transactionContexts: [
          {
            contextId: "SALE_PROMO",
            contextName: "Summer Promo Sale",
          },
        ],
      },
      {
        entityId: "Entity_B",
        entityName: "Entity B",
        transactionContexts: [
          {
            contextId: "SUMMER_SALE",
            contextName: "Summer Sale Context",
          },
        ],
      },
    ],
  },
  {
    campaignId: "HOLIDAY_SALE_2023",
    name: "Holiday Sale",
    description: "End of the year holiday sale with exclusive offers.",
    startDate: "2023-12-01T08:45:00Z",
    endDate: "2023-12-25T23:59:59Z",
    entities: [
      {
        entityId: "Entity_C",
        entityName: "Entity C",
        transactionContexts: [
          {
            contextId: "HOLIDAY_DISCOUNT",
            contextName: "Holiday Sale Discount",
          },
        ],
      },
    ],
  },
  {
    campaignId: "SPRING_DEALS_2024",
    name: "Spring Deals",
    description: "Exciting spring deals for all customers.",
    startDate: "2024-03-10T09:00:00Z",
    endDate: "2024-05-31T23:59:59Z",
    entities: [
      {
        entityId: "Entity_D",
        entityName: "Entity D",
        transactionContexts: [
          {
            contextId: "SPRING_DISCOUNTS",
            contextName: "Spring Discounts Context",
          },
        ],
      },
    ],
  },
];

export const collectionDropdown = [
  { label: "Customers", value: "CUSTOMERS" },
  { label: "Sales", value: "SALES" },
  { label: "Transactions", value: "TRANSACTIONS" },
];

export const collectionMappingTabData = {
  rows: [
    { name: "John Doe", age: 29, role: "Developer" },
    { name: "Jane Smith", age: 32, role: "Designer" },
    { name: "Sam Brown", age: 45, role: "Manager" },
  ],
  columns: [
    { id: "name", label: "Name", align: "left", sx: { color: "blue" } },
    { id: "age", label: "Age", align: "center" },
    { id: "role", label: "Role", align: "left" },
  ],
};

export const LocalVarSection = {
  variablesNames: [
    { label: "Customers", value: "customers" },
    { label: "Products", value: "products" },
    { label: "Transactions", value: "transactions" },
  ],
  types: [
    { label: "string", value: "string" },
    { label: "number", value: "number" },
    { label: "boolean", value: "boolean" },
  ],
};

export const receivedJsonData = {
  entities: [
    {
      entityId: "RETAIL_POS",
      entityName: "Retail Point of Sale",
      transactionContexts: [
        {
          contextId: "PURCHASE",
          contextName: "Product Purchase",
          rules: [
            {
              ruleId: "SUMMER_DISCOUNT",
              name: "Summer Discount Rule",
              description: "Applies a discount for purchases over $100",
              priority: 1,
              conditions: [
                {
                  type: "COMPARISON",
                  parameters: {
                    leftOperand: "{amount}",
                    operator: ">",
                    rightOperand: 100,
                  },
                },
              ],
              actions: [
                {
                  type: "APPLY_DISCOUNT",
                  parameters: {
                    discountAmount: "{DISCOUNT_AMOUNT}",
                  },
                },
              ],
              variableOperations: [
                {
                  variableId: "DISCOUNT_AMOUNT",
                  operation: "SET",
                  value: "{amount * 0.1}",
                },
                {
                  variableId: "TOTAL_PURCHASES",
                  operation: "ADD",
                  value: "{amount}",
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  persistentVariableDefinitions: [
    {
      variableId: "TOTAL_PURCHASES",
      name: "Total Purchases",
      description: "The total amount of purchases made by a customer",
      type: "integer",
      defaultValue: 0,
      collection: "Customers",
    },
  ],
  localVariableDefinitions: [
    {
      variableId: "DISCOUNT_AMOUNT",
      name: "Discount Amount",
      description: "Calculated discount amount for the current transaction",
      type: "amount",
      defaultValue: 0,
    },
  ],
  rules: {
    operators: ["==", "!=", "<", "<=", ">", ">="],
  },
};
export const dummyAnalyticsData = [
  {
    name: "Sales performance",
    id: "salesPerformance",
    fields: [
      { name: "Airtime Revenue", prefix: "$" },
      { name: "Bundle Sales Revenue", prefix: "$" },
      { name: "Transaction Count" },
      { name: "unique agent Count" },
    ],
    data: [
      {
        timestamp: "2024-11-01T13:07:12Z",
        values: [1000, 2000, 1000000, 500000],
      },
      {
        timestamp: "2024-11-02T13:07:12Z",
        values: [100, 2100, 10100, 500100],
      },
      {
        timestamp: "2024-11-03T13:07:12Z",
        values: [1200, 2200, 1000200, 500200],
      },
      {
        timestamp: "2024-11-04T13:07:12Z",
        values: [1300, 2300, 1000300, 500300],
      },
      {
        timestamp: "2024-11-05T13:07:12Z",
        values: [1400, 2400, 1000400, 500400],
      },
      {
        timestamp: "2024-11-06T13:07:12Z",
        values: [1500, 2500, 1000500, 500500],
      },
      {
        timestamp: "2024-11-07T13:07:12Z",
        values: [1600, 2600, 1000600, 500600],
      },
      {
        timestamp: "2024-11-08T13:07:12Z",
        values: [1700, 2700, 1000700, 500700],
      },
      {
        timestamp: "2024-11-09T13:07:12Z",
        values: [1800, 2800, 1000800, 500800],
      },
      {
        timestamp: "2024-11-10T13:07:12Z",
        values: [1900, 2900, 1000900, 500900],
      },
      {
        timestamp: "2024-11-11T13:07:12Z",
        values: [2000, 3000, 1001000, 1000],
      },
      {
        timestamp: "2024-11-12T13:07:12Z",
        values: [2100, 3100, 501100, 501100],
      },
      {
        timestamp: "2024-11-13T13:07:12Z",
        values: [2200, 3200, 501200, 501200],
      },
      {
        timestamp: "2024-11-14T13:07:12Z",
        values: [2300, 3300, 501300, 501300],
      },
      {
        timestamp: "2024-11-15T13:07:12Z",
        values: [2400, 3400, 501400, 501400],
      },
      {
        timestamp: "2024-11-16T13:07:12Z",
        values: [2500, 3500, 501500, 501500],
      },
      {
        timestamp: "2024-11-17T13:07:12Z",
        values: [2600, 3600, 501600, 501600],
      },
      {
        timestamp: "2024-11-18T13:07:12Z",
        values: [2700, 3700, 501700, 501700],
      },
      {
        timestamp: "2024-11-19T13:07:12Z",
        values: [3800, 3800, 1001800, 501800],
      },
      {
        timestamp: "2024-11-20T13:07:12Z",
        values: [2900, 3900, 1001900, 501900],
      },
      {
        timestamp: "2024-11-21T13:07:12Z",
        values: [3000, 4000, 1002000, 502000],
      },
      {
        timestamp: "2024-11-22T13:07:12Z",
        values: [3100, 4100, 1002100, 502100],
      },
      {
        timestamp: "2024-11-23T13:07:12Z",
        values: [3200, 4200, 1002200, 502200],
      },
      {
        timestamp: "2024-11-24T13:07:12Z",
        values: [3300, 4300, 1002300, 502300],
      },
      {
        timestamp: "2024-11-25T13:07:12Z",
        values: [3400, 4400, 1002400, 502400],
      },
      {
        timestamp: "2024-11-26T13:07:12Z",
        values: [3500, 4500, 2500, 502500],
      },
      {
        timestamp: "2024-11-27T13:07:12Z",
        values: [3600, 4600, 1002600, 502600],
      },
      {
        timestamp: "2024-11-28T13:07:12Z",
        values: [3700, 4700, 1002700, 502700],
      },
      {
        timestamp: "2024-11-29T13:07:12Z",
        values: [3800, 4800, 1002800, 502800],
      },
      {
        timestamp: "2024-11-30T13:07:12Z",
        values: [5900, 4900, 1002900, 502900],
      },
      {
        timestamp: "2024-12-01T13:07:12Z",
        values: [5000, 5000, 1003000, 503000],
      },
      {
        timestamp: "2024-12-02T13:07:12Z",
        values: [5100, 5100, 1003100, 503100],
      },
      {
        timestamp: "2024-12-03T13:07:12Z",
        values: [5200, 5200, 1003200, 503200],
      },
      {
        timestamp: "2024-12-04T13:07:12Z",
        values: [5300, 5300, 1003300, 503300],
      },
      {
        timestamp: "2024-12-05T13:07:12Z",
        values: [5400, 5400, 1003400, 503400],
      },
      {
        timestamp: "2024-12-06T13:07:12Z",
        values: [2500, 5500, 1003500, 503500],
      },
      {
        timestamp: "2024-12-07T13:07:12Z",
        values: [4600, 5600, 1003600, 503600],
      },
      {
        timestamp: "2024-12-08T13:07:12Z",
        values: [4700, 5700, 1003700, 503700],
      },
      {
        timestamp: "2024-12-09T13:07:12Z",
        values: [4800, 5800, 1003800, 503800],
      },
      {
        timestamp: "2024-12-10T13:07:12Z",
        values: [4900, 5900, 1003900, 403900],
      },
      {
        timestamp: "2024-12-11T13:07:12Z",
        values: [5000, 6000, 1004000, 504000],
      },
      {
        timestamp: "2024-12-12T13:07:12Z",
        values: [5100, 6100, 1004100, 604100],
      },
      {
        timestamp: "2024-12-13T13:07:12Z",
        values: [5200, 6200, 1004200, 504200],
      },
      {
        timestamp: "2024-12-14T13:07:12Z",
        values: [5300, 6300, 1004300, 504300],
      },
      {
        timestamp: "2024-12-15T13:07:12Z",
        values: [5400, 6400, 1004400, 504400],
      },
      {
        timestamp: "2024-12-16T13:07:12Z",
        values: [5500, 6500, 1004500, 504500],
      },
      {
        timestamp: "2024-12-17T13:07:12Z",
        values: [5600, 6600, 1004600, 504600],
      },
      {
        timestamp: "2024-12-18T13:07:12Z",
        values: [5700, 6700, 1004700, 504700],
      },
      {
        timestamp: "2024-12-19T13:07:12Z",
        values: [4800, 5800, 1003800, 503800],
      },
      {
        timestamp: "2024-12-20T13:07:12Z",
        values: [4900, 5900, 1003900, 403900],
      },
      {
        timestamp: "2024-12-21T13:07:12Z",
        values: [5000, 6000, 1004000, 504000],
      },
      {
        timestamp: "2024-12-22T13:07:12Z",
        values: [5100, 6100, 1004100, 604100],
      },
      {
        timestamp: "2024-12-23T13:07:12Z",
        values: [5200, 6200, 1004200, 504200],
      },
      {
        timestamp: "2024-12-24T13:07:12Z",
        values: [5300, 6300, 1004300, 504300],
      },
      {
        timestamp: "2024-12-25T13:07:12Z",
        values: [5400, 6400, 1004400, 504400],
      },
      {
        timestamp: "2024-12-26T13:07:12Z",
        values: [6500, 7500, 1005500, 305500],
      },
      {
        timestamp: "2024-12-27T13:07:12Z",
        values: [6600, 7600, 1005600, 305600],
      },
      {
        timestamp: "2024-12-28T13:07:12Z",
        values: [6700, 7700, 1005700, 305700],
      },
      {
        timestamp: "2024-12-29T13:07:12Z",
        values: [6800, 7800, 1005800, 305800],
      },
      {
        timestamp: "2024-12-30T13:07:12Z",
        values: [6900, 7900, 1005900, 305900],
      },
    ],
  },
  {
    name: "Cost performance",
    id: "costPerformance",
    fields: [
      { name: "Commissions", prefix: "$" },
      { name: "Trade Bonuses", prefix: "$" },
      { name: "Transaction Count" },
      { name: "Benefits", prefix: "$" },
    ],
    data: [
      {
        timestamp: "2024-11-01T13:07:12Z",
        values: [1000, 2000, 1000000, 500000],
      },
      {
        timestamp: "2024-11-02T13:07:12Z",
        values: [950, 1900, 1012000, 480000],
      },
      {
        timestamp: "2024-11-03T13:07:12Z",
        values: [1100, 2100, 1008000, 510000],
      },
      {
        timestamp: "2024-11-04T13:07:12Z",
        values: [1200, 1950, 1015000, 495000],
      },
      {
        timestamp: "2024-11-05T13:07:12Z",
        values: [1300, 2050, 1006000, 505000],
      },
      {
        timestamp: "2024-11-06T13:07:12Z",
        values: [1250, 2150, 1009000, 515000],
      },
      {
        timestamp: "2024-11-07T13:07:12Z",
        values: [1400, 2000, 1013000, 485000],
      },
      {
        timestamp: "2024-11-08T13:07:12Z",
        values: [1450, 2200, 1020000, 500000],
      },
      {
        timestamp: "2024-11-09T13:07:12Z",
        values: [1500, 2300, 1017000, 520000],
      },
      {
        timestamp: "2024-11-10T13:07:12Z",
        values: [1600, 2100, 1025000, 495000],
      },
      {
        timestamp: "2024-11-11T13:07:12Z",
        values: [1050, 2250, 1030000, 510000],
      },
      {
        timestamp: "2024-11-12T13:07:12Z",
        values: [1000, 2350, 1018000, 525000],
      },
      {
        timestamp: "2024-11-13T13:07:12Z",
        values: [950, 2450, 1023000, 515000],
      },
      {
        timestamp: "2024-11-14T13:07:12Z",
        values: [1750, 2500, 1029000, 505000],
      },
      {
        timestamp: "2024-11-15T13:07:12Z",
        values: [1850, 2600, 1035000, 520000],
      },
      {
        timestamp: "2024-11-16T13:07:12Z",
        values: [1900, 2700, 1040000, 530000],
      },
      {
        timestamp: "2024-11-17T13:07:12Z",
        values: [2000, 2800, 1032000, 540000],
      },
      {
        timestamp: "2024-11-18T13:07:12Z",
        values: [1950, 2750, 1045000, 535000],
      },
      {
        timestamp: "2024-11-19T13:07:12Z",
        values: [2050, 2900, 1050000, 550000],
      },
      {
        timestamp: "2024-11-20T13:07:12Z",
        values: [2150, 3000, 1040000, 545000],
      },
      {
        timestamp: "2024-11-21T13:07:12Z",
        values: [2200, 3150, 1048000, 560000],
      },
      {
        timestamp: "2024-11-22T13:07:12Z",
        values: [2300, 3200, 1053000, 570000],
      },
      {
        timestamp: "2024-11-23T13:07:12Z",
        values: [2400, 3350, 1060000, 580000],
      },
      {
        timestamp: "2024-11-24T13:07:12Z",
        values: [2500, 3450, 1065000, 590000],
      },
      {
        timestamp: "2024-11-25T13:07:12Z",
        values: [2550, 3550, 1058000, 600000],
      },
      {
        timestamp: "2024-11-26T13:07:12Z",
        values: [2650, 3650, 1063000, 605000],
      },
      {
        timestamp: "2024-11-27T13:07:12Z",
        values: [2750, 3750, 1070000, 615000],
      },
      {
        timestamp: "2024-11-28T13:07:12Z",
        values: [2850, 3850, 1068000, 625000],
      },
      {
        timestamp: "2024-11-29T13:07:12Z",
        values: [2950, 3950, 1075000, 630000],
      },
      {
        timestamp: "2024-11-30T13:07:12Z",
        values: [3000, 4050, 1080000, 640000],
      },
      {
        timestamp: "2024-12-01T13:07:12Z",
        values: [3100, 4200, 1085000, 650000],
      },
      {
        timestamp: "2024-12-02T13:07:12Z",
        values: [3200, 4300, 1090000, 660000],
      },
      {
        timestamp: "2024-12-03T13:07:12Z",
        values: [3250, 4400, 1082000, 655000],
      },
      {
        timestamp: "2024-12-04T13:07:12Z",
        values: [3350, 4500, 1095000, 670000],
      },
      {
        timestamp: "2024-12-05T13:07:12Z",
        values: [3450, 4600, 1100000, 675000],
      },
      {
        timestamp: "2024-12-06T13:07:12Z",
        values: [3500, 4700, 1093000, 680000],
      },
      {
        timestamp: "2024-12-07T13:07:12Z",
        values: [3600, 4850, 1098000, 685000],
      },
      {
        timestamp: "2024-12-08T13:07:12Z",
        values: [3700, 4950, 1105000, 690000],
      },
      {
        timestamp: "2024-12-09T13:07:12Z",
        values: [3800, 5000, 1110000, 700000],
      },
      {
        timestamp: "2024-12-10T13:07:12Z",
        values: [3900, 5100, 1102000, 705000],
      },
      {
        timestamp: "2024-12-11T13:07:12Z",
        values: [4000, 5200, 1115000, 710000],
      },
      {
        timestamp: "2024-12-12T13:07:12Z",
        values: [4100, 5350, 1120000, 720000],
      },
      {
        timestamp: "2024-12-13T13:07:12Z",
        values: [4200, 5400, 1113000, 730000],
      },
      {
        timestamp: "2024-12-14T13:07:12Z",
        values: [4300, 5500, 1128000, 735000],
      },
      {
        timestamp: "2024-12-15T13:07:12Z",
        values: [3400, 5600, 1133000, 740000],
      },
      {
        timestamp: "2024-12-16T13:07:12Z",
        values: [3500, 5700, 1125000, 750000],
      },
      {
        timestamp: "2024-12-17T13:07:12Z",
        values: [3600, 5800, 1138000, 760000],
      },
      {
        timestamp: "2024-12-18T13:07:12Z",
        values: [3700, 5900, 1140000, 770000],
      },
      {
        timestamp: "2024-12-19T13:07:12Z",
        values: [3800, 6000, 1145000, 780000],
      },
      {
        timestamp: "2024-12-20T13:07:12Z",
        values: [3900, 6150, 1138000, 775000],
      },
      {
        timestamp: "2024-12-21T13:07:12Z",
        values: [3000, 6200, 1150000, 785000],
      },
      {
        timestamp: "2024-12-22T13:07:12Z",
        values: [3100, 6300, 1155000, 790000],
      },
      {
        timestamp: "2024-12-23T13:07:12Z",
        values: [3200, 6450, 1160000, 800000],
      },
      {
        timestamp: "2024-12-24T13:07:12Z",
        values: [3300, 6500, 1153000, 810000],
      },
      {
        timestamp: "2024-12-25T13:07:12Z",
        values: [3400, 6600, 1168000, 820000],
      },
      {
        timestamp: "2024-12-26T13:07:12Z",
        values: [3500, 6700, 1170000, 830000],
      },
      {
        timestamp: "2024-12-27T13:07:12Z",
        values: [3600, 6800, 1162000, 835000],
      },
      {
        timestamp: "2024-12-28T13:07:12Z",
        values: [3700, 6900, 1175000, 840000],
      },
      {
        timestamp: "2024-12-29T13:07:12Z",
        values: [5800, 7000, 1180000, 850000],
      },
      {
        timestamp: "2024-12-30T13:07:12Z",
        values: [5900, 7100, 1172000, 860000],
      },
      {
        timestamp: "2024-12-31T13:07:12Z",
        values: [6000, 7200, 1185000, 870000],
      },
    ],
  },
];
