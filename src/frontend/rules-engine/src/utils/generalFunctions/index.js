import bcrypt from "bcryptjs";
import dayjs from "dayjs";

/**
 * @typedef {import("../../../jsDocs/components/dashboard").InputField} Inputs
 */
export const Helpers = {
  /**
 * defaultValues:
 * This function takes an array of keys as input and returns an object with the same keys and default values.
 * @param {string[]} keys - An array of strings representing the keys of the object.
 * @param {{[key: string]: string}} values - An object containing the default values for the keys.
 * @param {string[]} optionalKeys - An array of strings representing the keys that are optional.
 * @returns {{[key: string]: FormField}} An object where each key maps to a FormField object
 * 
 * @typedef {Object} FormField
 * @property {string} value - The current value of the field
 *    - For text fields: the text content
 *    - For date fields: the selected date
 *    - Empty string ('') if no value is provided
 * 
 * @property {boolean} isValid - The validation state of the field
 *    - true if:
 *      - The field is optional (included in optionalKeys)
 *      - The field has a non-empty value
 *    - false if:
 *      - Required field is empty
 *      - Validation rules are not met
 * 
 * @property {string} error - The error message for the field
 *    - Contains "[fieldName] is required" for required fields
 *    - Empty string if field is valid
 *    - Custom error message based on validation rules
 * 
 * @property {boolean} isShowError - Controls error message visibility
 *    - true: Show the error message to the user
 *    - false: Hide the error message
 *    - Initially set to false and typically updated on blur or submit
 * 
 * @example
 * const fields = defaultValues(
 *   ['name', 'email', 'description'],
 *   { name: 'John' },
 *   ['description']
 * );
 * // Returns:
 * // {
 * //   name: {
 * //     value: 'John',
 * //     isValid: true,
 * //     error: 'name is required',
 * //     isShowError: false
 * //   },
 * //   email: {
 * //     value: '',
 * //     isValid: false,
 * //     error: 'email is required',
 * //     isShowError: false
 * //   },
 * //   description: {
 * //     value: '',
 * //     isValid: true, // true because it's optional
 * //     error: 'description is required',
 * //     isShowError: false
 * //   }
 * // }

   * */
  defaultValues: (keys, values = {}, optionalKeys = []) => {
    /**
     * @type {{[key: string]: Inputs}}
     */
    const returnObj = {};
    keys.forEach((key) => {
      // If value is undefined or null, use an empty string
      const value = values[key] ?? ""; //values[key] || "";
      // Set isValid to true if the key is present in the validationFlags array
      const isValidFlag =
        optionalKeys.includes(key) || (value !== "" && value !== null);

      returnObj[key] = {
        value: value,
        isValid: isValidFlag,
        error: `${key} field is required`,
        isShowError: false,
      };
    });

    return returnObj;
  },
  /**
   * returnObj:
   * This function takes two parameters, success and data, and returns an object with two properties, success and data.
   * The success property is a boolean indicating whether the operation was successful or not.
   * The data property can be any type of data, depending on the context of the function call.
   * @param {boolean} success - A boolean indicating whether the operation was successful or not.
   * @param {any} data - The data to be returned in the object.
   * @returns {{success: boolean, data: any}} - An object with success and data properties.
   */
  returnObj: (success, data) => {
    return {
      success,
      data,
    };
  },
  /**
   * returnValues:
   * This function takes an object of form input values (as returned by defaultValues) and
   * returns a new object with the same keys, but the values are the actual input values.
   * The 'isValid' and 'error' properties of the input object are ignored.
   *
   * @param {{[key: string]: FormField}} inputs - The input object with 'isValid', 'error', 'isShowError' & 'value' properties
   * @returns {{[key: string]: string}} - The output with key and value only
   */
  returnValues: (inputs) => {
    // her i want that remove the isvalidflag an error from the inputs and return the key and the value shoould be the values?
    /**
     * @type {{[key: string]: string}}
     */
    const returnObj = {};
    for (const [key, value] of Object.entries(inputs)) {
      returnObj[key] =
        typeof value.value === "string" ? value.value?.trim() : value.value;
    }
    return returnObj;
  },
  /**
   * returnValues:
   * This function takes an array object of columns and data format
   * returns a new array with the same but formated data by
   *
   * @param {{columns: Array.<Object>, data: Object.<string, string>}} inputs - The input object
   * @returns {Array.<Object.<string, string>>} - The output object
   */
  returnArrayValues: (inputs) => {
    // Loop through the arrays and return the values
    return inputs.map((item) =>
      Object.entries(item.data).reduce((acc, [key, value]) => {
        acc[key] = value; // Store the key-value pairs in the accumulator
        return acc;
      }, {})
    );
  },
  // createCampaignObjectCreator: (state, setType, path) => {},
  /**
   * nameIdGenerator
   *
   * Takes a string and returns a string with all spaces replaced with
   * underscores and all characters converted to uppercase. This is used
   * to generate a unique ID from a given name.
   *
   * @param {string} name - The string to convert.
   * @returns {string} - The converted string.
   */
  nameIdGenerator: (name) => {
    return name.toUpperCase().replace(/\s+/g, "_");
  },
  /**
   * idGenerator
   *
   * Takes a string and converts it based on specified case format and separator.
   * Also handles maximum length constraints and special character removal.
   *
   * @param {string} name - The string to convert
   * @param {"upper"| "lower" | "capitalize" | "camel" | "pascal" | "kebab" | "snake"} [caseFormat='upper'] - The case format to apply:
   *                                        'upper', 'lower', 'capitalize',
   *                                        'camel', 'pascal', 'kebab', 'snake'
   * @param {Object} [options={}] - Additional options
   * @param {string} [options.separator='_'] - Character to replace spaces with
   * @param {number} [options.maxLength=Infinity] - Maximum length of output
   * @param {boolean} [options.removeSpecialChars=true] - Remove special characters
   * @returns {string} - The converted string
   */
  // idGenerator: (name, caseFormat = "upper", options = {}) => {
  //   // Default options
  //   const {
  //     separator = "_",
  //     maxLength = Infinity,
  //     removeSpecialChars = true,
  //   } = options;

  //   if (!name) return "";

  //   // Remove special characters if specified
  //   let processedName = name;
  //   if (removeSpecialChars) {
  //     processedName = processedName.replace(/[^a-zA-Z0-9\s]/g, "");
  //   }

  //   // Replace multiple spaces with single space
  //   processedName = processedName.trim().replace(/\s+/g, " ");

  //   // Apply case formatting
  //   switch (caseFormat.toLowerCase()) {
  //     case "lower":
  //       processedName = processedName.toLowerCase();
  //       break;

  //     case "upper":
  //       processedName = processedName.toUpperCase();
  //       break;

  //     case "capitalize":
  //       processedName = processedName
  //         .toLowerCase()
  //         .split(" ")
  //         .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
  //         .join(" ");
  //       break;

  //     case "camel":
  //       processedName = processedName
  //         .toLowerCase()
  //         .split(" ")
  //         .map((word, index) =>
  //           index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)
  //         )
  //         .join("");
  //       return processedName.substring(0, maxLength);

  //     case "pascal":
  //       processedName = processedName
  //         .toLowerCase()
  //         .split(" ")
  //         .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
  //         .join("");
  //       return processedName.substring(0, maxLength);

  //     case "kebab":
  //       processedName = processedName.toLowerCase();
  //       return processedName.replace(/\s+/g, "-").substring(0, maxLength);

  //     case "snake":
  //       processedName = processedName.toLowerCase();
  //       return processedName.replace(/\s+/g, "_").substring(0, maxLength);

  //     default:
  //       processedName = processedName.toUpperCase();
  //   }

  //   // Replace spaces with separator and apply max length
  //   return processedName.replace(/\s+/g, separator).substring(0, maxLength);
  // },
  /**
   * updateInputField
   *
   * Pass the name and the value and create the object of it along with other properties
   * @param {object} inputs - The input object
   * @param {{name: string, value: string, status: {success: boolean, error: string, message: string}}} update - The update object
   * @returns {object} - The updated object
   */
  idGenerator: (name, caseFormat = "upper", options = {}) => {
    // Default options
    const {
      separator = "_",
      maxLength = Infinity,
      removeSpecialChars = true,
    } = options;

    if (!name) return "";

    // Remove special characters if specified, but preserve accented characters
    let processedName = name;
    if (removeSpecialChars) {
      // This pattern uses Unicode property escapes to match letters from any language
      // \p{L} matches any Unicode letter
      processedName = processedName.replace(/[^\p{L}0-9\s]/gu, "");
    }

    // Replace multiple spaces with single space
    processedName = processedName.trim().replace(/\s+/g, " ");

    // Apply case formatting
    switch (caseFormat.toLowerCase()) {
      case "lower":
        processedName = processedName.toLowerCase();
        break;

      case "upper":
        processedName = processedName.toUpperCase();
        break;

      // @change  Added return here to prevent the separator replacement at the end
      case "capitalize":
        return processedName
          .toLowerCase()
          .split(" ")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ")
          .substring(0, maxLength);

      case "camel":
        processedName = processedName
          .toLowerCase()
          .split(" ")
          .map((word, index) =>
            index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)
          )
          .join("");
        return processedName.substring(0, maxLength);

      case "pascal":
        processedName = processedName
          .toLowerCase()
          .split(" ")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join("");
        return processedName.substring(0, maxLength);

      case "kebab":
        processedName = processedName.toLowerCase();
        return processedName.replace(/\s+/g, "-").substring(0, maxLength);

      case "snake":
        processedName = processedName.toLowerCase();
        return processedName.replace(/\s+/g, "_").substring(0, maxLength);
      case "upper-snake":
        processedName = processedName.toUpperCase();
        return processedName.replace(/\s+/g, "_").substring(0, maxLength);
      default:
        processedName = processedName.toUpperCase();
    }

    // Replace spaces with separator and apply max length
    return processedName.replace(/\s+/g, separator).substring(0, maxLength);
  },
  generateUniqueId: ({
    name,
    existingIds,
    caseFormat = "camel",
    maxLength = 50,
  }) => {
    let baseId = Helpers.idGenerator(name, caseFormat, { maxLength });

    let uniqueId = baseId;
    let counter = 1;

    while (existingIds.includes(uniqueId)) {
      const suffix = counter.toString();
      const maxBaseLength = 50 - suffix.length;
      uniqueId = baseId.slice(0, maxBaseLength) + suffix;
      counter++;
    }

    existingIds.push(uniqueId);
    return uniqueId;
  },
  capitalizeFirstLetter: (string) => {
    return string.split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');

  },

  /**
   * updateInputField: This function is used to update the input field in the input object
   * @param {Inputs} inputs - The input object
   * @param {{name: string, value: string, status: {success: boolean, error: string, }}} update - The update object
   * @returns {Inputs} - The updated object
   */
  updateInputField: (
    inputs,
    { name, value, status = { success: true, error: "" } } // Changed default error to empty string
  ) => {
    return {
      ...inputs,
      [name]: {
        value,
        isValid: status.success,
        isShowError: !status.success,
        // Changed to default to empty string instead of null
        error:
          status.error !== undefined
            ? status.error
            : status.message !== undefined
              ? status.message
              : "",
      },
    };
  },
  /**
   * tabStatusColor: This function is used to return the color of the tab based on the status of the tab
   * @param {string} tabName - The name of the tab
   * @param {string[]} option - The option of the tab
   * @returns {string} - The color of the tab
   */
  tabStatusColor: (tabName, option) => {
    switch (tabName) {
      case "entityId":
        return option && option.length > 0 ? "green" : "grey";
      case "contextId":
        return option && option.length > 0 ? "green" : "grey";
      case "category":
        return option && option.length > 0 ? "green" : "grey";
      case "collectionMappings":
        return option && option.length > 0 ? "green" : "grey";
      case "persistentVariables":
        return option && option.length > 0 ? "green" : "grey";
      case "localVariables":
        return option && option.length > 0 ? "green" : "grey";
      case "rules":
        // Assuming there is some data for Entities & Rules section in campaign
        return option && option.length > 0 ? "green" : "grey";
      case "evaluationRules":
        return option && option.length > 0 ? "green" : "grey";
      case "outcomeRules":
        return option && option.length > 0 ? "green" : "grey";
      default:
        return "grey";
    }
  },
  handleAddNewItem: () => { },
  /**
   * The handle is the function that will be called when the user clicks the login button.
   * It takes an object as a parameter, which contains the username and password.
   * The function checks if the username and password are correct.
   * If they are correct, it will log the user in and redirect them to the home page.
   * If they are incorrect, it will display an error message.
   *
   * @param {object} data - The data object
   * @param {string} data.username - The username entered by the user
   * @param {string} data.password - The password entered by the user
   */
  handleLogin: ({ username, password }) => {
    // Hardcoded hashed credentials (replace these with the actual hashed values)

    const hardcodedCredentials = {
      username: "$2a$10$q4QaXzk9vkkPxtmW9hsUDeFdk0wPB8hYGO.3WLSokxcU4AQ9igBD6", // '$2a$10$Ay4nWaLNOCmS5kcPRANy3.fHBTU17c3q.tXsr.qg9mapKeTFlfhby', // Hashed username: "admin"
      password: "$2a$10$g53ERdalVVrz8guE8lRMiOcE9W8cYQo9/Ng9sl04ecKwGuxhcErO6", // Hashed password: "ba005566"
    };
    // pss  : $2a$10$g53ERdalVVrz8guE8lRMiOcE9W8cYQo9/Ng9sl04ecKwGuxhcErO6
    // user: $2a$10$q4QaXzk9vkkPxtmW9hsUDeFdk0wPB8hYGO.3WLSokxcU4AQ9igBD6
    // // Hash the user input for comparison
    // const hashedUsernameInput = bcrypt.hashSync(username, 10); // Hash the input username
    // const hashedPasswordInput = bcrypt.hashSync(password, 10); // Hash the input password

    // Compare with hardcoded hashed credentials
    const usernameMatch = bcrypt.compareSync(
      username,
      hardcodedCredentials.username
    );
    const passwordMatch = bcrypt.compareSync(
      password,
      hardcodedCredentials.password
    );

    // return { success: true, data: { isAuthenticated: true, token:"token",user:{username:"admin"} } };
    if (usernameMatch && passwordMatch) {
      // Create a session and store it locally
      // sessionStorage.setItem('user', JSON.stringify({ isLoggedIn: true,username, }));
      return {
        success: true,
        data: {
          isAuthenticated: true,
          token:
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwiaWF0IjoxNzMyNzk3MjgzLCJleHAiOjE3NDMxNjUyODN9.Kecg52y93DRJv6kgxX4m4ltL-vSbGlbzbQtrql503Cg",
          user: { username: "admin", roleName: "user-admin" },
        },
      };
    } else {
      return { success: false, data: "Invalid username or password" };
    }
  },
  /**
   * Takes an array of entities and returns an array of objects with `value` and
   * `label` properties, suitable for use in a dropdown. The objects represent
   * the entities in the campaign.
   *
   * @param {Array<Object>} entities - The array of entities.
   * @returns {Array<{value: string, label: string}>} - Array of objects with `value` and `label` properties.
   */
  generateEntitiesOptions: (entities) => {
    let options = entities.map((entity) => {
      return {
        value: entity.entityId,
        label: entity.entityName,
      };
    });

    return options;
  },
  /**
   * Takes an entity object and returns an array of objects with `value` and
   * `label` properties, suitable for use in a dropdown. The objects represent
   * the transaction contexts associated with the given entity.
   *
   * @param {Object} entity - The entity object with `transactionContexts` property.
   * @returns {Array<Object>} - Array of objects with `value` and `label` properties.
   */
  generateContextOptions: (entity) => {
    if (!entity) {
      return [];
    }
    let options = entity.transactionContexts.map((context) => {
      return {
        value: context.contextId,
        label: context.contextName,
      };
    });

    return options;
  },
  /** Generate the options  from the variables array.
   *
   * @param {Array<Object>} variables - Array of variable objects with `variableId` and `name` properties.
   * @returns {Array<Object>} - Array of objects with `value` and `label` properties.
   */
  generateVariableDropdownOptions: (variables) => {
    return variables.map((item) => {
      return {
        label: item.name,
        value: item.variableId,
      };
    });
  },

  /**
   * It will return the function to handle the input change which we are using on many component
   * on which we have to update the state of the input field with propert object formating
   */
  handleInputChangeFactoryFn: (setData) => {
    return (e, status) => {
      let { name, value } = e.target;

      setData((prev) => ({
        ...prev, // Spread the previous state
        [name]: {
          value,
          isValid: status.success,
          error: status.success ? null : status.error,
          isShowError: !status.success,
        },
      }));
    };
  },

  /**
   * It will return the function to handle the dropdown change which we are using on many component
   * on which we have to update the state of the input field with propert object formating
   * @param {function(prevData) => void} setData - The setstate function to set the data
   * @param {function(name: string, value: string) => void} [callBackFn] - The callback function to be called after the state is updated
   */
  handleDropdownChangeFactoryFn: (setData, callBackFn = null) => {
    return (name, value) => {
      // Update state
      setData((prev) => {
        const updatedObj = {
          ...prev,
          [name]: {
            value,
            isValid:
              value === "" || value === null || value === undefined
                ? false
                : true,
            error:
              value === "" || value === null || value === undefined
                ? `${name} field is required`
                : null,
            isShowError: false,
          },
        };

        return updatedObj;
      });

      // If callback function is provided, call it
      if (callBackFn) {
        callBackFn(name, value);
      }
    };
  },
  /**
   * Parses a datetime string and returns date, time, or both in the specified format.
   * @param {string} dateTimeStr - The ISO datetime string to be parsed.
   * @param {"time" | "date" | "both"} returnType - Optional. Specifies what to return: 'date', 'time', or 'both'. Default is 'both'.
   * @param {object} format - The date format object that contain the date & time format.
   * @param {string}  [format.date = "YYYY-MM-DD"] The date format. It is optional.
   * @param {string} [format.time = "HH:mm:ss"] The time format. It is optional.
   * @returns {string | object} The formatted date, time, or both.
   */
  parseDateTime: (
    dateTimeStr,
    returnType = "both",
    format = {
      date: "YYYY-MM-DD",
      time: "HH:mm:ss",
    }
  ) => {
    const dateTime = dayjs(dateTimeStr);

    switch (returnType) {
      case "date":
        return dateTime.format(format.date); // Format only date part
      case "time":
        return dateTime.format(format.time); // Format only time part
      case "both":
      default:
        return {
          date: dateTime.format(format.date),
          time: dateTime.format(format.time),
        };
    }
  },
  /**
   * Validates and prepares the campaign object to be saved in the database.
   * @param {object} campaign - The campaign object to be validated and prepared.
   * @param {"creating" | "updating"} [status="creating"] - The status object to be updated with the validation result.
   * @returns {object} The prepared campaign object or null if the campaign is not valid.
   */
  validateCampaign: (campaign, status = "creating") => {
    const errors = {};

    // Basic Fields
    if (!campaign.name || campaign.name.trim() === "") {
      errors.name = "Rule set name is required";
    }

    if (!campaign.description || campaign.description.trim() === "") {
      errors.description = "Rule set description is required";
    }

    // Validate Dates
    if (!campaign.startDateTime) {
      errors.startDateTime = "Start date is required";
    }

    if (!campaign.endDateTime) {
      errors.endDateTime = "End date is required";
    }

    // if (
    //   campaign.startDateTime &&
    //   campaign.endDateTime &&
    //   new Date(campaign.startDateTime) > new Date(campaign.endDateTime)
    // ) {
    //   errors.dateOrder = "End date must be after the start date";
    // }

    // // Validate Collection Mappings
    // if (
    //   !Array.isArray(campaign.collectionMappings) ||
    //   campaign.collectionMappings.length < 1
    // ) {
    //   errors.collectionMappings = "At least one collection mapping is required";
    // }

    // // Validate Persistent Variable Definitions
    // if (
    //   !Array.isArray(campaign.persistentVariableDefinitions) ||
    //   campaign.persistentVariableDefinitions.length < 1
    // ) {
    //   errors.persistentVariableDefinitions =
    //     "At least one persistent variable definition is required";
    // }

    // // Validate Local Variable Definitions
    // if (
    //   !Array.isArray(campaign.localVariableDefinitions) ||
    //   campaign.localVariableDefinitions.length < 1
    // ) {
    //   errors.localVariableDefinitions =
    //     "At least one local variable definition is required";
    // }

    // Validate Entities
    // if (!Array.isArray(campaign.entities) || campaign.entities.length < 1) {
    //   errors.entities = "At least one entity is required";

    // }

    let success = Object.keys(errors).length === 0 ? true : false;
    let data = success ? campaign : errors;

    return Helpers.returnObj(success, data);
  },
  /**
   * Prepares the campaign object for further processing or saving.
   * Currently, it returns the campaign object as-is.
   *
   * @param {object} campaign - The campaign object to be prepared.
   * @param {"creating" | "updating"} status - The status of the campaign.
   * @returns {object} - The prepared campaign object.
   */
  prepareCampaign: (campaign, status) => {
    let updatedCampaign = {
      ...campaign,
    };

    if (status === "creating") {
      updatedCampaign.rulesetId = Helpers.idGenerator(
        updatedCampaign.name,
        "upper-snake",
        {
          maxLength: 50,
        }
      );
      updatedCampaign.version = 1;
      updatedCampaign.schemaVersion = "7.0.0";
      updatedCampaign.status = "ACTIVE"; //  "DRAFT", "ACTIVE", "PAUSED", "COMPLETED"
      /* Helpers.generateVersion({
        autoInitialize: true,
        incrementType: "patch",
      }) */
    }
    if (status === "updating") {
      // Campaign version incrementation implemented by the backend
      //updatedCampaign.campaignVersion++;
      /* Helpers.generateVersion({
        currentVersion: campaign.version,
        incrementType: "patch",
      }) */
    }
    // updatedCampaign.lastModifiedDateTime =
    //   new Date().toISOString()?.split(".")[0] + "Z"; // example: "2021-09-01T12:00:00.000Z"
    // updatedCampaign.startDateTime =
    //   updatedCampaign.startDateTime?.split(".")[0] + "Z";
    // updatedCampaign.endDateTime =
    //   updatedCampaign.endDateTime?.split(".")[0] + "Z";

    delete updatedCampaign.rulesetVersion;
    // delete updatedCampaign.evaluationRules[0].webhookCalls;
    // Exclude 'webhookCalls' from each rule in evaluationRules
    if (
      updatedCampaign.evaluationRules &&
      updatedCampaign.evaluationRules.length > 0
    ) {
      updatedCampaign.evaluationRules = updatedCampaign.evaluationRules.map(
        (rule) => {
          const { webhookCalls, ...rest } = rule; // Destructure to exclude webhookCalls
          return rule; // Return the new object without webhookCalls
        }
      );
    }

    updatedCampaign.lastModifiedDateTime = Helpers.convertToISO8601();
    updatedCampaign.startDateTime = Helpers.convertToISO8601(
      updatedCampaign.startDateTime
    );
    updatedCampaign.endDateTime = Helpers.convertToISO8601(
      updatedCampaign.endDateTime
    );

    return updatedCampaign;
  },
  /**
   * Create the rows util factory function to be use inside the component for the management of the rows array
   * @param {[]} itemsArray
   * @param {function(Array): void} setItems
   * @param {string} label
   * @returns
   */
  createRowsUtil: function (itemsArray, setItems, label, Alert) {
    /** @type {Helpers} */
    const HelperClass = this;

    const labelId = HelperClass.nameIdGenerator(label);

    return {
      /** Handle the edit button click on the row */

      handleEditBtnClickById: (id) => {
        const updatedData = itemsArray.map((item) => {
          if (item.id === id) {
            return { ...item, mode: "editing" };
          }
          return item;
        });

        setItems(updatedData);
      },
      handleDeleteItemById: (id) => {
        const updatedData = itemsArray.filter((item) => item.id !== id);
        setItems(updatedData);
      },
      handleEditBtnClick: (itemIndex) => {
        const updatedData = itemsArray.map((item, index) => {
          if (index === itemIndex) {
            return {
              ...item,
              mode: "editing",
            };
          }
          return item;
        });

        setItems(() => updatedData);
      },
      handleDeleteBtnClick: (itemIndex) => {
        const updatedData = itemsArray.filter(
          (_, index) => index !== itemIndex
        );
        setItems(() => updatedData);
      },

      handleAddNewItem: () => {
        const defId = `new_${labelId}`;
        // check if new item already exist
        let alreadyItem = itemsArray.find((item) => item.id === defId);

        if (alreadyItem) {
          return Alert(`Cannot add multiple new ${label}s`, "error");
        }
        // Add new item to the array
        const newItem = {
          id: defId,
          mode: "editing", // ["viewing", "editing"]
          columns: [{ content: `Create ${label}`, gridSize: 6 }],
          isEditable: true,
          isDeletable: true,
          isAllFieldsValid: false,
          validations: {},
          data: {
            type: "",
            parameter: "",
          },
        };

        setItems((prev) => [...prev, newItem]);
      },

      /** Handler to add the new item
       * @param {Object} basePerms? - The base permissions object
       */
      handleAddNewItemWebhook: (basePerms = { type: "", parameter: "" }) => {
        const defId = `new_${labelId}`;

        // check if new item already exist
        let alreadyItem = itemsArray.find((item) => item.id === defId);

        if (alreadyItem) {
          return Alert(`Cannot add multiple new ${label}s`, "error");
        }

        // Add new item to the array
        const newItem = {
          id: defId,
          mode: "editing", // ["viewing", "editing"]
          columns: [{ content: `Create ${label}`, gridSize: 6 }],
          isEditable: true,
          isDeletable: true,
          isAllFieldsValid: false,
          validations: {},
          data: {
            ...basePerms,
          },
        };

        setItems((prev) => [...prev, newItem]);
      },

      /**
       * If it's new item
       * @param {Object} item
       * @returns {Boolean}
       */
      isNewItem: (item) => {
        return item.id.toString().includes("newitem_");
      },

      /** Handler to delete the item */
      handleDeleteItem: (itemIndex) => {
        const updatedData = itemsArray.filter(
          (_, index) => index !== itemIndex
        );
        setItems(() => updatedData);
      },

      /** Handler to save the the item */
      handleSaveFunc: (id, inputs) => {
        const updatedData = itemsArray.map((item) => {
          return item.id == id
            ? {
              ...inputs,
              variableId: Helpers.nameIdGenerator(inputs.name || "temp"),
            }
            : { ...HelperClass.returnValues(item.inputs) };
        });

        // const updatedData = itemsArray.map((item) => {
        //   return item.id === id
        //     ? { ...inputs, variableId: Helpers.nameIdGenerator(inputs.name) }
        //     : { ...HelperClass.returnValues(item.inputs) };
        // });

        setItems(() => updatedData);

        // Set the alert here if successful
        Alert(`${label.toUpperCase()} added successfully`, "success");
      },

      /** Handler to cancel the the item */
      handleCancelFunc: (id) => {
        setItems((prev) => {
          const updatedData = prev.map((item) => {
            return item.id === id ? { ...item, mode: "viewing" } : item;
          });

          return updatedData;
        });
      },

      isValidNumber: (value) => {
        return !isNaN(value) && value.trim() !== "" && isFinite(value);
      },
    };
  },
  /**
   * Create the columns util factory function to be use inside the component for the management of the columns array
   * @param {createColumnsUtil_Param_columns} columns
   */
  createColumnsUtil: function (columns) {
    return {
      /**
       * Columns of the section header
       */
      columnsOfSectionHeader: columns.map((column) => ({
        label: column.label,
        gridSize: column.gridSize,
      })),

      /**
       * Generate the item content columns
       */
      generateItemContentColumns: (item) => {
        item = item || {};

        return columns.map((column) => {
          let content = "";

          switch (column.content.type) {
            // If the type is the array length then get the length of the array
            case "arrayLength":
              content = item[column.content.key]
                ? item[column.content.key].length
                : column.content.def;
              break;
            default:
              // If column.content.key is function then call it
              content =
                typeof column.content.key === "function"
                  ? column.content.key(item)
                  : item[column.content.key] || column.content.def || "";

              break;
          }

          return {
            content: content,
            gridSize: column.gridSize,
          };
        });
      },
    };
  },
  /**
   * Version Generator
   *
   * Creates and manages version numbers with simple, predictable incrementation
   *
   * @param {Object} options - Configuration options for version generation
   * @param {string} [options.currentVersion] - Current version to increment
   * @param {'major'| 'minor'| 'patch'} [options.incrementType='patch'] - Type of version increment ('major', 'minor', 'patch')
   * @param {boolean} [options.autoInitialize=true] - Automatically initialize version if not provided
   * @returns {string} - Generated version string
   */
  generateVersion: (options = {}) => {
    const {
      currentVersion = null,
      incrementType = "patch",
      autoInitialize = true,
    } = options;

    // Regular expression for validating version numbers
    const versionRegex = /^(\d+)(?:\.(\d+))?(?:\.(\d+))?$/;

    /**
     * Validates and parses a version string
     * @param {string} version - Version string to parse
     * @returns {Object} Parsed version components
     */
    const parseVersion = (version) => {
      // If no version is provided and autoInitialize is true, start at 1.0.0
      if (!version && autoInitialize) {
        return { major: 1, minor: 0, patch: 0 };
      }

      // Validate version format
      const match = version ? version.match(versionRegex) : null;

      if (!match) {
        throw new Error(`Invalid version format: ${version}`);
      }

      return {
        major: parseInt(match[1] || 1),
        minor: parseInt(match[2] || 0),
        patch: parseInt(match[3] || 0),
      };
    };

    /**
     * Increments version based on increment type
     * @param {Object} version - Parsed version object
     * @param {string} type - Increment type
     * @returns {Object} Updated version object
     */
    const incrementVersion = (version, type) => {
      const newVersion = { ...version };

      switch (type.toLowerCase()) {
        case "major":
          newVersion.major += 1;
          newVersion.minor = 0;
          newVersion.patch = 0;
          break;
        case "minor":
          newVersion.minor += 1;
          newVersion.patch = 0;
          break;
        case "patch":
          newVersion.patch += 1;
          break;
        default:
          throw new Error(`Invalid increment type: ${type}`);
      }

      return newVersion;
    };

    /**
     * Formats version object into string
     * @param {Object} version - Version object
     * @returns {string} Formatted version string
     */
    const formatVersion = (version) => {
      return `${version.major}.${version.minor}.${version.patch}`;
    };

    try {
      // Parse the current version (or initialize if not provided)
      let version = parseVersion(currentVersion);

      // Increment the version if current version was provided
      if (currentVersion) {
        version = incrementVersion(version, incrementType);
      }

      return formatVersion(version);
    } catch (error) {
      console.error(`Error generating version: ${error.message}`);
      return "1.0.0"; // Fallback to default version
    }
  },
  /**
   * Converts a date string to ISO 8601 format with time
   * @param {string} dateString - The date string to convert
   * @returns {string} The ISO 8601 formatted date string
   */
  toISO8601WithTime: (dateString) => {
    const date = new Date(dateString);
    return date.toISOString(); // Converts to 'YYYY-MM-DDTHH:mm:ss.sssZ'
  },
  /**
   * This function checks if a variable is used in any entity. If it is used then return true else false
   * @param {string} variableId - The variable ID to check
   * @param {Array<Object>} entities - The array of entities to check
   * @returns {boolean} - True if the variable is used in any entity, else false
   */
  isVariableUsed_old: (variableId, entities) => {
    // Check if the variable is used in any entity
    return entities.some((entity) => {
      return entity.transactionContexts.some((context) => {
        return context.rules.some((rule) => {
          return rule.variableOperations.some((condition) => {
            return condition.variableId === variableId;
          });
        });
      });
    });
  },
  /**
   * This function checks if a variable is used in any entity. If it is used then return true else false
   * @param {string} variableId - The variable ID to check
   * @param {Array<Object>} rules - The array of rules to check
   * @returns {boolean} - True if the variable is used in any entity, else false
   */
  isVariableUsed: (variableId, rules) => {
    return rules.some(
      (rule) =>
        // Ensure the rule has an array of variableAssignments
        rule.variableAssignments &&
        rule.variableAssignments.some(
          (assignment) =>
            // Check that the assignment has a variableId, and it matches
            assignment.variableId === variableId
        )
    );
  },
  /**
   *  This function generates the transaction context properties for the dropdown based on the entity and context selected
   * @param {object} entities The entities array
   * @param {string} entityId The entity id
   * @param {string} contextId The context id
   * @param {boolean} encloseValInCurlyBrack Whether to enclose the value in curly brackets
   * @returns {{ value:string,label:string }[]} The properties options
   */
  // generateTransContextOptions: (
  //   entities,
  //   entityId,
  //   contextId,
  //   encloseValInCurlyBrack = false
  // ) => {
  //   let entity = entities.find((entity) => entity.entityId === entityId);
  //   let context = entity.transactionContexts.find(
  //     (context) => context.contextId === contextId
  //   );
  //   let propertiesOptions = context.properties.map((property) => {
  //     return {
  //       value: encloseValInCurlyBrack ? `{${property.name}}` : property.name,
  //       label: `${property.name}${
  //         property.description ? ` - ${property.description}` : ""
  //       }`,
  //     };
  //   });

  //   return propertiesOptions;
  // },
  /**
   * Function to generate the category dropdown category options
   * @param {Array.<{id: "localVariables" | "persistentVariables" | "transactionVariables" | "transactionContextProperties", label?: string, data: [] | {}}>} categories
   * @param {object} campaign - The campaign object
   * @param {boolean} [entities=false] - Supply the entitties array if the category id we want is transactionContextProperties
   * @param {boolean} [disableCustomValue=false] - Whether to disable the custom user input value
   * @returns
   */
  generateTransContextOptions: (
    entities,
    entityId,
    contextId,
    encloseValInCurlyBrack = false
  ) => {
    // Added null check for entity
    let entity = entities.find((entity) => entity.entityId === entityId);
    if (!entity) {
      return [];
    }

    // Added null check for context
    let context = entity.transactionContexts.find(
      (context) => context.contextId === contextId
    );
    if (!context) {
      return [];
    }
    let propertiesOptions = context.properties.map((property) => {
      return {
        value: encloseValInCurlyBrack
          ? // ? `{transactionContext.${property.name}}`
          `{${property.propertyId}}`
          : property.propertyId,
        label: `${property.name}`,
        type: property.type,
        mutable: property.mutable,
        ...(property.constraints && { constraints: property.constraints }),
        description: property.description,
      };
    });

    return propertiesOptions;
  },
  // generateCategoryDropDownAllOptions(
  //   categories,
  //   campaign,
  //   entities = false,
  //   disableCustomValue = false
  // ) {
  //   const categoryOpyions = [];

  //   // Loop through the categories and generate the options
  //   categories.forEach((category) => {
  //     switch (category.id) {
  //       case "localVariables":
  //         categoryOpyions.push({
  //           value: category.id,
  //           label: category.label || "Local Variables",
  //         });
  //         break;

  //       default:
  //         break;
  //     }
  //   });
  // },
  /**
   * Converts a datetime string to the ISO 8601 format ("YYYY-MM-DDTHH:mm:ssZ").
   * If the input is already in the correct format, it returns the same string.
   * If no datetime string is provided, it returns the current date in ISO 8601 format.
   *
   * @param {string} [dateStr] - The datetime string to be converted. This can be in
   *                             various ISO 8601 formats, including milliseconds (e.g., "2024-11-21T19:00:00.000Z").
   *                             If not provided, the current date is used.
   * @returns {string} The datetime string in the format "YYYY-MM-DDTHH:mm:ssZ".
   * @throws {Error} If the provided date string is not a valid date.
   *
   * @example
   * // Returns "2024-11-21T19:00:00Z"
   * convertToISO8601("2024-11-21T19:00:00.000Z");
   *
   * @example
   * // Returns the current date in ISO 8601 format, e.g., "2024-11-14T13:45:00Z"
   * convertToISO8601();
   */
  generateCategoryDropDownAllOptions(
    categories,
    campaign,
    entities = false,
    disableCustomValue = false
  ) {
    // Initialize array to store category options
    const categoryOptions = [];

    // Added null check for categories
    if (!categories || !Array.isArray(categories)) {
      return categoryOptions;
    }

    // Loop through the categories and generate the options
    categories.forEach((category) => {
      switch (category.id) {
        case "localVariables":
          // Push local variables option with provided or default label
          categoryOptions.push({
            value: category.id,
            label: category.label || "Local Variables",
          });
          break;

        // Add default case to ignore unknown categories
        default:
          break;
      }
    });

    // Return the generated options
    return categoryOptions;
  },
  /**
   * Converts a given date string to ISO 8601 format (`YYYY-MM-DDTHH:mm:ssZ`).
   * If the input date string is already in ISO 8601 format, it is returned as-is.
   * If no date string is provided, the current date and time in UTC is returned in ISO 8601 format.
   *
   * @param {string} [dateStr] - Optional date string to convert. If omitted, the current date is used.
   *                             Acceptable date formats include any format compatible with JavaScript's Date constructor.
   *                             Example input: "2024-11-14" or "11/14/2024".
   *
   * @returns {string} The date in ISO 8601 format (e.g., "2024-11-14T13:07:12Z").
   *
   * @throws {Error} Throws an error if the input date string is invalid.
   *
   * @example
   * // Returns the current date in ISO 8601 format
   * convertToISO8601();
   *
   * @example
   * // Returns "2024-11-14T13:07:12Z" if `dateStr` is "2024-11-14T13:07:12Z"
   * convertToISO8601("2024-11-14T13:07:12Z");
   *
   * @example
   * // Converts "2024-11-14" to "2024-11-14T00:00:00Z"
   * convertToISO8601("2024-11-14");
   */
  convertToISO8601: (dateStr) => {
    // If dateStr is undefined, use the current date
    const date = dateStr ? new Date(dateStr) : new Date();

    // Check if the date is already in the correct format
    const iso8601Regex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/;
    if (dateStr && iso8601Regex.test(dateStr)) {
      return dateStr; // Return as-is if already in the correct format
    }

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      throw new Error("Invalid date format"); // Handle invalid dates
    }

    // Format the date to "YYYY-MM-DDTHH:mm:ssZ"
    const year = date.getUTCFullYear();
    const month = String(date.getUTCMonth() + 1).padStart(2, "0");
    const day = String(date.getUTCDate()).padStart(2, "0");
    const hours = String(date.getUTCHours()).padStart(2, "0");
    const minutes = String(date.getUTCMinutes()).padStart(2, "0");
    const seconds = String(date.getUTCSeconds()).padStart(2, "0");

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}Z`;
  },
  /**
   * Parses a UTC timestamp and returns detailed date and time information.
   *
   * @param {string} utcString - The UTC timestamp in ISO 8601 format (e.g., "2024-11-14T13:07:12Z").
   * @returns {{
   *  day: number, // Day of the month (1-31), e.g., 14
   *  dayNumeric: number, // Numeric day without leading zero, e.g., 14
   *  month: number, // Month of the year (1-12), e.g., 11
   *  monthNumeric: number, // Numeric month without leading zero, e.g., 11
   *  monthName: string, // Full name of the month, e.g., "November"
   *  year: number, // Full year, e.g., 2024
   *  hours: number, // Hour of the day in 24-hour format (0-23), e.g., 13
   *  minutes: number, // Minute of the hour (0-59), e.g., 7
   *  seconds: number, // Second of the minute (0-59), e.g., 12
   *  weekdayName: string, // Full name of the weekday, e.g., "Thursday"
   *  isoString: string, // Original UTC string, e.g., "2024-11-14T13:07:12Z"
   *  formattedDate: string, // Date in YYYY-MM-DD format, e.g., "2024-11-14"
   *  formattedTime: string // Time in HH:mm:ss UTC format, e.g., "13:07:12 UTC"
   * }} object An object containing detailed date and time information.
   */
  parseUTCDate: (utcString) => {
    const date = new Date(utcString);

    const day = date.getUTCDate(); // Day of the month (1-31)
    const dayNumeric = day; // Numeric day without leading zero
    const month = date.getUTCMonth() + 1; // Month (1-12)
    const monthNumeric = month; // Numeric month without leading zero
    const year = date.getUTCFullYear(); // Full year (e.g., 2024)
    const hours = date.getUTCHours(); // Hours (0-23)
    const minutes = date.getUTCMinutes(); // Minutes (0-59)
    const seconds = date.getUTCSeconds(); // Seconds (0-59)

    const monthName = date.toLocaleString("en-US", {
      month: "long",
      timeZone: "UTC",
    }); // Full name of the month (e.g., "November")

    const weekdayName = date.toLocaleString("en-US", {
      weekday: "long",
      timeZone: "UTC",
    }); // Full name of the weekday (e.g., "Thursday")

    return {
      day, // Day of the month (1-31), e.g., 14
      dayNumeric, // Numeric day without leading zero, e.g., 14
      month, // Month of the year (1-12), e.g., 11
      monthNumeric, // Numeric month without leading zero, e.g., 11
      monthName, // Full name of the month, e.g., "November"
      year, // Full year, e.g., 2024
      hours, // Hour of the day in 24-hour format (0-23), e.g., 13
      minutes, // Minute of the hour (0-59), e.g., 7
      seconds, // Second of the minute (0-59), e.g., 12
      weekdayName, // Full name of the weekday, e.g., "Thursday"
      isoString: utcString, // Original UTC string, e.g., "2024-11-14T13:07:12Z"
      formattedDate: `${year}-${String(month).padStart(2, "0")}-${String(
        day
      ).padStart(2, "0")}`, // e.g., "2024-11-14"
      formattedTime: `${String(hours).padStart(2, "0")}:${String(
        minutes
      ).padStart(2, "0")}:${String(seconds).padStart(2, "0")} UTC`, // e.g., "13:07:12 UTC"
    };
  },
  /**
   * Prepares analytics data by combining fields with their corresponding values and timestamps.
   *
   * @param {Array} fields - An array of field objects, each containing at least a 'name' property and optionally a 'prefix'.
   * @param {Array} data - An array of data entries, each containing a 'timestamp' and a 'values' array.
   * @returns {Array} - An array of objects, each containing 'values' (an array of field-value pairs) and 'timestamp'.
   */
  prepareAnalyticsFieldsData: (fields, data) => {
    const result = fields.map((field, index) => {
      // Sum all values for the current field across all data entries
      const totalValue = data.reduce((sum, entry) => {
        return sum + (entry.values[index] || 0);
      }, 0);

      return {
        ...field,
        value: totalValue, // Store the summed value for this field
      };
    });

    return [{ values: result }]; // Return the result in the expected format
  },

  /**
   * Organizes and structures analytics data fields into grouped arrays based on fields.
   *
   * This function takes an array of fields and an array of data points, where each data point contains
   * multiple values associated with each field. It organizes the data so that each field's data
   * is grouped together across timestamps.
   *
   * @param {Array} fields - Array of field objects that describe each metric.
   * @param {Array<{name: string, prefix?: string}>} fields - Array of field objects that describe each metric.
   * @param {Object[]} fields - Array of field objects that describe each metric.
   * @param {Object} fields[].prefix - The prefix for the metric (e.g., "$" for currency).
   * @param {Array} data - Array of data points, each with a timestamp and values corresponding to each field.
   * @param {object[]} data - Array of data points, each with a timestamp and values corresponding to each field.
   * @param {string} data[].timestamp - The timestamp for the data point in ISO 8601 format (e.g., "2024-11-01T13:07:12Z").
   * @param {Array<number>} data[].values - Array of numeric values, each corresponding to a field in `fields`.
   *
   * @returns {Array<Array<Object>>} - An array where each sub-array contains objects representing
   *                                   the values and metadata for a specific field across timestamps.
   *                                   Each object includes the field's name, prefix, timestamp, and value.
   *
   * @example
   * const fields = [
   *   { name: "Airtime Revenue", prefix: "$" },
   *   { name: "Transaction Count" }
   * ];
   * const data = [
   *   { timestamp: "2024-11-01T13:07:12Z", values: [1000, 500] },
   *   { timestamp: "2024-11-02T13:07:12Z", values: [1200, 550] }
   * ];
   *
   * const result = analyticsGraphFieldsOrganizer(fields, data);
   * // result:
   * // [
   * //   [
   * //     { name: "Airtime Revenue", prefix: "$", timestamp: "2024-11-01T13:07:12Z", value: 1000 },
   * //     { name: "Airtime Revenue", prefix: "$", timestamp: "2024-11-02T13:07:12Z", value: 1200 }
   * //   ],
   * //   [
   * //     { name: "Transaction Count", prefix: "", timestamp: "2024-11-01T13:07:12Z", value: 500 },
   * //     { name: "Transaction Count", prefix: "", timestamp: "2024-11-02T13:07:12Z", value: 550 }
   * //   ]
   * // ]
   */
  analyticsGraphFieldsOrganizer: (fields, data) => {
    let organizedData = data.map((item) => {
      return item.values.map((value, index) => {
        return {
          name: fields[index]?.name,
          prefix: fields[index].prefix,
          timestamp: item.timestamp,
          value: value,
        };
      });
    });

    let outerArray = [];
    organizedData.forEach((item) => {
      item.forEach((itm, indx) => {
        outerArray[indx] = outerArray[indx] || [];
        outerArray[indx].push(itm);
      });
    });

    return outerArray;
  },
  /**
   *
   * @param {{category: string, options: {value:string, label: string}[]}[]} options
   * @returns
   */
  wipeTheBracketsFromTheOptionsValue: (options) => {
    const newOptions = (iOptions) => {
      return iOptions.map((option) => {
        return {
          value: option.value.replace("{", "").replace("}", ""),
          label: option.label,
          type: option.type,
        };
      });
    };

    return options.map((option) => ({
      ...option,
      options: newOptions(option.options),
    }));
  },
  /**
   * Converts a given text to the specified case.
   *
   * @param {string} text - The text to convert.
   * @param {"pascal" | "upper" | "lower" | "capitalize" | "title"} caseType - The case type to convert to. Possible values:
   *  - "pascal" (e.g., "PascalCase")
   *  - "upper" (e.g., "UPPERCASE")
   *  - "lower" (e.g., "lowercase")
   *  - "capitalize" (e.g., "Capitalize First Letter")
   *  - "title" (e.g., "Title Case")
   * @returns {string} The text converted to the specified case.
   */
  convertCase: (text, caseType) => {
    if (!text || typeof text !== "string") {
      throw new Error("Invalid text input. Please provide a valid string.");
    }

    switch (caseType.toLowerCase()) {
      case "pascal":
        return text
          .split(/[\s_-]+/) // Split by spaces, underscores, or dashes
          .map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          .join("");

      case "upper":
        return text.toUpperCase();

      case "lower":
        return text.toLowerCase();

      case "capitalize":
        return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();

      case "title":
        return text
          .split(/[\s_-]+/) // Split by spaces, underscores, or dashes
          .map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
          )
          .join(" ");

      default:
        return "Error While Case Conversion"; // Return the original text if case type is not recognized
    }
  },

  /**
   * Aggregates data based on the specified period (daily, weekly, monthly, yearly).
   *
   * @param {Array} data - Array of data objects with name, timestamp, and value.
   * @param {string} period - The period to aggregate by ("daily", "weekly", "monthly", "yearly").
   * @returns {Array} Aggregated data for the specified period.
   */
  aggregateData(data, period = "daily") {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }

    // Sort data by timestamp
    const sortedData = [...data].sort(
      (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
    );

    // Helper function to format date
    const formatDate = (date) => {
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      });
    };

    // Helper function to get period boundaries
    const getPeriodBoundaries = (date, periodType) => {
      const newDate = new Date(date);

      switch (periodType) {
        case "weekly": {
          const dayOfWeek = newDate.getDay();
          const startOfWeek = new Date(newDate);
          startOfWeek.setDate(newDate.getDate() - dayOfWeek);
          const endOfWeek = new Date(newDate);
          endOfWeek.setDate(newDate.getDate() + (6 - dayOfWeek));
          return { start: startOfWeek, end: endOfWeek };
        }
        case "monthly": {
          const startOfMonth = new Date(
            newDate.getFullYear(),
            newDate.getMonth(),
            1
          );
          const endOfMonth = new Date(
            newDate.getFullYear(),
            newDate.getMonth() + 1,
            0
          );
          return { start: startOfMonth, end: endOfMonth };
        }
        case "yearly": {
          const startOfYear = new Date(newDate.getFullYear(), 0, 1);
          const endOfYear = new Date(newDate.getFullYear(), 11, 31);
          return { start: startOfYear, end: endOfYear };
        }
        default: // daily
          return { start: newDate, end: newDate };
      }
    };

    // Group data by periods
    const groupedData = {};

    sortedData.forEach((item) => {
      const itemDate = new Date(item.timestamp);
      const periodBoundaries = getPeriodBoundaries(itemDate, period);

      // Create a unique key for the period
      const periodKey = `${periodBoundaries.start.toISOString().split("T")[0]
        }_${periodBoundaries.end.toISOString().split("T")[0]}`;

      if (!groupedData[periodKey]) {
        groupedData[periodKey] = {
          items: [],
          boundaries: periodBoundaries,
        };
      }

      groupedData[periodKey].items.push(item);
    });

    // Transform grouped data into result format
    const aggregatedResults = Object.entries(groupedData).map(
      ([key, group], index) => {
        // Sum up values for the period
        const periodTotal = group.items.reduce(
          (sum, item) => sum + item.value,
          0
        );

        return {
          x_axis:
            period === "daily"
              ? formatDate(group.boundaries.start)
              : `${formatDate(group.boundaries.start)} to ${formatDate(
                group.boundaries.end
              )}`,
          name: data[0].name,
          prefix: data[0].prefix,
          value: periodTotal,
          [`${period}Count`]: index + 1,
          valueWithPrefix: data[0].prefix
            ? `${data[0].prefix}${periodTotal.toLocaleString()}`
            : periodTotal.toLocaleString(),
        };
      }
    );

    return aggregatedResults;
  },

  /**
   * Download the csv file by the blob object
   * @param {Blob} blob The blob object
   * @param {string} fileName
   */
  downloadCsvFileByBlob: (blob, fileName) => {
    // Create a URL for the Blob
    const url = window.URL.createObjectURL(blob);

    // Create an anchor element
    const a = document.createElement("a");
    a.href = url; // Set the href to the Blob URL
    a.download = fileName; // Set the desired file name for the download

    // Append the anchor to the DOM
    document.body.appendChild(a);

    // Trigger the download by simulating a click
    a.click();

    // Clean up the anchor and Blob URL
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  },

  /**
   * Function will try to parse to the number and if it's valid nubmer then return the number else return the string
   * @param {string | number} value
   * @returns
   */
  getNumberOrString: (value) => {
    value = value.toString();

    // If it's empty then return
    if (!value) return value;

    // Convert to the number
    const inum = Number(value);

    // If it's nan then return the value as string
    if (isNaN(inum)) return value;

    // If the number is not equal to the value then return the value as string. The case is if the value is 000123 then number function will converts it to 123, so in that case we will treat it as string
    if (inum.toString() !== value) return value;

    // If it's a number then return the number
    if (!isNaN(inum)) return inum;

    // We are here it means it's string

    // Regex to check if the value contains only the digits in the string
    /** Regex to check if the value is a number
     */
    const regex = /^(\d+(\.\d+)?|\.\d+)$/;

    // If it's not number then return as string
    if (!regex.test(value)) return value;

    // We are here it means it's number so return the number
    const number = Number(value);

    // If the number is not valid for some reason then return the value as string
    if (isNaN(number)) return value;

    return Number(value);
  },

  /**
   * Generate random number
   * @param {number} digits Number of digits we need in the random number
   * @returns
   */
  getRandomNumberByDigits(digits = 4) {
    if (digits <= 0) {
      throw new Error("Number of digits must be greater than 0");
    }

    const min = Math.pow(10, digits - 1); // Smallest number with the specified digits
    const max = Math.pow(10, digits) - 1; // Largest number with the specified digits

    return Math.floor(Math.random() * (max - min + 1)) + min;
  },

  /**
   * Extracts property values from an object into an array.
   * @param {Object} obj - The object to extract values from
   * @returns {Array} Array of property values
   */
  ObjToArray(obj) {
    if (!obj || typeof obj !== "object") {
      return [];
    }
    return Object.values(obj);
  },
  /**
   * Aggregates condition types data into a structured format to match the required format
   * @param {Object} data - The condition types data
   * @param {Array[]} data.variableAssignments - List of variable operations
   * @param {Array} data.conditionTypes - List of condition objects
   * @param {Array} data.functions - List of functions
   * @param {import("Api/GlobalService").ConfigData} data -
   * @returns {Object} Aggregated condition types with conditionTypes array and variableAssignments array
   */
  aggregateConditionAndActionsVarData(data) {
    if (!data || !data.conditionTypes || !Array.isArray(data.conditionTypes)) {
      return { conditionTypes: [], variableAssignments: [] };
    }

    // Initialize the result object with the required structure
    const result = {
      conditionTypes: [],
      variableAssignments: [],
    };

    // Transform each condition type into the required format
    data.conditionTypes.forEach((conditionType) => {
      const {
        conditionTypeId,
        name,
        operators,
        parameters,
        applicableTypes,
        description,
      } = conditionType;

      // Create the condition type object with sensible defaults for missing properties
      const typeObj = {
        label: name || conditionTypeId,
        value: conditionTypeId,
        description: description || "",
        applicableTypes: applicableTypes || [],
        parameters: parameters || [],
        // Default operator to first in list, or empty string if no operators
        operator: operators && operators.length > 0 ? operators[0] : "",
        // Transform operators to {label, value} format, or empty array if no operators
        operators:
          operators && Array.isArray(operators)
            ? operators.map((op) => ({ label: op, value: op }))
            : [],
      };

      // Add to result array (no early returns or condition checks that might skip entries)
      result.conditionTypes.push(typeObj);
    });

    // Add variable operations if they exist, formatted for Redux
    if (data.variableAssignments && Array.isArray(data.variableAssignments)) {
      result.variableAssignments = data.variableAssignments.map(
        (operation) => ({
          label: operation.name,
          value: operation.assignmentTypeId,
          description: operation.description,
          applicableTypes: operation.applicableTypes,
        })
      );
    }

    if (data.functions && Array.isArray(data.functions)) {
      result.functions = data.functions.map((functionObj) => ({
        label: functionObj.name,
        value: functionObj.functionId,
        description: functionObj.description,
        parameters: functionObj.parameters,
        returnType: functionObj.returnType,
        examples: functionObj.examples,
        ...(functionObj.returnValues && {
          returnValues: functionObj.returnValues,
        }),
      }));
    }
    return result;
  },
  /**
   * Comprehensive validation of variable name against all potential conflicts
   * @param {string} name - The variable name to validate
   * @param {Object[]} transactionVariables - Array of transaction variables
   * @param {Object[]} localVariables - Array of local variables
   * @param {Object[]} persistentVariables - Array of persistent variables
   * @param {Object[]} entities - Array of entities
   * @returns {{isValid: boolean, message: string|null, fieldError: string|null, source: string|null}} - Validation result
   */
  validateVariableName(
    name,
    transactionVariables,
    localVariables,
    persistentVariables,
    entities
  ) {
    // Default success response
    const successResult = {
      isValid: true,
      message: null,
      fieldError: null,
      source: null,
    };

    // Skip validation if name is empty (other validators will catch this)
    if (!name) return successResult;

    // Normalize name for case-insensitive comparison
    const normalizedName = name.toLowerCase();
    const currentVariableId = name;

    //  Check if name exists in transaction variables
    if (transactionVariables && transactionVariables.length > 0) {
      const conflictingTransactionVar = transactionVariables.find(
        (variable) => variable.value.toLowerCase() === normalizedName
      );

      if (conflictingTransactionVar) {
        return {
          isValid: false,
          message: `"${name}" conflicts with reserved transaction variable. Please choose a different name.`,
          fieldError: "This name conflicts with a system transaction variable.",
          source: "transaction",
        };
      }
    }

    // Check if name exists in local variable definitions
    if (localVariables && localVariables.length > 0) {
      const conflictingLocalVar = localVariables.find(
        (variable) => variable.variableId.toLowerCase() === normalizedName
      );

      if (conflictingLocalVar) {
        return {
          isValid: false,
          message: `The variable name "${name}" already exists in local variables. Variable names must be unique.`,
          fieldError: "This name already exists in local variables.",
          source: "local",
        };
      }
    }

    // Check if name exists in persistent variable definitions
    if (persistentVariables && persistentVariables.length > 0) {
      const conflictingPersistentVar = persistentVariables.find(
        (variable) =>
          variable.name.toLowerCase() === normalizedName &&
          variable.variableId !== currentVariableId
      );

      if (conflictingPersistentVar) {
        return {
          isValid: false,
          message: `The variable name "${name}" already exists in persistent variables. Variable names must be unique.`,
          fieldError: "This name already exists in persistent variables.",
          source: "persistent",
        };
      }
    }

    // Check if the variable name conflicts with any entity properties
    if (entities && entities.length > 0) {
      for (const entity of entities) {
        if (
          entity.transactionContexts &&
          entity.transactionContexts.length > 0
        ) {
          for (const context of entity.transactionContexts) {
            if (context.properties && context.properties.length > 0) {
              const conflictingProperty = context.properties.find(
                (property) => property.name.toLowerCase() === normalizedName
              );

              if (conflictingProperty) {
                return {
                  isValid: false,
                  message: `"${name}" conflicts with a property in entity: "${entity.entityName}" Transaction Context: "${context.contextName}". Variable names must not conflict with entity properties.`,
                  fieldError: "This name conflicts with an entity property.",
                  source: "entity",
                };
              }
            }
          }
        }
      }
    }
    // All checks passed, the name is valid
    return successResult;
  },
  /**
   * Check if a value exists in an array
   * @param {Object} inputs - The inputs object
   * @param {string} inputs.value - The value to check
   * @param {Array<string>} inputs.inputsData - The array to check against
   * @returns {boolean} True if the value exists in the array, false otherwise
   */
  isValueExist: ({ value, inputsData }) => {
    const isExist = inputsData.find((item) => item === value);

    if (isExist) {
      return true;
    }
    return false;
  },
  /**
   * Convert a string to camel case
   * @param {string} str - The string to convert
   * @returns {string} The camel case string
   */
  toCamelCase(str) {
    return str
      .trim()
      .toLowerCase()
      .replace(/[^a-zA-Z0-9 ]+/g, "") // Remove all non-alphanumeric characters except spaces
      .split(" ")
      .map((word, index) => {
        if (index === 0) return word;
        return word.charAt(0).toUpperCase() + word.slice(1);
      })
      .join("");
  },

  /**
   * Convert a string to upper snake case
   * @param {string} str - The string to convert
   * @returns {string} The upper snake case string
   */
  toUpperSnakeCase(str) {
    return str
      .trim()
      .replace(/[^a-zA-Z0-9 ]+/g, "") // Remove special characters
      .replace(/\s+/g, "_") // Replace spaces with underscores
      .toUpperCase();
  },
  /**
   * Generate formatted webhook data
   * @param {Object} inputs - The inputs object
   * @param {Object} tab - The tab object
   * @returns {Object} The formatted webhook data
   */
  generateFormattedWebhookData(inputs, tab) {
    let webhookId = Helpers.generateUniqueId({
      name: inputs.name.value,
      existingIds: tab.webhooks.map((item) => item.data.webhookId),
      maxLength: 40,
    });
    return {
      webhookId: tab.id === "newWebhook" ? webhookId : tab.data.webhookId,
      name: inputs.name.value.trim(),
      description: inputs.description.value.trim(),
      url: inputs.url.value.trim(),
      method: inputs.method.value.trim(),
      timeout: parseInt(inputs.timeout.value),
      retryPolicy: {
        maxRetries: parseInt(inputs.maxRetries.value),
        initialDelaySeconds: parseInt(inputs.initialDelaySeconds.value),
        backoffMultiplier: parseFloat(inputs.backoffMultiplier.value),
      },
      securityLevel: inputs.securityLevel.value,
      parameters: inputs.parameters.map(({ data }) => ({
        parameterId: data.parameterId?.value,
        name: data.name?.value.trim(),
        description: data.description?.value.trim(),
        type: data.type?.value,
        required: data.required?.value === "true",
      })),
      headers: Object.fromEntries(
        inputs.headers.map(({ data }) => [
          data.key?.value.trim(),
          data.value?.value.trim(),
        ])
      ),
      bodyTemplate: Object.fromEntries(
        inputs.bodyTemplate.map(({ data }) => [
          data.key?.value?.toString().trim(),
          data.value?.value?.toString().trim(),
        ])
      ),
    };
  },
  ensureDecimal(value) {
    const str = String(value).trim();
    return str.includes(".") ? str : `${str}.0`;
  },
  /**
   * Trims the values of an object
   * @param {Object} obj - The object to trim
   * @param {string} obj.value - The value to trim
   *
   * @returns {Object} The trimmed object
   */
  trimObjectValues(obj) {
    const trimmedObj = {};

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const item = obj[key];
        const trimmedValue =
          typeof item.value === "string" ? item.value.trim() : item.value;

        trimmedObj[key] = {
          ...item,
          value: trimmedValue,
          ...(trimmedValue === ""
            ? {
              ...item,
              isShowError: true,
              isValid: false,
              error: `${key.charAt(0).toUpperCase() + key.slice(1)
                } cannot be empty.`,
            }
            : {}),
        };
      }
    }

    return trimmedObj;
  },
  /**
   * Retrieves the entity name and context name based on entityId and contextId.
   * @param {Array} entities - The array of entity objects.
   * @param {string} entityId - The ID of the entity to find.
   * @param {string} contextId - The ID of the context to find.
   * @returns {Object} An object containing the entityName and contextName, or null if not found.
   */
  getEntityAndContextNames(entities, entityId, contextId) {
    const entity = entities.find((e) => e.entityId === entityId);
    if (!entity) return null;

    const context = entity.transactionContexts.find(
      (c) => c.contextId === contextId
    );
    if (!context) return null;

    return {
      entityName: entity.name,
      contextName: context.name,
    };
  },

  /**
   * Converts standardProperties data format to the transactionVariables format
   * @param {Array<Object>} standardProperties - Array of standard property objects from the API
   * @returns {Array<Object>} Array formatted in transactionVariables structure
   */
  transactionVarsFormatter(standardProperties) {
    if (!standardProperties || !Array.isArray(standardProperties)) {
      return [];
    }

    return standardProperties.map((property) => ({
      label: property.name,
      value: property.propertyId,
      type: property.type,
      description: property.description,
      availablePhases: property.availablePhases || [],
      ...(property.values && { values: property.values }),
      ...(property.itemType && { itemType: property.itemType }),
    }));
  },
  /**
   * Formats function data into a readable string representation
   *
   * @param {Object} functionData - The function data object
   * @param {string} functionData.functionId - The ID of the function
   * @param {Array<string>} functionData.args - The arguments for the function
   * @returns {string} A formatted string in the format "function: functionId(arg1, arg2, ...)"
   */
  formatFunctionData(functionData) {
    if (!functionData || !functionData.functionId) {
      return "";
    }

    const args = functionData.args || [];
    return `Function: ${functionData.functionId}(${args.join(", ")})`;
  },
  /**
   * Checks if a value is a date string
   * @param {any} value - The value to check
   * @returns {boolean} True if the value is a date string, false otherwise
   */
  isDateString(value) {
    const date = Date.parse(value);
    return (
      !isNaN(date) &&
      typeof value === "string" &&
      /\d{4}-\d{2}-\d{2}T/.test(value)
    );
  },

  /**
   * Determines the type and source of a value based on its format and available options
   *
   * @param {any} value - The value to analyze
   * @param {Array} variableOptions - Available variable options for reference matching
   * @param {Array} functions - Available functions for function matching
   * @param {Object} [options] - Additional options for value determination
   * @param {boolean} [options.isMembershipOperator=false] - Whether this is being used in a membership operator context
   * @param {Array} [options.listOptions=[]] - Available list options for membership operators
   * @returns {Object} Object containing recognized type and source information
   */
  determineValueTypeAndSource(value, variableOptions, functions, options = {}) {
    const { isMembershipOperator = false, listOptions = [] } = options;

    // Check if value is null or undefined
    if (value === null || value === undefined || value === "") {
      return {
        type: null,
        source: null,
      };
    }

    // Special handling for membership operators
    if (isMembershipOperator) {
      // Check if value is a list object (has listId property)
      if (value && typeof value === "object" && value.listId) {
        // Find matching list option
        const matchedList = listOptions.find(
          (option) => option.value === value.listId
        );
        if (matchedList) {
          return {
            type: "list",
            source: "list",
            listId: value.listId,
            label: matchedList.label,
          };
        }
      }

      // Check if value is an array (enum values)
      if (Array.isArray(value)) {
        return {
          type: "enum",
          source: "custom",
          values: value,
        };
      }
    }

    // Check if value is a function object (has functionId and args properties)
    if (
      value &&
      typeof value === "object" &&
      value.functionId &&
      Array.isArray(value.args)
    ) {
      return {
        type: "functions",
        source: "function",
        functionId: value.functionId,
      };
    }

    // Check for array type
    if (Array.isArray(value)) {
      return {
        type: "array",
        source: "custom",
      };
    }

    // Check for boolean type
    if (typeof value === "boolean") {
      return {
        type: "boolean",
        source: "custom",
      };
    }

    if (typeof value === "number") {
      return {
        type: "number",
        source: "custom",
      };
    }

    if (
      typeof value === "string" &&
      value.startsWith("{") &&
      value.endsWith("}")
    ) {
      // Check in variable options
      const matchedVariable = this.findVariableInOptions(
        value,
        variableOptions
      );
      if (matchedVariable) {
        return {
          type: matchedVariable.type || "number",
          source: "reference",
          category: matchedVariable.category,
          label: matchedVariable.label,
          values: matchedVariable.values,
        };
      }

      return {
        type: "string",
        source: "reference",
      };
    }

    // For string type and possible references
    if (typeof value === "string" && Helpers.isDateString(value)) {
      return {
        type: "date",
        source: "custom",
      };


      // Default to string type with custom source
    }
    if (typeof value === "string") {
      return {
        type: "string",
        source: "custom",
      };
    }
    // Default case for objects or anything else
    return {
      type: "string",
      source: "custom",
    };
  },

  /**
   * Helper function to find a variable in the options array
   *
   * @param {string} value - The variable value to find
   * @param {Array} variableOptions - The array of variable option groups
   *
   */
  findVariableInOptions(value, variableOptions) {
    for (const group of variableOptions) {
      for (const option of group.options) {
        if (option.value === value) {
          return {
            /** @type {string} */
            type: option.type || "string",
            /** @type {string} */
            category: group.category,
            /** @type {string} */
            label: option.value,
            /** @type {Array | undefined} */
            values: option.values,
          };
        }
      }
    }
    return null;
  },
  /**
   * Formats a data value based on its type for display
   *
   * @param {any} data - The data value to format
   * @returns {string} A formatted string representation of the data
   */
  formatDataValue(data) {
    // Handle null, undefined, or empty values
    if (data === null || data === undefined || data === "") {
      return "";
    }

    // Handle function objects with functionId and args
    if (
      data &&
      typeof data === "object" &&
      data.functionId &&
      Array.isArray(data.args)
    ) {
      return this.formatFunctionData(data);
    }

    // Handle arrays
    if (Array.isArray(data)) {
      // return `[${data.join(", ")}]`;
      return `[${data.map((item) => `"${item}"`).join(", ")}]`;
    }

    // Handle objects with listId property
    if (data && typeof data === "object" && data.listId) {
      return `List: ${data.listId}`;
    }

    // Handle date strings (ISO format)
    if (
      typeof data === "string" &&
      !isNaN(Date.parse(data)) &&
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(data)
    ) {
      return `Date: ${data}`;
    }

    // Handle boolean and number values (return as is)
    if (typeof data == "boolean" || typeof data == "number") {
      return data;
    }

    // Handle string values
    if (typeof data === "string") {
      // If string is a reference (enclosed in curly braces), leave as is
      if (data.startsWith("{") && data.endsWith("}")) {
        return data;
      }
      // Otherwise, wrap in quotes
      return `"${data}"`;
    }

    // For any other type of object, stringify
    if (typeof data === "object") {
      try {
        return JSON.stringify(data);
      } catch (e) {
        return "[Complex Object]";
      }
    }

    // Default case - return as string
    return String(data);
  },
  /**
   * Determines the applicable data types based on the provided operator.
   *
   * @param {string} operator - The operator to check (e.g., "==", "!=", "<", "<=", ">", ">=", "IN", "NOT_IN", "CONTAINS", "STARTS_WITH", "ENDS_WITH")
   * @returns {string[]} An array of applicable data types for the given operator
   * @description
   * - Equality operators ("==", "!=") support string, number, boolean, and date types
   * - Comparison operators ("<", "<=", ">", ">=") support number and date types
   * - Membership operators ("IN", "NOT_IN") support list and enum types
   * - String operators ("CONTAINS", "STARTS_WITH", "ENDS_WITH") support string type only
   * - Returns default types if the operator doesn't match any of the above categories
   */
  getApplicableTypes(operator) {
    if (["==", "!="].includes(operator)) {
      return ["string", "number", "boolean", "date", "enum"];
    } else if (["<", "<=", ">", ">="].includes(operator)) {
      return ["number", "date", "functions"];
    } else if (["IN", "NOT_IN"].includes(operator)) {
      return ["list", "enum"];
    } else if (["CONTAINS", "STARTS_WITH", "ENDS_WITH"].includes(operator)) {
      return ["string"];
    }
    // Default types if operator doesn't match any of the above
    return ["string", "number", "date", "array", "boolean"];
  },
  /**
   * Validates SmartValue data based on its type and requirements
   *
   * @param {any} value - The value to validate
   * @param {boolean} emptySaveAllowed - Whether empty values are allowed
   * @param {Array} functions - Available functions for function validation
   * @returns {{isValid: boolean, errorMessage: string|null}} Validation result
   */
  validateSmartValue: (value, emptySaveAllowed, functions) => {
    // Check if value is a function object
    if (value && typeof value === "object" && value.functionId) {
      // Find the function definition to check required args
      const functionDef = functions.find(
        (func) => func.value === value.functionId
      );

      // Validate function object
      if (!functionDef) {
        return {
          isValid: false,
          errorMessage: "Please select a valid function",
        };
      }

      // Check if args array exists and has all required values
      if (
        !value.args ||
        value.args.length < functionDef.parameters.length ||
        value.args.some((arg) => {
          // Check if argument is empty
          return arg === undefined || arg === null || arg === "";
        })
      ) {
        return {
          isValid: false,
          errorMessage: "Please fill all required function parameters",
        };
      }
    }

    // Check if value is an object with listId property and if it's empty
    if (
      value &&
      typeof value === "object" &&
      Object.prototype.hasOwnProperty.call(value, "listId") &&
      (!value.listId || value.listId.trim() === "")
    ) {
      return {
        isValid: false,
        errorMessage: "Please select a list",
      };
    }
    // Check if value is an object with listId property and if it's empty
    if (
      value &&
      typeof value === "object" &&
      Object.prototype.hasOwnProperty.call(value, "transactionVariable") &&
      (!value.transactionVariable || value.transactionVariable.trim() === "")
    ) {
      return {
        isValid: false,
        errorMessage: "Please select a transaction variable",
      };
    }

    // Check if value is empty and emptySaveAllowed is false
    if (
      !emptySaveAllowed &&
      (value === null ||
        value === undefined ||
        (typeof value === "string" && value.trim() === "") ||
        (typeof value === "number" && isNaN(value)) ||
        (Array.isArray(value) && value.length === 0))
    ) {
      return {
        isValid: false,
        errorMessage: "Please Select a value",
      };
    }

    // If we made it here, the validation passed
    return {
      isValid: true,
      errorMessage: null,
    };
  },
  /**
 * Format date without timezone conversion
 * @param {string} dateString - ISO date string
 * @returns {string} Formatted date string (YYYY-MM-DD)
 */
  formatDateWithoutTimezone: (dateString) => {
    if (!dateString) return 'N/A';
    // Extract just the date part from ISO string to avoid timezone conversion
    return dateString.split('T')[0];
  },
  /**
   * Generate chart configuration options based on chart type and other parameters
   * @param {string} chartType - 'line' or 'bar'
   * @param {Function} shouldUseAutoTicks - Function to determine tick strategy
   * @param {Object} pagination - Pagination object
   * @returns {Object} Chart.js configuration options
   */
  generateChartOptions: (chartType, shouldUseAutoTicks, pagination) => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        tooltip: {
          mode: 'index',
          intersect: false,
          filter: function (tooltipItem) {
            return tooltipItem.raw !== 0;
          }
        },
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: `Analytics - Performance Over Time Chart`,
          font: {
            size: 16,
            weight: 'bold'
          }
        },
      },

      scales: {
        x: {
          title: {
            display: true,
            text: 'Date'
          },
          ticks: {
            callback: function (value, index, ticks) {
              return this.getLabelForValue(value);
            },
            // autoSkip: false,
            // Filter-based: auto vs exact timestamps
            autoSkip: shouldUseAutoTicks() ? true : false,
            maxTicksLimit: shouldUseAutoTicks() ? Math.max(20, Math.floor((pagination?.page * 10))) : undefined,
            maxRotation: 45,
            minRotation: 45,


          }
        },
        y: {
          title: {
            display: true,
            text: 'Value'
          },
          beginAtZero: chartType === 'bar',  // Bar charts typically start from zero
        }
      },
      interaction: {
        mode: 'index',
        axis: 'x',
        intersect: false
      },
      elements: chartType === 'line' ? {
        point: {
          hoverRadius: 8
        }
      } : {
        bar: {
          borderWidth: 1,
        }
      }
    };
  }
  
};
/**
 * @typedef {Object[]} createColumnsUtil_Param_columns
 * @property {string} label - The label of the column
 * @property {number} gridSize - The grid size of the column
 * @property {Object} content - The content of the column
 * @property {string | function(Object): string} content.key - The key of the item object in the generateItemContentColumns function to get the content for the column or the function to get the content
 * @property {string} [content.def] - The default value of the column if the key is not present in the item object or the value is empty/undefined/null
 * @property {"value" | "arrayLength"} [content.type] - (Optional): The type of the content to be displayed in the column. If it's array and we want the length then we can use arrayLength
 */
