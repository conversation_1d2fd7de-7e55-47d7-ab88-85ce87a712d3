import { createTheme } from "@mui/material/styles";

// Define your custom theme
export const MuiTheme = createTheme({
  palette: {
    primary: {
      main: "#FF784B", // Set your primary color
      secondary:"#4BBFFF",
    },
    secondary: {
      main: "#4BBFFF", // Set your secondary color
    },
    mainColor: {
      main: "#fff", // Set your secondary color
    },
    textColor: {
      main: "#4A4A4A",
      primary:"#494949",
      secondary:"darkGray",
    },
    bgPrimary: {
      main: "#fff",
    },
    bgSecondary: {
      main: "rgba(241, 241, 241, 0.99)",//"#e0e0e0"
    },
    danger:{
      main:"#d50000"// "#FF4B4B",// "#d50000"
    },
    white: {
      main: "#fff",
    },

    background: {
      default: "#f5f5f5", // Set default background color
    },

    text: {
      primary: "#333333", // Set default text color
    },
  },
  typography: {
    htmlFontSize: 10, // Set the base font size to 10px
    text_10: {
      fontSize: {
        // theme.typography.text_10  or if you want to apply only fontSize then use fontSize: theme.typography.text_10.fontSize
        xs: "6px",
        sm: "7px",
        md: "7px",
        lg: "8px",
      },
    },
    h1: {
      fontSize: "2.4rem", // 20px (for mobile)
      "@media (min-width:600px)": {
        fontSize: "2.3rem", // 25px (for tablet)
      },
      "@media (min-width:900px)": {
        fontSize: "3.4rem", // 30px (for laptop)
      },
      "@media (min-width:1200px)": {
        fontSize: "3.4rem", // 35px (for desktop)
      },
    },
    h2: {
      fontSize: "1.8rem", // 17.5px
      "@media (min-width:600px)": {
        fontSize: "2rem", // 20px
      },
      "@media (min-width:900px)": {
        fontSize: "2.4rem", // 25px
      },
      "@media (min-width:1200px)": {
        fontSize: "3rem", // 30px
      },
    },
    h3: {
      fontSize: "1.8rem", // 15px
      "@media (min-width:600px)": {
        fontSize: "2.4rem", // 17.5px
      },
      "@media (min-width:900px)": {
        fontSize: "2rem", // 20px
      },
      "@media (min-width:1200px)": {
        fontSize: "2.4rem", // 25px
      },
    },
    h4: {
      fontSize: "1.8rem", // 12.5px
      "@media (min-width:600px)": {
        fontSize: "1.8rem", // 15px
      },
      "@media (min-width:900px)": {
        fontSize: "1.8rem", // 17.5px
      },
      "@media (min-width:1200px)": {
        fontSize: "2rem", // 20px
      },
    },
    h5: {
      fontSize: "1.6rem", // 10px
      "@media (min-width:600px)": {
        fontSize: "1.8rem", // 12.5px
      },
      "@media (min-width:900px)": {
        fontSize: "1.8rem", // 15px
      },
      "@media (min-width:1200px)": {
        fontSize: "1.8rem", // 18px
      },
    },
    h6: {
      fontSize: "1.6rem", // 8.75px
      "@media (min-width:600px)": {
        fontSize: "1.6rem", // 10px
      },
      "@media (min-width:900px)": {
        fontSize: "1.6rem", // 12.5px
      },
      "@media (min-width:1200px)": {
        fontSize: "1.6rem", // 16px
      },
    },
    body1: {
      fontSize: "1rem", // 10px
      "@media (min-width:600px)": {
        fontSize: "1.4rem", // 11.25px
      },
      "@media (min-width:900px)": {
        fontSize: "1.4rem", // 12.5px
      },
      "@media (min-width:1200px)": {
        fontSize: "1.6rem", // 13.75px
      },
    },
    body2: {
      fontSize: "1.1rem", // 8.75px
      "@media (min-width:600px)": {
        fontSize: "1.2rem", // 10px
      },
      "@media (min-width:900px)": {
        fontSize: "1.2rem", // 11.25px
      },
      "@media (min-width:1200px)": {
        fontSize: "1.2rem", // 12.5px
      },
    },
    button: {
      fontSize: "1.1rem", // 8.75px (Mobile)
      "@media (min-width:600px)": {
        fontSize: "1.2rem", // 10px (Tablet)
      },
      "@media (min-width:900px)": {
        fontSize: "1.2", // 11.25px (Laptop)
      },
      "@media (min-width:1200px)": {
        fontSize: "1.25rem", // 12.5px (Desktop)
      },
    },
  },
});
