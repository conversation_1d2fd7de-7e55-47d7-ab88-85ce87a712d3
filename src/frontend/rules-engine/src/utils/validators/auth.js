
import validator from "validator";

export let authValidators = {
  global: {
    /**
     * Validates an email address.
     * @param {string} email - The email address to be validated.
     * @returns {Object} An object with a success flag and a message.
     *                  success is true if the email is valid, false otherwise.
     *                  message is an empty string if success is true, an error message otherwise.
     */
    email: (email) => {
      if (!validator.isEmail(email)) {
        return {
          success: false,
          message: "Invalid email address.",
        };
      }
      return { success: true, message: "" };
    },
    /**
     * Validates a password.
     * @param {string} password - The password to be validated.
     * @returns {Object} An object with a success flag and a message.
     *                  success is true if the password is valid, false otherwise.
     *                  message is an empty string if success is true, an error message otherwise.
     */
    password: (password) => {
 if (!validator.isLength(password, { min: 6 })) {
        return {
          success: false,
          message: "Password must be at least 6 characters long.",
        };
      }

      // // Check minimum length
      // if (!validator.isLength(password, { min: 8 })) {
      //   return {
      //     success: false,
      //     message: "Password must be at least 8 characters long.",
      //   };
      // }

      // // Check for at least one uppercase letter
      // if (!/[A-Z]/.test(password)) {
      //   return {
      //     success: false,
      //     message: "Password must contain at least one uppercase letter.",
      //   };
      // }

      // // Check for at least one lowercase letter
      // if (!/[a-z]/.test(password)) {
      //   return {
      //     success: false,
      //     message: "Password must contain at least one lowercase letter.",
      //   };
      // }

      // // Check for at least one special character
      // if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      //   return {
      //     success: false,
      //     message: "Password must contain at least one special character.",
      //   };
      // }

      return { success: true, message: "" };
    },
    /**
     * Validates a name.
     * @param {string} name - The name to be validated.
     * @returns {Object} An object with a success flag and a message.
     *                  success is true if the name is valid, false otherwise.
     *                  message is an empty string if success is true, an error message otherwise.
     */
    name: (name) => {
      if (!validator.isLength(name, { min: 1 })) {
        return {
          success: false,
          message: "Name must be at least 1 characters long.",
        };
      }
      return { success: true, message: "" };
    },
    /**
     * Validates a username.
     * @param {string} username - The username to be validated.
     * @returns {Object} An object with a success flag and a message.
     *                  success is true if the username is valid, false otherwise.
     *                  message is an empty string if success is true, an error message otherwise.
     */
    username: (username) => {
      // if (!validator.isAlphanumeric(username) || username.length < 3) {
      //   return {success:false,message:"Username must be at least 3 characters long and contain only letters and numbers."};
      // }
      // return {success:true,message:null};

      if (!username || typeof username !== "string") {
        return {
          success: false,
          message: "Username is required and must be a string.",
        };
      }
      if (!/^[A-Za-z]/.test(username)) {
        return {
          success: false,
          message: "Username must start with a letter.",
        };
      }

      if (!validator.isAlphanumeric(username)) {
        return {
          success: false,
          message: "Username must contain only letters and numbers.",
        };
      }

      if (username.length < 3) {
        return {
          success: false,
          message: "Username must be at least 3 characters long.",
        };
      }

      return { success: true, message: null };
    },
  },
  /**
   * Validates a login form.
   * @param {Object} obj - The object containing the username and password.
   * @param {string} obj.username - The username to be validated.
   * @param {string} obj.password - The password to be validated.
   * @returns {{success: boolean, message: string}} An object with a success flag and a message.
   *                  success is true if the form is valid, false otherwise.
   *                  message is an empty string if success is true, an error message otherwise.
   */
  isLoginFormValid: ({ username, password }) => {
    // let isEmailValid = authValidators.global.email(email);
    let isValidUsername = authValidators.global.username(username);
    let isPasswordValid = authValidators.global.password(password);
    
    // if (!isEmailValid.success) {
    //   return isPasswordValid;
    // }
    if(!username){
      return {
        success: false,
        message: "Username is required!",
      };
    }
    if (!isValidUsername.success) {
      return isValidUsername;
    }
    if(!password){
      return {
        success: false,
        message: "Password is required!",
      };
    }
    if(password.length < 6){
      return {
        success: false,
        message: "Password must be at least 6 characters long!",
      };
    }
    // if (!isPasswordValid.success) {
    //   return isPasswordValid;
    // }
    return { success: true, message: null };
  },
  /**
   * Validates password fields including current password, new password, and confirmation.
   * @param {Object} passwordData - Object containing password fields to validate
   * @param {string} passwordData.password - Current password to validate
   * @param {string} passwordData.newPassword - New password to validate
   * @param {string} passwordData.confirmPassword - Password confirmation to validate
   * @returns {{success:Boolean,data:any}} Validation result object
   * @throws {Error} If authValidators.global.password validation fails
   */
  validatePasswords: ({ password, newPassword, confirmPassword }) => {

    if (!password) {
      return {
        success: false,
        data: "Password is required!",
      };
    }
    if(password.length < 6){
      return {
        success: false,
        data: "Password must be at least 6 characters long!",
      };
    }

    if (!newPassword) {
      return {
        success: false,
        data: "new password is required!",
      };
    }
    if(newPassword.length < 6){
      return {
        success: false,
        data: "New password must be at least 6 characters long!",
      };
    }
    if (!confirmPassword) {
      return {
        success: false,
        data: "Confirm password is required!",
      };
    }

    // let isPasswordValid = authValidators.global.password(password);
    // if (!isPasswordValid.success) {
    //   return { success: false, data: "Invalid password" };
    // }
    // let isNewPasswordValid = authValidators.global.password(newPassword);
    // if (!isNewPasswordValid.success) {
    //   return { success: false, data: "Invalid new password" };
    // }

    if (newPassword !== confirmPassword) {
      return { success: false, data: "New password and confirm password are not same!" };
    }
    if (newPassword === password) {
      return { success: false, data: "New password and old password can not be same!" };
    }
    return { success: true, data: null };
  },

  /**
   * Validates a register form.
   * @param {Object} obj - The object containing the name, email, password and confirm password.
   * @returns {{success: boolean, message: string}} An object with a success flag and a message.
   *                  success is true if the form is valid, false otherwise.
   *                  message is an empty string if success is true, an error message otherwise.
   */
  isRegisterFormValid: ({ name, email, password, cPassword }) => {
    const isNameValid = authValidators.global.name(name);
    let isEmailValid = authValidators.global.email(email);
    let isPasswordValid = authValidators.global.password(password);

    if (!isNameValid.success) {
      return isNameValid;
    }
    if (!isEmailValid.success) {
      return isEmailValid;
    }
    if (!isPasswordValid.success) {
      return isPasswordValid;
    }
    if (password !== cPassword) {
      return {
        success: false,
        message: "Password and confirm password are not same!",
      };
    }
    return { success: true, message: null };
  },
  /**
 * Validates a user register form.
 * @param {Object} inputs - The object containing the name, email, password and confirm password.
 * @param {boolean} isCreateNew - The flag to check if the user is creating a new user or updating an existing user.
 * @returns {{success: boolean, message: string}} An object with a success flag and a message.
 *                  success is true if the form is valid, false otherwise.
 *                  message is an empty string if success is true, an error message otherwise.
 */
  isUserRegisterFormValid: (isCreateNew, inputs) => {

    const {  username, password, confirmPassword, role } = inputs

    // const isNameValid = authValidators.global.name(name);
    let isUsernameValid = authValidators.global.username(username);
    // let isPasswordValid = authValidators.global.password(password);

      if (password !== confirmPassword) {
        return {
          success: false,
          message: "Password and confirm password are not same!",
        };
      }

    if (!isUsernameValid.success) {
      return isUsernameValid;
    }
   
    if (role === '') {
      return {
        success: false,
        message: "Role is required!",
      };
    }

    return { success: true, message: null };
  },
  /**
   * Validates a forgot password form.
   *
   * @param {Object} obj - The object containing the email.
   * @param {string} obj.email - The email to be validated.
   * @returns {{success: boolean, message: string}} An object with a success flag and a message.
   *                  success is true if the email is valid, false otherwise.
   *                  message is an empty string if success is true, an error message otherwise.
   */
  isForgotPasswordFormValid: ({ email }) => {
    let isEmailValid = authValidators.global.email(email);

    if (!isEmailValid.success) {
      return isEmailValid;
    }
    return { success: true, message: null };
  },
  /**
   * Validates the reset password form.
   *
   * @param {Object} obj - The object containing the password and confirmation password.
   * @param {string} obj.password - The new password to be validated.
   * @param {string} obj.cPassword - The confirmation password to check against the new password.
   * @returns {{success: boolean, message: string}} An object with a success flag and a message.
   *                  success is true if the passwords are valid and match, false otherwise.
   *                  message is an empty string if success is true, an error message otherwise.
   */
  isResetPasswordFormValid: ({ password, cPassword }) => {
    // let isEmailValid = authValidators.global.password(password);
    // if (!isEmailValid.success) {
    //   return isEmailValid;
    // }
    if (!password) {
      return {
        success: false,
        message: "Password is required!",
      };
    }
    if (!cPassword) {
      return {
        success: false,
        message: "Confirm password is required!",
      };
    }
    if(password.length < 6){
      return {
        success: false,
        message: "Password must be at least 6 characters long!",
      };
    }
    if (password !== cPassword) {
      return {
        success: false,
        message: "Password and confirm password are not same!",
      };
    }
    return { success: true, message: null };
  },
};
