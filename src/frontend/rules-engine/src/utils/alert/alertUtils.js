// src/utils/alertUtils.js
// import { showAlertWithTimeout } from "../../redux/slices/alert";
import { showAlertWithTimeout } from "../../redux/slices/global/alert";
import { store } from "../../redux/store";

/**
 * Utility function to trigger an alert.
 * @param {string} message - The message to display in the alert.
 * @param {string} type - The type of the alert (success, error, warning, info).
 */
export const Alert = (message, type = "error") => {

  store.dispatch(
    showAlertWithTimeout({
      message,
      severity: type,
    })
  );
};
