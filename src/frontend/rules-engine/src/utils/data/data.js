export const Data = {
 /** 
  * The data for the webhook service
  */
  services: {
    /**
     * The data for the webhook service
     */
    webhook: {
      /**
       * The values for the input keys
       */
      inputKeys: {
        /**
         * The values for the details keys
         */
        detailsKeys: {
          keys: [ "webhookId","name", "description", "url", "method", "timeout", "securityLevel", "maxRetries", "initialDelaySeconds", "backoffMultiplier",],
          optionalKeys: ["webhookId",  "bodyTemplate", "retryPolicy", "parameters",],
        },
        /**
         * The values for the parameters keys
         */
        parametersKeys: {
          keys: ["parameterId", "name", "description", "type", "required"],
          optionalKeys: ["parameterId"],
        },
        headersKeys: {
          keys: ["key", "value"],
          optionalKeys: [],
        },
        bodyTemplateKeys: {
          keys: ["key", "value"],
          optionalKeys: [],
        },
      },
      /**
       * The values for the section headers
       */
      sectionHeaders: {
        detailsColumns: [ { label: "Name", gridSize: 4, sx: {} },{ label: "Description", gridSize: 6, sx: {} },{ label: "Actions", gridSize: 2, sx: {} }, ],
        parametersColumns:[ { label: "Name", gridSize: 5, sx: {} },{ label: "Type", gridSize: 5, sx: {} },{ label: "Actions", gridSize: 2, sx: {} }, ],
        headersColumns: [ { label: "Key", gridSize: 5, sx: {} },{ label: "Value", gridSize: 5, sx: {} },{ label: "Actions", gridSize: 2, sx: {} }, ],
        bodyTemplateColumns: [ { label: "Key", gridSize: 5, sx: {} },{ label: "Value", gridSize: 5, sx: {} },{ label: "Actions", gridSize: 2, sx: {} }, ],
      },
    },
  },
};
