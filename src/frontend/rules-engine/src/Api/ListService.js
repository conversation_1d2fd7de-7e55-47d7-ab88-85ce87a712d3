import APIService from "./APIService";
import endPoints from "./EndPoints";
import { Helpers } from "../utils";

class ListService {
  /**
   *
   * @param {endPoints["bulkNotificationList"] | endPoints["list"]} listEndpoint
   */
  constructor(listEndpoint) {
    /** @type {endPoints["bulkNotificationList"] | endPoints["list"]} */
    this.listEndpoint = listEndpoint;
  }

  /**
   * Add a new list
   * @param {Object.<string, any>} data
   * @returns
   */
  addList = async (data) => {
    // If filters are not provided then set it to empty object
    try {
      // Create a FormData object
      const formData = new FormData();
      // Loop through the data object and append each field to the form data
      for (const [key, value] of Object.entries(data)) {
        formData.append(key, value);
      }

      // If it have the dataType then remove it and instead add the type key as the backend accepts type instead of the dataType
      if (formData.has("dataType")) {
        formData.append("type", formData.get("dataType"));
        formData.delete("dataType");
      }
      // Send the POST request with form data
      const response = await APIService.getApiService().post(
        this.listEndpoint.add,
        formData,
        null,
        {
          headers: {
            "Content-Type": "multipart/form-data", // Ensure the correct content type
          },
        }
      );

      // We are here it means success
      return Helpers.returnObj(true, response.data?.message);
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to create list")
      );
    }
  };

  /**
   * This function calls the API to download a list.
   * @param {string} listId The list id
   * @returns {Promise.<{success:boolean, data: {blob:Blob, filename:string}}>} Containing whether the call was successful and the blob and filename.
   */
  downloadList = async (listId) => {
    try {
      // End point for download

      const response = await APIService.getApiService().get(
        this.listEndpoint.download(listId)
      );

      // Extract the filename from the Content-Disposition header
      const contentDisposition = response.headers["content-disposition"];
      const filename = contentDisposition
        ? contentDisposition.split("filename=")[1].replace(/"/g, "")
        : "list.csv";

      // Create a Blob URL from the response data
      const blob = new Blob([response.data], { type: "text/csv" });

      // Return the success message
      return Helpers.returnObj(true, { blob, filename });
    } catch (error) {
      console.error("Error downloading CSV:", error.message);
      return Helpers.returnObj(false, APIService.getErrorMessage(error, "Failed to download the CSV file."));
    }
  };

  /**
   * This function calls the API to delete a list.
   * @param {string} listId - The unique identifier for the list to be deleted.
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and any response data.
   */
  deleteList = async (listId) => {
    // If filters are not provided then set it to empty object
    try {
      await APIService.getApiService().delete(
        `${this.listEndpoint.delete}${listId}`
      );

      // We are here it means success

      return Helpers.returnObj(true, "List deleted successfully!");
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to delete the list")
      );
    }
  };
  /**
   * This function calls the API to get all condition types that can be used in a campaign's rule.
   * @param {string} campaignId - The unique identifier for the campaign to be fetched.
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and the condition types data.
   */
  getAllList = async () => {
    // If filters are not provided then set it to empty object
    try {
      const response = await APIService.getApiService().get(
        this.listEndpoint.getAll
      );
      return Helpers.returnObj(true, response.data);
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to get lists")
      );
    }
  };

  /**
   * This function calls the API to send sms to the list, used for the bulk notification list.
   * @param {string} listId - The list id.
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and the condition types data.
   */
  sendBulkSms = async (listId) => {
    // If filters are not provided then set it to empty object
    try {
      const response = await APIService.getApiService().get(
        this.listEndpoint.sendBulkSms(listId)
      );

      return Helpers.returnObj(true, response.data);
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to send bulk sms to the list")
      );
    }
  };
}

export default ListService; // Export as a singleton instance
