import axios from 'axios';
import AuthService from './AuthService';
import { Helpers } from "../utils";
import { VITE_ANALYTICS_API_URL } from "../config/env";
class AnalyticsService {
    constructor(analyticsEndpoint) {
        this.analyticsEndpoint = analyticsEndpoint;

        // Create dedicated axios instance for analytics with separate base URL
        this.analyticsApi = axios.create({
            baseURL: VITE_ANALYTICS_API_URL,
            headers: {
                'Content-Type': 'application/json',
            },
            withCredentials: true,
        });

        // Setup request interceptor to add auth token
        this.analyticsApi.interceptors.request.use(
            (config) => {
                const token = AuthService.getToken();
                if (token) {
                    config.headers['Authorization'] = `Bearer ${token}`;
                }
                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );
    }


    /**
     * Query time-series analytics data with filtering and pagination
     * 
     * @param {Object} filters - Filter criteria for analytics query
     * @param {string} [filters.entity_id] - Entity ID filter
     * @param {string} [filters.context_id] - Context ID filter
     * @param {string} [filters.rule_set_id] - Rule set ID filter
     * @param {number} [filters.rule_set_version] - Rule set version filter
     * @param {string} [filters.collection_id] - Collection ID filter
     * @param {string} [filters.collection_key] - Collection key filter
     * @param {string} [filters.collection_value] - Collection value filter
     * @param {string[]} [filters.variable_ids] - Array of variable IDs
     * @param {string[]} [filters.initiating_rule_ids] - Array of rule IDs
     * @param {string} [filters.start_date] - Start date (ISO string)
     * @param {string} [filters.end_date] - End date (ISO string)
     * @param {Object} pagination - Pagination settings
     * @param {number} [pagination.page=1] - Page number
     * @param {number} [pagination.limit=100] - Records per page
     * @returns {Promise<{success: boolean, data: any}>} API response
     */
    queryTimeSeriesData = async (filters = {}, pagination = {}) => {
        try {
            // Prepare request payload following backend API structure
            const requestPayload = {
                filters: filters,
                pagination: pagination
            };
            // console.log("the request payload is: ##", requestPayload);
            // Make API call to backend analytics endpoint using dedicated analytics axios instance
            const response = await this.analyticsApi.post(
                '/api/v1/analytics/query',
                requestPayload
            );
            // console.log("the response data is: ##", response.data.data);
            return Helpers.returnObj(true, response.data.data);
        } catch (error) {
            return Helpers.returnObj(
                false,
                this.getErrorMessage(error, "Failed to query time-series analytics data")
            );
        }
    };

    /**
     * Returns the error message from the error object, or a default message if the error object does not contain a message.
     * @param {Object} error - The error object
     * @param {string} [defaultMsg='An error occurred'] - The default message to return if no error message is found
     * @returns {string} The error message
     */
    getErrorMessage(error, defaultMsg = 'An error occurred') {
        // Handle network errors specifically
        if (error.message === 'Network Error' || error.code === 'ERR_NETWORK') {
            return 'Unable to connect to analytics server. Please try again later.';
        }
        if (error.response) {
            return error.response.data?.message || error.response.data?.error || error.response.data || defaultMsg;
        }
        return error.message || defaultMsg;
    }



}

export default AnalyticsService;
