import APIService from "./APIService";
import endPoints from "./EndPoints";
import { Helpers } from "../utils";

class CampaignServiceClass {
  /**
   * This function calls the api to get all campaigns
   * @returns {Object} Containing whether the call was successful and the campaigns data
   */

  /**
   * This function calls the api to get all campaigns
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and the campaigns data
   */
  async getCampaigns() {
    // ---------- @todo: remove the hard coded data --------
    // return Helpers.returnObj(true, [
    //   {
    //     name: "Dynamic Pricing Rules 2025",
    //     description: "sgs",
    //     startDateTime: "2025-03-25T19:00:00Z",
    //     endDateTime: "2025-03-28T19:00:00Z",
    //     category: "PRICING",
    //     ruleSetId: "Campaign2",
    //     contextId: "WHOLESALE_AIRTIME_PURCHASE_POST",
    //     entityId: "CREDIVERSE",
    //     schemaVersion: "2.4.0",
    //     version: 1,
    //     lastModifiedDateTime: "2025-03-28T22:08:07Z",
    //     status: "DRAFT",
    //     collectionMappings: [
    //       {
    //         collectionId: "collection2",
    //         name: "Collection2",
    //         keyMapping: {
    //           propertyId: "buyerAgentZones",
    //         },
    //       },
    //     ],
    //     persistentVariables: [
    //       {
    //         variableId: "Per1",
    //         name: "per1",
    //         description: "gsd",
    //         type: "string",
    //         defaultValue: "sdf",
    //         collectionId: "collection2",
    //       },
    //       {
    //         variableId: "Per3",
    //         name: "per3",
    //         description: "sgds",
    //         type: "string",
    //         defaultValue: "dfgdfg",
    //         collectionId: "collection2",
    //       },
    //     ],
    //     localVariables: [
    //       {
    //         variableId: "Local1",
    //         name: "local1",
    //         description: "sgs",
    //         type: "string",
    //         defaultValue: "fhdfg",
    //       },
    //       {
    //         variableId: "Local2",
    //         name: "local2",
    //         description: "sdgsddfgfh",
    //         type: "boolean",
    //         defaultValue: "true",
    //       },
    //       {
    //         variableId: "Local3",
    //         name: "local3",
    //         description: "asgdssgds",
    //         type: "boolean",
    //         defaultValue: "false",
    //       },
    //     ],
    //     // change type to conditionTypeId

    //     evaluationRules: [
    //       {
    //         ruleId: "HIGH_VALUE_PRICING",
    //         name: "High Value Customer Pricing",
    //         description:
    //           "Apply special pricing for high value customers on purchases over $100",
    //         priority: 7,
    //         condition: {
    //           conditionTypeId: "COMPARISON",
    //           operator: "==",
    //           parameters: {
    //             leftOperand: "{Per1}",
    //             rightOperand: "{Local1}",
    //           },
    //         },
    //         variableAssignments: [
    //           {
    //             variableId: "discountAmount",
    //             assignmentTypeId: "SET",
    //             value: 10,
    //           },
    //           {
    //             variableId: "purchaseAmount",
    //             assignmentTypeId: "DECREASE_BY_PERCENTAGE",
    //             value: 10,
    //           },
    //         ],
    //         webhookCalls: [
    //           {
    //             webhookId: "smsNotification",
    //             parameters: {
    //               recipient: "{agentMsisdn}",
    //               message:
    //                 "Congratulations! You've reached your daily target of {targetValue} bundles.",
    //             },
    //           },
    //         ],
    //       },
    //     ],
    //     outcomeRules: [
    //       {
    //         ruleId: "HIGH_VALUE_PRICING",
    //         name: "High Value Customer Pricing",
    //         description:
    //           "Apply special pricing for high value customers on purchases over $100",
    //         priority: 7,
    //         condition: {
    //           conditionTypeId: "COMPARISON",
    //           operator: "==",
    //           parameters: {
    //             leftOperand: "{Per1}",
    //             rightOperand: "{Local1}",
    //           },
    //         },
    //         variableAssignments: [
    //           {
    //             variableId: "discountAmount",
    //             assignmentTypeId: "SET",
    //             value: 10,
    //           },
    //           {
    //             variableId: "purchaseAmount",
    //             assignmentTypeId: "DECREASE_BY_PERCENTAGE",
    //             value: 10,
    //           },
    //         ],
    //         webhookCalls: [
    //           {
    //             webhookId: "smsNotification",
    //             parameters: {
    //               recipient: "{SMS_NUMBER}",
    //               message:
    //                 "Congratulations! You've reached your daily target of {targetValue} bundles.",
    //             },
    //           },
    //         ],
    //       },
    //     ],
    //   },
    // ]);

    // -----------------------------------------------------

    try {
      const response = await APIService.getApiService().get(
        endPoints.campaign.getCampaigns
      );


      return Helpers.returnObj(true, response.data);
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to get the campaigns list")
      );
    }
  }

  /**
   * This function calls the api to get all entities
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and the campaigns data
   */
  async getEntities() {
    // ---------- @todo: remove the hard coded data --------

    // return Helpers.returnObj(true, [
    //   {
    //     entityId: "550e8400-e29b-41d4-a716-446655440000",
    //     name: "E-commerce Platform",
    //     description: "Online shopping platform",
    //     transactionContexts: [
    //       {
    //         contextId: "PURCHASE",
    //         name: "Product Purchase",
    //         description: "Customer purchasing products",
    //         properties: [
    //           {
    //             propertyId: "customerId",
    //             name: "Customer ID",
    //             description: "Unique identifier for the customer",
    //             type: "string",
    //             mutable: false,
    //           },
    //           {
    //             propertyId: "productPrice",
    //             name: "Product Price",
    //             description: "Price of the product",
    //             type: "number",
    //             mutable: true,
    //             constraints: {
    //               min: 0,
    //             },
    //           },
    //           {
    //             propertyId: "discountPercentage",
    //             name: "Discount Percentage",
    //             description: "Discount percentage",
    //             type: "number",
    //             mutable: true,
    //             constraints: {
    //               min: 0,
    //               max: 50,
    //             },
    //           },
    //           {
    //             propertyId: "customerType",
    //             name: "Customer Type",
    //             description: "Type of customer",
    //             type: "enum",
    //             mutable: false,
    //             values: ["NEW", "RETURNING", "LOYAL", "VIP"],
    //           },
    //           {
    //             propertyId: "shippingAddress",
    //             name: "Shipping Address",
    //             description: "Customer's shipping address",
    //             type: "object",
    //             mutable: false,
    //             properties: [
    //               {
    //                 propertyId: "street",
    //                 name: "Street",
    //                 description: "Street address",
    //                 type: "string",
    //                 mutable: false,
    //               },
    //               {
    //                 propertyId: "city",
    //                 name: "City",
    //                 description: "City name",
    //                 type: "string",
    //                 mutable: false,
    //               },
    //               {
    //                 propertyId: "postalCode",
    //                 name: "Postal Code",
    //                 description: "Postal code",
    //                 type: "string",
    //                 mutable: false,
    //               },
    //               {
    //                 propertyId: "country",
    //                 name: "Country",
    //                 description: "Country",
    //                 type: "string",
    //                 mutable: false,
    //               },
    //             ],
    //           },
    //         ],
    //       },
    //       {
    //         contextId: "CART_ABANDONMENT",
    //         name: "Cart Abandonment",
    //         description: "Customer abandoned cart without completing purchase",
    //         properties: [
    //           {
    //             propertyId: "customerId",
    //             name: "Customer ID",
    //             description: "Unique identifier for the customer",
    //             type: "string",
    //             mutable: false,
    //           },
    //           {
    //             propertyId: "cartTotal",
    //             name: "Cart Total",
    //             description: "Total value of abandoned cart",
    //             type: "number",
    //             mutable: false,
    //           },
    //           {
    //             propertyId: "reminderDelay",
    //             name: "Reminder Delay",
    //             description: "Delay before sending reminder (hours)",
    //             type: "number",
    //             mutable: true,
    //             constraints: {
    //               min: 1,
    //               max: 48,
    //             },
    //           },
    //           {
    //             propertyId: "offerType",
    //             name: "Offer Type",
    //             description: "Type of offer to send",
    //             type: "enum",
    //             mutable: true,
    //             values: ["DISCOUNT", "FREE_SHIPPING", "BONUS_ITEM"],
    //             constraints: {
    //               validValues: ["DISCOUNT", "FREE_SHIPPING"],
    //             },
    //           },
    //         ],
    //       },
    //     ],
    //   },
    // ]);
    // -----------------------------------------------------

    try {
      const response = await APIService.getApiService().get(
        endPoints.campaign.listEntities
      );

      return Helpers.returnObj(true, response.data);
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to get the entities list")
      );
    }
  }
  /**
   * This function calls the api to get single campaign
   * @param {string} campaignId - The unique identifier for the campaign
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and the campaign data
   */
  async getCampaign(campaignId) {
    try {
      const response = await APIService.getApiService().put(
        `${endPoints.campaign.getCampaign}${campaignId}`
      );
      // If not successful then return
      if (!response.data.success) return response.data;
      return response.data;
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to get the campaign")
      );
    }
  }

  async createCampaign(data) {
    console.log(data)
    // If filters are not provided then set it to empty object
    try {
      const response = await APIService.getApiService().post(
        endPoints.campaign.createCampaign,
        data
      );

      // We are here it means success

      return Helpers.returnObj(true, response.data?.message);
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to create the campaign")
      );
    }
  }
  /**
   * This function calls the api to create campaign
   * @param {string} campaignId The existing campaign Id.
   * @param {Object} data - The data to be sent in the request body
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and the campaign data
   */
  async updateCampaign(data) {
    // Call the create campaign API, as it's the same as updating a campaign
    return this.createCampaign(data);
  }

  /**
   * This function calls the API to duplicate a campaign.
   * @param {string} campaignId - The unique identifier for the existed campaign.
   * @param {number} campaignVersion - The version of the existed campaign.
   * @param {string} newCampaignId - The unique identifier for the new campaign.
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and any response data.
   */
  async duplicateCampaign(campaignId, campaignVersion, newCampaignId) {
    // If filters are not provided then set it to empty object
    try {
      const response = await APIService.getApiService().post(
        `${endPoints.campaign.mainRoute}/${campaignId}/${campaignVersion}/copy/${newCampaignId}`
      );

      // We are here it means success

      return Helpers.returnObj(true, "Campaign duplicated successfully!");
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to duplicate the campaign")
      );
    }
  }

  /**
   * This function calls the API to delete a campaign.
   * @param {string} campaignId - The unique identifier for the campaign to be deleted.
   * @param {number} campaignVersion - The version of the campaign to be deleted.
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and any response data.
   */
  async deleteCampaign(campaignId) {
    // If filters are not provided then set it to empty object
    try {
      const response = await APIService.getApiService().delete(
        `${endPoints.campaign.deleteCampaign}${campaignId}`
      );

      // We are here it means success

      return Helpers.returnObj(true, "Campaign deleted successfully!");
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to delete the campaign")
      );
    }
  }

  /**
   * Activate the campaign
   */
  async activateCampaign(campaignId, campaignVersion) {
    try {
      // Make the API call
      const response = await APIService.getApiService().post(
        `${endPoints.campaign.mainRoute}/${campaignId}/${campaignVersion}/activate`
      );

      // We are here it means it's success
      return Helpers.returnObj(true, "Campaign activated successfully!");
    } catch (error) {
      // Return a formatted error response
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to activate the campaign")
      );
    }
  }

  /**
   * Activate the campaign
   */
  async deactivateCampaign(campaignId, campaignVersion) {
    try {
      // Make the API call
      const response = await APIService.getApiService().post(
        `${endPoints.campaign.mainRoute}/${campaignId}/${campaignVersion}/deactivate`
      );

      // We are here it means it's success
      return Helpers.returnObj(true, "Campaign deactivated successfully!");
    } catch (error) {
      // Return a formatted error response
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to deactivate the campaign")
      );
    }
  }

  /**
   * This function calls the API to get all condition types that can be used in a campaign's rule.
   * @param {string} campaignId - The unique identifier for the campaign to be fetched.
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and the condition types data.
   */
  async getConditionTypes() {
    try {
      const response = await APIService.getApiService().get(
        endPoints.campaign.getConditionTypes
      );

      // If not successful then return
      if (!response.data.success) return response.data;

      return response.data;
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to get condition types")
      );
    }
  }
}

const CampaignService = new CampaignServiceClass();

export default CampaignService; // Export as a singleton instance

export const campaignsStatusEnums = ["DRAFT", "ACTIVE", "COMPLETED"];
