import APIService from "./APIService";
import endPoints from "./EndPoints";
import { Helpers } from "../utils";

class WebhookServiceClass {
  /**
   * This function calls the api to get all campaigns
   * @returns {Object} Containing whether the call was successful and the campaigns data
   */

  /**
   * This function calls the api to get all campaigns
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and the campaigns data
   */
  async getWebhooks({ page = 0, pageSize = 20, securityLevel = "" }) {
    // ---------- @todo: remove the hard coded data --------

    // return Helpers.returnObj(true, [
    //   {
    //     webhookId: "smsNotification",
    //     name: "SMS Notification",
    //     description: "Sends SMS notifications via the corporate SMS gateway",
    //     url: "https://api.sms-gateway.com/send",
    //     method: "POST",
    //     headers: {
    //       Authorization: "Bearer ${SECRET:sms_api_key}",
    //       "Content-Type": "application/json",
    //     },
    //     bodyTemplate: {
    //       to: "{recipient}",
    //       message: "{message}",
    //       sender: "RuleForge",
    //     },
    //     timeout: 3000,
    //     retryPolicy: {
    //       maxRetries: 3,
    //       initialDelaySeconds: 60,
    //       backoffMultiplier: 2,
    //     },
    //     securityLevel: "SYSTEM",
    //     parameters: [
    //       {
    //         parameterId: "recipient",
    //         name: "Recipient",
    //         description: "The phone number to send the SMS to",
    //         type: "string",
    //         required: true,
    //       },
    //       {
    //         parameterId: "message",
    //         name: "Message",
    //         description: "The content of the SMS message",
    //         type: "string",
    //         required: true,
    //       },
    //     ],
    //   },
    //   {
    //     webhookId: "emailNotification",
    //     name: "Email Notification",
    //     description:
    //       "Sends email notifications via the corporate email gateway",
    //     url: "https://api.email-gateway.com/send",
    //     method: "POST",
    //     headers: {
    //       Authorization: "Bearer ${SECRET:email_api_key}",
    //       "Content-Type": "application/json",
    //     },
    //     bodyTemplate: {
    //       to: "{recipient}",
    //       subject: "{subject}",
    //       body: "{body}",
    //     },
    //     timeout: 3000,
    //     retryPolicy: {
    //       maxRetries: 3,
    //       initialDelaySeconds: 60,
    //       backoffMultiplier: 2,
    //     },
    //     securityLevel: "SYSTEM",
    //     parameters: [
    //       {
    //         parameterId: "recipient",
    //         name: "Recipient",
    //         description: "The email address to send the email to",
    //         type: "string",
    //         required: false,
    //       },
    //       {
    //         parameterId: "subject",
    //         name: "Subject",
    //         description: "The subject of the email",
    //         type: "string",
    //         required: true,
    //       },
    //     ],
    //   },
    //   {
    //     webhookId: "userManagement",
    //     name: "User Management",
    //     description:
    //       "Manages user accounts and permissions",
    //     url: "https://api.user-management.com/manage",
    //     method: "POST",
    //     headers: {
    //       Authorization: "Bearer ${SECRET:user_management_api_key}",
    //       "Content-Type": "application/json",
    //     },
    //     bodyTemplate: {
    //       userId: "{userId}",
    //       role: "{role}",
    //       status: "{status}",
    //     },
    //     timeout: 3000,
    //     retryPolicy: {
    //       maxRetries: 3,
    //       initialDelaySeconds: 60,
    //       backoffMultiplier: 2,
    //     },
    //     securityLevel: "SYSTEM",
    //     parameters: [
    //       {
    //         parameterId: "userId",
    //         name: "User ID",
    //         description: "The user ID to manage",
    //         type: "string",
    //         required: true,
    //       },
    //       {
    //         parameterId: "role",
    //         name: "Role",
    //         description: "The role of the user",
    //         type: "string",
    //         required: false,
    //       },
    //       {
    //         parameterId: "status",
    //         name: "Status",
    //         description: "The status of the user",
    //         type: "string",
    //         required: true,
    //       }
    //     ],
    //   },
    // ]);

    // -----------------------------------------------------

    try {
      const response = await APIService.getApiService().get(
        endPoints.webhooks.getAll,
        {
          page: page+1,
          pageSize: pageSize,
          securityLevel: securityLevel === "" ? undefined : securityLevel,
        }
      );
      return Helpers.returnObj(true, response.data);
    } catch (error) {
      let err = APIService.getErrorMessage(error);
      return Helpers.returnObj(false, err.message);
    }
  }

  /**
   * This function calls the api to get single webhook
   * @param {string} webhookId - The unique identifier for the webhook
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and the webhook data
   */
  async getWebhook(webhookId) {
    try {
      const response = await APIService.getApiService().put(
        `${endPoints.webhooks.getSingle}${webhookId}`
      );

      return Helpers.returnObj(true, response.data);
    } catch (error) {
      let err = APIService.getErrorMessage(error, "Failed to get the webhook");
      return Helpers.returnObj(false, err.message);
    }
  }

  async createWebhook(data) {
    // If filters are not provided then set it to empty object
    try {
      const response = await APIService.getApiService().post(
        endPoints.webhooks.add,
        data
      );
      console.log(response);

      // We are here it means success

      return Helpers.returnObj(true, response.data.message);
    } catch (error) {
      let err = APIService.getErrorMessage(
        error,
        "Failed to create the webhook"
      );
      return Helpers.returnObj(false, err.message);
    }
  }
  /**
   * This function calls the api to update webhook
   * @param {string} webhookId The existing webhook Id.
   * @param {Object} data - The data to be sent in the request body
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and the webhook data
   */
  async updateWebhook(webhookId, data) {
    try {
      const response = await APIService.getApiService().put(
        `${endPoints.webhooks.update}${webhookId}`,
        data
      );
      // Call the create webhook API, as it's the same as updating a webhook
      // return this.createWebhook(data);
      return Helpers.returnObj(true, response.data.message);
    } catch (error) {
      let err = APIService.getErrorMessage(
        error,
        "Failed to update the webhook"
      );
      return Helpers.returnObj(false, err.message);
    }
  }
  /**
   * This function calls the API to delete a campaign.
   * @param {string} webhookId - The unique identifier for the webhook to be deleted.
   * @returns {Promise<{success: boolean, data: any}>} Containing whether the call was successful and any response data.
   */
  async deleteWebhook(webhookId) {
    // If filters are not provided then set it to empty object
    try {
      const response = await APIService.getApiService().delete(
        `${endPoints.webhooks.delete}${webhookId}`
      );
      // We are here it means success
      return Helpers.returnObj(true, "Webhook deleted successfully!");
    } catch (error) {
      let err = APIService.getErrorMessage(error);
      return Helpers.returnObj(false, err.message);
    }
  }
}

const WebhookService = new WebhookServiceClass();

export default WebhookService; // Export as a singleton instance

export const campaignsStatusEnums = ["DRAFT", "ACTIVE", "COMPLETED"];
