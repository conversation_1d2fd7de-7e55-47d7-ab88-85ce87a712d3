import UMSAPIService from "./UmsApiService";
import endPoints from "./EndPoints";
import { Helpers } from "../utils";
import { loginSuccess, logoutSuccess } from "../redux/slices/global/auth";
import { VITE_TENANT_ID, VITE_REFRESH_TOKEN_INTERVAL } from "../config/env";

class AuthServiceClass {
  /**
   * Registers a new user with the given email, password, and name.
   *
   * This function sends a POST request to the registration endpoint
   * with the provided user details. If the registration is successful,
   * the user data is set and the response data is returned. In case of
   * an error, an error message is returned.
   *@param {Object} data - Object containing user registration data
   * @param {string} data.username - The username of the user to register.
   * @param {string} data.password - The password of the user to register.
   * @param {Object.<string, any>} data.profile_data - Profile data
   * @param {string} data.name - The name of the user to register.
   * @returns {Promise<{ success: boolean, data: any }>} The response data if successful, or an error message if failed.
   */
  async register({ username, password,  }) {
    try {
      const response = await UMSAPIService.getApiService().post(
        endPoints.auth.register,
        {
          username,
          password,
          // profile_data,
          // role_id: roleId,
          // sendEmail: sendEmail,
        }
      );

      // If not successfull then return
      if (!response.data.success) return response.data;

      // We are here it means success
      // this._setData(response.data.data);

      return response.data;
    } catch (error) {
      // @ts-ignore
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Registration failed")
      );
    }
  }

  /**
   * Logs in a user with the given email and password.
   *
   * This function sends a POST request to the login endpoint
   * with the provided email and password. If the login is successful,
   * the user data is set and the response data is returned. In case of
   * an error, an error message is returned.
   *
   * @async
   * @function login
   * @param {Object} data - Object containing user login data
   * @param {string} data.email - The email of the user to login.
   * @param {string} data.password - The password of the user to login.
   * @returns {Promise<{ success: boolean, data: any }>} The response data if successful, or an error message if failed.
   */
  async getUsers({ email, password }) {
    try {
      const response = await UMSAPIService.getApiService().post(
        endPoints.auth.getUser,
        { email, password, tenant_id: "1" }
      );

      // If not successfull then return
      if (!response.data.success) return response.data;

      // // Set data
      // this._setData(response.data.data);

      return response.data;
    } catch (error) {
      console.log(error,);
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Login failed")
      );
    }
  }
  /**
   * Gets the list of users from the database.
   * @returns {Promise<{success:Boolean,data:any}>} Promise resolving to an object containing:
   *   - success {boolean}: Indicates if the operation was successful
   *   - data {Object|null}: Contains user profile information if successful
   */
  async getUsersList() {
    // now i wnat to retrun the dummy data
    return {
      success: true,
      data: {
        users: [
          {
            id: "1",
            name: "John Doe",
            email: "<EMAIL>",
            role: "user-admin",
          },
          {
            id: "2",
            name: "smith",
            email: "<EMAIL>",
            role: "campaign-admin",
          },
          {
            id: "3",
            name: "api",
            email: "<EMAIL>",
            role: "api",
          },
          {
            id: "4",
            name: "api",
            email: "<EMAIL>",
            role: "api",
          },
        ],
        page: 1,
        total: 10,
        limit: 10,
      },
    };
    /*
    try {
      const response = await UMSAPIService.getApiService().get(
        endPoints.auth.getUsersList
      );
      if (!response.data.success) return response.data;
      return response.data;
    } catch (error) {
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Login failed")
      );
    }
    */
  }

  /**
   * Logs in a user with the given email and password.
   *
   * This function sends a POST request to the login endpoint
   * with the provided email and password. If the login is successful,
   * the user data is set and the response data is returned. In case of
   * an error, an error message is returned.
   *
   * @async
   * @function login
   * @param {Object} data - Object containing user login data
   * @param {string} data.username - The username of the user to login.
   * @param {string} data.password - The password of the user to login.
   * @returns {Promise<{ success: boolean, data: any }>} The response data if successful, or an error message if failed.
   */
  async login({ username, password }) {
   
    try {
      const response = await UMSAPIService.getApiService().post(
        endPoints.auth.login,
        { username, password, tenant_id: VITE_TENANT_ID }
      );

      // If not successfull then return
      if (!response.data.success) return response.data;

      // // Set data
      // this._setData(response.data.data);

      return response.data;
    } catch (error) {
      console.log(error, "#w");
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Login failed")
      );
    }
  }

  /**
   * Logs out the current user.
   *
   * This function removes the user's token and user data from session storage,
   * dispatches a logout success action, and sends a POST request to the logout endpoint.
   * In case of an error, it returns the error response data or a default logout failed message.
   */
  async logout() {
    try {
      sessionStorage.removeItem("token"); // Remove the token from localStorage
      sessionStorage.removeItem("user"); // Remove the user from localStorage
      sessionStorage.removeItem("isAuthenticated");

      // Dispatch the logout success action
      UMSAPIService.getApiService().dispatch(logoutSuccess());

      UMSAPIService.getApiService().post(endPoints.auth.logout);
    } catch (error) {
      return error.response?.data || "Logout failed";
    }
  }

  /**
   * This will set the redux state if the user is logged in
   */

  /**
   * Initializes authentication state from session storage.
   *
   * This method retrieves the authentication token and user data
   * from session storage. If both are available, it dispatches a
   * login success action with the retrieved token and user data
   * to update the Redux state accordingly.
   */
  initAuth() {
    const token = sessionStorage.getItem("token");
    const user = sessionStorage.getItem("user");

    if (token && user) {
      UMSAPIService.getApiService().dispatch(
        loginSuccess({ token, user: JSON.parse(user) })
      );
    }
  }

  /**
   * Checks if the user is authenticated.
   *
   * This method retrieves the authentication token from session storage
   * and returns true if the token exists, otherwise false.
   *
   * @returns {boolean} True if the user is authenticated, false otherwise
   */

  isAuthenticated() {
    const token = sessionStorage.getItem("token");
    return !!token; // Return true if the token exists
  }

  /**
   * Retrieves the authentication token from session storage.
   *
   * @returns {string | null} The authentication token if it exists, null otherwise.
   */
  getToken() {
    return sessionStorage.getItem("token");
  }

  /**
   * Retrieves the user data object from session storage.
   *
   * This method retrieves the user data object from session storage
   * and returns it as a parsed JSON object. If no user data exists,
   * it returns null.
   *
   * @returns {Object | null} The user data object if it exists, null otherwise
   */
  getUserData() {
    const user = sessionStorage.getItem("user");
    return user ? JSON.parse(user) : null; // Parse the user object
  }

  /**
   * Sets the authentication state with the given data and redirects to the given URL
   * if provided.
   *
   * This method sets the authentication token and user data to session storage and
   * dispatches a login success action with the provided data. If a redirect URL is
   * provided, it navigates to that URL after setting the authentication state.
   *
   * @param {Object} data - The data object containing the authentication token and user data.
   * @param {string} [redirect] - The URL to redirect to after setting the authentication state.
   * @private
   */
  _setData(data, redirect = "/") {
    // Set the token to the session storage
    sessionStorage.setItem("access_token", data.token);
    sessionStorage.setItem("user", JSON.stringify(data.user));
    
    // Dispatch the login success action
    UMSAPIService.getApiService().dispatch(loginSuccess(data));

    if (redirect) {
      UMSAPIService.getApiService().navigate(redirect);
    }
  }
  /**
   * Retrieves user profile data from the API service.
   * @async
   * @function getUserProfileData
   * @returns {Promise<{success:Boolean,data:any}>} Promise resolving to an object containing:
   *   - success {boolean}: Indicates if the operation was successful
   *   - data {Object|null}: Contains user profile information if successful
   * @throws {Error} If API request fails or response is invalid
   */
  async getUserProfileData() {
    try {
      
      const response = await UMSAPIService.getApiService().post(
        endPoints.auth.getUserProfileData
      );

      // // If not successful then return
      if (!response.data.success) return response.data;
      console.log(response, "#");
      // Set data
      // this._setData(response.data.data);

      return response.data;

      return {
        success: true,
        data: {
          email: "<EMAIL>",
          name: "Wahab khurram",
        },
      };
    } catch (error) {
      console.log("error", error);
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Login failed")
      );
    }
  }
  /**
   * Updates user profile data through the API service.
   * @async
   * @function updateUserProfileData
   * @param {Object} userData - Object containing user data to update
   * @param {string} userData.name - User email address
   * @returns {Promise<{success:Boolean,data:any}>} Promise resolving to an object containing:
   *   - success {boolean}: Indicates if the operation was successful
   *   - data {Object|null}: Contains updated user profile information if successful
   * @throws {Error} If API request fails, validation fails, or response is invalid
   */
  async updateUserProfileData(obj) {
    try {
      const response = await UMSAPIService.getApiService().put(
        endPoints.auth.updateProfile,
        { profile_data: obj.profile_data }
      );

      // If not successful then return
      if (!response.data.success) return response.data;
      // store the data locally
      // Set data
      // this._setData(response.data.data);

      return response.data;
    } catch (error) {
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Login failed")
      );
    }
  }
  /**
   * Updates user profile password by calling the api.
   * @async
   * @function updateUserProfileData
   * @param {Object} userData - Object containing user data to update
   * @param {string} userData.password - The current password
   * @param {string} userData.newPassword - The new password
   * @returns {Promise<{success:Boolean,data:any}>} Promise resolving to an object containing:
   *   - success {boolean}: Indicates if the operation was successful
   *   - data {Object|null}: Contains updated user profile information if successful
   * @throws {Error} If API request fails, validation fails, or response is invalid
   */
  async updatePassword({ password, newPassword }) {
    
    try {
      const response = await UMSAPIService.getApiService().put(
        endPoints.auth.updatePassword,
        { currentPassword: password, newPassword }
      );

      // If not successful then return
      if (!response.data.success) return response.data;
      // store the data locally
      // Set data
      // this._setData(response.data.data);

      // return response.data;
      return {
        success: true,
        data: {
          email: "",
          fullName: "",
        },
      };
    } catch (error) {
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Login failed")
      );
    }
  }
  /**
   * Retrieves all users from the API service.
   * @async
   * @function getAllUsers
   * @param {Object} params - Object containing pagination parameters
   * @param {number} params.page - The page number
   * @returns {Promise<{success:Boolean,data:any}>} Promise resolving to an object containing:
   *   - success {boolean}: Indicates if the operation was successful
   *   - data {Object|null}: Contains the list of users if successful
   * @throws {Error} If API request fails or response is invalid
   */
  async getAllUsers({ page }) {
    try {
      const response = await UMSAPIService.getApiService().post(
        endPoints.auth.getAllUsers,
        { page }
      );
      if (!response.data.success) return response.data;
      return response.data.data;
    } catch (error) {
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Login failed")
      );
    }
  }
  /**
   * Deletes a user by calling the API service.
   * @async
   * @function deleteUser
   * @param {string} userId - The ID of the user to delete
   * @returns {Promise<{success:Boolean,data:any}>} Promise resolving to an object containing:
   *   - success {boolean}: Indicates if the operation was successful
   *   - data {Object|null}: Contains the deleted user data if successful
   * @throws {Error} If API request fails or response is invalid
   */
  async deleteUser(userId) {
    try {
      const response = await UMSAPIService.getApiService().delete(
        `${endPoints.auth.deleteUser}${userId}`
      );
      if (!response.data.success) return response.data;
      return response.data;
    } catch (error) { 
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Delete user failed")
      );
    }
  }
  /**
   * Add this as a class property to store the timeout ID
   */
  _refreshTokenTimeout = null;

  /**
   * Refreshes the authentication token.
   * @async
   * @function refreshToken
   * @returns {Promise<{success:Boolean,data:any}>} Promise resolving to an object containing:
   *   - success {boolean}: Indicates if the operation was successful
   *   - data {Object|null}: Contains the refreshed token if successful
   * @throws {Error} If API request fails or response is invalid
   */
  async refreshToken(refreshTime) {
    try {
      let token = AuthService.getToken();
      if(!token) return;
    
      const response = await UMSAPIService.getApiService().post(
        endPoints.auth.refreshToken,
        {  }
      );
      
      if (!response.data.success) {
        throw new Error('Failed to refresh token');
      }
      
      sessionStorage.setItem("token", response.data.data.token);
      
      // Schedule next refresh
      setTimeout(() => {
        AuthService.refreshToken(refreshTime);
      }, refreshTime);
      
    } catch (error) {
      console.log('Token refresh failed, retrying in 10 seconds...', error);
      
      // Retry after 10 seconds on failure
      setTimeout(() => {
        AuthService.refreshToken(refreshTime);
      }, 10000);
      
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Refresh token failed")
      );
    }
  }
  /**
   * Forgot password.
   * @async
   * @function forgotPassword
   * @param {string} email - The email of the user.
   * @returns {Promise<{success:Boolean,data:any}>} Promise resolving to an object containing:
   */ 
  async forgotPassword(email) {
    try {
      const response = await UMSAPIService.getApiService().post(
        endPoints.auth.forgotPassword,
        { email } 
      );
      if (!response.data.success) return response.data;
      return response.data;
    } catch (error) {
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Forgot password failed")
      );
    }
  }
  /**
   * Reset password.
   * @async
   * @function resetPassword
   * @param {Object} inputs - The inputs of the user.
   * @param {string} inputs.email - The email of the user.
   * @param {string} inputs.password - The password of the user.
   * @param {string} inputs.verificationCode - The verification code of the user.
   * @returns {Promise<{success:Boolean,data:any}>} Promise resolving to an object containing:
   */ 
  async resetPassword({email,password,verificationCode}) {
    try {
      const response = await UMSAPIService.getApiService().post(
        endPoints.auth.resetPassword,
        { email,newPassword:password,code:verificationCode }
      );
      
      if (!response.data.success) return response.data;
      return response.data;
    } catch (error) {
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Reset password failed")
      );
    }
    } 
  /**
   * Creates a new API key.
   * @async
   * @function createApiKey
   * @returns {Promise<{success:Boolean,data:any}>} Promise resolving to an object containing:
   */
  async createApiKey() {
    try {
      const response = await UMSAPIService.getApiService().post(
        endPoints.auth.createApiKey
      );
      if (!response.data.success) return response.data;
      return response.data;
    } catch (error) {
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Create API key failed")
      );
    }
  }
  /**
   * Gets all API keys.
   * @async
   * @function getAllApiKeys
   * @returns {Promise<{success:Boolean,data:any}>} Promise resolving to an object containing:
   */
  async getAllApiKeys({page,limit}) {
    try {
      const response = await UMSAPIService.getApiService().post(
        endPoints.auth.getAllApiKeys,
        // {page,limit}
      );
      if (!response.data.success) return response.data;
      return response.data;
    } catch (error) {
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Get all API keys failed")
      );
    }
    }
  /**
   * Deletes an API key.
   * @async
   * @function deleteApiKey
   * @param {string} apiKeyId - The ID of the API key to delete
   * @returns {Promise<{success:Boolean,data:any}>} Promise resolving to an object containing:
   */
  async deleteApiKey(apiKeyId) {
    try {
      const response = await UMSAPIService.getApiService().delete(
        `${endPoints.auth.deleteApiKey}/${apiKeyId}`
      );
      if (!response.data.success) return response.data;
      return response.data;
    } catch (error) {
      return Helpers.returnObj(
        false,
        UMSAPIService.getErrorMessage(error, "Delete API key failed")
      );
    }
  }

}

const AuthService = new AuthServiceClass();

export default AuthService; // Export as a singleton instance
