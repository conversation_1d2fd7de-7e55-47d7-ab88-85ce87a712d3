function getPath(path) {
  return `/api/v1/${path}`;
}

function getUmsPath(path) {
  return `${path}`;
}

const endPoints = {
  global: {
    config: "system/config-schema",
    conditionTypes: "system/conditionTypes",
    variableAssignments: "system/variableAssignments",
    functions: "system/functions",
    standardProperties: "system/standardProperties",
  },

  campaign: {
    mainRoute: getPath("rulesets"),
    getCampaigns: getPath("rulesets"),
    getCampaign: getPath("rulesets/"),
    listEntities: getPath("entities"), // Including the id '/entities/{entityId}'
    createCampaign: getPath("rulesets"),
    updateCampaign: getPath("rulesets"),
    getCampaignDetails: getPath(`rulesets/`),
    listCampaigns: getPath("rulesets"),
    deleteCampaign: getPath("rulesets/"),
    activateCampaign: getPath("rulesets/activate/"),
    getGlobalContext: getPath(`globalContext`),
    evaluateTransaction: getPath("evaluate"),
  },
  list: { 
    getAll: getPath("lists"),
    add: getPath("lists"),
    delete: getPath("lists/"),
    /**
     * Get the path to download a specific notification by ID.
     *
     * @param {string|number} id - The unique identifier of the notification to download.
     * @returns {string} The full path to download the notification.
     */
    download: (id) => getPath(`lists/${id}/download`),
  },
  bulkNotificationList: {
    getAll: getPath("bulk-notification"),
    add: getPath("bulk-notification"),
    delete: getPath("bulk-notification/"),
    /**
     * Get the path to download a specific bulk notification by ID.
     *
     * @param {string|number} id - The unique identifier of the bulk notification to download.
     * @returns {string} The full path to download the bulk notification.
     */
    download: (id) => getPath(`bulk-notification/${id}/download`),
    /**
     * Get the path to send bulk sms to the specific bulk notification list.
     *
     * @param {string|number} id - The unique identifier of the bulk notification to download.
     * @returns {string} The full path to send the bulk sms.
     */
    sendBulkSms: (id) => getPath(`bulk-notification/${id}/sendsms`),
  },
  analytics: {
    getAll: getPath("analytics/getCampaignSalesReport"),
    query: getPath("analytics/query"),
  },
  auth: {
    deleteUser: getUmsPath("auth/delete-user/"),
    login: getUmsPath("auth/login"),
    register: getUmsPath("auth/register"),
    getUser: getUmsPath("auth/get-user"),
    updateProfile: getUmsPath("auth/profile"),
    updatePassword: getUmsPath("auth/password"),
    getUserProfileData: getUmsPath("auth/get-user"),
    getAllUsers: getUmsPath("auth/get-all-users"),
    refreshToken: getUmsPath("auth/refresh-token"),
    forgotPassword: getUmsPath("auth/password/reset"),
    resetPassword: getUmsPath("auth/password/reset-set-pass"),
    createApiKey: getUmsPath("auth/create-api-key"),
    getAllApiKeys: getUmsPath("auth/get-all-api-keys"),
    deleteApiKey: getUmsPath("auth/delete-api-key"),
  },
  webhooks: {
    getAll: getPath("webhooks"),
    add: getPath("webhooks"),
    delete: getPath("webhooks/"),
    update: getPath("webhooks/"),
    getById: getPath("webhooks/"),
  },
};

export default endPoints;
