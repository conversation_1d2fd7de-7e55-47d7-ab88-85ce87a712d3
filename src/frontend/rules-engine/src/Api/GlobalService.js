import APIService from "./APIService";
import endPoints from "./EndPoints";
import { Helpers } from "../utils";

class GlobalServiceClass {
  /**
   * This function calls the API to get the global config.
   * @returns {Promise<{success: boolean, data: ConfigData | string}>} Containing whether the call was successful and the config data.
   */
  async getConfig() {
    // const dummyConditionTypesData = {
    //   variableAssignments: [
    //     {
    //       assignmentTypeId: "SET",
    //       name: "Set Value",
    //       description: "Sets the variable to the specified value",
    //       applicableTypes: ["string", "number", "boolean", "date", "enum"],
    //     },
    //     {
    //       assignmentTypeId: "ADD",
    //       name: "Add",
    //       description: "Adds the specified value to the variable",
    //       applicableTypes: ["number"],
    //     },
    //     {
    //       assignmentTypeId: "SUBTRACT",
    //       name: "Subtract",
    //       description: "Subtracts the specified value from the variable",
    //       applicableTypes: ["number"],
    //     },
    //     {
    //       assignmentTypeId: "MULTIPLY",
    //       name: "Multiply",
    //       description: "Multiplies the variable by the specified value",
    //       applicableTypes: ["number"],
    //     },
    //     {
    //       assignmentTypeId: "DIVIDE",
    //       name: "Divide",
    //       description: "Divides the variable by the specified value",
    //       applicableTypes: ["number"],
    //     },
    //     {
    //       assignmentTypeId: "INCREASE_BY_PERCENTAGE",
    //       name: "Increase by Percentage",
    //       description: "Increases the variable by the specified percentage",
    //       applicableTypes: ["number"],
    //     },
    //     {
    //       assignmentTypeId: "DECREASE_BY_PERCENTAGE",
    //       name: "Decrease by Percentage",
    //       description: "Decreases the variable by the specified percentage",
    //       applicableTypes: ["number"],
    //     },
    //   ],
    //   conditionTypes: [
    //     {
    //       conditionTypeId: "COMPARISON",
    //       name: "Comparison",
    //       description: "Compares two values using a specified operator",
    //       applicableTypes: ["string", "number", "boolean", "date", "enum"],
    //       parameters: [
    //         {
    //           parameterId: "leftOperand",
    //           name: "Left Operand",
    //           type: "any",
    //           description: "The left-hand side of the comparison",
    //           required: true,
    //         },
    //         {
    //           parameterId: "rightOperand",
    //           name: "Right Operand",
    //           type: "any",
    //           description: "The right-hand side of the comparison",
    //           required: true,
    //         },
    //       ],
    //       operators: [
    //         "==",
    //         "!=",
    //         "<",
    //         "<=",
    //         ">",
    //         ">=",
    //         "IN",
    //         "NOT_IN",
    //         "CONTAINS",
    //         "STARTS_WITH",
    //         "ENDS_WITH",
    //       ],
    //     },
    //     {
    //       conditionTypeId: "LOGICAL",
    //       name: "Logical",
    //       description: "Combines multiple conditions with a logical operator",
    //       applicableTypes: ["boolean"],
    //       parameters: [
    //         {
    //           parameterId: "conditions",
    //           name: "Conditions",
    //           type: "array",
    //           description: "The conditions to combine",
    //           required: true,
    //         },
    //       ],
    //       operators: ["AND", "OR", "NOT"],
    //     },
    //   ],
    //   functions: [
    //     {
    //       functionId: "dayOfWeek",
    //       name: "Day of Week",
    //       description: "Returns the day of the week for a given date",
    //       parameters: [
    //         {
    //           parameterId: "date",
    //           name: "Date",
    //           type: "date",
    //           description: "The date to get the day of week for",
    //         },
    //       ],
    //       returnType: "string",
    //       returnValues: [
    //         "MONDAY",
    //         "TUESDAY",
    //         "WEDNESDAY",
    //         "THURSDAY",
    //         "FRIDAY",
    //         "SATURDAY",
    //         "SUNDAY",
    //       ],
    //       examples: [
    //         {
    //           reference: {
    //             functionId: "dayOfWeek",
    //             args: ["2025-03-28T00:00:00Z"],
    //           },
    //           result: "FRIDAY",
    //         },
    //       ],
    //     },
    //     {
    //       functionId: "formatDate",
    //       name: "Format Date",
    //       description: "Formats a date according to the specified pattern",
    //       parameters: [
    //         {
    //           parameterId: "date",
    //           name: "Date",
    //           type: "date",
    //           description: "The date to format",
    //         },
    //         {
    //           parameterId: "pattern",
    //           name: "Pattern",
    //           type: "string",
    //           description:
    //             "The format pattern (follows Java SimpleDateFormat syntax)",
    //         },
    //       ],
    //       returnType: "string",
    //       examples: [
    //         {
    //           reference: {
    //             functionId: "formatDate",
    //             args: ["2025-03-28T00:00:00Z", "yyyy-MM-dd"],
    //           },
    //           result: "2025-03-28",
    //         },
    //       ],
    //     },
    //   ],
    // };

    // try {
    //   return Helpers.returnObj(true, dummyConditionTypesData);
    // } catch (error) {
    //   return Helpers.returnObj(
    //     false,
    //     APIService.getErrorMessage(error, "Failed to get condition types")
    //   );
    // }
    try {
      const response = await APIService.getApiService().get(
        endPoints.global.config
      );

      return Helpers.returnObj(true, response.data);
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to get config")
      );
    }
  }

  async getConditionTypes() {
    try {
      const response = await APIService.getApiService().get(
        endPoints.global.conditionTypes
      );
      return Helpers.returnObj(true, response.data);
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to get condition types")
      );
    }
  }

   /**
   * This function calls the API to get the variable assignments.
   * @returns {Promise<{success: boolean, data: Object[] | string}>} Containing whether the call was successful and the variable assignments data.
   */
   async getVariableAssignments() {
    try {
      const response = await APIService.getApiService().get(
        endPoints.global.variableAssignments
      );
      return Helpers.returnObj(true, response.data);
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to get variable assignments")
      );
    }
  }
  
  /**
   * This function calls the API to get the functions.
   * @returns {Promise<{success: boolean, data: Object[] | string}>} Containing whether the call was successful and the functions data.
   */
  async getFunctions() {
    try {
      const response = await APIService.getApiService().get(
        endPoints.global.functions
      );
      return Helpers.returnObj(true, response.data);
    } catch (error) {
      return Helpers.returnObj(
        false,
        APIService.getErrorMessage(error, "Failed to get functions")
      );
    }
  }

  /**
   * This function calls the API to get the standard properties.
   * @returns {Promise<{success: boolean, data: Object[] | string}>} Containing whether the call was successful and the standard properties data.
   */
  async getStandardProperties() {
    try {
      const response = await APIService.getApiService().get(
        endPoints.global.standardProperties
      );
      return Helpers.returnObj(true, response.data);
    } catch (error) {
      return Helpers.returnObj(
        false,  
        APIService.getErrorMessage(error, "Failed to get standard properties")
      );
    }
  }

}

const GlobalService = new GlobalServiceClass();

export default GlobalService; // Export as a singleton instance

// ===============

/**
 * @typedef {Object} ConfigData
 * @property {Object[]} variableOperations - Array of available variable operations
 * @property {Object[]} conditions - Array of available conditions
 * @property {('LIST'|'COMPARISON'|'DATE'|'LOGICAL')} conditions[].type - The type of condition
 * @property {('IN'|'=='|'!='|'<'|'<='|'>'|'>='|'IS_DAY')} conditions[].operator - The operator for the condition
 * @property {Object[]} conditions[].parameters - Array of parameters for the condition
 * @property {string} conditions[].parameters[].name - Name of the parameter ('list'|'value'|'leftOperand'|'rightOperand'|'day'|'date')
 * @property {string} conditions[].parameters[].type - Type of the parameter ('list'|'string'|'any'|'enum'|'date')
 * @property {string[]} [conditions[].parameters[].values] - Optional array of valid values for enum type parameters
 */
