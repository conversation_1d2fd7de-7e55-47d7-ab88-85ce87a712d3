// src/hoc/withPageHOC.js

import PropTypes from "prop-types";
import { Box, useMediaQuery } from "@mui/material";
import DefaultHeader from "../components/pages/dashboard/organisms/header/Index";
import DefaultSidebar from "../components/pages/dashboard/organisms/sidebar/Index";
import { Component, useState } from "react";

import { IconBtnAtom } from "../components/global/atoms/buttons/IconBtn";
import DynamicIcon from "../components/global/atoms/icons/Index";
import { useDispatch } from "react-redux";
import React from "react";
import { setIsSidebarOpen as setIsSidebarOpenRedux } from "../redux/slices/global/dashboard";

/**
 * HOC for wrapping page components with flexible layout options.
 * It includes optional header and sidebar components with default behavior.
 * The sidebar can be collapsed or hidden on both desktop and mobile for flexibility.
 *
 * @param {Component} WrappedPage - The page component that you want to wrap.
 * @param {Object} defaultStyles - The default styles (padding, margin, etc.) to apply.
 * @returns {Component} - The wrapped page component with layout.
 */
export const withPageHOC = (WrappedPage, defaultStyles = {}) => {
  /**
   * Higher-order component that provides a flexible page layout with an optional header and sidebar.
   * The sidebar can be expanded or collapsed based on screen size and user interaction.
   * @param {Object} props - The page component to be wrapped.
   * @param {React.ReactElement} props.headerComponent - The component to be used as the header. Defaults to a standard header.
   * @param {React.ReactElement} props.sidebarComponent - The component to be used as the sidebar. Defaults to a standard sidebar.
   * @param {Array} props.menuItems - An array of menu items for the default sidebar.
   * @param {string} props.sidebarWidth - The width of the sidebar when expanded (default is "250px").
   * @param {string} props.collapsedSidebarWidth - The width of the sidebar when collapsed (default is "70px").
   * @param {Object} props.sx - Additional custom styles for the page layout.
   * @param {Object} props.rest - Additional props to be passed to the wrapped page component.
   * @returns {React.ReactElement} The page component wrapped with the layout including header and sidebar.
   */
  const PageHOC = ({
    headerComponent,
    sidebarComponent,
    menuItems,
    sidebarWidth = "250px", // Default sidebar width
    collapsedSidebarWidth = "70px", // Width when collapsed
    sx = {},
    ...rest
  }) => {
    // const { isSidebarOpen } = useSelector((state) => state.dashboard);
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);

    const dispatch = useDispatch();
    const isSmallScreen = useMediaQuery((theme) =>
      // @ts-ignore
      theme.breakpoints.down("md")
    );

    const handleSidebarToggle = () => {
      
      setIsSidebarOpen(!isSidebarOpen);
      // dispatch(setIsSidebarOpenRedux(!isSidebarOpen)); // Toggle sidebar open/close
    };

    const handleMouseEnter = () => {
      if (!isSmallScreen) {
        setIsSidebarOpen(true);
        // dispatch(setIsSidebarOpenRedux(true));
      } // Only open on hover if not small screen
    };

    const handleMouseLeave = () => {
      if (!isSmallScreen) {
        setIsSidebarOpen(false);
        // dispatch(setIsSidebarOpenRedux(false));
      } // Only close on mouse leave if not small screen
    };

    const styles = {
      display: "flex",
      flexDirection: "column",
      minHeight: "100vh", // Ensure the page takes full height of the screen
      //   position: "relative",
      ...defaultStyles,
      ...sx, // allows overriding default styles
      overflowX: "hidden",
    };
    let headerHeight = "70px";
    let sideBarWidth = "70px";

    return (
      <Box sx={styles}>
        {/* Render Header */}
        <Box
          component="header"
          sx={{
            width: "100%",
            display: "flex",
            boxShadow: "0 0 10px 0 rgba(0, 0, 0, 0.1)",
            alignItems: "center",
            px: { xs: 1, md: 2 },
            flexShrink: 0,
            height: headerHeight,
            maxHeight: headerHeight,
            position: {
              md: "fixed",
              backgroundColor: "primary.main",
            },
            top: 0,
            zIndex: 1000,
            md: "fixed",
            backgroundColor: "mainColor.main",
            maxWidth: "1600px",
          }}
        >
          {headerComponent || <DefaultHeader  {...rest} />}
          
           <IconBtnAtom
            sx={{
              // position: "absolute", // Fixed on mobile, absolute on desktop
              // top: 16,
              // left: 16,
              zIndex: 4,
              my: 2,
              mx: 2, // Ensure the button stays on top
              fontSize: "34px",
              display: isSmallScreen ? "flex" : "none",
              // justifyContent: "center",
              alignItems: "center",
              "&:hover": {
                "& svg": {
                  color: "#fff", // Change the icon color to white
                },
              },
            }}
            iconParams={{
              iconName: isSidebarOpen ? "Close" : "Menu",
              color: "secondary",
              size: 25,
              title: "More Options",
            }}
            onClick={handleSidebarToggle}
          />
        </Box>

        <Box
          sx={{
            display: "flex",
            flexGrow: 1,
            position: "relative",
            width: "100%",
          }}
        >
          {/* Box for sidebar */}
          <Box
            component="aside"
            onMouseEnter={handleMouseEnter} // Expand on hover
            onMouseLeave={handleMouseLeave} // Collapse on mouse leave
            sx={{
              width: {
                xs: isSidebarOpen ? "100%" : "100%",
                sm: isSidebarOpen ? "100%" : "100%",
                md: isSidebarOpen ? sidebarWidth : sideBarWidth,
              },
              flexShrink: 0,
              backgroundColor: "background.paper",
              boxShadow: "0 2px 4px rgba(0, 0, 0, 0.1)",
              transition: "all 0.5s ease-in-out",
              overflowX: "hidden",
              zIndex: 3,
              height: "100vh",
              position: {
                xs: "absolute", // On small screens, position absolute so it overlays the content
                sm: "absolute",
                md: "fixed", // relative
              },
              // position:{
              //   xs:'absolute',
              //   md:"absolute",
              // },
              top: {
                md: 0, // Slide in the sidebar on larger screens when open
                overflow: "hidden",
              },
              // left: {
              //   md: isSidebarOpen ? "0" : "-100%", // Slide in the sidebar on larger screens when open
              // },
              left: {
                xs: isSidebarOpen ? "0" : "-100%", // Slide out the sidebar offscreen when closed on small screens
                sm: isSidebarOpen ? "0" : "-100%", // Slide out the sidebar offscreen when closed on small screens
                md: "0", // Keep the sidebar in place on larger screens
              },
              mt: {
                md: headerHeight,
              },
            }}
          >
            {sidebarComponent ? (
              sidebarComponent
            ) : (
              <DefaultSidebar
                isSidebarOpen={isSidebarOpen}
                menuItems={menuItems}
               
              />
            )}
          </Box>

          {/* Main Content */}
          <Box

            sx={{
              width: {
                xs: "100%", // Always full width on small screens
                sm: "100%", // Always full width on small screens
                md: `calc(100% - ${collapsedSidebarWidth})`,
                //  isSidebarOpen
                //   ? `calc(100% - ${sidebarWidth})`
                //   : `calc(100% - ${collapsedSidebarWidth})`, // On larger screens, width adjusts based on sidebar
              },
              height: { sm: "auto", md: "auto" },
              position: {
                xs: "relative", // Content remains in place, allowing sidebar to overlay on small screens
                sm: "relative",
                md: "static", // Normal behavior on larger screens
              },
              p: { xs: 1, sm: 2, md: 3 },
              zIndex: 1,
              ml: {
                md: sideBarWidth,
              },
              mt: {
                md: headerHeight,
              },
            }}
          >
            <WrappedPage  {...rest} />
          </Box>
        </Box>
      </Box>
    );
  };

  // PropType checking for flexibility and proper usage
  PageHOC.propTypes = {
    headerComponent: PropTypes.element, // Optional header component
    sidebarComponent: PropTypes.element, // Optional sidebar component
    menuItems: PropTypes.arrayOf(
      PropTypes.shape({
        text: PropTypes.string.isRequired,
        onClick: PropTypes.func,
      })
    ), // Menu items for default sidebar
    sidebarWidth: PropTypes.string, // Custom sidebar width
    collapsedSidebarWidth: PropTypes.string, // Sidebar width when collapsed
    containerStyles: PropTypes.object, // Custom styles for the main container
    sx: PropTypes.object, // Additional custom styles for the page layout
  };

  return PageHOC;
};
