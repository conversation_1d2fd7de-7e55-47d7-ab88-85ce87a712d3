import { createSlice } from "@reduxjs/toolkit";

export const initialState = {
  /** The user's authentication status (whether they are logged in or not)
   * @type {boolean} It is initially set to false.
   */
  isAuthenticated:sessionStorage.getItem("isAuthenticated") === "true" ? true : false, 
  /*  isAuthenticated: sessionStorage.getItem("isAuthenticated")
    ? sessionStorage.getItem("isAuthenticated")
    : false, */
    /**
     * Indicates whether the token is being refreshed
     * @type {boolean} It is initially set to false.
     */
    isTokenRefreshing:false,

  /** The user object
   * @type {Object} It is initially set to null.
   */
  user: sessionStorage.getItem("user")
    ? JSON.parse(sessionStorage.getItem("user"))
    : {},
    /** 
     * The user's role name
     * @type {string} It is initially set to an empty string
     */
    // roleName: sessionStorage.getItem("roleName")
    // ? sessionStorage.getItem("roleName"): '',

  /** The user's authentication token
   * @type {string} It is initially set to null.
   */
  token: sessionStorage.getItem("token")
    ? sessionStorage.getItem("token")
    : '',
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    loginSuccess: (state, action) => {
      state.user = action.payload.user;
      state.token = action.payload.token;
      state.isAuthenticated = action.payload.isAuthenticated;
      sessionStorage.setItem("user", JSON.stringify(action.payload.user));
      sessionStorage.setItem("token",action.payload.token);
      sessionStorage.setItem("isAuthenticated", action.payload.isAuthenticated);
      sessionStorage.setItem("refresh_token_t", action.payload.refresh_token_t);
      
    },
    logoutSuccess: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      sessionStorage.removeItem("user");
      sessionStorage.removeItem("token");
      sessionStorage.removeItem("isAuthenticated");
      sessionStorage.removeItem("refresh_token_t");
    },
    /**
     * Sets the token refreshing status
     * @param {boolean} action.payload - The new token refreshing status
     */
    setIsTokenRefreshing: (state, action) => {
      state.isTokenRefreshing = action.payload;
    },
  },
});

// Action creators are generated for each case reducer function
export const { loginSuccess, logoutSuccess, setIsTokenRefreshing } = authSlice.actions;

export default authSlice.reducer;
