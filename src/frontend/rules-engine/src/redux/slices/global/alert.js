// src/redux/alertSlice.js
import { createSlice } from '@reduxjs/toolkit';

export const initialState = {
  /** The list of alerts */
  alerts: []
};

/**
 * Redux slice for managing alert notifications.
 * 
 * @namespace alertSlice
 * 
 * @property {string} name - The name of the slice.
 * @property {Object} initialState - The initial state of the slice.
 * @property {Object} reducers - The reducer functions to handle actions.
 * @property {Function} reducers.showAlert - Adds a new alert to the state if an identical alert does not already exist.
 * @property {Function} reducers.dismissAlert - Removes an alert from the state by its ID.
 * @property {Function} reducers.resetAlerts - Clears all alerts from the state.
 */
const alertSlice = createSlice({
  name: 'alerts',
  initialState,
  reducers: {
    showAlert: (state, action) => {
      // state.alerts.push(action.payload);
      const existingAlert = state.alerts.find(
        (alert) => alert.message === action.payload.message && alert.type === action.payload.type
      );
      
      // If no identical alert exists, add the new alert
      if (!existingAlert) {
        state.alerts.push(action.payload);
      }
    },
    dismissAlert: (state, action) => {
      state.alerts = state.alerts.filter(alert => alert.id !== action.payload);
    },
    resetAlerts: (state) => {
      state.alerts = [];
    }
  }
});

export const { showAlert, dismissAlert, resetAlerts } = alertSlice.actions;


/**
 * Shows an alert with a timeout to dismiss it automatically after a set time.
 * 
 * @param {Object} alert - The alert to show, with the following properties:
 *   - message: The text to display in the alert.
 *   - type: One of "success", "error", "warning", "info" to control the alert's color.
 *   - id: (optional) The ID of the alert, if provided. Otherwise, a random ID is generated.
 * @param {Function} dispatch - The Redux dispatch function to use for sending actions.
 * 
 * @returns {void}
 */
export const showAlertWithTimeout = (alert) => (dispatch) => {
  const id = alert.id || Math.random().toString(36).substring(7); // Generate a unique ID for the alert
  dispatch(showAlert({ ...alert, id }));
  
  // Automatically dismiss the alert after 5 seconds
  setTimeout(() => {
    dispatch(dismissAlert(id));
  }, 5000); // Adjust time as needed
};

export default alertSlice.reducer;
