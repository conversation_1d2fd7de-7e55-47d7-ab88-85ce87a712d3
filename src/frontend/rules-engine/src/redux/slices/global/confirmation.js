// confirmationSlice.js
import { createSlice } from "@reduxjs/toolkit";

export const initialState = {
  isOpen: false,
  title: "",
  message: "",
  onConfirm: null,
  onCancel: null,
};

const confirmationSlice = createSlice({
  name: "confirmation",
  initialState,
  reducers: {
    /**
     * Opens the confirmation dialog with specified properties.
     *
     * @param {object} state - The current state of the confirmation dialog.
     * @param {object} action - The action dispatched to open the dialog.
     * @param {object} action.payload - The payload of the action.
     * @param {string} action.payload.title - The title of the confirmation dialog.
     * @param {string} action.payload.message - The content text of the dialog.
     * @param {string} action.payload.confirmationId - The ID for confirmation callback.
     */
    openDialog: (state, action) => {
      state.isOpen = true;
      state.title = action.payload.title;
      state.message = action.payload.message;
      // We can't store functions in Redux, so we'll use an ID system
      state.confirmationId = action.payload.confirmationId;
    },
    closeDialog: (state) => {
      state.isOpen = false;
      state.title = "";
      state.message = "";
      state.confirmationId = null;
    },
  },
});

export const { openDialog, closeDialog } = confirmationSlice.actions;
export const selectConfirmation = (state) => state.confirmation;
export default confirmationSlice.reducer;
