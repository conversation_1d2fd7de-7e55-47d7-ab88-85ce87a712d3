import { createSlice } from "@reduxjs/toolkit";

export const initialState = {
  /** The global tab state
   * @type {string} It is initially set to "campaigns".
   */
  tab: "campaigns",
  /** The campaign tab state
   * @type {string} It is initially set to "".
   */
  campaignTab: "",
  /** The sidebar open state
   * @type {boolean} It is initially set to false.
   * */
  isSidebarOpen: false,
};
  

export const dashboardSlice = createSlice({
  name: "dashboard",
  initialState,
  reducers: {
    /**
     * Sets the global tab state.
     * @param {Object} state - The current state of the slice.
     * @param {Object} action - The action dispatched.
     * @param {string} action.payload - The payload of the action, which is the new global tab state.
     */
    setGlobalTab: (state, action) => {
      state.tab = action.payload;
    },
    /**
     * Sets the campaign tab state.
     * @param {Object} state - The current state of the slice.
     * @param {Object} action - The action dispatched.
     * @param {string} action.payload - The payload of the action, which is the new campaign tab state.
     */
    setCampaignTab: (state, action) => {
      
      state.campaignTab = action.payload;
    },
    /**
     * Sets the sidebar open state.
     * @param {Object} state - The current state of the slice.
     * @param {Object} action - The action dispatched.
     * @param {boolean} action.payload - The payload of the action, which is the new sidebar open state. If true, the sidebar is open. If false, the sidebar is closed.
     */
    setIsSidebarOpen: (state, action) => {
      state.isSidebarOpen = action.payload;
    },
  },
});
 

/**
 * Sets the global tab state.
 * @function
 * @param {string} tab - The new global tab state.
 * @returns {Function} The Redux thunk function to dispatch actions.
 */
export const { setGlobalTab, setCampaignTab, setIsSidebarOpen } =
  dashboardSlice.actions;

export default dashboardSlice.reducer;
