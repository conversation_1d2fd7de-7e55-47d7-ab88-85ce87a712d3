import { createSlice } from "@reduxjs/toolkit";

/**
 *  {
    "campaigns": [
      {
        "campaignId": "string",
        "name": "string",
        "status": "string",
        "startDate": "string",
        "endDate": "string"
      }
    ],
    "totalCount": "integer",
    "page": "integer",
    "pageSize": "integer"
  }
 */
export const initialState = {
  /** The campaigns tab state to be either "createCampaign", "viewCampaigns", "analytics" */
  campaignsTab: "viewCampaigns",
  /** The singe campaign */
  campaign: {},
  /** The list of campaigns data  */
  campaignsData: {
    /** The list of campaigns */
    campaigns: [],
    /** The total number of campaigns */
    totalCampaigns: 0,
    /** Page number for pagination (default: 1) */
    page: 0,
    /**  Number of items per page (default: 20) */
    pageSize: 10,
    /**  Page number for pagination (default: 1) */
    status: "all",
  },
  analyticsData: {
    analytics: [],
  },
};

/**
 * Redux slice for managing the campaigns tab state.
 *
 * @namespace campaignsTabSlice
 * @property {string} name - The name of the slice.
 * @property {Object} initialState - The initial state of the slice.
 * @property {Object} reducers - The reducers for the slice.
 * @property {Function} reducers.setCampaignsTab - Reducer to set the campaigns tab state.
 * @param {Object} state - The current state of the slice.
 * @param {Object} action - The action dispatched.
 * @param {any} action.payload - The payload of the action.
 */

export const campaignsTabSlice = createSlice({
  name: "campaigns",
  initialState,
  reducers: {
    /**
     * Sets the campaigns tab state.
     * @param {Object} state - The current state of the slice.
     * @param {Object} action - The action dispatched.
     *
     * @param {string} action.payload - The payload of the action, which must be either "createCampaign" or "viewCampaigns".
     * @returns {Object} The updated state of the slice.
     */
    setCampaignsTab: (state, action) => {
      state.campaignsTab = action.payload;
    },
    setCampaigns: (state, action) => {
      state.campaignsData = {
        ...initialState.campaignsData,
        ...action.payload,
      };
    },
    updateCampaignsList: (state, action) => {
      state.campaignsData.campaigns = action.payload.campaigns;
    },
    /**
     * Sets the analytics data state.
     * @param {Object} state - The current state of the slice.
     * @param {Object} action - The action dispatched.
     * @param {Object} action.payload - The payload of the action.
     * @param {string} action.payload.campaignId - The campaign ID.
     * @returns {void} The updated state of the slice.
     */
    setAnalyticsData: (state, action) => {
      state.analyticsData = {
        ...state.analyticsData, // Preserve existing analytics data
        ...action.payload, // Update with the new data
      };
    },
    resetCampaignsData: (state) => {
      state.campaignsData = initialState.campaignsData;
    }
  },
});

// Action creators are generated for each case reducer function
export const { setCampaignsTab, setCampaigns, setAnalyticsData,resetCampaignsData } =
  campaignsTabSlice.actions;

export default campaignsTabSlice.reducer;

/*

{
  "page": 1,
  "npages": 5,
  "recordsPerPage": 10,
  "campaignId": "CampaignID123",
  "timeRange": {
    "start": "2024-11-01T00:00:00Z",
    "end": "2024-11-30T23:59:59Z"
  },
  "metrics": [
    {
      "date": "2024-11-01T00:00:00Z",
      "salesPerformance": {
        "uniqueAgents": 120,
        "uniqueSubscribers": 350,
        "airtimeRevenue": 15000.50,
        "bundleSalesRevenue": 22000.75,
        "transactionCount": 560
      },
      "costData": {
        "commissionSpend": 3200.00,
        "tradeBonusSpend": 1800.00,
        "benefitsSpend": 500.00
      }
    },
    {
      "date": "2024-11-02T00:00:00Z",
      "salesPerformance": {
        "uniqueAgents": 110,
        "uniqueSubscribers": 340,
        "airtimeRevenue": 14500.25,
        "bundleSalesRevenue": 21000.30,
        "transactionCount": 540
      },
      "costData": {
        "commissionSpend": 3100.00,
        "tradeBonusSpend": 1750.00,
        "benefitsSpend": 520.00
      }
    }
    // Additional entries for other dates
  ],
  "aggregates": {
    "totalUniqueAgents": 1200,
    "totalUniqueSubscribers": 3500,
    "totalAirtimeRevenue": 450000.00,
    "totalBundleSalesRevenue": 620000.00,
    "totalTransactionCount": 16800,
    "totalCommissionSpend": 96000.00,
    "totalTradeBonusSpend": 54000.00,
    "totalBenefitsSpend": 15000.00
  }
}


*/
