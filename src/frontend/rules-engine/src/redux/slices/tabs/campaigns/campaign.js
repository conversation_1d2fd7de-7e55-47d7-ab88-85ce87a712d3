import { Label } from "@mui/icons-material";
import { createSlice } from "@reduxjs/toolkit";

export const initialState = {
  /** The campaign data that is being created or edited */
  campaign: {
    rulesetId: "",
    name: "",
    description: "",
    entityId: "",
    contextId: "",
    category: "",
    startDateTime: "",
    endDateTime: "",
    collectionMappings: [
      // {
      //   collectionName: "",
      //   keyMappings: [
      //     {
      //       entityId: "",
      //       contextId: "",
      //       propertyName: "",
      //     },
      //     {
      //       entityId: "",
      //       contextId: "",
      //       propertyName: "",
      //     },
      //   ],
      // },
    ],
    persistentVariables: [],
    localVariables: [],
    // entities: [],
    // rules: [],
    evaluationRules: [],
    outcomeRules: [],
  },
  /** The status of the campaign "creating" when creating the new campaign or "updating" updating the existing campaign */
  status: "creating", // ["creating","updating"]
  /** The received data that will be used during the campaign creation */
  receivedData: {
    /** The entities data that will be used during the campaign creation */
    entities: null,
    /** The persistent variable data that will be used during the campaign creation */
    types: [
      { label: "string", value: "string" },
      { label: "number", value: "number" },
      { label: "boolean", value: "boolean" },
    ],

    variableAssignments: [],
    logicObject: {
      label: "Logical",
      value: "LOGICAL",
      items: [],
      operators: [
        { label: "AND", value: "AND" },
        { label: "OR", value: "OR" },
      ],
    },
    conditionTypes: [],
    functions: [],
    dateFormatePatterns: [
      { value: "yyyy-MM-dd", label: "yyyy-MM-dd", description: "2023-01-31" },
      { value: "dd/MM/yyyy", label: "dd/MM/yyyy", description: "31/01/2023" },
      { value: "MM/dd/yyyy", label: "MM/dd/yyyy", description: "01/31/2023" },
      {
        value: "yyyy-MM-dd HH:mm:ss",
        label: "yyyy-MM-dd HH:mm:ss",
        description: "2023-01-31 14:30:45",
      },
      {
        value: "yyyy-MM-dd'T'HH:mm:ss'Z'",
        label: "yyyy-MM-dd'T'HH:mm:ss'Z'",
        description: "2023-01-31T14:30:45Z (ISO)",
      },
      {
        value: "EEE, dd MMM yyyy",
        label: "EEE, dd MMM yyyy",
        description: "Tue, 31 Jan 2023",
      },
      {
        value: "EEEE, dd MMMM yyyy",
        label: "EEEE, dd MMMM yyyy",
        description: "Tuesday, 31 January 2023",
      },
      { value: "HH:mm:ss", label: "HH:mm:ss", description: "14:30:45 (24h)" },
      {
        value: "hh:mm:ss a",
        label: "hh:mm:ss a",
        description: "02:30:45 PM (12h)",
      },
      {
        value: "dd-MMM-yyyy",
        label: "dd-MMM-yyyy",
        description: "31-Jan-2023",
      },
      {
        value: "MMM dd, yyyy",
        label: "MMM dd, yyyy",
        description: "Jan 31, 2023",
      },
      { value: "yyyy/MM/dd", label: "yyyy/MM/dd", description: "2023/01/31" },
      { value: "dd.MM.yyyy", label: "dd.MM.yyyy", description: "31.01.2023" },
      { value: "yyyy.MM.dd", label: "yyyy.MM.dd", description: "2023.01.31" },
      {
        value: "yyyy-MM-dd'T'HH:mm:ssXXX",
        label: "yyyy-MM-dd'T'HH:mm:ssXXX",
        description: "2023-01-31T14:30:45+01:00",
      },
    ],
    apiCallsTypes: [
      {
        label: "Adjust Stock",
        value: "crediverseTransfer",
        /** Structured data needs to be set on the output json */
        data: {
          name: "payAirtimeSalesCommission",
          type: "crediverseTransfer",
        },
      },
    ],
    transactionVariables: [],

    filteredTransactionVariables: [],
    /** @type {{id: string, name: string, elemsCount: number, type: string}[]} The lookup list (The list management list) */
    lists: [],
  },
};

export const campaignsTabSlice = createSlice({
  name: "campaign",
  initialState,
  reducers: {
    /**
     * Updates the campaign state by merging the existing state with the new payload.
     * The payload should be a subset of the campaign state, and only the properties
     * that are present in the payload will be updated in the state.
     * @param {Object} state - The current campaign state
     * @param {Object} action - The action payload containing the updated campaign state
     * @returns {Object} The updated campaign state
     */
    setCampaign: (state, action) => {
      state.campaign = {
        ...state.campaign, // Merge existing campaign state
        ...action.payload, // Update only the part being passed
      };
    },
    /**
     * Set full campaign data
     */
    setFullCampaign: (state, action) => {
      state.campaign = action.payload.campaign;
      state.status = action.payload.status;
    },
    setReceivedData: (state, action) => {
      state.receivedData = { ...state.receivedData, ...action.payload };
    },
    /**
     * Sets filtered transaction variables based on the rule type
     * @param {Object} state - The current state
     * @param {Object} action - Action containing the filtered variables
     */
    setFilteredTransactionVariables: (state, action) => {
      state.receivedData.filteredTransactionVariables = action.payload;
    },
    /**
     * Add logical conditions to the beginning of conditionTypes array
     */
    addLogicalConditions: (state, action) => {
      const logicObject = {
        type: "LOGICAL",
        operator: "AND",
        parameters: [
          {
            name: "conditions",
            type: "array",
            values: [],
          },
        ],
      };
      state.receivedData.conditionTypes = [
        logicObject,
        ...state.receivedData.conditionTypes,
      ];
    },
    /**
     * Reset the campaign data to the initial state
     */
    resetData: (state) => {
      state.campaign = initialState.campaign;
      state.status = initialState.status;
    },
  },
});

// Action creators are generated for each case reducer function
export const {
  setCampaign,
  setFullCampaign,
  setReceivedData,
  resetData,
  addLogicalConditions,
  setFilteredTransactionVariables,
} = campaignsTabSlice.actions;

export default campaignsTabSlice.reducer;

/**
 * @typedef {initialState} CampaignInitialState
 *
 */
