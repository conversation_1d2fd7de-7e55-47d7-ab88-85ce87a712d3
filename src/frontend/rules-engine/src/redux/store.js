import { configureStore } from '@reduxjs/toolkit'
import alertReducer from './slices/global/alert';
import dashboardReducer from './slices/global/dashboard';
import campaignsReducer from './slices/tabs/campaigns/campaigns';
import campaignReducer from './slices/tabs/campaigns/campaign'
import authReducer from './slices/global/auth'
import confirmDialogReducer from './slices/global/confirmation';


  /** 
  * Redux store state object.
  * @type {import("../../jsDocs/redux/store.js).AllObjectorJSDOC}
  */
export const store = configureStore({
  reducer: {
    alerts: alertReducer,
    dashboard: dashboardReducer,
    campaigns: campaignsReducer,
    campaign:campaignReducer,
    auth: authReducer,
    confirmDialog: confirmDialogReducer
  },
})




