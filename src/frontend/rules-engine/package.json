{"name": "ruleforage_frontent", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "coverage": "vitest run --coverage"}, "dependencies": {"@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/icons-material": "^6.1.1", "@mui/material": "^6.1.1", "@mui/x-date-pickers": "^7.19.0", "@reduxjs/toolkit": "^2.2.7", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "chart.js": "^4.4.6", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "i": "^0.3.7", "npm": "^10.9.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-redux": "^9.1.2", "react-router-dom": "^6.26.2", "validator": "^13.12.0"}, "devDependencies": {"@eslint/js": "^9.9.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.1", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-c8": "^0.33.0", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "jsdoc": "^4.0.4", "jsdom": "^25.0.1", "vite": "^5.4.1"}}