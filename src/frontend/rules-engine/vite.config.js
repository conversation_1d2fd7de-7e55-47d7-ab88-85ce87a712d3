/** 
 * @vitest-environment jsdom
 */
/// <reference types="vitest" />
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  test: {
    // root:"tests", // Updated path for src/tests
    globals: true,
    environment: 'jsdom',
    setupFiles: './tests/setup.js',  // Updated path for src/tests
    coverage: {
      provider: 'c8',
      reporter: ['text', 'json', 'html'],
    },
    include: ['tests/**/*.{test,spec}.{js,jsx,ts,tsx}'], // Updated to look in src
    exclude: ['node_modules', 'dist', '.idea', '.git', '.cache']
  },
  // resolve: {
  //   alias: {
  //     '@': path.resolve(__dirname, './src')
  //   }
  // }
})