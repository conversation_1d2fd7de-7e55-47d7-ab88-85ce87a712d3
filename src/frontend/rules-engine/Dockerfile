# Stage 1: Build the app with all dependencies (including devDependencies)
FROM node:18-alpine AS build

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json to the container
COPY package*.json ./

# # Remove any existing node_modules and package-lock.json, then install all dependencies (including devDependencies for building the app)
RUN rm -rf node_modules package-lock.json && npm install


# Copy the rest of the application code
COPY . .

# Build the app for production
RUN npm run build

# Stage 2: Serve the production build using nginx
FROM nginx:stable-alpine

# Copy the build output from the previous stage (dist folder) to nginx's html directory
COPY --from=build /app/dist /usr/share/nginx/html

# Expose port 80 for nginx to serve the app
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]