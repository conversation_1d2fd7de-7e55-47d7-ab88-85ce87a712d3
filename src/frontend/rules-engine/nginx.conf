worker_processes 1;

events {
  worker_connections 1024;
}

http {
  include       mime.types;
  default_type  application/octet-stream;

  sendfile        on;
  keepalive_timeout 65;

  server {
    listen 80;

    # Serve static files
    root /usr/share/nginx/html;
    index index.html index.htm;

    # Route to rules engine backend
    location /ruleforge/ {
      proxy_pass https://ruleforge_cre_service:3000/;
      proxy_http_version 1.1;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # Route to user management backend
    location /ums/ {
      proxy_pass http://rulesforge_user_service:5000/;
      proxy_http_version 1.1;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # Fallback for frontend routes (SPA)
    location / {
      try_files $uri $uri/ /index.html;
    }
  }
}

