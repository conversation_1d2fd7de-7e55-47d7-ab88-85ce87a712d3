import campaignValidator  from '../../../src/utils/validators/campaign';
import { describe, it, expect } from "vitest";
import validator from "validator";
import { describe, it, expect } from 'vitest';
import campaignValidators from '../../../src/utils/validators/campaign'

describe('campaignValidators.global', () => {
  describe('isNotEmpty', () => {
    it('should return success for non-empty string', () => {
      expect(campaignValidators.global.isNotEmpty("Test")).toEqual({ success: true, message: "" });
    });

    it('should return error for empty string', () => {
      expect(campaignValidators.global.isNotEmpty("")).toEqual({ success: false, message: "Value cannot be empty." });
    });
  });

  describe('isValidAllFields', () => {
    it('should return success for all valid fields', () => {
      const fields = { field1: { isValid: true }, field2: { isValid: true } };
      expect(campaignValidators.global.isValidAllFields(fields).success).toBe(true);
    });

    it('should return errors for invalid fields', () => {
      const fields = {
        field1: { isValid: false, error: "Error in field1" },
        field2: { isValid: true },
      };
      const result = campaignValidators.global.isValidAllFields(fields);
      expect(result.success).toBe(false);
      expect(result.errors).toEqual([{ field: "field1", error: "Error in field1" }]);
    });
  });
 
  describe('isBeforeOrEqual', () => {
    it('should return success for valid date order', () => {
      expect(campaignValidators.global.isBeforeOrEqual("2023-01-01", "2023-12-31")).toEqual({ success: true, message: "" });
    });

    it('should return error for invalid date order', () => {
      expect(campaignValidators.global.isBeforeOrEqual("2023-12-31", "2023-01-01")).toEqual({ success: false, message: "Start date must be before or equal to end date." });
    });
  }); 

  describe('isISO8601Date', () => {
    it('should return success for valid ISO 8601 date', () => {
      expect(campaignValidators.global.isISO8601Date("2023-12-31")).toEqual({ success: true, message: "" });
    });

    it('should return error for invalid ISO 8601 date', () => {
      expect(campaignValidators.global.isISO8601Date("12/31/2023")).toEqual({ success: false, message: "Date must be in ISO 8601 format (YYYY-MM-DD)." });
    });
  });

  describe('isLengthValid', () => {
    it('should return success for valid string length', () => {
      expect(campaignValidators.global.isLengthValid("Test", "abc", 1, 5)).toEqual({ success: true, message: "" });
    });

    it('should return error for string too short', () => {
      expect(campaignValidators.global.isLengthValid("Test", "a", 2, 5)).toEqual({ success: false, message: "Test must be between 2 and 5 characters." });
    });

    it('should return error for string too long', () => {
      expect(campaignValidators.global.isLengthValid("Test", "abcdef", 1, 5)).toEqual({ success: false, message: "Test must be between 1 and 5 characters." });
    });
  });

  describe('noSpaces', () => {
    it('should return success if value has no spaces', () => {
      expect(campaignValidators.global.noSpaces("Value", "TestValue")).toEqual({ success: true, message: "" });
    });

    it('should return error if value contains spaces', () => {
      expect(campaignValidators.global.noSpaces("Value", "Test Value")).toEqual({ success: false, message: "Value must not contain spaces." });
    });
  });
});

describe('campaignValidators.details', () => {
  describe('name', () => {
    it('should validate campaign name length', () => {
      expect(campaignValidators.details.name("Campaign Name")).toEqual({ success: true, message: "" });
      expect(campaignValidators.details.name("A")).toEqual({ success: false, message: "Name must be between 5 and 100 characters." });
    });
  });

  describe('description', () => {
    it('should validate campaign description length', () => {
      expect(campaignValidators.details.description("This is a campaign description")).toEqual({ success: true, message: "" });
      expect(campaignValidators.details.description("")).toEqual({ success: true, message: "" });
    });
  });

  describe('date', () => {
    it('should validate campaign date format', () => {
      expect(campaignValidators.details.date("2023-01-01")).toEqual({ success: true, message: "" });
      expect(campaignValidators.details.date("01-01-2023")).toEqual({ success: false, message: "Date must be in ISO 8601 format (YYYY-MM-DD)." });
    });
  });

  describe('dates', () => {
    it('should validate start and end date format and order', () => {
      expect(campaignValidators.details.dates("2023-01-01", "2023-12-31")).toEqual({ success: true, message: "" });
      expect(campaignValidators.details.dates("2023-12-31", "2023-01-01")).toEqual({ success: false, message: "Start date must be before or equal to end date." });
    });
  });
});

describe('campaignValidators.colMapping', () => {
  describe('collectionName', () => {
    it('should validate collection name length and no spaces', () => {
      expect(campaignValidators.colMapping.collectionName("Collection1")).toEqual({ success: true, message: "" });
      expect(campaignValidators.colMapping.collectionName("")).toEqual({ success: false, message: "Collection name must be between 1 and 50 characters." });
    });
  });

  describe('propertyName', () => {
    it('should validate property name length', () => {
      expect(campaignValidators.colMapping.propertyName("PropertyName")).toEqual({ success: true, message: "" });
      expect(campaignValidators.colMapping.propertyName("")).toEqual({ success: false, message: "Property Name must be between 1 and 100 characters." });
    });
  });
});

describe('campaignValidators.variables', () => {
  describe('name', () => {
    it('should validate variable name length', () => {
      expect(campaignValidators.variables.name("Variable")).toEqual({ success: true, message: "" });
      expect(campaignValidators.variables.name("")).toEqual({ success: false, message: "Name must be between 1 and 100 characters." });
    });
  });

  describe('description', () => {
    it('should validate variable description length', () => {
      expect(campaignValidators.variables.description("Variable Description")).toEqual({ success: true, message: "" });
      expect(campaignValidators.variables.description("")).toEqual({ success: false, message: "Description must be between 1 and 250 characters." });
    });
  });

  describe('type', () => {
    it('should validate variable type', () => {
      expect(campaignValidators.variables.type("string")).toEqual({ success: true, message: "" });
      expect(campaignValidators.variables.type("unknown")).toEqual({ success: false, message: "Invalid type. Allowed types: integer, amount, float, string, boolean, date" });
    });
  });

  describe('defaultValue', () => {
    it('should return success for defaultValue validation placeholder', () => {
      expect(campaignValidators.variables.defaultValue("Default Value", "string")).toEqual({ success: true, message: null });
    });
  });
});

describe('campaignValidators.entities', () => {
  describe('ruleName', () => {
    it('should validate rule name length', () => {
      expect(campaignValidators.entities.ruleName("Rule Name")).toEqual({ success: true, message: "" });
      expect(campaignValidators.entities.ruleName("AB")).toEqual({ success: false, message: "Rule Name must be between 3 and 100 characters." });
    });
  });

  describe('ruleDescription', () => {
    it('should validate rule description length', () => {
      expect(campaignValidators.entities.ruleDescription("Rule Description")).toEqual({ success: true, message: "" });
      expect(campaignValidators.entities.ruleDescription("")).toEqual({ success: true, message: "" });
    });
  });

  describe('actionParameter', () => {
    it('should validate action parameter length', () => {
      expect(campaignValidators.entities.actionParameter("Parameter")).toEqual({ success: true, message: "" });
      expect(campaignValidators.entities.actionParameter("AB")).toEqual({ success: false, message: "Parameter  must be between 3 and 500 characters." });
    });
  });
});
