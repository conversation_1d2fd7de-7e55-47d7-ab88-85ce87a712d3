import validator from "validator";
import {authValidators} from "../../../src/utils/validators/auth.js";

import { describe, it, expect } from "vitest";

describe("authValidators", () => {
  describe("global.email", () => {
    it("should return success for a valid email", () => {
      const result = authValidators.global.email("<EMAIL>");
      expect(result).toEqual({ success: true, message: "" });
    });

    it("should return error for an invalid email", () => {
      const result = authValidators.global.email("invalid-email");
      expect(result).toEqual({
        success: false,
        message: "Invalid email address.",
      });
    });

    it("should return error for an empty email", () => {
      const result = authValidators.global.email("");
      expect(result).toEqual({
        success: false,
        message: "Invalid email address.",
      });
    });
  });

  describe("global.password", () => {
    it("should return success for a valid password", () => {
      const result = authValidators.global.password("Valid123!");
      expect(result).toEqual({ success: true, message: "" });
    });

    it("should return error if password is less than 8 characters", () => {
      const result = authValidators.global.password("Short1!");
      expect(result).toEqual({
        success: false,
        message: "Password must be at least 8 characters long.",
      });
    });

    it("should return error if password lacks uppercase letter", () => {
      const result = authValidators.global.password("lowercase1!");
      expect(result).toEqual({
        success: false,
        message: "Password must contain at least one uppercase letter.",
      });
    });

    it("should return error if password lacks lowercase letter", () => {
      const result = authValidators.global.password("UPPERCASE1!");
      expect(result).toEqual({
        success: false,
        message: "Password must contain at least one lowercase letter.",
      });
    });

    it("should return error if password lacks special character", () => {
      const result = authValidators.global.password("NoSpecial123");
      expect(result).toEqual({
        success: false,
        message: "Password must contain at least one special character.",
      });
    });

    it("should return error for empty password", () => {
      const result = authValidators.global.password("");
      expect(result).toEqual({
        success: false,
        message: "Password must be at least 8 characters long.",
      });
    });
  });

  describe("global.name", () => {
    it("should return success for a valid name", () => {
      const result = authValidators.global.name("John");
      expect(result).toEqual({ success: true, message: "" });
    });

    it("should return error if name is empty", () => {
      const result = authValidators.global.name("");
      expect(result).toEqual({
        success: false,
        message: "Name must be at least 1 characters long.",
      });
    });

    it("should return success for a name that is exactly 1 character long", () => {
      const result = authValidators.global.name("A");
      expect(result).toEqual({ success: true, message: "" });
    });
  });

  describe("global.username", () => {
  
    it("should return success for a valid username", () => {
      const result = authValidators.global.username("User123");
      expect(result).toEqual({ success: true, message: null });
    });
  
    it("should return an error if the username contains special characters", () => {
      const result = authValidators.global.username("User!@#");
      expect(result).toEqual({
        success: false,
        message: "Username must contain only letters and numbers.",
      });
    });
  
    it("should return an error if the username is less than 3 characters", () => {
      const result = authValidators.global.username("Us");
      expect(result).toEqual({
        success: false,
        message: "Username must be at least 3 characters long.",
      });
    });
  
    it("should return an error if the username is empty", () => {
      const result = authValidators.global.username("");
      expect(result).toEqual({
        success: false,
        message: "Username is required and must be a string.",
      });
    });
  
    it("should return an error if the username starts with a number", () => {
      const result = authValidators.global.username("1User123");
      expect(result).toEqual({
        success: false,
        message: "Username must start with a letter.",
      });
    });
  
    it("should return an error if the username is not a string", () => {
      const result = authValidators.global.username(12345); // Pass a number instead of a string
      expect(result).toEqual({
        success: false,
        message: "Username is required and must be a string.",
      });
    });
  });
  

  // describe("isLoginFormValid", () => {
  //   it("should return success for valid username and password", () => {
  //     const result = authValidators.isLoginFormValid({
  //       username: "ValidUser123",
  //       password: "ValidPass123!",
  //     });
  //     expect(result).toEqual({ success: true, message: null });
  //   });

  //   it("should return error for invalid username", () => {
  //     const result = authValidators.isLoginFormValid({
  //       username: "us", // too short
  //       password: "ValidPass123!",
  //     });
  //     expect(result).toEqual({
  //       success: false,
  //       message:
  //         "Username must be at least 3 characters long and contain only letters and numbers.",
  //     });
  //   });

  //   it("should return error for invalid password", () => {
  //     const result = authValidators.isLoginFormValid({
  //       username: "ValidUser123",
  //       password: "short",
  //     });
  //     expect(result).toEqual({
  //       success: false,
  //       message: "Password must be at least 8 characters long.",
  //     });
  //   });
  // });
 

  describe("isLoginFormValid", () => {
  
    it("should return success for valid username and password", () => {
      const result = authValidators.isLoginFormValid({
        username: "ValidUser123",
        password: "ValidPass123!",
      });
      expect(result).toEqual({ success: true, message: null });
    });
  
    it("should return error if the username is too short", () => {
      const result = authValidators.isLoginFormValid({
        username: "us", // too short
        password: "ValidPass123!",
      });
      expect(result).toEqual({
        success: false,
        message: "Username must be at least 3 characters long.",
      });
    });
  
    it("should return error if the username contains special characters", () => {
      const result = authValidators.isLoginFormValid({
        username: "User@123",
        password: "ValidPass123!",
      });
      expect(result).toEqual({
        success: false,
        message: "Username must contain only letters and numbers.",
      });
    });
  
    it("should return error if the username starts with a number", () => {
      const result = authValidators.isLoginFormValid({
        username: "1ValidUser",
        password: "ValidPass123!",
      });
      expect(result).toEqual({
        success: false,
        message: "Username must start with a letter.",
      });
    }); 
  
    it("should return error if the username is empty", () => {
      const result = authValidators.isLoginFormValid({
        username: "",
        password: "ValidPass123!",
      });
      expect(result).toEqual({
        success: false,
        message: "Username is required and must be a string.",
      });
    });
  
    it("should return error for an invalid password", () => {
      const result = authValidators.isLoginFormValid({
        username: "ValidUser123",
        password: "short", // Password too short
      });
      expect(result).toEqual({
        success: false,
        message: "Password must be at least 8 characters long.",
      });
    });
  });
  
  describe("isRegisterFormValid", () => {
    it("should return success for valid registration details", () => {
      const result = authValidators.isRegisterFormValid({
        name: "John Doe",
        email: "<EMAIL>",
        password: "ValidPass123!",
        cPassword: "ValidPass123!",
      });
      expect(result).toEqual({ success: true, message: null });
    });

    it("should return error if name is invalid", () => {
      const result = authValidators.isRegisterFormValid({
        name: "",
        email: "<EMAIL>",
        password: "ValidPass123!",
        cPassword: "ValidPass123!",
      });
      expect(result).toEqual({
        success: false,
        message: "Name must be at least 1 characters long.",
      });
    });

    it("should return error if email is invalid", () => {
      const result = authValidators.isRegisterFormValid({
        name: "John Doe",
        email: "invalid-email",
        password: "ValidPass123!",
        cPassword: "ValidPass123!",
      });
      expect(result).toEqual({
        success: false,
        message: "Invalid email address.",
      });
    });

    it("should return error if password is invalid", () => {
      const result = authValidators.isRegisterFormValid({
        name: "John Doe",
        email: "<EMAIL>",
        password: "short",
        cPassword: "short",
      });
      expect(result).toEqual({
        success: false,
        message: "Password must be at least 8 characters long.",
      });
    });

    it("should return error if password and confirm password do not match", () => {
      const result = authValidators.isRegisterFormValid({
        name: "John Doe",
        email: "<EMAIL>",
        password: "ValidPass123!",
        cPassword: "DifferentPass!",
      });
      expect(result).toEqual({
        success: false,
        message: "Password and confirm password are not same!",
      });
    });
  });

  describe("isForgotPasswordFormValid", () => {
    it("should return success for a valid email", () => {
      const result = authValidators.isForgotPasswordFormValid({
        email: "<EMAIL>",
      });
      expect(result).toEqual({ success: true, message: null });
    });

    it("should return error for an invalid email", () => {
      const result = authValidators.isForgotPasswordFormValid({
        email: "invalid-email",
      });
      expect(result).toEqual({
        success: false,
        message: "Invalid email address.",
      });
    });

    it("should return error for an empty email", () => {
      const result = authValidators.isForgotPasswordFormValid({
        email: "",
      });
      expect(result).toEqual({
        success: false,
        message: "Invalid email address.",
      });
    });
  });

  describe("isResetPasswordFormValid", () => {
    it("should return success for matching and valid passwords", () => {
      const result = authValidators.isResetPasswordFormValid({
        password: "ValidPass123!",
        cPassword: "ValidPass123!",
      });
      expect(result).toEqual({ success: true, message: null });
    });

    it("should return error for an invalid password", () => {
      const result = authValidators.isResetPasswordFormValid({
        password: "short",
        cPassword: "short",
      });
      expect(result).toEqual({
        success: false,
        message: "Password must be at least 8 characters long.",
      });
    });

    it("should return error if passwords do not match", () => {
      const result = authValidators.isResetPasswordFormValid({
        password: "ValidPass123!",
        cPassword: "DifferentPass!",
      });
      expect(result).toEqual({
        success: false,
        message: "Password and confirm password are not same!",
      });
    });
  });
});
