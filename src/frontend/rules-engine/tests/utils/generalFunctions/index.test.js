import { describe, it, expect } from 'vitest'
import bcrypt from "bcryptjs";
import dayjs from "dayjs";
import {Helpers} from '../../../src/utils/generalFunctions/index'

describe('Helpers', () => {
  describe('defaultValues', () => {
    it('should create default form fields with empty values', () => {
      const keys = ['name', 'email']
      const result = Helpers.defaultValues(keys)

      expect(result).toEqual({
        name: {
          value: '',
          isValid: false,
          error: 'name is required',
          isShowError: false
        },
        email: {
          value: '',
          isValid: false,
          error: 'email is required',
          isShowError: false
        }
      })
    })

    it('should handle initial values correctly', () => {
      const keys = ['name', 'email']
      const values = { name: '<PERSON>', email: '<EMAIL>' }
      const result = Helpers.defaultValues(keys, values)

      expect(result).toEqual({
        name: {
          value: '<PERSON>',
          isValid: true,
          error: 'name is required',
          isShowError: false
        },
        email: {
          value: '<EMAIL>',
          isValid: true,
          error: 'email is required',
          isShowError: false
        }
      })
    })

    it('should handle optional fields correctly', () => {
      const keys = ['name', 'email', 'description']
      const values = { name: 'John Doe' }
      const optionalKeys = ['description']
      const result = Helpers.defaultValues(keys, values, optionalKeys)

      expect(result.description.isValid).toBe(true)
      expect(result.email.isValid).toBe(false)
      expect(result.name.isValid).toBe(true)
    })

    it('should handle null values', () => {
      const keys = ['name']
      const values = { name: null }
      const result = Helpers.defaultValues(keys, values)

      expect(result.name.value).toBe('')
      expect(result.name.isValid).toBe(false)
    })
  })

  describe('returnObj', () => {
    it('should return success object with data', () => {
      const result = Helpers.returnObj(true, { id: 1 })
      expect(result).toEqual({
        success: true,
        data: { id: 1 }
      })
    })

    it('should return failure object with error data', () => {
      const result = Helpers.returnObj(false, 'Error message')
      expect(result).toEqual({
        success: false,
        data: 'Error message'
      })
    })
  })

  describe('returnValues', () => {
    it('should extract only values from form fields', () => {
      const inputs = {
        name: {
          value: '  John Doe  ',
          isValid: true,
          error: '',
          isShowError: false
        },
        email: {
          value: '<EMAIL>',
          isValid: true,
          error: '',
          isShowError: false
        }
      }

      const result = Helpers.returnValues(inputs)
      expect(result).toEqual({
        name: 'John Doe',
        email: '<EMAIL>'
      })
    })

    it('should handle empty values', () => {
      const inputs = {
        name: {
          value: '',
          isValid: false,
          error: 'name is required',
          isShowError: true
        }
      }

      const result = Helpers.returnValues(inputs)
      expect(result).toEqual({
        name: ''
      })
    })
  })

  describe('returnArrayValues', () => {
    it('should transform array of objects with data property', () => {
      const inputs = [
        {
          data: {
            id: '1',
            name: 'John'
          }
        },
        {
          data: {
            id: '2',
            name: 'Jane'
          }
        }
      ]

      const result = Helpers.returnArrayValues(inputs)
      expect(result).toEqual([
        {
          id: '1',
          name: 'John'
        },
        {
          id: '2',
          name: 'Jane'
        }
      ])
    })

    it('should handle empty array', () => {
      const result = Helpers.returnArrayValues([])
      expect(result).toEqual([])
    })
  })

  describe('nameIdGenerator', () => {
    it('should convert a name to uppercase and replace spaces with underscores', () => {
      const result = Helpers.nameIdGenerator('John Doe');
      expect(result).toBe('JOHN_DOE');
    });

    it('should handle a name with multiple spaces', () => {
      const result = Helpers.nameIdGenerator('John    Doe');
      expect(result).toBe('JOHN_DOE');
    });

    it('should handle an empty string gracefully', () => {
      const result = Helpers.nameIdGenerator('');
      expect(result).toBe('');
    });

    it('should handle special characters by leaving them unchanged', () => {
      const result = Helpers.nameIdGenerator('John@Doe!');
      expect(result).toBe('JOHN@DOE!');
    });
  });
 
  describe('idGenerator', () => {
    it('should generate an uppercase id with underscores as default', () => {
      const result = Helpers.idGenerator('John Doe');
      expect(result).toBe('JOHN_DOE');
    });

    it('should convert to lowercase when caseFormat is "lower"', () => {
      const result = Helpers.idGenerator('John Doe', 'lower');
      expect(result).toBe('john_doe');
    });

    it('should capitalize each word when caseFormat is "capitalize"', () => {
      const result = Helpers.idGenerator('john doe', 'capitalize');
      expect(result).toBe('John Doe');
    });

    it('should generate camel case id', () => {
      const result = Helpers.idGenerator('john doe', 'camel');
      expect(result).toBe('johnDoe');
    });

    it('should generate pascal case id', () => {
      const result = Helpers.idGenerator('john doe', 'pascal');
      expect(result).toBe('JohnDoe');
    });

    it('should generate kebab case id', () => {
      const result = Helpers.idGenerator('John Doe', 'kebab');
      expect(result).toBe('john-doe');
    });

    it('should generate snake case id', () => {
      const result = Helpers.idGenerator('John Doe', 'snake');
      expect(result).toBe('john_doe');
    });

    it('should replace spaces with custom separator', () => {
      const result = Helpers.idGenerator('John Doe', 'upper', { separator: '-' });
      expect(result).toBe('JOHN-DOE');
    });

    it('should limit the length of the result', () => {
      const result = Helpers.idGenerator('John Doe', 'upper', { maxLength: 4 });
      expect(result).toBe('JOHN');
    });

    it('should remove special characters if specified', () => {
      const result = Helpers.idGenerator('John@Doe!', 'upper', { removeSpecialChars: true });
      expect(result).toBe('JOHNDOE');
    });

    it('should retain special characters if removeSpecialChars is false', () => {
      const result = Helpers.idGenerator('John@Doe!', 'upper', { removeSpecialChars: false });
      expect(result).toBe('JOHN@DOE!');
    });

    it('should return an empty string if name is empty', () => {
      const result = Helpers.idGenerator('', 'upper');
      expect(result).toBe('');
    });
  });
   describe('updateInputField', () => {
    it('should update an existing field with new value and valid status', () => {
      const inputs = {
        name: { value: 'John', isValid: true, isShowError: false, error: '' }
      };
      const update = { name: 'name', value: 'Jane Doe', status: { success: true } };

      const result = Helpers.updateInputField(inputs, update);
 
      expect(result).toEqual({
        name: { value: 'Jane Doe', isValid: true, isShowError: false, error: '' }
      });
    });

    it('should mark field as invalid and set error message if status is unsuccessful', () => {
      const inputs = {
        email: { value: '', isValid: false, isShowError: false, error: '' }
      };
      const update = {
        name: 'email',
        value: 'invalid_email',
        status: { success: false, error: 'Invalid email format' }
      };

      const result = Helpers.updateInputField(inputs, update);

      expect(result).toEqual({
        email: { value: 'invalid_email', isValid: false, isShowError: true, error: 'Invalid email format' }
      });
    });

    it('should handle missing status fields and fallback to default', () => {
      const inputs = { password: { value: '', isValid: false, isShowError: true, error: '' } };
      const update = { name: 'password', value: 'new_password' };

      const result = Helpers.updateInputField(inputs, update);

      expect(result).toEqual({
        password: { value: 'new_password', isValid: true, isShowError: false, error: '' }
      });
    });
  }); 

  describe('tabStatusColor', () => {
    it('should return "green" when option array is non-empty for specific tab names', () => {
      const result = Helpers.tabStatusColor('collectionMappings', ['item']);
      expect(result).toBe('green');
    });

    it('should return "grey" when option array is empty for specific tab names', () => {
      const result = Helpers.tabStatusColor('persistentVariableDefinitions', []);
      expect(result).toBe('grey');
    });

    it('should return "grey" for unknown tab names', () => {
      const result = Helpers.tabStatusColor('unknownTab', ['item']);
      expect(result).toBe('grey');
    });
  });

  describe('handleLogin', () => {
    it('should return success and user data for valid credentials', () => {
      const mockUsername = 'testuser';
      const mockPassword = 'SecurePass123!';
      const result = Helpers.handleLogin({ username: mockUsername, password: mockPassword });

      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('isAuthenticated', true);
      expect(result.data).toHaveProperty('token');
      expect(result.data.user).toHaveProperty('username', 'admin');
    });

    it('should return failure for invalid credentials', () => {
      const result = Helpers.handleLogin({ username: 'wrongUser', password: 'wrongPass' });

      expect(result.success).toBe(false);
      expect(result.data).toBe('Invalid username or password');
    });
  });

  describe('generateEntitiesOptions', () => {
    it('should generate dropdown options for entities', () => {
      const entities = [{ entityId: '1', entityName: 'Entity1' }, { entityId: '2', entityName: 'Entity2' }];

      const result = Helpers.generateEntitiesOptions(entities);

      expect(result).toEqual([
        { value: '1', label: 'Entity1' },
        { value: '2', label: 'Entity2' }
      ]);
    });

    it('should handle an empty array gracefully', () => {
      const result = Helpers.generateEntitiesOptions([]);
      expect(result).toEqual([]);
    });
  });

  describe('generateContextOptions', () => {
    it('should generate dropdown options for transaction contexts within an entity', () => {
      const entity = {
        transactionContexts: [
          { contextId: 'ctx1', contextName: 'Context 1' },
          { contextId: 'ctx2', contextName: 'Context 2' }
        ]
      };

      const result = Helpers.generateContextOptions(entity);

      expect(result).toEqual([
        { value: 'ctx1', label: 'Context 1' },
        { value: 'ctx2', label: 'Context 2' }
      ]);
    });

    it('should return an empty array if entity is undefined', () => {
      const result = Helpers.generateContextOptions(undefined);
      expect(result).toEqual([]);
    });
  });
  describe('generateVariableDropdownOptions', () => {
    it('should generate dropdown options for variables array', () => {
      const variables = [
        { variableId: '1', name: 'Variable 1' },
        { variableId: '2', name: 'Variable 2' }
      ];

      const result = Helpers.generateVariableDropdownOptions(variables);

      expect(result).toEqual([
        { value: '1', label: 'Variable 1' },
        { value: '2', label: 'Variable 2' }
      ]);
    });

    it('should handle empty array gracefully', () => {
      const result = Helpers.generateVariableDropdownOptions([]);
      expect(result).toEqual([]);
    });
  });

  describe('handleInputChangeFactoryFn', () => {
    it('should update state with valid input and no error', () => {
      const setData = vi.fn();
      const handleChange = Helpers.handleInputChangeFactoryFn(setData);

      const event = { target: { name: 'username', value: 'JohnDoe' } };
      const status = { success: true };

      handleChange(event, status);

      expect(setData).toHaveBeenCalledWith(expect.any(Function));
      setData((prev) => {
        expect(prev.username).toEqual({
          value: 'JohnDoe',
          isValid: true,
          error: null,
          isShowError: false
        });
      });
    });

    it('should update state with invalid input and set error message', () => {
      const setData = vi.fn();
      const handleChange = Helpers.handleInputChangeFactoryFn(setData);

      const event = { target: { name: 'email', value: '' } };
      const status = { success: false, error: 'Email is required' };

      handleChange(event, status);

      expect(setData).toHaveBeenCalledWith(expect.any(Function));
      setData((prev) => {
        expect(prev.email).toEqual({
          value: '',
          isValid: false,
          error: 'Email is required',
          isShowError: true
        });
      });
    });
  });

  describe('handleDropdownChangeFactoryFn', () => {
    it('should update state for dropdown change and trigger callback if provided', () => {
      const setData = vi.fn();
      const callback = vi.fn();
      const handleDropdownChange = Helpers.handleDropdownChangeFactoryFn(setData, callback);

      handleDropdownChange('country', 'USA');

      expect(setData).toHaveBeenCalledWith(expect.any(Function));
      setData((prev) => {
        expect(prev.country).toEqual({
          value: 'USA',
          isValid: true,
          error: null,
          isShowError: false
        });
      });
      expect(callback).toHaveBeenCalledWith('country', 'USA');
    });

    it('should update state for dropdown change without callback', () => {
      const setData = vi.fn();
      const handleDropdownChange = Helpers.handleDropdownChangeFactoryFn(setData);

      handleDropdownChange('city', 'New York');

      expect(setData).toHaveBeenCalledWith(expect.any(Function));
      setData((prev) => {
        expect(prev.city).toEqual({
          value: 'New York',
          isValid: true,
          error: null,
          isShowError: false
        });
      });
    });
  });

  describe('parseDateTime', () => {
    it('should return both date and time when returnType is "both"', () => {
      const dateTimeStr = '2024-11-10T12:00:00';
      const result = Helpers.parseDateTime(dateTimeStr, 'both', {
        date: 'YYYY-MM-DD',
        time: 'HH:mm:ss'
      });

      expect(result).toEqual({
        date: '2024-11-10',
        time: '12:00:00'
      });
    });

    it('should return only date when returnType is "date"', () => {
      const dateTimeStr = '2024-11-10T12:00:00';
      const result = Helpers.parseDateTime(dateTimeStr, 'date', {
        date: 'YYYY-MM-DD'
      });

      expect(result).toBe('2024-11-10');
    });

    it('should return only time when returnType is "time"', () => {
      const dateTimeStr = '2024-11-10T12:00:00';
      const result = Helpers.parseDateTime(dateTimeStr, 'time', {
        time: 'HH:mm:ss'
      });

      expect(result).toBe('12:00:00');
    });
  });

  describe('validateCampaign', () => {
    it('should return success with valid campaign data', () => {
      const campaign = {
        name: 'Campaign1',
        description: 'Description of campaign',
        startDateTime: '2024-01-01T00:00:00',
        endDateTime: '2024-12-31T23:59:59'
      };
      const result = Helpers.validateCampaign(campaign);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(campaign);
    });

    it('should return errors for missing campaign name and description', () => {
      const campaign = {
        startDateTime: '2024-01-01T00:00:00',
        endDateTime: '2024-12-31T23:59:59'
      };
      const result = Helpers.validateCampaign(campaign);

      expect(result.success).toBe(false);
      expect(result.data).toEqual({
        name: 'Campaign name is required',
        description: 'Campaign description is required'
      });
    });

    it('should return error for missing start and end dates', () => {
      const campaign = {
        name: 'Campaign1',
        description: 'Description of campaign'
      };
      const result = Helpers.validateCampaign(campaign);

      expect(result.success).toBe(false);
      expect(result.data).toEqual({
        startDateTime: 'Start date is required',
        endDateTime: 'End date is required'
      });
    });
  });
  describe('prepareCampaign', () => {
    it('should prepare a campaign object for creating with initial properties', () => {
      const campaign = { name: 'Test Campaign', startDateTime: '2024-11-10T12:00:00', endDateTime: '2024-11-11T12:00:00' };
      const result = Helpers.prepareCampaign(campaign, 'creating');

      expect(result).toMatchObject({
        name: 'Test Campaign',
        campaignId: expect.any(String),
        campaignVersion: 1,
        schemaVersion: '2.4.0',
        status: 'ACTIVE'
      });
    });

    it('should increment campaign version on updating', () => {
      const campaign = { campaignVersion: 2, name: 'Existing Campaign' };
      const result = Helpers.prepareCampaign(campaign, 'updating');

      expect(result.campaignVersion).toBe(3);
    });
  });

  describe('createRowsUtil', () => {
    it('should add a new item with handleAddNewItem', () => {
      const itemsArray = [];
      const setItems = vi.fn();
      const Alert = vi.fn();
      const { handleAddNewItem } = Helpers.createRowsUtil(itemsArray, setItems, 'Test', Alert);

      handleAddNewItem();
      expect(setItems).toHaveBeenCalledWith(expect.any(Function));
    });

    it('should not add multiple new items with same id', () => {
      const itemsArray = [{ id: 'newTEST' }];
      const setItems = vi.fn();
      const Alert = vi.fn();
      const { handleAddNewItem } = Helpers.createRowsUtil(itemsArray, setItems, 'Test', Alert);

      handleAddNewItem();
      expect(Alert).toHaveBeenCalledWith('Cannot add multiple new Tests', 'error');
    });
  });

  describe('createColumnsUtil', () => {
    const columns = [
      { label: 'Column 1', gridSize: 6, content: { key: 'field1', type: 'value' } },
      { label: 'Column 2', gridSize: 6, content: { key: 'field2', type: 'arrayLength', def: 0 } }
    ];

    it('should generate column headers correctly', () => {
      const { columnsOfSectionHeader } = Helpers.createColumnsUtil(columns);
      expect(columnsOfSectionHeader).toEqual([
        { label: 'Column 1', gridSize: 6 },
        { label: 'Column 2', gridSize: 6 }
      ]);
    });

    it('should generate item content columns with default values if item is empty', () => {
      const { generateItemContentColumns } = Helpers.createColumnsUtil(columns);
      const result = generateItemContentColumns({ field1: 'test', field2: [] });

      expect(result).toEqual([
        { content: 'test', gridSize: 6 },
        { content: 0, gridSize: 6 }
      ]);
    });
  });

  describe('generateVersion', () => {
    it('should generate version 1.0.0 when autoInitialize is true and no version is provided', () => {
      const result = Helpers.generateVersion({ autoInitialize: true });
      expect(result).toBe('1.0.0');
    });

    it('should increment patch version correctly', () => {
      const result = Helpers.generateVersion({ currentVersion: '1.0.0', incrementType: 'patch' });
      expect(result).toBe('1.0.1');
    });

    it('should increment minor version correctly', () => {
      const result = Helpers.generateVersion({ currentVersion: '1.1.0', incrementType: 'minor' });
      expect(result).toBe('1.2.0');
    });

    it('should increment major version correctly', () => {
      const result = Helpers.generateVersion({ currentVersion: '1.0.0', incrementType: 'major' });
      expect(result).toBe('2.0.0');
    });
  });

  // describe('toISO8601WithTime', () => {
  //   it('should convert date to ISO 8601 format', () => {
  //     const dateString = '2024-11-10T12:00:00';
  //     const result = Helpers.toISO8601WithTime(dateString);

  //     expect(result).toBe('2024-11-10T12:00:00.000Z');
  //   });

  //   it('should handle invalid date input', () => {
  //     const dateString = 'invalid-date';
  //     expect(() => Helpers.toISO8601WithTime(dateString)).toThrow();
  //   });
  // });

  describe('isVariableUsed', () => {
    it('should return true if the variable is used in any entity', () => {
      const entities = [
        {
          transactionContexts: [
            {
              rules: [
                {
                  variableOperations: [
                    { variableId: 'var1' },
                    { variableId: 'var2' }
                  ]
                }
              ]
            }
          ]
        }
      ];
      const result = Helpers.isVariableUsed('var1', entities);
      expect(result).toBe(true);
    });

    it('should return false if the variable is not used in any entity', () => {
      const entities = [
        {
          transactionContexts: [
            {
              rules: [
                {
                  variableOperations: [
                    { variableId: 'var3' }
                  ]
                }
              ]
            }
          ]
        }
      ];
      const result = Helpers.isVariableUsed('var1', entities);
      expect(result).toBe(false);
    });

    it('should return false if entities array is empty', () => {
      const entities = [];
      const result = Helpers.isVariableUsed('var1', entities);
      expect(result).toBe(false);
    });
  });

  describe('generateTransContextOptions', () => {
    const entities = [
      {
        entityId: 'entity1',
        transactionContexts: [
          {
            contextId: 'context1',
            properties: [
              { name: 'property1', description: 'Description 1' },
              { name: 'property2' }
            ]
          }
        ]
      }
    ];

    it('should generate options with values enclosed in curly brackets when encloseValInCurlyBrack is true', () => {
      const result = Helpers.generateTransContextOptions(entities, 'entity1', 'context1', true);
      expect(result).toEqual([
        { value: '{property1}', label: 'property1 - Description 1' },
        { value: '{property2}', label: 'property2' }
      ]);
    });

    it('should generate options with plain values when encloseValInCurlyBrack is false', () => {
      const result = Helpers.generateTransContextOptions(entities, 'entity1', 'context1', false);
      expect(result).toEqual([
        { value: 'property1', label: 'property1 - Description 1' },
        { value: 'property2', label: 'property2' }
      ]);
    });

    it('should return an empty array if entityId or contextId does not match', () => {
      const result = Helpers.generateTransContextOptions(entities, 'entity2', 'context2', false);
      expect(result).toEqual([]);
    });
  });

  describe('generateCategoryDropDownAllOptions', () => {
    it('should generate dropdown options for localVariables category', () => {
      const categories = [
        { id: 'localVariables', label: 'Local Vars', data: [] }
      ];
      const campaign = {};
      const result = Helpers.generateCategoryDropDownAllOptions(categories, campaign);

      expect(result).toEqual([
        { value: 'localVariables', label: 'Local Vars' }
      ]);
    });

    it('should use default label if label is missing in localVariables category', () => {
      const categories = [
        { id: 'localVariables', data: [] }
      ];
      const campaign = {};
      const result = Helpers.generateCategoryDropDownAllOptions(categories, campaign);

      expect(result).toEqual([
        { value: 'localVariables', label: 'Local Variables' }
      ]);
    });

    it('should ignore unknown categories and return only supported options', () => {
      const categories = [
        { id: 'localVariables', data: [] },
        { id: 'unknownCategory', data: [] }
      ];
      const campaign = {};
      const result = Helpers.generateCategoryDropDownAllOptions(categories, campaign);

      expect(result).toEqual([
        { value: 'localVariables', label: 'Local Variables' }
      ]);
    });
  });

})