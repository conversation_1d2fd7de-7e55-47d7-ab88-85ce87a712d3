// src/tests/setup.js
import '@testing-library/jest-dom'
import { expect, afterEach } from 'vitest'
import { cleanup } from '@testing-library/react'
import * as matchers from '@testing-library/jest-dom/matchers'

// Extend vitest's expect with @testing-library/jest-dom's matchers
expect.extend(matchers)

// Cleanup after each test
afterEach(() => {
  cleanup()
})

// If you're using Happy DOM
// global.ResizeObserver = vi.fn().mockImplementation(() => ({
//     observe: vi.fn(),
//     unobserve: vi.fn(),
//     disconnect: vi.fn(),
// }))