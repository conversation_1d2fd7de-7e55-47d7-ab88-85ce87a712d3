// src/tests/components/global/atoms/alert/Alert.test.jsx
import { describe, it, expect, afterEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { Provider } from 'react-redux'
import AlertStack from '../../../../../components/global/atoms/alert/Alert.jsx' 
// Import your real store 
import store from '@/redux/store.js'  // Adjust this path to where your store is located

// You might also want to import your alert actions if you want to dispatch them
import { showAlert, dismissAlert } from '@/redux/slices/global/alert.js' // Adjust path as needed

describe('AlertStack with Real Redux Store', () => {
  it('renders alerts when dispatched to real store', () => {
    render(
      <Provider store={store}>
        <AlertStack />
      </Provider>
    )

    // Initially there should be no alerts
    expect(screen.queryByRole('alert')).not.toBeInTheDocument()

    // Dispatch a real alert
    store.dispatch(showAlert({
      id: '1',
      severity: 'success',
      message: 'Test Alert Message'
    }))

    // Check if alert appears
    expect(screen.getByText('Test Alert Message')).toBeInTheDocument()
  })

  it('removes alerts when dispatched', () => {
    render(
      <Provider store={store}>
        <AlertStack />
      </Provider>
    )

    const alertId = '2'
    
    // Add alert
    store.dispatch(showAlert({
      id: alertId,
      severity: 'error',
      message: 'Error Message'
    }))

    // Verify alert is shown
    expect(screen.getByText('Error Message')).toBeInTheDocument()

    // Remove alert
    store.dispatch(dismissAlert(alertId))

    // Verify alert is removed
    expect(screen.queryByText('Error Message')).not.toBeInTheDocument()
  })

  it('handles multiple alerts', () => {
    render(
      <Provider store={store}>
        <AlertStack />
      </Provider>
    )

    // Dispatch multiple alerts
    store.dispatch(showAlert({
      id: '3',
      severity: 'success',
      message: 'Success Message'
    }))

    store.dispatch(showAlert({
      id: '4',
      severity: 'warning',
      message: 'Warning Message'
    }))

    // Verify both alerts are shown
    expect(screen.getByText('Success Message')).toBeInTheDocument()
    expect(screen.getByText('Warning Message')).toBeInTheDocument()
  })

  // Reset store after each test
  afterEach(() => {
    // Clear all alerts from store
    const currentAlerts = store.getState().alerts.alerts
    currentAlerts.forEach(alert => {
      store.dispatch(dismissAlert(alert.id))
    })
  })
})