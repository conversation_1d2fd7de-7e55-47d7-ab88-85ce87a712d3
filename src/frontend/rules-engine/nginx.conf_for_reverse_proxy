events { }

http {
    include       mime.types;
    default_type  application/octet-stream;

    # Define a custom log format to include proxy details
    log_format proxy_log '$remote_addr - $remote_user [$time_local] "$request" '
                         '$status $body_bytes_sent "$http_referer" "$http_user_agent" '
                         'to: $proxy_host$request_uri upstream: $upstream_addr';

    # access_log /var/log/nginx/access.log proxy_log;

    server {
        listen 80;

        # Serve static files for the frontend app
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri /index.html;
        }

        # Reverse proxy /api requests and remove the /api prefix
        location /api/ {
            rewrite ^/api/(.*)$ /$1 break;  # Remove the /api prefix
            proxy_pass http://ruleforge_cre_service:3000/;  # Forward to the Node.js backend
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_cache_bypass $http_upgrade;

            # Enable access log specifically for /api requests (optional)
            # access_log /var/log/nginx/api-access.log proxy_log;
        }
    }
}