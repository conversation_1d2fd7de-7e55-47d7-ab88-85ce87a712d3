/**
 * @Note: It should run inside the docker container of the user-management service.
 * This script is used to create a new API user in the database.
 * It prompts the user for an email and password, and then creates a new API user with the given credentials.
 * It then prints the API token to the console.
 */

import { createInterface } from "readline";
import { initDb } from "../services/database.js";
import { registerUser } from "../services/authService.js";
import { ROLES } from "../config/roles.js";

const rl = createInterface({
  input: process.stdin,
  output: process.stdout,
});

const prompt = (question) => {
  return new Promise((resolve) => {
    rl.question(question, resolve);
  });
};

const createApiUser = async () => {
  try {
    console.log("Initializing database...");
    await initDb();

    // Prompt for credentials
    const email = await prompt("Enter email: ");
    const password = await prompt("Enter password: ");

    // Create the API user
    const response = await registerUser({
      email,
      password,
      role_id: ROLES.API.id,
      role_name: ROLES.API.slug,
      profile_data: {
        full_name: email,
      },
      tenant_id: 1,
    });

    console.log("API User created successfully!");
    
  } catch (error) {
    console.error("\nError creating API user:", error.message, error.stack || "");
  } finally {
    rl.close();
  }
};

// Run the script
createApiUser();
