services:
  rulesforge_users_db:
    image: mariadb:latest
    container_name: rulesforge_users_db
    environment:
      MYSQL_ROOT_PASSWORD: ussdgw
      MYSQL_DATABASE: rulesforge_users
      MYSQL_USER: rulesforge_user_management
      MYSQL_PASSWORD: ussdgw
      MARIADB_USE_TLS: "NO" # Disable TLS
    ports:
      - "3307:3306"
    volumes:
      - rulesforge_users_db_data:/var/lib/mysql
      - ./schema.sql:/docker-entrypoint-initdb.d/schema.sql
      - ./my.cnf:/etc/mysql/conf.d/my.cnf # Custom MariaDB configuration

    networks:
      - cluster
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "mariadb --protocol='tcp' -h 127.0.0.1 -u root -pussdgw -e 'SELECT 1'",
        ]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s # Allow more time for the initial startup

  rulesforge_user_service:
    build: .
    container_name: rulesforge_user_service
    working_dir: /usr/src/app
    command: bash -c "sleep 10 && node server.js" # Add a delay to allow DB to fully initialize
    volumes:
      - .:/usr/src/app
      # Map the log_level file
      # Set the application log level file host mounted path, this file will contain the log level number i.e 15 etc. It is the Bitmask mapping number from 1-31
      # Example: 1-FATAL, 2-ERROR, 3-FATAL+ERROR, 4-WARN, 5-FATAL+WARN, 6-ERROR+WARN, 7-FATAL+ERROR+WARN,
      # 8-INFO, 9-FATAL+INFO, 10-ERROR+INFO, 11-FATAL+ERROR+INFO, 12-WARN+INFO, 13-FATAL+WARN+INFO,
      # 14-ERROR+WARN+INFO, 15-FATAL+ERROR+WARN+INFO, 16-TRACE, 17-FATAL+TRACE, 18-ERROR+TRACE,
      # 19-FATAL+ERROR+TRACE, 20-WARN+TRACE, 21-FATAL+WARN+TRACE, 22-ERROR+WARN+TRACE,
      # 23-FATAL+ERROR+WARN+TRACE, 24-INFO+TRACE, 25-FATAL+INFO+TRACE, 26-ERROR+INFO+TRACE,
      # 27-FATAL+ERROR+INFO+TRACE, 28-WARN+INFO+TRACE, 29-FATAL+WARN+INFO+TRACE,
      # 30-ERROR+WARN+INFO+TRACE, 31-FATAL+ERROR+WARN+INFO+TRACE
      - ./logger/ums_log.level:/ums_log.level
    depends_on:
      rulesforge_users_db:
        condition: service_healthy
    environment:
      PORT: 5000
      DB_HOST: rulesforge_users_db
      DB_USER: rulesforge_user_management
      DB_PASS: ussdgw
      DB_NAME: rulesforge_users
      DB_PORT: 3306
      # ------ Email Configuration ------
      SMTP_HOST: smtp.mailtrap.io
      SMTP_PORT: 2525
      SMTP_USER: 5f4b7b7b7b7b7b
      SMTP_PASS: 7b7b7b7b7b7b7b
      SMTP_FROM: <EMAIL>
      SMTP_SECURE: false
      COMPANY_NAME: Ruleforge # This is the name of the company that will be used in the email templates
      # ---------------------------------
      JWT_EXPIRATION: 900 # 900s = 15min, JWT expiration time should be in number only which represents seconds, don't use '1h' or '1hr' or '1 hour' etc.
      LOG_INSTANCE_NAME: user-management
    networks:
      - cluster
    ports:
      - "5000:5000"
    restart: always

networks:
  cluster:
    external: true

volumes:
  rulesforge_users_db_data:
