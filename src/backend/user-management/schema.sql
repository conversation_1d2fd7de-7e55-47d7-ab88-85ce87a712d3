-- schema.sql

-- Create the database
CREATE DATABASE IF NOT EXISTS rulesforge_users;

-- Use the database
USE rulesforge_users;

-- Create the users table
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
  middle_name <PERSON><PERSON><PERSON><PERSON>(50),
  last_name <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
  username VA<PERSON><PERSON><PERSON>(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  cell_number VARCHAR(20),
  password VARCHAR(255) NOT NULL
);

-- Create the api_tokens table
CREATE TABLE IF NOT EXISTS api_tokens (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  token VARCHAR(500) NOT NULL,
  expires_at DATETIME NOT NULL,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

