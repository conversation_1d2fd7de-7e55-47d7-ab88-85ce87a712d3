import cors from "cors";

// If we want to restrict the origins, we can add them to the allowedOrigins array otherwise we can leave it empty to allow all origins
const allowedOrigins = [];

export const configureCors = () => {
  return cors({
    origin: (origin, callback) => {

        // If allowedOrigins is empty, allow all origins
        if (allowedOrigins.length === 0) {
            return callback(null, true);
        }

      if (!origin) {
        // Allow requests with no origin (e.g., curl, Postman)
        return callback(null, true);
      }
      if (allowedOrigins.includes(origin)) {
        // Allow requests from allowed origins
        return callback(null, true);
      } else {
        // For non-allowed origins, skip adding CORS headers
        return callback(null, false); // Let the request proceed without CORS headers
      }
    },
    credentials: true, // Allow credentials (cookies, authorization headers)
  });
};