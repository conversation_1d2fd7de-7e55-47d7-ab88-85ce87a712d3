import {ROLES} from '../config/roles.js';
import { verifyJWT } from '../utils/jwtUtils.js';
import { sendErrorResponse } from '../utils/responseHandler.js';


/**
 * Middleware to check if the user has an admin role.
 * @param {Request} req - Express request object.
 * @param {Response} res - Express response object.
 * @param {Function} next - Express next middleware function.
 */
export const requireAdmin = async (req, res, next) => {
    try {

        // Get token from headers
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return sendErrorResponse(res, 'Unauthorized: No token provided', null, 401);
        }

        // Extract token
        const token = authHeader.split(' ')[1];

        // Hard coded tenant id
        const tenantId = 1;


        let decoded = await verifyJWT(token, tenantId);


        // Verify token
        if (!decoded || !decoded.id) {
            return sendErrorResponse(res, 'Unauthorized: Invalid token', null, 401)
        }

        // Check if is not the admin role
        if (decoded.role_id !== ROLES.ADMIN.id) {
            return sendErrorResponse(res, 'Forbidden: Admin access required', null, 403)
        }

        req.user = decoded;
        req.user_id = decoded.id ;
        req.isAadmin = decoded.role_id.toString() === ROLES.ADMIN.id.toString();

        next();
        
    } catch (error) {

        console.error(`[Admin Middleware] Error: ${error.message}`, error.stack || "");

        return res.status(401).json({ message: 'Unauthorized: Invalid token' });
    }
};