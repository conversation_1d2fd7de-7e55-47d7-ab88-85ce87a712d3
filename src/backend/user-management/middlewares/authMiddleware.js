import {ROLES} from '../config/roles.js';
import { verifyJWT } from '../utils/jwtUtils.js';
import { sendErrorResponse } from '../utils/responseHandler.js';


/**
 * Middleware to verify the token and to set the user ID for request processing.
 *
 * This middleware checks if the user has an admin role and if a `user_id` is provided in the request body.
 * If the user is an admin, it allows the admin to perform actions on behalf of the specified user.
 * Otherwise, it defaults to the authenticated user's ID.
 *
 * @param {Request} req - Express request object.
 * @param {Response} res - Express response object.
 * @param {Function} next - Express next middleware function.
 */
export const requireLogin = async (req, res, next) => {

    try {

        // Get token from headers
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return sendErrorResponse(res, 'Unauthorized: No token provided', null, 401);
        }

        // Extract token
        const token = authHeader.split(' ')[1];

        // Hard coded tenant id
        const tenantId = 1;


        let decoded = await verifyJWT(token, tenantId);
        


        // Verify token
        if (!decoded || !decoded.id) {
            return sendErrorResponse(res, 'Unauthorized: Invalid token', null, 401)
        }

      
        // If the user_id is provided and the role id is admin, then admin can do stuffs behind of that user
        req.user_id = decoded.id ;

        // If the user is an admin, then allow the admin to perform actions on behalf of the specified user id
        if (decoded.role_id.toString() === ROLES.ADMIN.id.toString() && req.body.user_id) {
            req.user_id = req.body.user_id;
        }

        req.user = decoded;
        req.isAadmin = decoded.role_id.toString() === ROLES.ADMIN.id.toString();

        next();
        
    } catch (error) {
        console.log("Require Login middleware error", error)

        return res.status(401).json({ message: 'Unauthorized: Invalid token' });
    }
};