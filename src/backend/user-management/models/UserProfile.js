import { DataTypes } from 'sequelize';
import sequelize from './index.js';
import User from './User.js';


/**
 * UserProfile Model
 * 
 * Represents a user profile in the application, storing various fields and their values
 * associated with a specific user.
 *
 */
const UserProfile = sequelize.define('UserProfile', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true},
    user_id: { type: DataTypes.INTEGER, allowNull: false},
    field: { type: DataTypes.STRING, allowNull: false},
    value: { type: DataTypes.TEXT, allowNull: false }
}, {
    timestamps: false,  // Disable timestamps
    indexes: [
        {
            unique: false,
            fields: ['user_id']  // Speeds up lookups for user profile fields
        },
        {
            unique: false,
            fields: ['field']  // Optimizes searches for specific profile fields
        }
    ]
});

// Associate UserProfile with User
UserProfile.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });

// Set the many relationship with User
User.hasMany(UserProfile, { foreignKey: 'user_id', as: 'userProfile', onDelete: 'CASCADE' });


export default UserProfile;



/**
 * @typedef {Object} Model_UserProfile
 * @property {number} id - The unique identifier for the user profile, auto-incremented.
 * @property {number} user_id - The identifier of the user to whom this profile belongs.
 * @property {string} field - The name of the profile field (e.g., 'name'').
 * @property {string} value - The value associated with the profile field.
 * 
 * @see {@link User} for the user model associated with this profile.
 * 
 */