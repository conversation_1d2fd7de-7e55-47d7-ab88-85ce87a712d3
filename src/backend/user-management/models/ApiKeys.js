// cols: id, timestemp, apikey will be the generated token
// define me a model for the above
import { DataTypes } from 'sequelize';
import sequelize from './index.js';
// import User from './User.js';


/**
 * UserProfile Model
 * 
 * Represents a user profile in the application, storing various fields and their values
 * associated with a specific user.
 *
 */
const ApiKeys = sequelize.define('ApiKeys', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true},
    api_key: { type: DataTypes.STRING, allowNull: false},
    timestamp: { type: DataTypes.DATE, allowNull: false},
   
}, {
    timestamps: true,  // Disable timestamps
    indexes: [
        {
            unique: false,
            fields: ['timestamp']  // Speeds up lookups for user profile fields
        }
    ]
});

// Associate UserProfile with User
// UserProfile.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });

// // Set the many relationship with User
// User.hasMany(ApiKeys, { foreignKey: 'user_id', as: 'apiKeys', onDelete: 'CASCADE' });


export default ApiKeys;



/**
 * @typedef {Object} ApiKeys
 * 
 * @property {number} id - The unique identifier for the api key, auto-incremented.
 * @property {string} apikey - The api key.
 * @property {number} timestamp - The timestamp of the api key.
 * 
 */