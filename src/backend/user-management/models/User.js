import { DataTypes } from "sequelize";
import sequelize from "./index.js";
import Tenant from "./Tenant.js";
import { getRoleById } from "../config/roles.js";

/**
 * Represents a User in the system.
 *
 *
 * @description
 * The User model is defined using Sequelize and represents a user entity in the system.
 * It includes fields for user identification, authentication, and role assignment.
 *
 */
const User = sequelize.define(
  "User",
  {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    tenant_id: { type: DataTypes.INTEGER, allowNull: false },
    username: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      // validate: {
      //   isEmail: { msg: "Invalid email format!" }, // Ensures the email is correctly formatted
      // },
    },
    password: { type: DataTypes.STRING, allowNull: true }, // Null for API users
    role_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        async isValidRole(roleId) {
          if (!getRoleById(roleId)) {
            throw new Error("Invalid role ID");
          }
        },
      },
    },
    profile_completed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },

    // Password Reset Code (Exactly 5 Digits)
    pass_reset_code: {
      type: DataTypes.STRING(5), // Ensures exactly 5 characters
      allowNull: true,
      validate: {
        isNumeric: { msg: "Reset code must contain only numbers" },
        len: { args: [5, 5], msg: "Reset code must be exactly 5 digits" },
      },
    },

    // Password Reset Code generation time
    pass_code_gen_time: {
      type: DataTypes.DATE,
      allowNull: true,
      validate: {
        isDate: { msg: "Must be a valid date format" },
      },
    },
  },
  {
    timestamps: true, // Enables createdAt & updatedAt
    indexes: [
      {
        fields: ["username"],
      },
      {
        fields: ["tenant_id"],
      },
      {
        fields: ["role_id"],
      },
    ],
    defaultScope: {
      // Excludes timestamps from the returned data
      attributes: { exclude: ["createdAt", "updatedAt"] },
    },
  },
);

User.belongsTo(Tenant, { foreignKey: "tenant_id" });

/**
 * Serializes the user object to a JSON serializable object.
 * @returns {Object} - The user object as a JSON serializable object.
 */
User.prototype.toJSON = function () {
  let values = { ...this.get() };

  // Delete the password field when serializing to JSON
  delete values.password;

  // If role_id is then also add the role slug to the object
  if (values.role_id) {
    values.role_name = getRoleById(values.role_id).slug;
    values.role_label = getRoleById(values.role_id).label;
  }

  // Convert the user profile array to an object where the key will be the field name and the value will be the field value
  if (values.userProfile && Array.isArray(values.userProfile)) {
    values.userProfile = values.userProfile.reduce((acc, profile) => {
      acc[profile.field] = profile.value;
      return acc;
    }, {});
  }

  return values;
};

export default User;

/**
 * @typedef {Object} Model_User
 * @property {number} id - Unique identifier for the user, auto-incremented.
 * @property {number} tenant_id - Identifier for the tenant the user belongs to.
 * @property {string} email - Unique email address of the user.
 * @property {string|null} password - Hashed password for the user, can be null for API users.
 * @property {number} role_id - Identifier for the role assigned to the user.
 * @property {boolean} profile_completed - Flag indicating if the user has completed their profile.
 */
