import { DataTypes } from 'sequelize';
import sequelize from './index.js';

/**
 * Defines the Tenant model for the database.
 * 
 * This model represents a tenant with a unique identifier and a name.
 * It is defined using Sequelize's ORM capabilities and maps to a corresponding table in the database.
 * 
 * @returns {Model} A Sequelize model representing the Tenant entity.
 */
const Tenant = sequelize.define('Tenant', {
    id: { type: DataTypes.INTEGER, primaryKey: true, autoIncrement: true },
    name: { type: DataTypes.STRING, allowNull: false, unique: true }
}, {
    timestamps: false,  // Disable timestamps
});

export default Tenant;


/**
 * @typedef {Object} Model_Tenant
 * @property {number} id - The unique identifier for the tenant, automatically incremented.
 * @property {string} name - The name of the tenant, which must be unique and cannot be null.
 * 
 */