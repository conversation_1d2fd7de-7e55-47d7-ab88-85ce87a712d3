import nodemailer from 'nodemailer';
import { loadEmailTemplate } from './emailTemplates.js';
import { emailConfig } from './config.js';

const transporter = nodemailer.createTransport({
    host: emailConfig.SMTP_HOST,
    port: emailConfig.SMTP_PORT,
    secure: emailConfig.SMTP_SECURE, // Use SSL/TLS if true
    auth: {
        user: emailConfig.SMTP_USER,
        pass: emailConfig.SMTP_PASS,
    },
});

/**
 * Sends an email using Nodemailer.
 * @param {string} toEmail - Recipient email address
 * @param {string} subject - Email subject
 * @param {"newUser" | "passwordReset"} templateName - Template name (newUser, passwordReset)
 * @param {Object} templateData - Data to inject into the template
 */
export const sendEmail = async (toEmail, subject, templateName, templateData) => {
    try {

        console.info("[Email] Sending email...");

        const htmlBody = loadEmailTemplate(templateName, templateData);

        const mailOptions = {
            from: emailConfig.SMTP_FROM,
            to: toEmail,
            subject,
            html: htmlBody
        };

        const info = await transporter.sendMail(mailOptions);
        console.info(`[Email] Email sent for '${templateName}': ${info.messageId}`);
        return info;
    } catch (error) {
        console.error(`[Email] Error sending email: ${error.message}`, error.stack || "");
        throw new Error('Email sending failed');
    }
};