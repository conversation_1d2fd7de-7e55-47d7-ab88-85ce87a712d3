import { newUserTemplate } from './templates/newUser.js';
import { passwordResetTemplate } from './templates/passwordReset.js';

const templates = {
    newUser: newUserTemplate,
    passwordReset: passwordResetTemplate
};

/**
 * Returns the compiled email template.
 * @param {string} templateName - The template name (newUser, passwordReset)
 * @param {Object} data - Dynamic content to inject
 * @returns {string} - Processed email HTML
 */
export const loadEmailTemplate = (templateName, data) => {
    if (!templates[templateName]) {
        throw new Error(`Template "${templateName}" not found`);
    }
    return templates[templateName](data);
};
