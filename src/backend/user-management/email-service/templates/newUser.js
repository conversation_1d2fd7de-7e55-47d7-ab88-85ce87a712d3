import { emailHeader } from './helper/header.js';
import { emailFooter } from './helper/footer.js';
import { sanitizeInput } from '../../utils/sanitize.js';

/**
 * Generates an HTML email template for welcoming a new user.
 *
 * @param {Object} params - The parameters for the email template.
 * @param {string} params.email - The email address of the new user.
 * @param {string} params.name - The full name of the new user.
 * @param {string} [params.apiToken=""] - The API token for the new api user, optional.
 * @returns {string} The HTML content of the welcome email.
 */
export const newUserTemplate = ({ email, name, apiToken = "" }) => `
${emailHeader()}
<h1>Welcome, ${sanitizeInput(name)}!</h1>
<p>We're excited to have you on board.</p>

<table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
    <tr>
        <td style="padding: 10px; background: #f4f4f4; font-weight: bold;">Email:</td>
        <td style="padding: 10px;">${sanitizeInput(email)}</td>
    </tr>
    ${apiToken ? `
    <tr>
        <td style="padding: 10px; background: #f4f4f4; font-weight: bold;">API Token:</td>
        <td style="padding: 10px;"><b>You can view your token under the Account Settings</b></td>
    </tr>
    ` : ""}
</table>

${emailFooter()}
`;


/**
 * Email template for the new user
 * @typedef {Object} Doc_ETempl_NewUser - The parameters for the email template.
 * @property {string} email - The email address of the new user.
 * @property {string} name - The full name of the new user.
 * @property {string} password - The password for the new user.
 * @property {string} [apiToken=""] - The API token for the new api user, optional.
 */