import { emailConfig } from "../../config.js";

export const emailHeader = () => `
<!DOCTYPE html>
<html>
<head>
    <title>Email Notification</title>
    <style>
        body { font-family: Arial, sans-serif; background-color: #f4f4f4; padding: 20px; }
        .container { max-width: 600px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1); }
        .header { background: #FF784B; color: white; padding: 10px; text-align: center; font-size: 20px; border-radius: 10px 10px 0 0; }
        .content { padding: 20px; }
        .footer { text-align: center; font-size: 12px; color: gray; padding: 10px; border-top: 1px solid #ddd; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">${emailConfig.COMPANY_NAME}</div>
        <div class="content">
`;
