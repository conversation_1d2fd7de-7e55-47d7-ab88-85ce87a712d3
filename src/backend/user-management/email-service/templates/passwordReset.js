import { emailHeader } from './helper/header.js';
import { emailFooter } from './helper/footer.js';
import { sanitizeInput } from '../../utils/sanitize.js';

/**
 * Generates an HTML email template for a password reset request using a one-time code.
 *
 * @param {Object} params - The parameters for the email template.
 * @param {string} params.name - The full name of the user.
 * @param {string} params.otpCode - The one-time password reset code.
 * @returns {string} The HTML content of the password reset email.
 */
export const passwordResetTemplate = ({ name, otpCode }) => `
${emailHeader()}
<h1>Password Reset Request</h1>
<p>Hello <strong>${sanitizeInput(name)}</strong>,</p>
<p>We received a request to reset your password. Use the code below to proceed:</p>

<div style="
    font-size: 22px;
    font-weight: bold;
    text-align: center;
    background: #f4f4f4;
    padding: 15px;
    border-radius: 10px;
    width: fit-content;
    margin: 20px auto;
    letter-spacing: 2px;">
    ${sanitizeInput(otpCode)}
</div>

<p>Enter this code in the password reset page to proceed. This code is valid for <strong>15 minutes</strong>.</p>

<p>If you didn’t request this, please ignore this email. Your password will remain unchanged.</p>

${emailFooter()}
`;


/**
 * Email template for the password reset
 * @typedef {Object} Doc_ETempl_PassReset
 * @property {string} name - The full name of the user.
 * @property {string} otpCode - The one-time password reset code.
 */
