export const emailConfig = {
    SMTP_HOST: process.env.EMAIL_SMTP_HOST || 'smtp.mailtrap.io',
    SMTP_PORT: process.env.EMAIL_SMTP_PORT || 2525,
    SMTP_USER: process.env.EMAIL_SMTP_USER || 'user',
    SMTP_PASS: process.env.EMAIL_SMTP_PASS || 'pass',
    SMTP_FROM: process.env.EMAIL_SMTP_FROM || '<EMAIL>',
    SMTP_SECURE: process.env.EMAIL_SMTP_SECURE === 'true', // Convert to boolean
    COMPANY_NAME: process.env.COMPANY_NAME || 'Ruleforge',
};
