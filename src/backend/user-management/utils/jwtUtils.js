import jwt from 'jsonwebtoken';
import { getTenantKeys } from '../services/tenantService.js';
import { JWT } from '../config/env.js';

/**
  * Generates a JSON Web Token (JWT) for a given tenant.
 * 
 * This asynchronous function retrieves the tenant's private key and uses it to sign
 * the provided payload, creating a JWT. The token is signed using the ES256 algorithm
 * and includes an expiration time defined in the JWT configuration.
 * 
 * @param {number} tenantId - Tenant ID.
 * @param {{id: number, tenant_id: number, role_id} & Object.<string, any>} payload - Payload object containing id, tenant_id, role_id etc.
 * @param {string | null} [expiry=JWT.EXPIRATION] - Expiry time for the token, if we want to set unlimited expiry then pass null.
 * @returns {Promise<string>} A promise that resolves to the signed JWT as a string.
 */
export const generateJwtToken = async (tenantId, payload, expiry = JWT.EXPIRATION) => {
    const { privateKey } = await getTenantKeys(tenantId);
    
    return jwt.sign(payload, privateKey, { algorithm: 'ES256', expiresIn: expiry});
};

/**
 * Verifies a JWT token using Ed25519 public key.
 * @param {string} token - JWT token to verify.
 * @param {number} tenantId - Tenant ID.
 * @returns {Promise<{id: number, tenant_id: number, role_id} & Object.<string, any>>} - Decoded token payload.
 */
export const verifyJWT = async (token, tenantId) => {

    const { publicKey } = await getTenantKeys(tenantId);

    return jwt.verify(token, publicKey, { algorithms: ['ES256'] });
};