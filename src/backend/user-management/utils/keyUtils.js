import crypto from 'crypto';


 /**
   * Generates an ECDSA-P256 (ES256) key pair used for the tenant's JWT signing.
   * 
   * This function generates a pair of ECDSA-P256 keys (public and private) using Node's
   * built-in `crypto` module. The keys are generated with the NIST P-256 curve, suitable
   * for ES256 signing. The keys can be returned in PEM format or Base64-encoded PEM format.
   * 
   * 
   * @param {boolean} [base64=true] - Whether to return the keys in Base64-encoded format.
   * 
   * @returns {Object} An object containing the generated ES256 key pair.
   * @returns {string} return.privateKey - The generated private key, optionally Base64-encoded.
   * @returns {string} return.publicKey - The generated public key, optionally Base64-encoded.

   */
export const generateKeyPair = (base64 = true) => {
    console.info('Generating ECDSA-P256 (ES256) public and private key pair...');

    let { publicKey, privateKey } = crypto.generateKeyPairSync('ec', {
        namedCurve: 'P-256', // Use the NIST P-256 curve for ES256
        publicKeyEncoding: { format: 'pem', type: 'spki' },
        privateKeyEncoding: { format: 'pem', type: 'pkcs8' }
    });

    if (base64) {
        privateKey = Buffer.from(privateKey, 'utf8').toString('base64');
        publicKey = Buffer.from(publicKey, 'utf8').toString('base64');
    }

    return { privateKey, publicKey };
};



/**
 * @todo, we need to implement this function. As for strange reason the jwt sign is not showing the eddsa algorithm
 * This function generates a pair of Ed25519 keys (public and private) using Node's
 * built-in `crypto` module. The keys are exported in PEM format and then encoded
 * in Base64 for storage or transmission.
 * @note Ed25519 (EdDSA) is a modern digital signature algorithm which is secure and efficient.
 * 
 * @param {boolean} base64 - Whether to encode the keys in Base64 format.
 * @returns {Object} An object containing the generated Ed25519 key pair.
 * @returns {string} return.privateKey - The generated private key in PEM format.
 * @returns {string} return.publicKey - The generated public key in PEM format.
 */
// export const generateKeyPair = (base64 = true) => {

//     console.info('Generating RSA public and private keys pair for the tenant...');

//     let { publicKey, privateKey } = crypto.generateKeyPairSync('ed25519');

//     privateKey = privateKey.export({ format: 'pem', type: 'pkcs8' });
//     publicKey = publicKey.export({ format: 'pem', type: 'spki' });

//     if (base64) {
//         privateKey = Buffer.from(privateKey, 'utf8').toString('base64');
//         publicKey = Buffer.from(publicKey, 'utf8').toString('base64');
//     }

//     return { privateKey, publicKey };
// };