
/**
 * User Profile Fields Configuration
 * 
 * This configuration object defines the structure and validation rules for user profile fields baesed on the role id.
 * As the differen role may have the different user profile fields, this configuration allows for flexibility in defining
 * It includes global fields that apply to all users types profile and can be extended with role-specific fields.
 * Each field is defined with its type, requirement status, and a validation function.
 */
const userProfileFields = {
    global: {
        name: { type: 'text', required: false, validator: (name)=> name.length > 0 }
    }
}

/**
 * Retrieves user profile fields based on a role ID.
 * 
 * This function merges global user profile fields with role-specific fields for a given role ID.
 * If no specific fields are found for the role ID, only the global fields are returned.
 */
function getUserProfileFieldsByRoleId(roleId) {

    const fields = userProfileFields[roleId] || {};

    return {
        ...userProfileFields.global,
        ...fields
    };

}