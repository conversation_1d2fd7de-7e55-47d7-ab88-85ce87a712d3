import argon2 from 'argon2';

/**
 * Hashes a password using the Argon2 algorithm.
 * 
 * This asynchronous function takes a plain text password and hashes it using the Argon2id variant
 * for optimal security. It is configured with specific parameters to enhance resistance against
 * various types of attacks, including increased memory cost and multiple iterations.
 * 
 * 
 * @param {string} password - The plain text password to be hashed.
 * 
 * @returns {Promise<string>} A promise that resolves to the hashed password.
 */
export const hashPassword = async (password) => {
    return await argon2.hash(password, {
        type: argon2.argon2id, // Use Argon2id for best security
        memoryCost: 2 ** 16, // 64MB (increased resistance)
        timeCost: 3, // 3 iterations
        parallelism: 1 // Single-threaded (default)
    });
};

/**
 * Generates a random password of a specified length.
 *
 * @param {number} [length=12] - The desired length of the generated password. Defaults to 12 if not specified.
 * @returns {string} A randomly generated password consisting of numbers and letters.
 */
export const generatePassword = (length = 12) => {

    const characters = '0123456789abc<PERSON><PERSON><PERSON><PERSON><PERSON>lmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let password = '';

    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        password += characters[randomIndex];
    }

    return password;
}


/**
 * Verifies a password against a hashed value using Argon2.
 * 
 * This asynchronous function checks if the provided plain text password matches the given
 * hashed password. It uses the Argon2 algorithm to perform the verification, ensuring that
 * the password is correct and matches the stored hash.
 * 
 * 
 * @param {string} password - The plain text password to verify.
 * @param {string} hash - The hashed password to compare against.
 * 
 * @returns {Promise<boolean>} A promise that resolves to `true` if the password matches the hash, or `false` otherwise.
 * 
 */
export const verifyPassword = async (password, hash) => {
    return await argon2.verify(hash, password);
};