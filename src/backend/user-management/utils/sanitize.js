import sanitizeHtml from 'sanitize-html';

/**
 * Sanitizes user input to prevent XSS attacks.
 * @param {string} input - The user input to sanitize.
 * @returns {string} - The sanitized input.
 */
export const sanitizeInput = (input) => {
    return sanitizeHtml(input, {
        allowedTags: [], // Remove all HTML tags
        allowedAttributes: {}, // Remove all attributes
        disallowedTagsMode: 'escape' // Escapes disallowed tags instead of stripping
    });
};
