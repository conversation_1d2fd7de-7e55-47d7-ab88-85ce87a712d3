/**
 * Validates data against a given Joi schema.
 *
 * @param {import("joi").Schema} schema - The Joi schema to validate against.
 * @param {Object} data - The data to be validated.
 * @param {boolean} [abortEarly=false] - Whether to stop validation on the first error.
 * @returns {Object} An object containing validation errors, if any.
 */
export const validateJoi = (schema, data, throwError = true) => {

    const { error } = schema.validate(data, { abortEarly: false });

    if (!error) return null;

    const errors = {};
    error.details.forEach((err) => {
        errors[err.context.key] = err.message;
    });

    // Throw a custom error if validation fails
    if (throwError) throw new JoiError(errors);

    return errors;
}


/**
 * Custom error class for handling Joi validation errors.
 *
 * @class JoiError
 * @extends {Error}
 */
export class JoiError extends Error {
    constructor(errors) {
        super();
        this.errors = errors;
    }
}

