// Import database
import * as database from "./services/database.js";
import app from "./app.js";
import routePath, { registerAllRoutes } from "./routes/index.js";

// Initialize the database
database.initDb();

import express from "express";
import helmet from "helmet";

import { configureCors } from "./middlewares/cors.js";

// Middlewares
app.use(express.json()); // Parse JSON requests
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded data
app.use(configureCors());
app.use(helmet()); // Set security headers

// Declare routes
registerAllRoutes();
console.log("Routes registered")
// Health check endpoint
app.get(routePath("health"), (req, res) => {
  res.status(200).json({ success: true, message: "API is running smoothly" });
});
 
// Global Error Handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ success: false, message: "Internal Server Error" });
});
 
// Start Server and Connect to Database
const PORT = process.env.PORT || 5000;
console.log(PORT,"#48494") 

app.listen(PORT, () => {
  console.info(`Server is running on port ${PORT}`);
});
