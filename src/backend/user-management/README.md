# Developer instructions

## Docker Environment for MariaDB and Node.js

This project uses Docker Compose to set up a MariaDB database and a Node.js application. The MariaDB service initializes the `user_management` schema and tables, while the Node.js service runs the `server.js` file.

### Prerequisites

- [Docker](https://docs.docker.com/get-docker/) installed on your machine.
- [Docker Compose](https://docs.docker.com/compose/install/) installed.

### Getting Started

#### Step 1: Clone the repository

If you haven't already, clone this repository to your local machine:

```bash
git clone <repository_url>
cd <repository_directory>
```

#### Step 2: Set up your environment

Ensure the following files are present in the project directory:

- **schema.sql**: Contains the SQL commands to create the database schema.
- **server.js**: Your Node.js application script.

#### Step 3: Modify Environment Variables

In the `docker-compose.yml` file, you can adjust the environment variables for the database and Node.js app if needed:

```yaml
MYSQL_ROOT_PASSWORD: root_password # Replace with your root password
MYSQL_DATABASE: user_management # Database name (already initialized)
MYSQL_USER: user # Replace with your desired database user
MYSQL_PASSWORD: user_password # Replace with your desired password
```

#### Step 4: Build and run the containers

To build and start the containers, run the following command from the project directory:

```bash
docker-compose up -d
```

This command will:

- Start the **MariaDB** service and initialize the database using `schema.sql`.
- Start the **Node.js** service by running `node server.js`.

#### Step 5: Access the services

- **MariaDB** is available at `localhost:3306`. You can connect to it using any MySQL/MariaDB client.
  - Host: `localhost`
  - Port: `3306`
  - Username: `user` (or your custom username)
  - Password: `user_password` (or your custom password)
  - Database: `user_management`
- **Node.js** is available at `http://localhost:3000` (assuming your `server.js` runs on port 3000).

#### Step 6: Stopping the services

To stop the services, use:

```bash
docker-compose down
```

This will stop and remove the containers but preserve your database data in the `db_data` volume.

#### Step 7: Cleaning up

If you want to remove the containers and the persistent data:

```bash
docker-compose down -v
```

This command will stop the containers and also remove the `db_data` volume where MariaDB stores its data.

### Additional Information

- The Node.js service waits for MariaDB to start before it runs.
- Both services are networked together via an internal Docker network (`app-network`), so the Node.js app can communicate with MariaDB using the service name `mariadb` as the host.

### Troubleshooting

- If the containers fail to start, make sure Docker is running and you have the required files (e.g., `schema.sql` and `server.js`) in the correct directory.
- Check the logs of each service using:

```bash
docker-compose logs mariadb
docker-compose logs node-app
```

---

## Testing Scripts

This section describes the testing scripts included in the project and provides instructions on how to use them to test the User Management Service.

### Overview

The project includes two main testing scripts:

1. **Test Client (`testClient.js`)**: Simulates user interactions with the User Management Service, including registration, login, API token creation, token validation, and profile access.

2. **Test API Client (`testApiClient.js`)**: Simulates an API client that validates an API token with the User Management Service.

### Prerequisites

Before running the testing scripts, ensure the following prerequisites are met:

- **Node.js**: Installed on your machine.
- **MySQL Database**: Running and accessible.
- **SSL Certificates**: Generated `key.pem` and `cert.pem` files for HTTPS.
- **Dependencies**: Installed necessary npm packages.

#### Install Dependencies

Run the following command in your project directory to install all required packages:

    `npm install express body-parser jsonwebtoken bcryptjs mysql2 axios https`

### Configure Environment Variables

> **Important**: Never commit the `.env` file to version control.

### Testing Scripts Details

#### 1. Test Client (`testClient.js`)

The `testClient.js` script performs the following actions:

- **User Registration**: Registers a new user with predefined details.
- **User Login**: Logs in using the registered user's credentials and retrieves a web token.
- **API Token Creation**: Creates an API token using the obtained web token.
- **API Token Validation**: Validates the created API token with the service.
- **Profile Access**: Accesses the user's profile information using the web token.

##### Usage

Run the test client with the following command:

```
    node testClient.js
```

The script will output the results of each action to the console.

##### Expected Output

- Confirmation of successful user registration.
- Web token received upon successful login.
- API token created and displayed.
- Validation result of the API token.
- Retrieved user profile information.

##### Notes

- The script uses `axios` to make HTTPS requests to the service.
- It accepts self-signed certificates for testing purposes by setting `rejectUnauthorized: false` in the HTTPS agent.

#### 2. Test API Client (`testApiClient.js`)

The `testApiClient.js` script simulates an external API client that needs to validate an API token.

##### Prerequisites

- Obtain the API token generated by `testClient.js`.

##### Usage

1. Open `testApiClient.js` and replace `'your_api_token_here'` with the actual API token:

```
       // testApiClient.js
       const apiToken = 'your_actual_api_token';
```

2. Run the test API client with the following command:

```
   node testApiClient.js
```

##### Expected Output

- Validation result of the API token, indicating whether it is valid and the associated user ID.

##### Notes

- Ensure the User Management Service is running before executing the script.
- The script also accepts self-signed certificates for testing purposes.

### Full Testing Workflow

Follow these steps to test the entire workflow:

1.  **Start the Service**: Ensure the User Management Service is running.

    node server.js

2.  **Run Test Client**: Execute `testClient.js` to simulate user interactions.

```
        node testClient.js
```

    - Note down the API token displayed in the output.

3.  **Configure Test API Client**: Replace the placeholder API token in `testApiClient.js` with the one obtained from the previous step.

```
    // testApiClient.js
    const apiToken = 'your_actual_api_token';
```

4.  **Run Test API Client**: Execute `testApiClient.js` to validate the API token.

```
    node testApiClient.js
```

5.  **Review Outputs**: Check the console outputs of both scripts to ensure all actions were performed successfully.

### Troubleshooting

- **Connection Errors**: Ensure that the service is running and accessible over HTTPS at `https://localhost:3000`.
- **Database Errors**: Verify that the database schema has been applied and the MySQL service is running.
- **SSL Certificate Warnings**: When using self-signed certificates, the scripts are configured to accept them. In browsers, you may need to manually accept the certificate.

### Security Considerations

- **Self-Signed Certificates**: Suitable for testing only. Use trusted certificates in production.
- **Environment Variables**: Store sensitive information like secret keys and database credentials securely.
- **Token Handling**: Do not expose tokens in logs or output in a production environment.

### Additional Notes

- **Dependencies**: Ensure all npm packages listed in `package.json` are installed.
- **Node.js Version**: The scripts use ES6 syntax; ensure you're running a Node.js version that supports ES6 (Node.js v12 or higher).
- **HTTPS Agent Configuration**: The `httpsAgent` in both scripts is set to accept self-signed certificates. Remove or modify this setting when using trusted certificates.

---

By following the instructions above, you can effectively test the User Management Service using the provided scripts. These scripts simulate real-world usage scenarios and help ensure that the service functions as expected.
