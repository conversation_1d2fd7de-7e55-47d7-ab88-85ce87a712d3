import {
  registerUser,
  loginUser,
  updateProfile,
  getUser,
  updatePassword,
  deleteUser,
  getAllUsers,
  refreshToken,
  resetPassword,
  setResetPassword,
  getAllApiKeys,
  create<PERSON><PERSON><PERSON><PERSON>,
  delete<PERSON>pi<PERSON>ey,
} from "../controllers/authController.js";
import { requireAdmin } from "../middlewares/adminMiddleware.js";
import routePath from "./index.js";
import app from "../app.js";
import { requireLogin } from "../middlewares/authMiddleware.js";

/**
 * Register authentication routes.
 */
function registerAuthRoutes() {
  // Register user
  app.post(routePath("auth/register"), requireAdmin, registerUser);

  // Login user
  app.post(routePath("auth/login"), loginUser);

  // Get user data
  app.post(routePath("auth/get-user"), requireLogin, getUser);

  // Update profile
  app.put(routePath("auth/profile"), requireLogin, updateProfile);

  // Update password
  app.put(routePath("auth/password"), requireLogin, updatePassword);

  // Reset password
  app.post(routePath("auth/password/reset"), resetPassword);

  // Set Reset password
  app.post(routePath("auth/password/reset-set-pass"), setResetPassword);

  // Delete user
  app.delete(routePath("auth/delete-user/:user_id"), requireAdmin, deleteUser);

  // Get all users
  app.post(routePath("auth/get-all-users"), requireAdmin, getAllUsers);

  // Refresh token
  app.post(routePath("auth/refresh-token"), requireLogin, refreshToken);

  app.post(routePath("auth/get-all-api-keys"), requireAdmin, getAllApiKeys);
  app.post(routePath("auth/create-api-key"), requireAdmin, createApiKey);
  app.delete(routePath("auth/delete-api-key/:api_key_id"), requireAdmin, deleteApiKey);
}

export default registerAuthRoutes;
