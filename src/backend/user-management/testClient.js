// testClient.js
import axios from "axios";
import https from "https";

const api = axios.create({
  baseURL: "https://localhost:3001",
  httpsAgent: new https.Agent({
    rejectUnauthorized: false, // Accept self-signed certificates for testing
  }),
});

(async () => {
  try {
    // User registers
    let response = await api.post("/register", {
      name: "<PERSON>",
      middleName: "A.",
      lastName: "<PERSON><PERSON>",
      username: "jd",
      email: "<EMAIL>",
      cellNumber: "1234567890",
      password: "password123",
    });
    

    // User logs in and gets a web token
    response = await api.post("/login", {
      username: "joh<PERSON><PERSON>",
      password: "password123",
    });
    
    const webToken = response.data.token;

    // User creates an API token
    response = await api.post(
      "/api-token",
      {},
      {
        headers: { "x-access-token": webToken },
      },
    );
    
    const apiToken = response.data.apiToken;

    // Test API client validates an API token
    response = await api.get("/validate-api-token", {
      headers: { "x-api-token": apiToken },
    });
    

    // User uses a web token
    response = await api.get("/profile", {
      headers: { "x-access-token": webToken },
    });
    
  } catch (error) {
    console.error(
      "Error:",
      error.response ? error.response.data : error.message,
    );
  }
})();
