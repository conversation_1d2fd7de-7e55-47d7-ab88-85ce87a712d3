import <PERSON><PERSON> from "joi";
import * as authService from "../services/authService.js";
import * as responseHandler from "../utils/responseHandler.js";
import { validateJoi } from "../utils/validationsUtils.js";
import { getRoleById } from "../config/roles.js";
import { generatePassword } from "../utils/hashUtils.js";
import { generateJwtToken } from "../utils/jwtUtils.js";
import { verifyJWT } from "../utils/jwtUtils.js";
import UserProfile from "../models/UserProfile.js";
import User from "../models/User.js";
import { sendEmail } from "../email-service/emailService.js";

/**
 * Registers a new user.
 *
 * This function handles the registration of a new user by validating the input data,
 * ensuring the user is created under the correct tenant, and sending a success or error response.
 *
 * @async
 * @param {Object} req - The Express request object.
 * @param {Doc_RegisterUserParams} req.body - The request body containing user registration details.
 *  @param {Object} res - The Express response object.
 * @returns {Promise<void>} Sends a response to the client.
 */
const registerUser = async (req, res) => {
  try {
    console.info(`[Auth] Registering user ...`);

    // For security reasons, the admin who is trying to register a user should only be able to create user on his tenant
    const tenant_id = req.user.tenant_id; // The data came from the middleware

    const data = {
      username: req.body.username,
      password: req.body.password,
      role_id: "2",
      // profile_data: req.body.profile_data,
      tenant_id,
    };

    

    validateJoi(registerSchema, data);

    const userData = await authService.registerUser(data);

    let emailFail = false;

    // Send the email if the user wants to
    if (false) {
      /** @type {import('../email-service/templates/newUser.js').Doc_ETempl_NewUser} */
      const emailData = {
        email: userData.usrObj.email,
        name: userData.usrObj.profile_data.full_name,
        password: userData.rawPass,
        apiToken: userData.userData.apiToken,
      };

      try {
        await sendEmail(
          userData.usrObj.email,
          "Your Login Details",
          "newUser",
          emailData
        );
      } catch (error) {
        emailFail = true;
        console.error(
          `[Auth] failed to send email: ${error.message}`,
          error.stack || ""
        );
      }
    }

    console.info(`[Auth] User successfully created!`);

    responseHandler.sendSuccessResponse(
      res,
      null,
      `User successfully created! ${
        emailFail ? "But failed to send email" : ""
      }`
    );
  } catch (error) {
    console.error(
      `[Auth] failed to register user: ${error.message}`,
      error.stack || ""
    );

    responseHandler.sendErrorResponse(res, error);
  }
};

/**
 * Handles user login requests.
 *
 * This function validates the request body, attempts to log in the user using the provided
 * credentials, and sends a success or error response based on the outcome.
 *
 * @async
 * @param {Object} req - The Express request object.
 * @param {Doc_LoginUserParams} req.body - The request body containing user credentials.
 * @param {Object} res - The Express response object.
 * @returns {Promise<void>} Sends a response to the client.
 */
const loginUser = async (req, res) => {
  try {
    const { username, password, tenant_id } = req.body;

    // Validate the request body
    validateJoi(loginSchema, { username, password, tenant_id });

    console.info(`[Auth] Logging in user ...`);

    const data = await authService.loginUser(username, password, tenant_id);

    console.info(`[Auth] User successfully logged in!`);

    responseHandler.sendSuccessResponse(
      res,
      data,
      "User successfully logged in!"
    );
  } catch (error) {
    console.error(
      `[Auth] failed to login user: ${error.message}`,
      error.stack || ""
    );

    responseHandler.sendErrorResponse(res, error);
  }
};

/**
 * Updates the user's profile.
 *
 * This function handles updating a user's profile by validating the input data
 * and applying the changes. It sends a success or error response based on the outcome.
 *
 * @async
 * @param {Object} req - The Express request object.
 * @param {Doc_UserProfileParams} req.body - The request body containing user profile update details.
 * @param {Object} res - The Express response object.
 * @returns {Promise<void>} Sends a response to the client.
 */
const updateProfile = async (req, res) => {
  try {
   

    const user_id = req.user_id; // The data came from the middleware
    const profileData = req.body.profile_data;

    validateJoi(userProfileSchema, { profile_data: profileData });

    await authService.updateProfile(user_id, profileData);
    responseHandler.sendSuccessResponse(
      res,
      null,
      "Profile updated successfully!"
    );
  } catch (error) {
    console.error(
      `[Auth] failed to update profile: ${error.message}`,
      error.stack || ""
    );
    responseHandler.sendErrorResponse(res, error);
  }
};

/**
 * Retrieves the user's data along with profile data.
 *
 * This function fetches the user data along with profile data for a user
 * It sends a success response with the profile data or an error response if the operation fails.
 *
 * @async
 * @param {Object} req - The Express request object.
 * @param {Doc_GetUserParams} req.body - The user's unique identifier, provided by middleware.
 * @param {Object} res - The Express response object.
 * @returns {Promise<void>} Sends a response to the client with the profile data or an error message.
 */
const getUser = async (req, res) => {
  try {
    const user_id = req.user_id; // The data came from the middleware

    const userData = await authService.getUser(user_id);

    responseHandler.sendSuccessResponse(
      res,
      userData,
      "User data retrieved successfully!"
    );
  } catch (error) {
    console.error(
      `[Auth] failed to get profile data: ${error.message}`,
      error.stack || ""
    );
    responseHandler.sendErrorResponse(res, error);
  }
};

/**
 * Updates the user's password.
 *
 * This function handles updating a user's password by validating the input data
 * and applying the changes. It sends a success or error response based on the outcome.
 *
 * @async
 * @param {Object} req - The Express request object.
 * @param {Object} req.body - The request body containing password update details.
 * @param {string|number} req.body.user_id - The user's unique identifier.
 * @param {string} req.body.currentPassword - The user's current password.
 * @param {string} req.body.newPassword - The user's new password.
 * @param {Object} res - The Express response object.
 * @returns {Promise<void>} Sends a response to the client.
 */
const updatePassword = async (req, res) => {
  try {
    const user_id = req.user_id;
    const currentPassword = req.body.currentPassword;
    const newPassword = req.body.newPassword;

    validateJoi(passwordSchema, { currentPassword, newPassword });

    await authService.updatePassword(user_id, currentPassword, newPassword);

    // Send a success response
    responseHandler.sendSuccessResponse(
      res,
      null,
      "Password updated successfully!"
    );
  } catch (error) {
    console.log(error, "error");
    console.error(
      `[Auth] failed to update password: ${error.message}`,
      error.stack || ""
    );
    responseHandler.sendErrorResponse(res, error);
  }
};

/**
 * Handles the password reset request by validating the input, invoking the reset password service,
 * and sending an appropriate response back to the client.
 *
 * @async
 * @function resetPassword
 * @param {Object} req - The request object from the client.
 * @param {Object} req.body - The body of the request containing the email.
 * @param {string} req.body.email - The email address of the user requesting a password reset.
 * @param {Object} res - The response object used to send back the HTTP response.
 * @returns {Promise<void>} A promise that resolves when the password reset process is complete.
 * @throws {Error} Sends an error response if validation fails or if the password reset process encounters an error.
 */
const resetPassword = async (req, res) => {
  try {
    const { email } = req.body;
    
    // Validate the request body
    validateJoi(resetPassSchema, { email });
    
    await authService.resetPassword(email);
    
    responseHandler.sendSuccessResponse(
      res,
      null,
      "Password reset code sent successfully!"
    );
  } catch (error) {
    console.error(
      `[Auth] failed to reset password: ${error.message}`,
      error.stack || ""
    );
    responseHandler.sendErrorResponse(res, error);
  }
};

/**
 * Sets a new password for a user after validating the reset code and new password.
 *
 * @async
 * @function setResetPassword
 * @param {Object} req - The request object from the client.
 * @param {Doc_SetResetPasswordParams} req.body - The body of the request containing the email, code, and new password.
 * @param {Object} res - The response object used to send back the HTTP response.
 * @returns {Promise<void>} A promise that resolves when the password reset process is complete.
 * @throws {Error} Sends an error response if validation fails or if the password reset process encounters an error.
 */
const setResetPassword = async (req, res) => {
  try {
    const { email, code, newPassword } = req.body;

    validateJoi(setResetPassSchema, { email, code, newPassword });

    await authService.setResetPassword(email, code, newPassword);

    responseHandler.sendSuccessResponse(
      res,
      null,
      "Password reset successfully!"
    );
  } catch (error) {
    console.error(
      `[Auth] failed to verify reset password token: ${error.message}`,
      error.stack || ""
    );
    responseHandler.sendErrorResponse(res, error);
  }
};

/**
 * Deletes a user.
 *
 * This function handles the deletion of a user by validating the input data
 * and applying the changes. It sends a success or error response based on the outcome.
 *
 * @async
 * @param {Object} req - The Express request object.
 * @param {Object} req.body - The request body containing user deletion details.
 * @param {string | number} req.body.user_id - The user's unique identifier.
 * @param {Object} res - The Express response object.
 * @returns {Promise<void>} Sends a response to the client.
 */
const deleteUser = async (req, res) => {
  try {
    const user_id = req.params.user_id;

    validateJoi(deleteUserSchema, { user_id });

    const isDeleted = await authService.deleteUser(user_id);

    if (!isDeleted) {
      responseHandler.sendErrorResponse(res, new Error("User not found"));
    }

    responseHandler.sendSuccessResponse(
      res,
      null,
      "User deleted successfully!"
    );
  } catch (error) {
    console.error(
      `[Auth] failed to delete user: ${error.message}`,
      error.stack || ""
    );
    responseHandler.sendErrorResponse(res, error);
  }
};

/**
 * Get all users.
 *
 * This function handles the retrieval of all users by validating the input data
 * and sending a success or error response based on the outcome.
 *
 * @async
 * @param {Object} req - The Express request object.
 * @param {Object} req.body - The request body containing user deletion details.
 * @param {string | number} req.body.user_id - The user's unique identifier.
 * @param {Object} res - The Express response object.
 * @returns {Promise<void>} Sends a response to the client.
 */
const getAllUsers = async (req, res) => {
  try {
    const { page } = req.body;
    const limit = 10;
    const user_id = req.user_id;

    const users = await authService.getAllUsers({ page, limit, user_id });

    responseHandler.sendSuccessResponse(
      res,
      users,
      "Users retrieved successfully!"
    );
  } catch (error) {
    console.error(
      `[Auth] failed to get all users: ${error.message}`,
      error.stack || ""
    );
    responseHandler.sendErrorResponse(res, error);
  }
};

/**
 * Refreshes a user's token.
 *
 * This function handles the refreshing of a user's token by validating the input data
 * and sending a success or error response based on the outcome.
 *
 * @async
 * @param {Object} req - The Express request object.
 * @param {Object} req.body - The request body containing the refresh token.
 * @param {string} req.body.refreshToken - The user's refresh token.
 * @param {Object} res - The Express response object.
 * @returns {Promise<void>} Sends a response to the client.
 */
const refreshToken = async (req, res) => {
  try {
    let user = req.user;
    const { email, tenant_id } = user;
    let { iat, exp, ...userObj } = user;

    let tokenObj = { ...userObj, email, tenant_id };

    const token = await generateJwtToken(tenant_id, tokenObj);

    responseHandler.sendSuccessResponse(
      res,
      { token },
      "Token refreshed successfully!"
    );
  } catch (error) {
    console.error(
      `[Auth] failed to refresh token: ${error.message}`,
      error.stack || ""
    );
    responseHandler.sendErrorResponse(res, error);
  }
};

const getAllApiKeys = async (req, res) => {
  try {
    // const { page, limit } = req.body;
    const apiKeys = await authService.getAllApiKeys();
    responseHandler.sendSuccessResponse(
      res,
      apiKeys,
      "API keys retrieved successfully!" 
    );
  } catch (error) {
    console.error(
      `[Auth] failed to get all API keys: ${error.message}`,
      error.stack || ""
    );
    responseHandler.sendErrorResponse(res, error);
  }
};
const createApiKey = async (req, res) => {

  try {
    const apiKey = await authService.createApiKey();
    responseHandler.sendSuccessResponse(
      res,
      apiKey,
      "API key created successfully!"
    );
  } catch (error) {
    console.error(
      `[Auth] failed to create API key: ${error.message}`,
      error.stack || ""
    );
    responseHandler.sendErrorResponse(res, error);
  }
};
const deleteApiKey = async (req, res) => {
  
  try {
    const apiKeyId = req.params.api_key_id;
    const apiKey = await authService.deleteApiKey(apiKeyId);

    responseHandler.sendSuccessResponse(
      res,
      apiKey,
      "API key deleted successfully!"
    );
  } catch (error) { 
    console.error(
      `[Auth] failed to delete API key: ${error.message}`,
      error.stack || ""
    );
    responseHandler.sendErrorResponse(res, error);
  }
};

export {
  registerUser,
  loginUser,
  updateProfile,
  updatePassword,
  getUser,
  deleteUser,
  getAllUsers,
  refreshToken,
  resetPassword,
  setResetPassword,
  getAllApiKeys,
  createApiKey,
  deleteApiKey,
};

// ============== JOI SCHEMA ================

// Register schema
const registerSchema = Joi.object({
  username: Joi.string().required().lowercase().trim().min(3).max(30).messages({
    "string.base": "Username must be a string",
    "any.required": "Username is required",
    "string.min": "Username must be at least 3 characters long",
    "string.max": "Username must be at most 30 characters long",
  }),
  password: Joi.string().min(6).required().messages({
    "string.min": "Password must be at least 6 characters long",
    "any.required": "Password is required",
  }),
  role_id: Joi.alternatives()
    .try(Joi.string(), Joi.number())
    .required()
    .custom((roleId) => {
      if (!getRoleById(roleId)) {
        throw new Error("Role ID is invalid");
      }
    })
    .messages({
      "any.required": "Role ID is required",
    }),
  tenant_id: Joi.alternatives()
    .try(Joi.string(), Joi.number())
    .required()
    .messages({
      "any.required": "Tenant ID is required",
    }),
  // sendEmail: Joi.boolean().messages({
  //     'any.required': 'Send email is required',
  // }),
  password: Joi.string().min(6).required().messages({
    "string.min": "Password must be at least 6 characters long",
    "any.required": "Password is required",
  }),
  // profile_data: Joi.object({
  //     full_name: Joi.string().min(5).required().messages({
  //         'string.min': 'Full name must be at least 5 characters long',
  //         'string.base': 'Full name must be a string',
  //         'any.required': 'Full name is required'
  //     })
  // }).required().messages({
  //     'any.required': 'Profile data is required'
  // })
});

// Login schema
const loginSchema = Joi.object({
  username: Joi.string().required().lowercase().trim().min(3).max(30).messages({
    "string.base": "Username must be a string",
    "any.required": "Username is required",
    "string.min": "Username must be at least 3 characters long",
    "string.max": "Username must be at most 30 characters long",
  }),
  // email: Joi.string().email().required().messages({
  //     'string.email': 'Email must be a valid email address',
  //     'any.required': 'Email is required',
  // }),
  password: Joi.string().min(6).required().messages({
    "string.min": "Password must be at least 6 characters long",
    "any.required": "Password is required",
  }),
  tenant_id: Joi.alternatives()
    .try(Joi.string(), Joi.number())
    .required()
    .messages({
      "any.required": "Tenant ID is required",
    }),
});

// User profile schema
const userProfileSchema = Joi.object({
  profile_data: Joi.object().required().messages({
    "object.base": "Profile data must be an object",
    "any.required": "Profile data is required",
  }),
});

// Password schema
const passwordSchema = Joi.object({
  currentPassword: Joi.string().min(6).required().messages({
    "string.base": "Current password must be a string",
    "string.min": "Current password must be at least 6 characters long",
    "any.required": "Current password is required",
  }),
  newPassword: Joi.string().min(6).required().messages({
    "string.base": "New password must be a string",
    "string.min": "New password must be at least 6 characters long",
    "any.required": "New password is required",
  }),
});

// Delete user schema
const deleteUserSchema = Joi.object({
  user_id: Joi.string().required().messages({
    "any.required": "User ID is required",
  }),
});

// Reset password schema
const resetPassSchema = Joi.object({
  email: Joi.string().email().required().messages({
    "string.email": "Email must be a valid email address",
    "any.required": "Email is required",
  }),
});

const setResetPassSchema = Joi.object({
  email: Joi.string().email().required().messages({
    "string.email": "Email must be a valid email address",
    "any.required": "Email is required",
  }),
  code: Joi.string().required().messages({
    "any.required": "Code is required",
  }),
  newPassword: Joi.string().min(6).required().messages({
    "string.base": "New password must be a string",
    "string.min": "New password must be at least 6 characters long",
    "any.required": "New password is required",
  }),
});

// ============== JS DOC ================

/**
 * @typedef {Object} Doc_RegisterUserParams
 * @property {string} email - The user's email address.
 * @property {string | number} role_id - The role ID associated with the user.
 * @property {{full_name: string}} profile_data - The profile data
 * @property {boolean} sendEmail - Whether to send an email to the user.
 * @property {string} password - The user's password.
 */

/**
 * @typedef {Object} Doc_LoginUserParams
 * @property {string} email - The user's email address.
 * @property {string} password - The user's password.
 * @property {string} tenant_id - The tenant ID associated with the user.
 */

/**
 * @typedef {Object} Doc_UserProfileParams
 * @property {Object.<string, any>} profile_data - An object containing the user's profile data.
 * @property {string | number} [user_id] - The user id, if not provided it will be taken from the token middleware. This is only for admin users who are updating other users' profile.
 */

/**
 * @typedef {Object} Doc_UpdatePasswordParams
 * @property {string} currentPassword - The user's current password.
 * @property {string} newPassword - The user's new password.
 * @property {string | number} [user_id] - The user id, if not provided it will be taken from the token middleware. This is only for admin users who are updating other users' passwords.
 */

/**
 * @typedef {Object} Doc_ResetPasswordParams
 * @property {string} email - The user's email
 */

/**
 * @typedef {Object} Doc_SetResetPasswordParams
 * @property {string} email - The user's email
 * @property {string} code - The reset code sent to the user's email.
 * @property {string} newPassword - The new password to be set for the user.
 */

/**
 * @typedef {Object} Doc_GetUserParams
 * @property {string | number} [user_id] - The user id, if not provided it will be taken from the token middleware. This is only for admin users who are fetching other users' profile.
 */
