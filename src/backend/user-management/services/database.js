import { Sequelize } from "sequelize";
import sequelize from "../models/index.js";
import { DB } from "../config/env.js";
import { hashPassword } from "../utils/hashUtils.js";

// Import all models
import User from "../models/User.js";
import Tenant from "../models/Tenant.js";
import UserProfile from "../models/UserProfile.js";

/**
 * Ensures that the specified database exists. If the database does not exist, it creates it.
 *
 * This function connects to the default `mysql` database using the provided credentials
 * and checks for the existence of the target database. If the target database does not exist,
 * it creates the database.
 *
 * @async
 * @function ensureDatabase
 * @returns {Promise<void>} A promise that resolves when the database check and creation (if needed) is complete.
 *
 * @throws {Sequelize.ConnectionError} If there is an error connecting to the database.
 * @throws {Sequelize.DatabaseError} If there is an error executing the database creation query.
 */
async function ensureDatabase() {
  console.info(`[DB] Checking if database '${DB.NAME}' exists...`);
  
  // Connect to the database
  const rootSequelize = new Sequelize("", DB.USER, DB.PASSWORD, {
    host: DB.HOST,
    port: DB.PORT,
    dialect: "mariadb",
    logging: false,
  });

  // Check if the target database exists, and create it if it does not
  try {
    await rootSequelize.query(`CREATE DATABASE IF NOT EXISTS ${DB.NAME};`);
    console.info(`[DB] Database '${DB.NAME}' is ready.`);
  } catch (error) {
    console.error(`[DB] Error:`, error.message, error.stack || "");
    if (error.name === "SequelizeConnectionError") {
      console.fatal(
        `[DB] Connection Error: Failed to connect to MariaDB at ${DB.HOST}.`,
        error
      );
    } else if (error.name === "SequelizeDatabaseError") {
      console.fatal(
        `[DB] Database Error: Failed to create database '${DB.NAME}'.`,
        error
      );
    } else {
      console.fatal(`[DB] Unknown Error:`, error.message, error.stack || "");
    }

    process.exit(1);
  } finally {
    await rootSequelize.close();
  }
}

let maxRetries = 5;
let retries = 0;
/**
 * Ensures that the specified database exists. If the database does not exist, it creates it.
 *
 * This function connects to the default `mysql` database using the provided credentials
 * and checks for the existence of the target database. If the target database does not exist,
 * it creates the database.
 *
 * @async
 * @function ensureDatabase
 * @returns {Promise<void>}
 *
 * @throws {Sequelize.ConnectionError} If there is an error connecting to the database.
 * @throws {Sequelize.DatabaseError} If there is an error executing the database creation query.
 */
async function connectWithRetry() {
  return new Promise((resolve, reject) => {
    try {
      console.info(`[DB] Connecting to database '${DB.NAME}'...`);

      sequelize.authenticate().then(() => {
        console.info(`[DB] Successfully connected to database '${DB.NAME}'`);
        resolve();
      });
    } catch (error) {
      console.error(
        `[DB] Database connection failed. Retrying in 5 seconds...`,
        error.message,
        error.stack || ""
      );
      retries++;
      if (retries >= maxRetries) {
        console.fatal(
          `[DB] Maximum connection retries exceeded. Exiting application...`
        );
        process.exit(1); // Stop the application if connection fails
      }

      setTimeout(connectWithRetry, 5000); // Retry after 5 seconds
    }
  });
}

/**
 * Synchronizes all database tables.
 *
 * This function attempts to synchronize all defined models with the database tables
 * using Sequelize's `sync` method. If the table did not exists then it will create it. It logs the process and handles any errors that occur
 * during synchronization.
 *
 * @async
 * @function syncDatabaseTables
 *
 * @returns {Promise<void>}
 *
 * @throws Will log a fatal error and exit the process if the synchronization fails.
 *
 */
const syncDatabaseTables = async () => {
  console.info("[Db] Syncing database tables...");
  try {
    await sequelize.sync(); // Bulk sync all models in one DB call. This will create the model tables if it is not exist
    console.info("[Db] Database synced successfully!");
  } catch (error) {
    console.log("[Db] Database sync failed:", error.message, error.stack || "");
    process.exit();
  }
};

/**
 * Ensures the database is seeded with default data.
 *
 * This asynchronous function checks if the database is empty by counting the number of users.
 * If no users are found, it seeds the database with default data, including a default tenant,
 * an admin user, and associated user profile data.
 *
 * @returns {Promise<void>} A promise that resolves when the default data is successfully seeded.
 *
 */
const ensureSeedData = async () => {
  if ((await Tenant.count()) === 0) {
    // Seed the default Tenant table with default data
    await Tenant.create({ name: "Ruleforge" });
  }

  // Check if there are zero users which means the database is empty
  if ((await User.count()) > 0) return;

  // Create default admin user
  await User.create({
    tenant_id: 1, // Default tenant
    username: "admin",
    password: await hashPassword("password"),
    role_id: 1, // Admin role
    profile_completed: true,
  });

  // Add user profile data
  await UserProfile.create({
    user_id: 1,
    field: "full_name",
    value: "Admin",
  });

  console.info("[DB] Default data successfully seeded...");
};

/**
 * Initializes the database.
 *
 * This asynchronous function performs the necessary steps to initialize the database
 * for the application. It ensures the database exists, establishes a connection with
 * retry logic, and synchronizes all defined models with the database tables.
 *
 */
async function initDb() {
  try {
    await ensureDatabase();
    await connectWithRetry();
    await syncDatabaseTables();
    await ensureSeedData();
  } catch (error) {
    console.error(
      "[DB] Database initialization failed:",
      error.message,
      error.stack || ""
    );
    process.exit(1);
  }
}

export { sequelize, initDb, User, Tenant, UserProfile };
