import argon2 from "argon2";
import User from "../models/User.js";
import UserProfile from "../models/UserProfile.js";
import { generateJwtToken } from "../utils/jwtUtils.js";
import { hashPassword } from "../utils/hashUtils.js";
import { sequelize } from "./database.js";
import { ROLES } from "../config/roles.js";
import { Op } from "sequelize";
import { sendEmail } from "../email-service/emailService.js";
import { JWT } from "../config/env.js";
import ApiKeys from "../models/ApiKeys.js";

/**
 * Registers a new user
 * @param {{email: string, password: string, tenant_id: number, role_id: number, profile_data: Object.<string, string>}} usrObj - User object
 * @returns  - Registered user, api token if the user is the api
 */
export const registerUser = async (usrObj) => {
  const rawPass = usrObj.password;
  usrObj.profile_data = {};
  const existingUser = await User.findOne({
    where: { username: usrObj.username, tenant_id: usrObj.tenant_id },
  });

  if (existingUser) throw new Error("User already exists");

  // Hash the password
  usrObj.password = await hashPassword(usrObj.password);
  usrObj.profile_completed = 1; // Profile is always completed on registration

  // Lowercase the email
  usrObj.username = usrObj.username.toLowerCase();

  // Create a transaction to ensure data integrity
  return await sequelize.transaction(async (transaction) => {
    // Create the user
    const user = await User.create(usrObj, { transaction });

    return {
      userData: {
        user,
      },
      usrObj,
      rawPass,
    }; // Auto-committed if no errors
  });
};

/**
 * Logs in a user and returns a JWT token.
 * @param {string} email - User's email.
 * @param {string} password - User's plain text password.
 * @param {number} tenantId - Tenant ID for multi-tenancy.
 * @returns {Promise<string>} - JWT token.
 */
export const loginUser = async (username, password, tenantId) => {
  /** @type {import('../models/User.js').Model_User} */
  const user = await User.findOne({
    where: { username, tenant_id: tenantId },
    include: {
      model: UserProfile,
      as: "userProfile", // Ensure alias matches association definition
      attributes: ["field", "value"], // only return field and value
    },
  });

  if (!user) {
    throw new Error("Invalid username or password");
  }

  const passwordMatch = await argon2.verify(user.password, password);
  if (!passwordMatch) {
    throw new Error("Invalid username or password");
  }

  const userData = user.toJSON();
  const token = await generateJwtToken(tenantId, userData);

  return {
    token,
    user: userData,
    refresh_token_t: JWT.EXPIRATION,
  };
};

/**
 * Updates the user profile data.
 * @param {number} userId
 * @param {import('../models/User.js').Model_User} userData
 * @param {Object.<string, string | number} [profileData]
 * @param {number} [role_id] If we want to update the role of the user
 * @returns
 */
export const updateProfile = async (user_id, profileData, role_id = null) => {
  return await sequelize.transaction(async (transaction) => {
    // If the role id is provided
    if (role_id) {
      await User.update({ role_id }, { where: { id: user_id }, transaction });
    }

    const updates = Object.keys(profileData).map((key) => ({
      user_id: user_id,
      field: key,
      value: profileData[key],
    }));

    await UserProfile.bulkCreate(updates, {
      updateOnDuplicate: ["value"], // This updates existing records instead of inserting duplicates
      transaction,
    });
  });
};

/**
 * Retrieves a user's profile by their user ID.
 *
 * This function fetches the user's profile data from the database, including associated user profile fields.
 * It throws an error if the user is not found.
 *
 * @async
 * @param {string|number} userId - The unique identifier of the user.
 * @returns {Promise<Object>} A promise that resolves to the user's profile data as a JSON object.
 * @throws {Error} Throws an error if the user is not found.
 */
export const getUser = async (userId) => {
  const profile = await User.findByPk(userId, {
    attributes: ["id", "username",],
    // include: {
    //   model: UserProfile,
    //   as: "userProfile",
    //   attributes: ["field", "value"],
      
    // },
  });
  
  if (!profile) {
    throw new Error("User not found");
  }
  

  return profile.toJSON();
};

/**
 * Updates the user password.
 * @param {number} userId
 * @param {string} currentPassword
 * @param {string} newPassword
 * @returns {Promise<void>}
 */

export const updatePassword = async (userId, currentPassword, newPassword) => {
  
  const user = await User.findByPk(userId);
  
  if (!user) {
    throw new Error("User not found");
  }

  const passwordMatch = await argon2.verify(user.password, currentPassword);

  if (!passwordMatch) {
    throw new Error("Invalid current password");
  }

  const hashedPassword = await hashPassword(newPassword);

  await user.update({ password: hashedPassword });
};

/**
 * Resets the password for a user by generating a reset code and sending it via email.
 *
 * @param {string} email - The email address of the user requesting a password reset.
 * @returns {Promise<void>} A promise that resolves when the password reset process is complete.
 * @throws {Error} Throws an error if the user is not found or if the transaction fails.
 */
export const resetPassword = async (email) => {
  try {
    const user = await User.findOne({
      where: { email },
      include: {
        model: UserProfile,
        as: "userProfile",
        attributes: ["field", "value"],
      },
    });

    // Create a transaction to ensure data integrity
    return await sequelize.transaction(async (transaction) => {
      // Generate a random 5 digit code
      const resetCode = Math.floor(10000 + Math.random() * 90000);

      // Store the reset code in the user profile
      await user.update(
        { pass_reset_code: resetCode, pass_code_gen_time: new Date() },
        { transaction }
      );

      // Send the email
      /** @type {import('../email-service/templates/resetPasswordCode')} */
      const emailData = {
        name: user.userProfile.find((p) => p.field === "full_name").value,
        otpCode: resetCode,
      };

      // Send email notification
      await sendEmail(email, "Password Reset Code", "passwordReset", emailData);
    });
  } catch (error) {
    console.error(
      `[Auth] failed to reset password: ${error.message}`,
      error.stack || ""
    );
    // responseHandler.sendErrorResponse(res, error);
  }
};

// function to set the password, by the email, code, and new password. Verify if the code is valid and not expired 15 minutes, then update the password
/**
 * @param {string} email - The email address of the user requesting a password reset.
 * @param {number} resetCode - The reset code sent to the user's email.
 * @param {string} newPassword - The new password to set.
 * @returns {Promise<void>} A promise that resolves when the password reset process is complete.
 * @throws {Error} Throws an error if the user is not found, the reset code is invalid or expired, or if the transaction fails.
 */
export const setResetPassword = async (email, resetCode, newPassword) => {
  const user = await User.findOne({ where: { email } });

  if (!user) {
    throw new Error("User not found");
  }

  // Check if the reset code is valid
  if (user.pass_reset_code !== resetCode) {
    throw new Error("Invalid reset code");
  }

  // Check if the reset code is expired (15 minutes)
  const codeGenTime = new Date(user.pass_code_gen_time);
  const currentTime = new Date();
  const diff = currentTime - codeGenTime;
  const diffMinutes = Math.floor(diff / 60000); // Convert to minutes

  if (diffMinutes > 15) {
    throw new Error("Reset code expired");
  }

  // Hash the new password
  const hashedPassword = await hashPassword(newPassword);

  // Update the user's password
  await user.update({
    password: hashedPassword,
    pass_reset_code: null,
    pass_code_gen_time: null,
  });
};

/**
 * Deletes a user.
 * @param {number} userId
 * @returns {Promise<void>}
 */
export const deleteUser = async (userId) => {
  try {
    await User.destroy({ where: { id: userId } });
    return true;
  } catch (error) {
    console.error(
      `[Auth] failed to delete user: ${error.message}`,
      error.stack || ""
    );
    return false;
  }
};

/**
 * Retrieves all users.
 * @returns {Promise<Array>}
 */
export const getAllUsers = async ({ page, limit, user_id }) => {
  // First get the total count of all users
  const totalCount = await User.count({
    where: {
      id: {
        [Op.ne]: user_id, // Exclude the current user
      },
    },
  });
  const users = await User.findAll({
    where: {
      id: {
        [Op.ne]: user_id, // Exclude the current user
      },
    },
    attributes: ["id", "username"],
    // include: {
    //   model: UserProfile,
    //   as: "userProfile",
    //   attributes: ["field", "value"],
    //   separate: true, // Fetch UserProfile separately
    //   where: {
    //     field: {
    //       [Op.ne]: "api_token", // Exclude entries where field is 'api_token'
    //     },
    //   },
    // },
    order: [["id", "DESC"]], // Sort by id in descending order
    offset: (page - 1) * limit,
    limit: limit,
  });
  let resObj = {
    success: true,
    data: {
      users: users,
      total: totalCount,
      page: page,
      limit: limit,
    },
  };

  return resObj;
};

export const getAllApiKeys = async () => {
  try {
    const apiKeys = await ApiKeys.findAll({
      attributes: ["id", "timestamp", "api_key"],
      // order: [["timestamp", "DESC"]],
      // offset: (page - 1) * limit,
      // limit: limit,
    });
    // const totalCount = await ApiKeys.count();
    let resObj = {
      success: true,
      data: {
        apiKeys: apiKeys,
        // total: totalCount,
        // page: page,
        // limit: limit,
      },
    };
    return resObj;
  } catch (error) {
    console.error(
      `[Auth] failed to get all API keys: ${error.message}`,
      error.stack || ""
    );
    throw error;
  }
};

export const createApiKey = async () => {


  // If role is the API user, generate & store API token
  // if (usrObj.role_id.toString() === ROLES.API.id.toString()) {
  let apiToken = await generateJwtToken(
    1,
    {
      id: 1,
      tenant_id: 1,
      role_name: ROLES.API.slug,
    },
    2629744 // 1 month in seconds
  );
  let obj = {
    api_key: apiToken,
    timestamp: new Date(),
  };
  try {
    const newApiKey = await ApiKeys.create(obj,{
      returning: true,
      attributes: ['id', 'api_key']
    });
    return {
      api_key: newApiKey.api_key,
      id: newApiKey.id,
    };

  } catch (error) {
    console.error(
      `[Auth] failed to create API key: ${error.message}`,
      error.stack || ""
    );
    throw error;
  }
};

export const deleteApiKey = async (apiKeyId) => {
  try {
    await ApiKeys.destroy({ where: { id: apiKeyId } });
    return true;
  } catch (error) {
    console.error(
      `[Auth] failed to delete API key: ${error.message}`,
      error.stack || ""
    );
    throw error;
  }
};
