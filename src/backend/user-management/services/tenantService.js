import tenantKeys from '../config/tenants.js';

// For ease of handling the keys, we are storing as base64 and for performance reasons we can decode them only once
Object.keys(tenantKeys).forEach(key => {
    tenantKeys[key].privateKey = Buffer.from(tenantKeys[key].privateKey, 'base64').toString('utf8');
    tenantKeys[key].publicKey = Buffer.from(tenantKeys[key].publicKey, 'base64').toString('utf8');
});


/**
 * Retrieves the tenant's public-private key pair from the database.
 * 
 * @note We can generate the keys using the `generateKeyPair` function from `keyUtils.js`.
 * @param {number} tenantId - The ID of the tenant.
 * @returns {Promise<{privateKey: string, publicKey: string}>} - Object containing the tenant's keys.
 */
export const getTenantKeys = async (tenantId) => {

    // Check if tenant not exists
    if (!tenantKeys[tenantId]) {
        throw new Error('Tenant not found');
    }

    return tenantKeys[tenantId]
};
