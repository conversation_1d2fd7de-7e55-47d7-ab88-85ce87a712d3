// testApiClient.js
import axios from "axios";
import https from "https";

const api = axios.create({
  baseURL: "https://localhost:3000",
  httpsAgent: new https.Agent({
    rejectUnauthorized: false, // Accept self-signed certificates for testing
  }),
});

(async () => {
  try {
    // Replace with the actual API token obtained from testClient.js
    const apiToken =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwiaWF0IjoxNzI4ODkxNzM1LCJleHAiOjE3Mjk0OTY1MzV9.zv_W0DIzBugHVIULpuRc-0yl7d6lZjMUPg5ShWbrDk4";

    // Validate API token
    const response = await api.get("/validate-api-token", {
      headers: { "x-api-token": apiToken },
    });
    console.log("API Token Validation:", response.data);
  } catch (error) {
    console.error(
      "Error:",
      error.response ? error.response.data : error.message,
    );
  }
})();
