// For now it is hard coded, but for the SAAS application it should be fetched from the database and the private key should be encrypted and stored in the database.
const tenantKeys = {
    1: {
        privateKey: '************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        publicKey: 'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUZrd0V3WUhLb1pJemowQ0FRWUlLb1pJemowREFRY0RRZ0FFaHRGQ0U3blo0RlhvZzMvN3lGRG5velRCSlhLdQpoNWpYWDI5T1NYOW0xbWxseWNMTlNzdE8vWWRDTk1oQmxmdXZnWjNuSWluUlNyWlBYQTRmQmZ1Wmd3PT0KLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg==',
    }
}

export default tenantKeys;