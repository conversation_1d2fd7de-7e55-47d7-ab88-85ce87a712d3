/**
 * Application Roles Configuration
 * 
 * This object defines the various roles available within the application, each with a unique
 * identifier, slug, and label. These roles are used to manage access control and permissions
 * throughout the application.
 */
const ROLES = {
    ADMIN:
    {
        id: 1,
        slug: 'user-admin',
        label: 'User Administrator'
    },
    USER:
    {
        id: 2,
        slug: 'campaign-admin',
        label: 'Campaign Administrator'
    },
    API:
    {
        id: 3,
        slug: 'api',
        label: 'API User'
    }
}

/**
 * Retrieves a role object by its slug.
 * 
 * This function searches through the defined roles in the `ROLES` configuration
 * and returns the role object that matches the provided slug. If no matching role
 * is found, it returns `undefined`.
 * @param {string} slug The slug of the role to retrieve.
 * @returns {{id: number, slug: string, label: string}|undefined} The role object with the matching slug, or `undefined` if no match is found.
 */
function getRoleBySlug(slug) {
    return Object.values(ROLES).find(role => role.slug === slug);
}

/**
 * Retrieves a role object by its ID.
 * 
 * This function searches through the defined roles in the `ROLES` configuration
 * and returns the role object that matches the provided ID. If no matching role
 * is found, it returns `undefined`.
 * 
 * @param {number} id - The ID of the role to retrieve.
 * @returns {{id: number, slug: string, label: string}|undefined} The role object with the matching ID, or `undefined` if no match is found.
 */
function getRoleById(id) {
    return Object.values(ROLES).find(role => role.id.toString() === id.toString());
}


/**
 * Get an array of role IDs.
 * @returns {number[]} An array of role IDs
 */
function getRoleIds() {
    return Object.values(ROLES).map(role => role.id);
}


export { ROLES, getRoleBySlug, getRoleById, getRoleIds };