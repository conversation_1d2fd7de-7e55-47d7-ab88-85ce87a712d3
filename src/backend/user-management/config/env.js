import path from 'path';
import { fileURLToPath } from 'url';

// Resolve directory paths properly in ES Modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export const PORT = process.env.PORT || 3000;

export const DB = {
    HOST: process.env.DB_HOST || '127.0.0.1',
    PORT: process.env.DB_PORT || 3307,
    NAME: process.env.DB_NAME || 'ruleforge_user_management',
    USER: process.env.DB_USER || 'root',
    PASSWORD: process.env.DB_PASS || 'ussdgw',
};

export const JWT = {
    /** Expiration time for the JWT token in seconds */
    EXPIRATION: parseInt(process.env.JWT_EXPIRATION || 900) ,
};