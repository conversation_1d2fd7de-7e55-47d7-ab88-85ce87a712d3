/**
 * Traverse `obj` and whenever you find {} at a path in `arrayPaths`,
 * replace it with [].
 *
 * Handles nested arrays too.
 *
 * @param {object} obj
 * @param {string[]} arrayPaths  e.g. ['some_array.valueToChange']
 * @returns {object}  the same object you passed in, with replacements done
 */
export function transformEmptyObjectsToArrays(obj, arrayPaths) {
  const paths = new Set(arrayPaths);

  function recurse(current, basePath) {
    if (Array.isArray(current)) {
      // dive into each array element with the same basePath
      current.forEach(item => recurse(item, basePath));
    } else if (current && typeof current === 'object') {
      for (const key of Object.keys(current)) {
        const fullPath = basePath ? `${basePath}.${key}` : key;
        const val = current[key];

        // replace exactly-empty-objects at matching paths
        if (
          val &&
          typeof val === 'object' &&
          !Array.isArray(val) &&
          Object.keys(val).length === 0 &&
          paths.has(fullPath)
        ) {
          current[key] = [];
        } else {
          recurse(val, fullPath);
        }
      }
    }
  }

  recurse(obj, '');
  return obj;
}

