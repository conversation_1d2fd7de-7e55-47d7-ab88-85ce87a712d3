import { Readable } from 'stream';

/**
 * Creates a CSV stream from the given data.
 * The function dynamically handles data in various formats: objects, arrays, or simple strings.
 * It outputs a readable stream with CSV-formatted data.
 *
 * @param {Array<Object|Array|string>} data - The data to be converted into CSV. Each element can be:
 *                                            - An object: Keys are matched against the `headers`.
 *                                            - An array: Each array is treated as a row.
 *                                            - A string: Each string is treated as a single value row.
 * @param {Array<string>} [headers=[]] - The headers for the CSV. These are used to extract values from
 *                                       objects in the `data` array. If not provided, the function
 *                                       assumes no headers are needed.
 * @returns {Readable} - A readable stream containing the CSV content.
 *
 * @example
 * // Example with objects
 * const data = [
 *   { name: "<PERSON>", age: 25, email: "<EMAIL>" },
 *   { name: "<PERSON>", age: 30, email: "<EMAIL>" },
 * ];
 * const headers = ["name", "age", "email"];
 * const stream = createCsvStream(data, headers);
 * stream.pipe(process.stdout);
 *
 * @example
 * // Example with arrays
 * const data = [
 *   ["Alice", 25, "<EMAIL>"],
 *   ["<PERSON>", 30, "<EMAIL>"],
 * ];
 * const headers = ["name", "age", "email"];
 * const stream = createCsvStream(data, headers);
 * stream.pipe(process.stdout);
 *
 * @example
 * // Example with strings
 * const data = ["Alice", "Bob", "Charlie"];
 * const headers = ["Name"];
 * const stream = createCsvStream(data, headers);
 * stream.pipe(process.stdout);
 */
export default function createCsvStream(data, headers = []) {
    const readable = new Readable({
      read() {
        // Write headers
        if (headers.length) {
          this.push(headers.join(',') + '\n');
        }
  
        // Write each row of data
        data.forEach((row) => {
          if (Array.isArray(row)) {
            // Treat `row` as an array
            this.push(row.join(',') + '\n');
          } else if (typeof row === 'object') {
            // Treat `row` as an object
            const values = headers.map((header) => escapeCsvValue(row[header] || ''));
            this.push(values.join(',') + '\n');
          } else {
            // Treat `row` as a single value
            this.push(escapeCsvValue(row) + '\n');
          }
        });
  
        // Signal end of stream
        this.push(null);
      },
    });
  
    return readable;
  }

/**
 * Escapes a value for CSV formatting. When a value contains commas, double quotes, or newlines, you need to properly encode the value for CSV formatting. In a CSV file, such values should be enclosed in double quotes, and any double quotes inside the value must be escaped by doubling them ("").
 * @param {string} value - The value to escape
 * @returns {string} - The escaped value
 */
function escapeCsvValue(value) {
    if (typeof value === 'string') {
      // Escape double quotes and wrap the value in quotes if it contains special characters
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        return `"${value.replace(/"/g, '""')}"`;
      }
    }
    return value;
  }

