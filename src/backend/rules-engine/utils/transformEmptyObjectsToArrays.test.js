import { jest, expect, describe, test, beforeEach } from "@jest/globals";

import {transformEmptyObjectsToArrays} from "./transformEmptyObjectsToArrays.js";


describe("transforEmptyObjectsToArrays",()=>{
  test("it changes the incomming json by replacing empty objects with empty arrays for specified fields",async ()=>{

    const entity = {
      entityId: "RETAIL_POS",
      entityName: "Retail Point of Sale System",
      entityDescription: "Handles in-store purchases and customer interactions",
      globalActions: {},
      transactionContexts:{} 
    }
    const arrayPaths = ["globalActions", "transactionContexts"];

    let transformed = transformEmptyObjectsToArrays(entity, arrayPaths);

    console.log({transformed });

    expect(transformed.globalActions).toEqual([]);
    expect(transformed.transactionContexts).toEqual([]);

  })
})


