import csvParser from 'csv-parser';
import { Readable } from 'stream';

class CSVProcessingService {
    /**
     * Processes a CSV file and extracts data from the specified column with the option to cast the data to a specific type.
     * @param {Buffer} fileBuffer - The CSV file content as a buffer.
     * @param {number} columnIndex - The index of the column to extract (default is the first column).
     * @param {"string" | "number"} castType - The type to cast the extracted data to (e.g., 'string', 'number'
     * @returns {Promise<Array>} - A promise that resolves to an array of extracted data.
     */
    static async extractColumnData(fileBuffer, castType = 'string', columnIndex = 0,) {
        return new Promise((resolve, reject) => {
            const data = []; // Array to store extracted data
            const readableStream = Readable.from(fileBuffer); // Convert buffer to readable stream
            let isValid = false;

            readableStream
                .pipe(csvParser({ headers: false }))
                .on('data', (row) => {
                    const columnValue = Object.values(row)[columnIndex]; // Get the specified column value
                    let castedValue = columnValue;
                    if (castedValue) {

                        // Cast to the number if specified
                        if (castType === 'number') {
                            castedValue = Number(columnValue);
                            
                            // If not value is not a number then reject
                            if (isNaN(castedValue)) {
                                return reject(new Error(`Unable to cast value to number: '${columnValue}' at row ${data.length + 2}`));
                            }
                        }

                        // Cast to the string if specified
                        if (castType === 'string') {
                            castedValue = String(columnValue);
                        }


                        data.push(castedValue);
                    }
                    isValid = true; // File contains valid data
                })
                .on('end', () => {
                    if (!isValid) {
                        return reject(new Error('The CSV file is empty or invalid'));
                    }
                    resolve(data); // Resolve the promise with the extracted data
                })
                .on('error', (error) => {
                    reject(new Error(`Error parsing CSV file: ${error.message}`)); // Reject on error
                });
        });
    }


    /**
     * Processes a CSV file and returns all rows as an array of objects.
     * @param {Buffer} fileBuffer - The CSV file content as a buffer.
     * @returns {Promise<Array>} - A promise that resolves to an array of row objects.
     */
    static async processCSV(fileBuffer) {
        return new Promise((resolve, reject) => {
            const rows = []; // Array to store all rows
            const readableStream = Readable.from(fileBuffer);

            readableStream
                .pipe(csvParser())
                .on('data', (row) => {
                    rows.push(row); // Add each row to the array
                })
                .on('end', () => {
                    resolve(rows); // Resolve the promise with all rows
                })
                .on('error', (error) => {
                    reject(new Error(`Error processing CSV file: ${error.message}`));
                });
        });
    }
}

export default CSVProcessingService;