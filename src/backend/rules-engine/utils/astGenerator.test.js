import { jest, expect, describe, test, beforeEach } from "@jest/globals";

import { createAst, executeRulesetEvaluation, generateCode , ExecutionTarget, executeRulesetOutcomeNotification} from "./astGenerator.js";

import { validateRuleset } from "../validations/rulesetValidation.js";

const loggingContext = [{ unitTestingModule: "astGenerator.test.js" }];

const entityId = "45a6997d-0f79-483f-95fb-40e222946667";

const createMockReq = (body) => ({ body });
const createMockRes = () => {
  const res = {};
  res.status = jest.fn((status)=>{console.log({status:status});}).mockReturnValue(res);
  res.json = jest.fn((json)=>{console.log({json:json});}).mockReturnValue(res);
  return res;
};

expect.extend({
  toEqualIgnoringOrder(received, expected) {
    const compareObjects = (obj1, obj2) => {
      if (Array.isArray(obj1) && Array.isArray(obj2)) {
        if (obj1.length === obj2.length) return false;
        return obj1.every((item1) =>
          obj2.some((item2) => compareObjects(item1, item2)),
        );
      } else if (
        typeof obj1 === "object" &&
        obj1 !== null &&
        typeof obj2 === "object" &&
        obj2 !== null
      ) {
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);
        if (keys1.length !== keys2.length) return false;
        return keys1.every((key) => compareObjects(obj1[key], obj2[key]));
      } else {
        return obj1 === obj2;
      }
    };

    const pass = compareObjects(received, expected);
    return {
      pass,
      message: () =>
      `expected ${JSON.stringify(received)} to equal ${JSON.stringify(expected)} ignoring order`,
    };
  },
});


describe("astGenerator", () => {
  let req, res, next;



  const mockedRedlock = {
    acquire: jest.fn(),
  };

  const redisStorage = {};

  // Create a mocked Redis client
  const mockedRedisClient = {
    connect: jest.fn().mockResolvedValue(undefined),
    on: jest.fn(),
    hset: jest.fn(async (hash, key, value) => {
      if (!redisStorage[hash]) {
        redisStorage[hash] = {};
      }
      redisStorage[hash][key] = value;
      return 1; // Return 1 field added
    }),
    sismember: jest.fn(async (/*elementsKey, value*/) => {
      return true;
    }),
    quit: jest.fn(async () => {}),
    eval: jest.fn(async () => {}),
    evalsha: jest.fn(async () => {}),
    hget: jest.fn(async (hash, key) => {
      if (hash === "lists:{common}" && key === "topSellers") {
        return JSON.stringify({
          name: "topSellers",
          type: "string",
          elemsCount: 7,
          elementsKey: "lst:{common}:elems:topsellers",
        });
      } else if (redisStorage[hash] && redisStorage[hash][key] !== undefined) {
        return redisStorage[hash][key];
      } else {
        return null; // Field does not exist
      }
    }),
     // Add the missing Redis methods
     lpush: jest.fn(async (key, value) => {
      // Mock lpush by returning the length of the list (Redis lpush returns new length)
      if (!redisStorage[key]) {
        redisStorage[key] = [];
      }
      redisStorage[key].unshift(value); // Add to beginning of array (like lpush)
      return redisStorage[key].length;
    }),
  };
  
  describe("Functions", () => {
    const rulesetId = "PMF_FUNCTION_TESTING";
    const contextId = "PMF_FUNCTION_TESTING_CONTEXT";

    const ruleset = {
      rulesetId,
      entityId,
      version: 1,
      category: "UNIT_TESTING",
      name: "Set String Testing",
      description: "for testing lists",
      status: "ACTIVE", 
      schemaVersion: "1.0.0",
      startDateTime: "1970-01-01T00:00:00Z",
      endDateTime: "2034-01-01T00:00:00Z",
      lastModifiedDateTime: "2024-01-01T00:00:00Z",
      localVariables: [
        {
          variableId: "currentDate",
          name: "Current date",
          description: "Todays date",
          type: "string",
          defaultValue: "",
        },
      ],
      collectionMappings: [],
      contextId,
      persistentVariables: [],
      outcomeRules: [],
      evaluationRules: [
        {
          ruleId: "RULE_1",
          name: "RULE_1",
          description: "Tests all variable operations",
          priority: 0,
          condition: {
            conditionTypeId: "COMPARISON",
            operator: "==",
            parameters: {leftOperand: "TRUE", rightOperand: "TRUE"}
          },
          variableAssignments: [
           {
             "variableId": "currentDate",
             "assignmentTypeId": "SET",
             "value": { "functionId": "formatDate", "args": ["{timestamp}", "yyyy-MM-dd"] }
           }
          ],

        } 
      ]
    };

    const transactionContextParameterDefinitions = [
      {
        name: "sellerMsisdn",
        propertyId: "sellerMsisdn",
        type: "string",
        description: "the seller msisdn",
        mutable: false,
      },
      {
        name: "purchaseAmount",
        propertyId: "purchaseAmount",
        type: "number",
        description: "the value of the transaction",
        mutable: false,
      },
    ];

    beforeEach(() => {
      next = jest.fn();
      req = createMockReq(ruleset);
      res = createMockRes();
    });

    test("Function results are assigned to properties", async () => {

      let ast;
      let code;

      validateRuleset(req,res,next);


      expect(next).toHaveBeenCalled();


      ast = createAst(
        ExecutionTarget.EVALUATION,
        ruleset,
        contextId,
        transactionContextParameterDefinitions,
        loggingContext,
      );
      code = generateCode(ast);

        const mutatedProperties = `formatDate(timestamp, "yyyy-MM-dd");`;

        expect(code).toContain(mutatedProperties);

      });
  });

  describe("Outcome Rules", () => {
    const rulesetId = "OUTCOME_RULESET";
    const contextId = "OUTCOME_RULESET_CONTEXT";

    const transactionContextParameterDefinitions = [
      {
        name: "sellerMsisdn",
        propertyId: "sellerMsisdn",
        type: "string",
        description: "the seller msisdn",
        mutable: false,
      },
      {
        name: "purchaseAmount",
        propertyId: "purchaseAmount",
        type: "number",
        description: "the value of the transaction",
        mutable: true,
      },
    ];

    const ruleset = {
      rulesetId,
      entityId,
      version: 1,
      category: "UNIT_TESTING",
      name: "Outcome Testing",
      description: "for testing outcome rules",
      status: "ACTIVE", 
      schemaVersion: "1.0.0",
      startDateTime: "1970-01-01T00:00:00Z",
      endDateTime: "2034-01-01T00:00:00Z",
      lastModifiedDateTime: "2024-01-01T00:00:00Z",
      localVariables: [
        {
          variableId: "priceModificationApplied",
          name: "price modification applied",
          description: "indicates whether price was modified",
          type: "boolean",
          defaultValue: false,
        },
      ],
      collectionMappings: [],
      contextId,
      persistentVariables: [],
      outcomeRules: [
        {
          ruleId: "RULE_1",
          name: "RULE_1",
          description: "Tests whether a value was modified.",
          priority: 0,
          condition: {
            conditionTypeId: "LOGICAL",
            operator: "AND",
            parameters: {
              conditions: [{
                conditionTypeId: "COMPARISON",
                operator: "==",
                parameters: {leftOperand: "COMPLETED", rightOperand: "{status}"}
              }, {
                conditionTypeId: "COMPARISON",
                operator: "IN",
                parameters: {leftOperand: "purchasePrice", rightOperand: "{modificationsApplied}"}
              }]
            }
          },
          variableAssignments: [
            {
              assignmentTypeId: "SET",
              variableId: "priceModificationApplied",
              value: true,
            },
          ],

        } 
      ],
      evaluationRules: []
    };

    let ast;
    let code;

    beforeEach(() => {
      next = jest.fn();
      req = createMockReq(ruleset);
      res = createMockRes();

      validateRuleset(req,res,next);
      expect(next).toHaveBeenCalled();

      ast = createAst(
        ExecutionTarget.OUTCOME_NOTIFICATION,
        ruleset,
        contextId,
        transactionContextParameterDefinitions,
        "COMPLETED",
        loggingContext,
      );
      code = generateCode(ast);
      console.log(code);
    });


    test("OutcomeRules have access to modificationStatus variables",()=>{
      expect(code).toContain('if (("COMPLETED" === status && modificationsApplied.includes("purchasePrice"))) {');
    });

    test("Outcome Status and applied property changes are part of the function parameters",()=>{
      console.log(code);

      let expectedCode = 
        "async function evaluate(callingEntityId, transactionContextId, originTransactionId, timestamp, transactionData, status, modificationsApplied, callerContext)";

      expect(code).toContain(expectedCode );
    });

    describe("Posting Outcome Notifications",()=>{
      test("`status` and `modificationsApplied` is available",async ()=>{

        const transactionData = {
          sellerMsisdn: "08000000",
          purchaseAmount: 10,
        };
        
        const modificationsApplied =  ["purchasePrice"];

        const status = "COMPLETED";


        let response = await executeRulesetOutcomeNotification(
          ruleset,
          "entityId", // callingEntityId
          transactionContextParameterDefinitions,
          contextId,
          "UNIT_TESTING",
          new Date().toISOString(), // timestamp
          transactionData,
          status,
          modificationsApplied,
          mockedRedisClient,
          mockedRedlock,
          loggingContext,
        );

        const result = response.result;

        console.log("result", result);

        let expectedResult = {
          "transactionId": "UNIT_TESTING",
          "message": "Outcome notification received"
        };

        const journalData = response.journalData;
   
        expect(result).toEqual(expectedResult)
        
        expect(journalData?.evaluationRecord?.localVariables?.priceModificationApplied).toBe(true);

      });
    });
  });

  describe("Mutable Transaction Properties", () => {

    beforeEach(() => {
      next = jest.fn();
      req = createMockReq(ruleset);
      res = createMockRes();
    });

    const rulesetId = "PMF_RULESET";
    const contextId = "PMF_TRANSACTION_CONTEXT";

    const transactionContextParameterDefinitions = [
      {
        name: "sellerMsisdn",
        propertyId: "sellerMsisdn",
        type: "string",
        description: "the seller msisdn",
        mutable: false,
      },
      { 
        name: "purchaseAmount",
        propertyId: "purchaseAmount",
        type: "number",
        description: "the value of the transaction",
        mutable: true,
      },
    ];

    const ruleset = {
      rulesetId,
      entityId,
      version: 1,
      category: "UNIT_TESTING",
      name: "Set String Testing",
      description: "for testing lists",
      status: "ACTIVE", 
      schemaVersion: "1.0.0",
      startDateTime: "1970-01-01T00:00:00Z",
      endDateTime: "2034-01-01T00:00:00Z",
      lastModifiedDateTime: "2024-01-01T00:00:00Z",
      localVariables: [
        {
          variableId: "smsMessage",
          name: "SMS Message",
          description: "Message to send",
          type: "string",
          defaultValue: "You have sold airtime",
        },
      ],
      collectionMappings: [],
      contextId,
      persistentVariables: [],
      outcomeRules: [],
      evaluationRules: [
        {
          ruleId: "RULE_1",
          name: "RULE_1",
          description: "Tests all variable operations",
          priority: 0,
          condition: {
            conditionTypeId: "COMPARISON",
            operator: "==",
            parameters: {leftOperand: "TRUE", rightOperand: "TRUE"}
          },
          variableAssignments: [
            {
              assignmentTypeId: "SET",
              variableId: "purchaseAmount",
              value: 100,
            },
          ],

        } 
      ]
    };

    // Variables to hold common values for tests.
    let ast;
    let code;

    beforeEach(() => {
      validateRuleset(req,res,next);

      expect(next).toHaveBeenCalled();


      ast = createAst(
        ExecutionTarget.EVALUATION,
        ruleset,
        contextId,
        transactionContextParameterDefinitions,
        loggingContext,
      );
      code = generateCode(ast);
    });

    describe("Generated code Execution", () => {
      test("Mutated properties are returned", async () => {
        const transactionData = {
          sellerMsisdn: "08000000",
          purchaseAmount: 10,
        };

        let result = (await executeRulesetEvaluation(
          ruleset,
          "entityId", // callingEntityId
          transactionContextParameterDefinitions,
          contextId,
          "UNIT_TESTING",
          new Date().toISOString(), // timestamp
          transactionData,
          mockedRedisClient,
          mockedRedlock,
          loggingContext,
        ))?.result;

        let expectedResult = {
          transactionId: "UNIT_TESTING",
          modifiedProperties: {
            purchaseAmount: {
              original: 10,
              modified: 100,
              rulesetId: rulesetId,
              ruleId: "RULE_1",
            },
          },
        };


        expect(result).toEqual(expectedResult);
      });
    });

    describe("Code Generation", () => {
      test("Mutated properties are included in the return statement", () => {
        const mutatedProperties = ` modifiedProperties: propertyChangeLog.serialize()`;

        expect(code).toContain(mutatedProperties);
      });

      test("Mutable properties are registered with the propertyChangeLog when they are changed", () => {
        const propertyValueSaveCode = `const original_purchaseAmount = purchaseAmount;`;
        const propertyValueChangeCode = `purchaseAmount = 100;`;
        const porpertyValueLogCode = `propertyChangeLog.addChangedProperty("RULE_1", "purchaseAmount", original_purchaseAmount, purchaseAmount)`;

        expect(code).toContain(propertyValueSaveCode);
        expect(code).toContain(propertyValueChangeCode);
        expect(code).toContain(porpertyValueLogCode);
      });

      test("Mutable Properties are declared with let and immutable with const", () => {
        const expectedSellerMsisdnCode = "const sellerMsisdn";
        const expectedPurchaseAmountCode = "let purchaseAmount";

        expect(code).toContain(expectedSellerMsisdnCode);
        expect(code).toContain(expectedPurchaseAmountCode);
      });
    });
  });

  describe("Setting Strings", () => {
    test.skip("Setting a string to a string containing variable names in parenthesis interpolates the variable values into the string", async () => {
      const entityId = "45a6997d-0f79-483f-95fb-40e222946667";
      const rulesetId = "SET_STRING_RULESET";
      const contextId = "SET_STRING_CONTEXT";
      const ruleset = {
        rulesetId,
        entityId,
        category: "UNIT_TESTING",
        name: "Set String Testing",
        description: "for testing lists",
        status: "ACTIVE", 
        schemaVersion: "1.0.0",
        version: 1,
        startDateTime: "1970-01-01T00:00:00Z",
        endDateTime: "2034-01-01T00:00:00Z",
        lastModifiedDateTime: "2024-01-01T00:00:00Z",
        contextId,
        localVariables: [
          {
            variableId: "smsMessage",
            name: "SMS Message",
            description: "Message to send",
            type: "string",
            defaultValue: "You have sold airtime",
          },
        ],
        collectionMappings: [],
        persistentVariables: [],
        outcomeRules: [],
        evaluationRules: [
          {
            ruleId: "RULE_1",
            name: "RULE_1",
            description: "Tests all variable operations",
            priority: 0,
            condition: {
              conditionTypeId: "ALLWAYS",
            },
            variableAssignments: [
              {
                variableId: "smsMessage",
                asignMentTypeId: "SET",
                value:
                "Congrats {sellerMsisdn}; you have sold {purchaseAmount} of airtime.",
              },
            ],
            actions: [
              {
                type: "SEND_SMS",
                parameters: {
                  actionParameter: "{smsMessage}",
                },
              },
            ],
          },
        ],
      };

      next = jest.fn();
      req = createMockReq(ruleset);
      res = createMockRes();
      validateRuleset(req, res, next);
      expect(next).toHaveBeenCalled();

      const transactionContextParameterDefinitions = [
        {
          name: "sellerMsisdn",
          propertyId: "sellerMsisdn",
          type: "string",
          description: "the seller msisdn",
        },
        {
          name: "purchaseAmount",
          propertyId: "purchaseAmount",
          type: "number",
          description: "the value of the transaction",
        },
      ];

      const ast = createAst(
        ExecutionTarget.EVALUATION,
        ruleset,
        contextId,
        transactionContextParameterDefinitions,
        loggingContext,
      );
      const code = generateCode(ast);

      let expectedCode =
        'smsMessage = "Congrats {sellerMsisdn}; you have sold {purchaseAmount} of airtime.";';


      expect(code).toContain(expectedCode);

      // test execution
      //
        const transactionData = {
          sellerMsisdn: "08000000",
          purchaseAmount: 101,
        };

      let resultMatching = (await executeRulesetEvaluation(
        ruleset,
        entityId, // callingEntityId
        transactionContextParameterDefinitions,
        contextId,
        "UNIT_TESTING_TRAN1",
        new Date().toISOString(), // timestamp
        transactionData,
        mockedRedisClient,
        mockedRedlock,
        loggingContext,
      ))?.result;

      expect(resultMatching.actions).toHaveLength(1);

      expect(resultMatching).toEqual({
        transactionId: "UNIT_TESTING_TRAN1",
        modifiedProperties: {},
        actions: [
          {
            type: "SEND_SMS",
            rulesetId,
            ruleId: "RULE_1",
            parameters: {
              actionParameter:
              "Congrats 08000000; you have sold 101 of airtime.",
            },
          },
        ],
      });
    });
  });

  describe("lists", () => {
    test("IN LIST Condition is created and executes correctly", async () => {
      const rulesetId = "IN_LIST_RULESET";
      const contextId = "IN_LIST_CONTEXT";
      const ruleset = {
        entityId,
        rulesetId,
        contextId,
        category: "UNIT_TESTING",
        name: "List  Testing",
        description: "for testing lists",
        status: "ACTIVE", 
        schemaVersion: "1.0.0",
        version: 1,
        startDateTime: "1970-01-01T00:00:00Z",
        endDateTime: "2034-01-01T00:00:00Z",
        lastModifiedDateTime: "2024-01-01T00:00:00Z",
        localVariables: [],
        collectionMappings: [],
        persistentVariables: [],
        outcomeRules: [],
        evaluationRules: [
          {
            ruleId: "RULE_1",
            name: "RULE_1",
            description: "Tests all variable operations",
            priority: 0,
            condition: {
              conditionTypeId: "COMPARISON",
              operator: "IN",
              parameters: {
                leftOperand: "{sellerMsisdn}",
                rightOperand: {"listId": "topSellers"},
              },
            },
            variableAssignments: [],
            actions: [
              {
                type: "IN_GROUP",
                parameters: {
                  actionParameter: "The seller is in an elegible group",
                },
              },
            ],
          },
        ]
      };

      next = jest.fn();
      req = createMockReq(ruleset);
      res = createMockRes();
      validateRuleset(req, res, next);
      expect(next).toHaveBeenCalled();

      const transactionContextParameterDefinitions = [
        {
          name: "sellerMsisdn",
          propertyId: "sellerMsisdn",
          type: "string",
          description: "the seller msisdn",
        },
      ];

      const ast = createAst(
        ExecutionTarget.EVALUATION,
        ruleset,
        contextId,
        transactionContextParameterDefinitions,
        loggingContext,
      );
      const code = generateCode(ast);


      let expectedCode = ` if (await isValueInList(sellerMsisdn, "topSellers", callerContext)) {`; 

        expect(code).toContain(expectedCode);


        // test execution
        //
          const transactionData = {
            sellerMsisdn: "08000000",
          };

        let resultMatching = (await executeRulesetEvaluation(
          ruleset,
          "entityId", // callingEntityId
          transactionContextParameterDefinitions,
          contextId,
          "UNIT_TESTING",
          new Date().toISOString(), // timestamp
          transactionData,
          mockedRedisClient,
          mockedRedlock,
          loggingContext,
        ))?.result;

        console.log({resultMatching} );
        
        expect(resultMatching.actions).toHaveLength(1);

        expect(resultMatching).toEqual({
          transactionId: "UNIT_TESTING",
          modifiedProperties: {},
          actions: [
            {
              type: "IN_GROUP",
              rulesetId,
              ruleId: "RULE_1",
              parameters: {
                actionParameter: "The seller is in an elegible group",
              },
            },
          ],
        });
      });
    });

  describe("arrays", () => {
    const rulesetId = "IN_ARRAY_RULESET";
    const contextId = "IN_ARRAY_CONTEXT";
    const ruleset = {
      rulesetId,
      entityId,
      contextId,
      category: "UNIT_TESTING",
      name: "Array Testing",
      description: "for testing arrays",
      status: "ACTIVE", 
      schemaVersion: "1.0.0",
      version: 1,
      startDateTime: "1970-01-01T00:00:00Z",
      endDateTime: "2034-01-01T00:00:00Z",
      lastModifiedDateTime: "2024-01-01T00:00:00Z",
      localVariables: [
        {
          variableId: "rulesetGroups",
          name: "Ruleset Groups",
          description: "groups for this ruleset",
          type: "array",
          defaultValue: ["COPPER", "TIN"],
        },
      ],
      collectionMappings: [],
      persistentVariables: [],
      outcomeRules: [],
      evaluationRules: [
        {
          ruleId: "RULE_1",
          name: "RULE_1",
          description: "Tests all variable operations",
          priority: 0,
          condition: {
            conditionTypeId: "LOGICAL",
            operator: "OR",
            parameters: {
              conditions: [
                {
                  conditionTypeId: "COMPARISON",
                  operator: "IN",
                  parameters: {
                    leftOperand: "{sellerGroup}",
                    rightOperand: ["GOLD", "SILVER", "BRONZE"],
                  },
                },
                {
                  conditionTypeId: "COMPARISON",
                  operator: "IN",
                  parameters: {
                    leftOperand: "{sellerGroup}",
                    rightOperand: "{extraEligibleGroups}",
                  },
                },
                {
                  conditionTypeId: "COMPARISON",
                  operator: "IN",
                  parameters: {
                    leftOperand: "{sellerGroup}",
                    rightOperand: "{rulesetGroups}",
                  },
                },
              ],
            },
          },
          variableAssignments: [],
          actions: [
            {
              type: "IN_GROUP",
              parameters: {
                actionParameter: "The seller is in an elegible group",
              },
            },
          ],
        },
      ]      
    };

    beforeEach(() => {
      next = jest.fn();
      req = createMockReq(ruleset);
      res = createMockRes();

      validateRuleset(req, res, next);

      expect(next).toHaveBeenCalled();

    });

    test("it generates code to test whether a value is in an array", async () => {

      const transactionContextParameterDefinitions = [
        {
          name: "sellerGroup",
          propertyId: "sellerGroup",
          type: "string",
          description: "the seller group",
        },
        {
          name: "extraEligibleGroups",
          propertyId: "extraEligibleGroups",
          description: "extra eligible groups",
          type: "array",
        },
      ];

      const ast = createAst(
        ExecutionTarget.EVALUATION,
        ruleset,
        contextId,
        transactionContextParameterDefinitions,
        loggingContext,
      );
      const code = generateCode(ast);

      let expectedCode = 
        `if ((["GOLD", "SILVER", "BRONZE"].includes(sellerGroup) || extraEligibleGroups.includes(sellerGroup) || rulesetGroups.includes(sellerGroup))) {`;
      expect(code).toContain(expectedCode);

      });
    });

  describe("Persistent Operations", () => {
    test("a persistent variable is updated", async () => {
      const rulesetId = "SET_UP_PERSISTENT_VARIABLES_RULESET";
      const contextId = "SET_UP_PERSISTENT_VARIABLES_CONTEXT";
      const ruleset = {
        rulesetId,
        entityId,
        contextId,
        category: "UNIT_TESTING",
        name: "Persistent Variable Testing",
        description: "for testing",
        status: "ACTIVE", 
        schemaVersion: "1.0.0",
        version: 1,
        startDateTime: "1970-01-01T00:00:00Z",
        endDateTime: "2034-01-01T00:00:00Z",
        lastModifiedDateTime: "2024-01-01T00:00:00Z",
        localVariables: [],
        collectionMappings: [
          {
            collectionId: "sellerArea",
            name: "sellerArea",
            keyMapping: {
                propertyId: "sellerId",
            }
          },
        ],
        persistentVariables: [
          {
            variableId: "sellerAreaCount",
            name: "Big transaction counter",
            description: "Counts transactions",
            type: "number",
            defaultValue: 0,
            collectionId: "sellerArea",
          },
        ],
        outcomeRules: [],
        evaluationRules: [
          {
            ruleId: "RULE_1",
            name: "RULE_1",
            description: "Update selle area transaction count",
            priority: 0,
            condition: {
              conditionTypeId: "COMPARISON",
              operator: "==",
              parameters: {
                leftOperand: 1,
                rightOperand: 1,
              },
            },
            variableAssignments: [
              {
                variableId: "sellerAreaCount",
                assignmentTypeId: "ADD",
                value: 1,
              },
            ],
            actions: [
              {
                type: "NotifyCounter",
                parameters: {
                  sellerAreaCount: "{sellerAreaCount}",
                },
              },
            ],
          },
        ]
      };

      next = jest.fn();
      req = createMockReq(ruleset);
      res = createMockRes();
      validateRuleset(req, res, next);
      expect(next).toHaveBeenCalled();

      const transactionContextParameterDefinitions = [
        {
          name: "salesAmount",
          propertyId: "salesAmount",
          type: "number",
          description: "the value of the sale",
        },
        {
          name: "sellerId",
          propertyId: "sellerId",
          type: "string",
          description: "the id of the seller",
        },
        {
          name: "area",
          propertyId: "area",
          type: "string",
          description: "the area of the transactoin",
        },
      ];

      const ast = createAst(
        ExecutionTarget.EVALUATION,
        ruleset,
        contextId,
        transactionContextParameterDefinitions,
        loggingContext,
      );
      const code = generateCode(ast);

      let expectetMapDeclaration =
        "const persistentVariableKeys = new Map();\n";
      expect(code).toContain(expectetMapDeclaration);

      let expectedCode =
        'persistentVariableKeys.set("sellerAreaCount", "SET_UP_PERSISTENT_VARIABLES_RULESET:1:{SET_UP_PERSISTENT_VARIABLES_RULESET_1}:sellerArea:sellerAreaCount:" + (sellerId + ":"));\n';

      //'persistentVariableKeys.set("sellerAreaCount", "SET_UP_PERSISTENT_VARIABLES_RULESET:1:SET_UP_PERSISTENT_VARIABLES_CONTEXT:sellerArea:sellerAreaCount:" + (sellerId + ":") + (area + ":"));');
    expect(code).toContain(expectedCode);

    const transactionData = {
      salesAmount: 100,
      area: "North",
      sellerId: "Seller_001",
    };

      await executeRulesetEvaluation(
        ruleset,
        "entityId", // callingEntityId
        transactionContextParameterDefinitions,
        contextId,
        "UNIT_TESTING_TRAN",
        "2024-10-07T00:00:00Z", //new Date().toISOString(), // timestamph
        transactionData,
        mockedRedisClient,
        mockedRedlock,
        loggingContext,
      );

      let result = (await executeRulesetEvaluation(
        ruleset,
        "entityId", // callingEntityId
        transactionContextParameterDefinitions,
        contextId,
        "UNIT_TESTING_TRAN",
        "2024-10-07T00:00:00Z", //new Date().toISOString(), // timestamph
        transactionData,
        mockedRedisClient,
        mockedRedlock,
        loggingContext,
      ))?.result;
      console.log("result 11", result);

      const expectedResult = {
        transactionId: "UNIT_TESTING_TRAN",
        modifiedProperties: {},
        actions: [
          {
            type: "NotifyCounter",
            rulesetId,
            ruleId: "RULE_1",
            parameters: {
              sellerAreaCount: 2,
            },
          },
        ],
      };

      expect(result).toEqual(expectedResult);
    });

    test("a key is generated", () => {
      const rulesetId = "SET_UP_PERSISTENT_VARIABLES_RULESET";
      const contextId = "SET_UP_PERSISTENT_VARIABLES_CONTEXT";
      const ruleset = {
        rulesetId,
        contextId,
        entityId,
        category: "UNIT_TESTING",
        name: "Persistent Variable Testing",
        description: "for testing",
        status: "ACTIVE", 
        schemaVersion: "1.0.0",
        version: 1,
        startDateTime: "1970-01-01T00:00:00Z",
        endDateTime: "2034-01-01T00:00:00Z",
        lastModifiedDateTime: "2024-01-01T00:00:00Z",
        localVariables: [],
        collectionMappings: [
          {
            collectionId: "sellerArea",
            name: "sellerArea",
            keyMapping: {
                propertyId: "sellerId",
            },
          },
        ],
        persistentVariables: [
          {
            variableId: "sellerAreaCount",
            name: "Big transaction counter",
            description: "Counts transactions",
            type: "number",
            defaultValue: 0,
            collectionId: "sellerArea",
          },
        ],
        outcomeRules: [],
        evaluationRules: [
          {
            ruleId: "RULE_1",
            name: "RULE_1",
            description: "Update selle area transaction count",
            priority: 0,
            condition: {
              conditionTypeId: "COMPARISON",
              operator: "==",
              parameters: {
                leftOperand: 1,
                rightOperand: 1,
              },
            },
            variableAssignments: [
              {
                variableId: "sellerAreaCount",
                assignmentTypeId: "ADD",
                value: 1,
              },
            ],
            actions: [
              {
                type: "NotifyCounter",
                parameters: {
                  actionParameter: "{sellerAreaCount}",
                },
              },
            ],
          },
        ]        
      };

      next = jest.fn();
      req = createMockReq(ruleset);
      res = createMockRes();
      validateRuleset(req, res, next);
      expect(next).toHaveBeenCalled();

      const transactionContextParameterDefinitions = [
        {
          name: "salesAmount",
          propertyId: "salesAmount",
          type: "number",
          description: "the value of the sale",
        },
        {
          name: "sellerId",
          propertyId: "sellerId",
          type: "string",
          description: "the id of the seller",
        },
        {
          name: "area",
          propertyId: "area",
          type: "string",
          description: "the area of the transactoin",
        },
      ];

      const ast = createAst(
        ExecutionTarget.EVALUATION,
        ruleset,
        contextId,
        transactionContextParameterDefinitions,
        loggingContext,
      );
      const code = generateCode(ast);

      let expectetMapDeclaration =
        "const persistentVariableKeys = new Map();\n";
      expect(code).toContain(expectetMapDeclaration);

      let expectedCode =
        'persistentVariableKeys.set("sellerAreaCount", "SET_UP_PERSISTENT_VARIABLES_RULESET:1:{SET_UP_PERSISTENT_VARIABLES_RULESET_1}:sellerArea:sellerAreaCount:" + (sellerId + ":"));\n';

      expect(code).toContain(expectedCode);
    });
  });

test("It generates executable variableAssignments for  operations on variables of type number", async () => {
  const rulesetId = "ADD_SUBTRACT_TEST";
  const contextId = "ADD_SUBTRACT_TEST_CTX";
  const ruleset = {
    rulesetId,
    contextId,
    entityId,
    category: "UNIT_TESTING",
    name: "VariableOperations Testing",
    description: "for testing",
    status: "ACTIVE", 
    schemaVersion: "1.0.0",
    version: 1,
    startDateTime: "1970-01-01T00:00:00Z",
    endDateTime: "2034-01-01T00:00:00Z",
    lastModifiedDateTime: "2024-01-01T00:00:00Z",
    localVariables: [
      {
        variableId: "localVariable",
        name: "Local Variable",
        description: "Local Variable for testing",
        type: "number",
        defaultValue: 100.0,
      },
      {
        variableId: "localPlusTran",
        name: "Local Variable Plus",
        description: "Local Variable for testing addition",
        type: "number",
        defaultValue: 10.0,
      },
      {
        variableId: "localMinusTran",
        name: "Local Variable Minus",
        description: "Local Variable for testing subtraction",
        type: "number",
        defaultValue: 200.0,
      },
      {
        variableId: "localTimesTran",
        name: "Local Times Times",
        description: "Local Variable for testing subtraction",
        type: "number",
        defaultValue: 10,
      },
      {
        variableId: "localDividedByTran",
        name: "Local Times Times",
        description: "Local Variable for testing subtraction",
        type: "number",
        defaultValue: 2200,
      },
    ],
    collectionMappings: [],
    persistentVariables: [],
    outcomeRules: [],
    evaluationRules: [
      {
        ruleId: "RULE_1",
        name: "RULE_1",
        description: "Tests all variable operations",
        priority: 0,
        condition: {
          conditionTypeId: "COMPARISON",
          operator: "==",
          parameters: {
            leftOperand: 1,
            rightOperand: 1,
          },
        },
        variableAssignments: [
          {
            variableId: "localPlusTran",
            assignmentTypeId: "ADD",
            value: "{transactionAmountProperty}",
          },
          {
            variableId: "localMinusTran",
            assignmentTypeId: "SUBTRACT",
            value: "{transactionAmountProperty}",
          },
          {
            variableId: "localTimesTran",
            assignmentTypeId: "MULTIPLY",
            value: "{transactionIntegerProperty}",
          },
          {
            variableId: "localDividedByTran",
            assignmentTypeId: "DIVIDE",
            value: "{transactionAmountProperty}",
          },
        ],
        actions: [
          {
            type: "added_value",
            parameters: {
              actionParameter: "{localPlusTran}",
            },
          },
          {
            type: "subtracted_value",
            parameters: {
              actionParameter: "{localMinusTran}",
            },
          },
          {
            type: "multiplied_value",
            parameters: {
              actionParameter: "{localTimesTran}",
            },
          },
          {
            type: "divided_by_value",
            parameters: {
              actionParameter: "{localDividedByTran}",
            },
          },
        ],
      },
    ]      
  };

  next = jest.fn();
  req = createMockReq(ruleset);
  res = createMockRes();
  validateRuleset(req, res, next);
  expect(next).toHaveBeenCalled();



  const transactionContextParameterDefinitions = [
    {
      name: "transactionProperty",
      propertyId: "transactionProperty",
      type: "string",
      description: "Some property of the transaction",
    },
    {
      name: "transactionAmountProperty",
      propertyId: "transactionAmountProperty",
      type: "number",
      description: "Some property of the transaction",
    },
    {
      name: "transactionIntegerProperty",
      propertyId: "transactionIntegerProperty",
      type: "number",
      description: "Some integer property of the transaction",
    },
  ];

  const ast = createAst(
        ExecutionTarget.EVALUATION,
    ruleset,
    contextId,
    transactionContextParameterDefinitions,
    loggingContext,
  );
  const code = generateCode(ast);

  let expectedCodeLines = [
    "localPlusTran += transactionAmountProperty;",
    "localMinusTran -= transactionAmountProperty;",
    "localTimesTran *= transactionIntegerProperty;",
    "localDividedByTran /= transactionAmountProperty;",
  ];

  expectedCodeLines.forEach((line) => {
    expect(code).toContain(line);
  });

  const transactionDataMatching = {
    transactionProperty: "TRANSACTION",
    transactionAmountProperty: 110.0,
    transactionIntegerProperty: 10,
  };

  let resultMatching = (await executeRulesetEvaluation(
    ruleset,
    "entityId", // callingEntityId
    transactionContextParameterDefinitions,
    contextId,
    "UNIT_TESTING",
    new Date().toISOString(), // timestamp
    transactionDataMatching,
    mockedRedisClient,
    mockedRedlock,
    loggingContext,
  ))?.result;
  expect(resultMatching.actions).toHaveLength(4);

  expect(resultMatching).toEqual({
    transactionId: "UNIT_TESTING",
    modifiedProperties: {},
    actions: [
      {
        type: "added_value",
        rulesetId,
        ruleId: "RULE_1",
        parameters: {
          actionParameter: 120,
        },
      },
      {
        type: "subtracted_value",
        rulesetId,
        ruleId: "RULE_1",
        parameters: {
          actionParameter: 90,
        },
      },
      {
        type: "multiplied_value",
        rulesetId,
        ruleId: "RULE_1",
        parameters: {
          actionParameter: 100,
        },
      },
      {
        type: "divided_by_value",
        rulesetId,
        ruleId: "RULE_1",
        parameters: {
          actionParameter: 20,
        },
      },
    ],
  });
});

test("It generates executable variableAssignments for the SET operation", async () => {
  const rulesetId = "SET_TEST";
  const contextId = "SET_TEST_CTX";
  const ruleset = {
    rulesetId,
    contextId,
    entityId,
    category: "UNIT_TESTING",
    name: "VariableOperations Testing",
    description: "for testing",
    status: "ACTIVE", 
    schemaVersion: "1.0.0",
    version: 1,
    startDateTime: "1970-01-01T00:00:00Z",
    endDateTime: "2034-01-01T00:00:00Z",
    lastModifiedDateTime: "2024-01-01T00:00:00Z",
    collectionMappings: [],
    persistentVariables: [],

    localVariables: [
      {
        variableId: "localVariable",
        name: "Local Variable",
        description: "Local Variable for testing",
        type: "number",
        defaultValue: 0,
      },
    ],
    outcomeRules: [],
    evaluationRules: [
      {
        ruleId: "RULE_1",
        name: "RULE_1",
        description:
        "Emails the the caller of the value of the transactionProperty",
        priority: 0,
        condition: {
          conditionTypeId: "COMPARISON",
          operator: ">=",
          parameters: {
            leftOperand: "TRUE",
            rightOperand: "TRUE",
          },
        },
        variableAssignments: [
          {
            variableId: "localVariable",
            assignmentTypeId: "SET",
            value: "{transactionIntegerProperty}",
          },
        ],
        actions: [
          {
            type: "variableAssignmentsOnLocalVars",
            parameters: {
              actionParameter: "{localVariable}",
            },
          },
        ],
      },
    ]
  };

  next = jest.fn();
  req = createMockReq(ruleset);
  res = createMockRes();
  validateRuleset(req, res, next);
  expect(next).toHaveBeenCalled();


  const transactionContextParameterDefinitions = [
    {
      name: "transactionProperty",
      propertyId: "transactionProperty",
      type: "string",
      description: "Some property of the transaction",
    },
    {
      name: "transactionAmountProperty",
      propertyId: "transactionAmountProperty",
      type: "number",
      description: "Some property of the transaction",
    },
    {
      name: "transactionIntegerProperty",
      propertyId: "transactionIntegerProperty",
      type: "number",
      description: "Some integer property of the transaction",
    },
  ];

  const ast = createAst(
        ExecutionTarget.EVALUATION,
    ruleset,
    contextId,
    transactionContextParameterDefinitions,
    loggingContext,
  );
  const code = generateCode(ast);

  let expectedCode = "localVariable = transactionIntegerProperty;\n";

  expect(code).toContain(expectedCode);

  const transactionDataMatching = {
    transactionProperty: "TRANSACTION",
    transactionAmountProperty: 100.0,
    transactionIntegerProperty: 88,
  };

  const resultMatching = (await executeRulesetEvaluation(
    ruleset,
    "entityId", // callingEntityId
    transactionContextParameterDefinitions,
    contextId,
    "UNIT_TESTING",
    new Date().toISOString(), // timestamp
    transactionDataMatching,
    mockedRedisClient,
    mockedRedlock,
    loggingContext,
  ))?.result;

  expect(resultMatching.actions).toHaveLength(1);

  expect(resultMatching).toEqual({
    transactionId: "UNIT_TESTING",
    modifiedProperties: {},
    actions: [
      {
        type: "variableAssignmentsOnLocalVars",
        rulesetId,
        ruleId: "RULE_1",
        parameters: {
          actionParameter: 88,
        },
      },
    ],
  });
});

    describe("Date Constants", () => {
      test("Date Constants are set up correctly and available", async () => {
        const ruleset = {
          rulesetId: "TESTING_RULESET",
          entityId,
          contextId: "TESTING_CONTEXT",
          category: "UNIT_TESTING",
          name: "TestingCapaign",
          description: "for testing",
          schemaVersion: "1.0.0",
          version: 1,
          status: "ACTIVE",
          startDateTime: "1970-01-01T00:00:00Z",
          endDateTime: "2034-01-01T00:00:00Z",
          lastModifiedDateTime: "2024-01-01T00:00:00Z",
          collectionMappings: [],
          persistentVariables: [],
          localVariables: [],
          outcomeRules: [],
          evaluationRules: [
            {
              ruleId: "RETURNDATECONSTANTS",
              name: "returns date constants",
              description:
              "Notifies the caller of the current values of the date constants",
              priority: 0,
              condition: {
                conditionTypeId: "COMPARISON",
                  operator: "==",
                  parameters: {
                    leftOperand: "true",
                    rightOperand: "true",
                  },
               },
              actions: [
                {
                  type: "DateConstantsNotification",
                  parameters: {
                    dayOfWeek: "{DAY_OF_WEEK}",
                    dayOfMonth: "{dayOfMonth}",
                    weekOfYear: "{weekOfYear}",
                    monthOfYear: "{monthOfYear}",
                    hourOfDay: "{hourOfDay}",
                  },
                },
              ],
              variableAssignments: [],
            },
          ]
        };

        next = jest.fn();
        req = createMockReq(ruleset);
        res = createMockRes();
        validateRuleset(req, res, next);
        expect(next).toHaveBeenCalled();


        const transactionContextParameterDefinitions = [
          {
            name: "transactionProperty",
            propertyId: "transactionProperty",
            type: "string",
            description: "Some property of the transaction",
          },
          {
            name: "transactionDate",
            propertyId: "transactionDate",
            type: "date",
            description: "The date of the transaction",
          },
        ];

        const transactionData = {
          transactionProperty: "TRANSACTION",
          transactionDate: "2024-10-07T00:00:00Z",
        };

        const result = (await executeRulesetEvaluation(
          ruleset,
          "TESTING_ENTITY", 
          transactionContextParameterDefinitions,
          "TESTING_CONTEXT",
          "UNIT_TESTING_TRAN",
          "2024-11-14T09:29:56.904Z", 
          transactionData,
          mockedRedisClient,
          mockedRedlock,
          loggingContext,
        ))?.result;

        const expectedResult = {
          transactionId: "UNIT_TESTING_TRAN",
          modifiedProperties: {},
          actions: [
            {
              type: "DateConstantsNotification",
              rulesetId: "TESTING_RULESET",
              ruleId: "RETURNDATECONSTANTS",
              parameters: {
                dayOfWeek: "THURSDAY",
                dayOfMonth: 14,
                weekOfYear: 46,
                monthOfYear: "NOVEMBER",
                hourOfDay: 9
              },
            },
          ],
        };

        expect(result).toEqual(expectedResult);
      });
    });

describe("Conditions", () => {
  test("Logical Conditions are created in terms of Simple Conditions", async () => {
    const rulesetId = "CONDITION_SIMPLE_LOGICAL_TEST";
    const contextId = "CONDITION_SIMPLE_LOGICAL_TEST_CTX";
    const ruleset = {
      rulesetId,
      entityId,
      category: "UNIT_TESTING",
      name: "VariableOperations Testing",
      description: "for testing",
      status: "ACTIVE", 
      schemaVersion: "1.0.0",
      version: 1,
      startDateTime: "1970-01-01T00:00:00Z",
      endDateTime: "2034-01-01T00:00:00Z",
      lastModifiedDateTime: "2024-01-01T00:00:00Z",
      collectionMappings: [],
      persistentVariables: [],
      localVariables: [],
      contextId,
      outcomeRules: [],
      evaluationRules: [
        {
          ruleId: "RULE_1",
          name: "RULE_1",
          description:
          "Emails the the caller of the value of the transactionProperty",
          priority: 0,
          condition: {
            conditionTypeId: "LOGICAL",
            operator: "AND",
            parameters: {
              conditions: [
                {
                  conditionTypeId: "COMPARISON",
                  operator: "==",
                  parameters: {
                    leftOperand: "{transactionProperty}",
                    rightOperand: "TRANSACTION",
                  },
                },
                {
                  conditionTypeId: "COMPARISON",
                  operator: ">=",
                  parameters: {
                    leftOperand: "{transactionAmountProperty}",
                    rightOperand: 10.0,
                  },
                },
              ],
            },
          },
          variableAssignments: [],
          actions: [],
        },
      ]
    };

    next = jest.fn();
    req = createMockReq(ruleset);
    res = createMockRes();
    validateRuleset(req, res, next);
    expect(next).toHaveBeenCalled();

    const transactionContextParameterDefinitions = [
      {
        name: "transactionProperty",
        propertyId: "transactionProperty",
        type: "string",
        description: "Some property of the transaction",
      },
      {
        name: "transactionAmountProperty",
        propertyId: "transactionAmountProperty",
        type: "number",
        description: "Some property of the transaction",
      },
      {
        name: "transactionIntegerProperty",
        propertyId: "transactionIntegerProperty",
        type: "number",
        description: "Some integer property of the transaction",
      },
    ];

    const ast = createAst(
        ExecutionTarget.EVALUATION,
      ruleset,
      contextId,
      transactionContextParameterDefinitions,
      loggingContext,
    );
    const code = generateCode(ast);

    let expectedCode =
      'if ((transactionProperty === "TRANSACTION" && transactionAmountProperty >= 10)) {}\n';

    expect(code).toContain(expectedCode);
  });

  test("IS_DAY Condition is created and executes correctly", async () => {
    const rulesetId = "IS_DAY_TEST";
    const contextId = "IS_DAY_TEST_CTX";
    const ruleset = {
      rulesetId,
      entityId,
      category: "UNIT_TESTING",
      name: "ConditionTesting",
      description: "for testing",
      schemaVersion: "1.0.0",
      version: 1,
      status: "ACTIVE",
      startDateTime: "1970-01-01T00:00:00Z",
      endDateTime: "2034-01-01T00:00:00Z",
      lastModifiedDateTime: "2024-01-01T00:00:00Z",
      collectionMappings: [],
      persistentVariables: [],
      localVariables: [],
      contextId,
      outcomeRules: [],
      evaluationRules: [
        {
          ruleId: "IS_BONUS_DAY",
          name: "Is today a bonus day",
          description: "Notifies the caller if it's bonus day",
          priority: 0,
          condition: {
            conditionTypeId: "DATE",
            operator: "IS_DAY",
            parameters: {
              date: "{transactionDate}",
              day: "MONDAY",
            },
          },
          actions: [
            {
              type: "IsBonusDay",
              parameters: {},
            },
          ],
          variableAssignments: [],
        },
      ]
    };

    next = jest.fn();
    req = createMockReq(ruleset);
    res = createMockRes();
    validateRuleset(req, res, next);
    expect(next).toHaveBeenCalled();

    const transactionContextParameterDefinitions = [
      {
        name: "transactionProperty",
        propertyId: "transactionProperty",
        type: "string",
        description: "Some property of the transaction",
      },
      {
        name: "transactionDate",
        propertyId: "transactionDate",
        type: "date",
        description: "The date of the transaction",
      },
    ];

    const expectedCode = '  if (isDay(transactionDate, "MONDAY")) {\n';

      const ast = createAst(
        ExecutionTarget.EVALUATION,
        ruleset,
        contextId,
        transactionContextParameterDefinitions,
        loggingContext,
      );
      const code = generateCode(ast);

      expect(code).toContain(expectedCode);

      const transactionData = {
        transactionProperty: "TRANSACTION",
        transactionDate: "2024-10-07T00:00:00Z",
      };

      const result = (await executeRulesetEvaluation(
        ruleset,
        "entityId", // callingEntityId
        transactionContextParameterDefinitions,
        contextId,
        "UNIT_TESTING_TRAN",
        "2024-10-07T00:00:00Z", //new Date().toISOString(), // timestamph
        transactionData,
        mockedRedisClient,
        mockedRedlock,
        loggingContext,
      ))?.result;

      const expectedResult = {
        transactionId: "UNIT_TESTING_TRAN",
        modifiedProperties: {},
        actions: [
          {
            type: "IsBonusDay",
            rulesetId,
            ruleId: "IS_BONUS_DAY",
            parameters: {},
          },
        ],
      };

      expect(result).toEqual(expectedResult);

      const notMondayTransactionData = {
        transactionProperty: "TRANSACTION",
        transactionDate: "2024-10-05T00:00:00Z",
      };

      const notMondayResult = (await executeRulesetEvaluation(
        ruleset,
        "entityId", // callingEntityId
        transactionContextParameterDefinitions,
        contextId,
        "UNIT_TESTING_TRAN",
        "2024-10-05T00:00:00Z", //new Date().toISOString(), // timestamph
        notMondayTransactionData,
        mockedRedisClient,
        mockedRedlock,
        loggingContext,
      ))?.result;

      const notMondayExpectedResult = {
        transactionId: "UNIT_TESTING_TRAN",
        modifiedProperties: {},
      };

      expect(notMondayResult).toEqual(notMondayExpectedResult);
    });

  test("Simple Conditions are created in terms of transactionContext Variables", async () => {
    const rulesetId = "CONDITION_TRANSACTION_CONTEXT_TEST";
    const contextId = "CONDITION_TRANSACTION_CONTEXT_TEST_CTX";
    const ruleset = {
      rulesetId,
      entityId,
      category: "UNIT_TESTING",
      name: "ConditionTesting",
      description: "for testing",
      schemaVersion: "1.0.0",
      version: 1,
      status: "ACTIVE",
      startDateTime: "1970-01-01T00:00:00Z",
      endDateTime: "2034-01-01T00:00:00Z",
      lastModifiedDateTime: "2024-01-01T00:00:00Z",
      collectionMappings: [],
      persistentVariables: [],
      localVariables: [],
      contextId,
      outcomeRules: [],
      evaluationRules: [
        {
          ruleId: "NOTIFYTRANSACTIONPROPERTY",
          name: "NotifyTransactionProperty",
          description:
          "Notifies the caller of the value of the transactionProperty",
          priority: 0,
          condition: {
            conditionTypeId: "COMPARISON",
            operator: "==",
            parameters: {
              leftOperand: "{transactionProperty}",
              rightOperand: "TRANSACTION",
            },
          },
          actions: [],
          variableAssignments: [],
        },
      ]
    };

    next = jest.fn();
    req = createMockReq(ruleset);
    res = createMockRes();
    validateRuleset(req, res, next);
    expect(next).toHaveBeenCalled();

    const transactionContextParameterDefinitions = [
      {
        name: "transactionProperty",
        propertyId: "transactionProperty",
        type: "string",
        description: "Some property of the transaction",
      },
    ];

    const expectedCode = '  if (transactionProperty === "TRANSACTION") {}\n';

    const ast = createAst(
        ExecutionTarget.EVALUATION,
      ruleset,
      contextId,
      transactionContextParameterDefinitions,
      loggingContext,
    );
    const code = generateCode(ast);

    expect(code).toContain(expectedCode);
  });

  test("Logical Conditions are created in terms of Simple Conditions and LogicalConditions", async () => {
    const rulesetId = "CONDITION_LOGICAL_TEST";
    const contextId = "CONDITION_LOGICAL_TEST_CTX";
    const ruleset = {
      entityId,
      rulesetId,
      category: "UNIT_TESTING",
      name: "VariableOperations Testing",
      description: "for testing",
      status: "ACTIVE", 
      schemaVersion: "1.0.0",
      version: 1,
      startDateTime: "1970-01-01T00:00:00Z",
      endDateTime: "2034-01-01T00:00:00Z",
      lastModifiedDateTime: "2024-01-01T00:00:00Z",
      collectionMappings: [],
      persistentVariables: [],
      localVariables: [],
      contextId,
      outcomeRules: [],
      evaluationRules: [
        {
          ruleId: "RULE_1",
          name: "RULE_1",
          description:
          "Emails the the caller of the value of the transactionProperty",
          priority: 0,
          condition: {
            conditionTypeId: "LOGICAL",
            operator: "OR",
            parameters: {
              conditions: [
                {
                  conditionTypeId: "COMPARISON",
                  operator: "==",
                  parameters: {
                    leftOperand: "{transactionProperty}",
                    rightOperand: "GIVE_BONUS_ANYWAY",
                  },
                },
                {
                  conditionTypeId: "LOGICAL",
                  operator: "AND",
                  parameters: {
                    conditions: [
                      {
                        conditionTypeId: "COMPARISON",
                        operator: ">=",
                        parameters: {
                          leftOperand: "{transactionAmountProperty}",
                          rightOperand: 10.0,
                        },
                      },
                      {
                        conditionTypeId: "COMPARISON",
                        operator: "<",
                        parameters: {
                          leftOperand: "{transactionAmountProperty}",
                          rightOperand: 1000.0,
                        },
                      },
                    ],
                  },
                },
              ],
            },
          },
          variableAssignments: [],
          actions: [],
        },
      ]
    };

    next = jest.fn();
    req = createMockReq(ruleset);
    res = createMockRes();
    validateRuleset(req, res, next);
    expect(next).toHaveBeenCalled();

    const transactionContextParameterDefinitions = [
      {
        name: "transactionProperty",
        propertyId: "transactionProperty",
        type: "string",
        description: "Some property of the transaction",
      },
      {
        name: "transactionAmountProperty",
        propertyId: "transactionAmountProperty",
        type: "number",
        description: "Some property of the transaction",
      },
      {
        name: "transactionIntegerProperty",
        propertyId: "transactionIntegerProperty",
        type: "number",
        description: "Some integer property of the transaction",
      },
    ];

    const ast = createAst(
        ExecutionTarget.EVALUATION,
      ruleset,
      contextId,
      transactionContextParameterDefinitions,
      loggingContext,
    );
    const code = generateCode(ast);

    let expectedCode =
      'if ((transactionProperty === "GIVE_BONUS_ANYWAY" || (transactionAmountProperty >= 10 && transactionAmountProperty < 1000))) {}\n';

    expect(code).toContain(expectedCode);
  });
  });

test("Generated code executes correctly, multiple rules considered", async () => {
  const rulesetId = "MULTIPLE_RULES_TESTING";
  const contextId = "MULTIPLE_RULES_TESTING_CTX";
  const ruleset = {
    entityId,
    rulesetId,
    category: "UNIT_TESTING",
    name: "MultipleRulesTesting",
    description: "for testing",
    schemaVersion: "1.0.0",
    version: 1,
    status: "ACTIVE",
    startDateTime: "1970-01-01T00:00:00Z",
    endDateTime: "2034-01-01T00:00:00Z",
    lastModifiedDateTime: "2024-01-01T00:00:00Z",
    collectionMappings: [],
    persistentVariables: [],
    localVariables: [],
    contextId,
    outcomeRules: [],
    evaluationRules: [
      {
        ruleId: "RULE_1",
        name: "RULE_1",
        description:
        "Emails the the caller of the value of the transactionProperty",
        priority: 0,
        condition: {
          conditionTypeId: "COMPARISON",
          operator: ">=",
          parameters: {
            leftOperand: "{transactionIntegerProperty}",
            rightOperand: 100,
          },
        },
        actions: [
          {
            type: "RULE_1_TEST_ACTION",
            parameters: {
              actionParameter: "{transactionProperty}",
            },
          },
        ],
        variableAssignments: [],
      },
      {
        ruleId: "RULE_2",
        name: "Rule2",
        description: "Notifies of Rule2",
        priority: 0,
        condition: {
          conditionTypeId: "COMPARISON",
          operator: "==",
          parameters: {
            leftOperand: "{transactionProperty}",
            rightOperand: "TRANSACTION",
          },
        },
        actions: [
          {
            type: "RULE_2_TEST_ACTION_1",
            parameters: {
              actionParameter: "{transactionProperty}",
            },
          },
          {
            type: "RULE_2_TEST_ACTION_2",
            parameters: {
              actionParameter2: "{transactionAmountProperty}",
            },
          },
          {
            type: "RULE_2_TEST_ACTION_3",
            parameters: {
              actionParameter3: "{transactionIntegerProperty}",
            },
          },
        ],
        variableAssignments: [],
      },
    ] 
  };

  next = jest.fn();
  req = createMockReq(ruleset);
  res = createMockRes();
  validateRuleset(req, res, next);
  expect(next).toHaveBeenCalled();

  const transactionContextParameterDefinitions = [
    {
      name: "transactionProperty",
      propertyId: "transactionProperty",
      type: "string",
      description: "Some property of the transaction",
    },
    {
      name: "transactionAmountProperty",
      propertyId: "transactionAmountProperty",
      type: "number",
      description: "Some property of the transaction",
    },
    {
      name: "transactionIntegerProperty",
      propertyId: "transactionIntegerProperty",
      type: "number",
      description: "Some integer property of the transaction",
    },
  ];

  const ast = createAst(
        ExecutionTarget.EVALUATION,
    ruleset,
    contextId,
    transactionContextParameterDefinitions,
    loggingContext,
  );
  generateCode(ast);

  // Execute the generated code with transactionData that meets the condition
  const transactionDataMatching = {
    transactionProperty: "TRANSACTION",
    transactionAmountProperty: 100.0,
    transactionIntegerProperty: 100,
  };

  const resultMatching = (await executeRulesetEvaluation(
    ruleset,
    "entityId", // callingEntityId
    transactionContextParameterDefinitions,
    contextId,
    "UNIT_TESTING",
    new Date().toISOString(), // timestamp
    transactionDataMatching,
    mockedRedisClient,
    mockedRedlock,
    loggingContext,
  ))?.result;

  expect(resultMatching.actions.length).toBe(4);

  expect(resultMatching).toEqual({
    transactionId: "UNIT_TESTING",
    modifiedProperties: {},
    actions: [
      {
        type: "RULE_1_TEST_ACTION",
        rulesetId,
        ruleId: "RULE_1",
        parameters: {
          actionParameter: "TRANSACTION",
        },
      },
      {
        type: "RULE_2_TEST_ACTION_1",
        rulesetId,
        ruleId: "RULE_2",
        parameters: {
          actionParameter: "TRANSACTION",
        },
      },
      {
        type: "RULE_2_TEST_ACTION_2",
        rulesetId,
        ruleId: "RULE_2",
        parameters: {
          actionParameter2: 100.0,
        },
      },
      {
        type: "RULE_2_TEST_ACTION_3",
        rulesetId,
        ruleId: "RULE_2",
        parameters: {
          actionParameter3: 100.0,
        },
      },
    ],
  });
});

test("Generated code executes correctly, producing actions when conditions are met", async () => {
  const rulesetId = "EXECUTION_TESTING";
  const contextId = "EXECUTION_TESTING_CTX";
  const ruleset = {
    rulesetId,
    entityId,
    category: "UNIT_TESTING",
    name: "ActionTesting",
    description: "for testing",
    schemaVersion: "1.0.0",
    version: 1,
    status: "ACTIVE",
    startDateTime: "1970-01-01T00:00:00Z",
    endDateTime: "2034-01-01T00:00:00Z",
    lastModifiedDateTime: "2024-01-01T00:00:00Z",
    collectionMappings: [],
    persistentVariables: [],

    localVariables: [
      {
        variableId: "localVariable",
        name: "Local Variable",
        description: "Local Variable for testing",
        type: "number",
        defaultValue: 0,
      },
    ],
    contextId,
    outcomeRules: [],
    evaluationRules: [
      {
        ruleId: "NOTIFYTRANSACTIONPROPERTY",
        name: "NotifyTransactionProperty",
        description:
        "Notifies the caller of the value of the transactionProperty",
        priority: 0,
        condition: {
          conditionTypeId: "COMPARISON",
          operator: "==",
          parameters: {
            leftOperand: "{transactionProperty}",
            rightOperand: "TRANSACTION",
          },
        },
        actions: [
          {
            type: "TEST_ACTION_1",
            parameters: {
              actionParameter: "{transactionProperty}",
            },
          },
          {
            type: "TEST_ACTION_2",
            parameters: {
              actionParameter2: "{transactionAmountProperty}",
            },
          },
          {
            type: "TEST_ACTION_3",
            parameters: {
              actionParameter3: "{localVariable}",
            },
          },
        ],
        variableAssignments: [
          {
            variableId: "localVariable",
            assignmentTypeId: "SET",
            value: "{transactionIntegerProperty}",
          },
        ],
      },
    ]
  };

  next = jest.fn();
  req = createMockReq(ruleset);
  res = createMockRes();
  validateRuleset(req, res, next);
  expect(next).toHaveBeenCalled();

  const transactionContextParameterDefinitions = [
    {
      name: "transactionProperty",
      propertyId: "transactionProperty",
      type: "string",
      description: "Some property of the transaction",
    },
    {
      name: "transactionAmountProperty",
      propertyId: "transactionAmountProperty",
      type: "number",
      description: "Some property of the transaction",
    },
    {
      name: "transactionIntegerProperty",
      propertyId: "transactionIntegerProperty",
      type: "number",
      description: "Some integer property of the transaction",
    },
  ];

  const ast = createAst(
        ExecutionTarget.EVALUATION,
    ruleset,
    contextId,
    transactionContextParameterDefinitions,
    loggingContext,
  );
  generateCode(ast);

  // Execute the generated code with transactionData that meets the condition
  const transactionDataMatching = {
    transactionProperty: "TRANSACTION",
    transactionAmountProperty: 100,
    transactionIntegerProperty: 100,
  };

  const resultMatching = (await executeRulesetEvaluation(
    ruleset,
    "entityId", // callingEntityId
    transactionContextParameterDefinitions,
    contextId,
    "ActionTesting",
    new Date().toISOString(), // timestamp
    transactionDataMatching,
    mockedRedisClient,
    mockedRedlock,
    loggingContext,
  ))?.result;


  expect(resultMatching).toEqual({
    transactionId: "ActionTesting",
    modifiedProperties: {},
    actions: [
      {
        type: "TEST_ACTION_1",
        rulesetId,
        ruleId: "NOTIFYTRANSACTIONPROPERTY",
        parameters: {
          actionParameter: "TRANSACTION",
        },
      },
      {
        type: "TEST_ACTION_2",
        rulesetId,
        ruleId: "NOTIFYTRANSACTIONPROPERTY",
        parameters: {
          actionParameter2: 100.0,
        },
      },
      {
        type: "TEST_ACTION_3",
        rulesetId,
        ruleId: "NOTIFYTRANSACTIONPROPERTY",
        parameters: {
          actionParameter3: 100.0,
        },
      },
    ],
  });

  // Execute the generated code with transactionData that does NOT meet the condition
  const transactionDataNonMatching = {
    transactionProperty: "SOME_OTHER_VALUE",
    transactionAmountProperty: 100.0,
    transactionIntegerProperty: 100,
  };

  const resultNonMatching = (await executeRulesetEvaluation(
    ruleset,
    "entityId", // callingEntityId
    transactionContextParameterDefinitions,
    contextId,
    "originTransactionId",
    new Date().toISOString(), // timestamp
    transactionDataNonMatching,
    mockedRedisClient,
    mockedRedlock,
    loggingContext,
  ))?.result;

  expect(resultNonMatching).toEqual({
    transactionId: "originTransactionId",
    modifiedProperties: {},
  });
});
  
  test("Adds Actions to publish if the Conditions are met", async () => {
    const rulesetId = "ACTIONS_TESTING";
    const contextId = "ACTIONS_TESTING_CTX";
    const ruleset = {
      rulesetId,
      entityId,
      category: "UNIT_TESTING",
      name: "ActionTesting",
      description: "for testing",
      schemaVersion: "1.0.0",
      version: 1,
      status: "ACTIVE",
      startDateTime: "1970-01-01T00:00:00Z",
      endDateTime: "2034-01-01T00:00:00Z",
      lastModifiedDateTime: "2024-01-01T00:00:00Z",
      collectionMappings: [],
      persistentVariables: [],
      localVariables: [],
      contextId,
      outcomeRules: [],
      evaluationRules: [
        {
          ruleId: "NOTIFYTRANSACTIONPROPERTY",
          name: "NotifyTransactionProperty",
          description:
          "Notifies the caller of the value of the transactionProperty",
          priority: 0,
          condition: {
            conditionTypeId: "COMPARISON",
            operator: "==",
            parameters: {
              leftOperand: "{transactionProperty}",
              rightOperand: "TRANSACTION",
            },
          },
          actions: [
            {
              type: "TEST_ACTION_1",
              parameters: {
                actionParameter: "{transactionProperty}",
              },
            },
            {
              type: "TEST_ACTION_2",
              parameters: {
                actionParameter2: "{transactionAmountProperty}",
              },
            },
          ],
          variableAssignments: [],
        },
      ]
    };

    next = jest.fn();
    req = createMockReq(ruleset);
    res = createMockRes();
    validateRuleset(req, res, next);
    expect(next).toHaveBeenCalled();

    const transactionContextParameterDefinitions = [
      {
        name: "transactionProperty",
        propertyId: "transactionProperty",
        type: "string",
        description: "Some property of the transaction",
      },
    ];

    const expectedCode =
      '    if (transactionProperty === "TRANSACTION") {\n' +
          "      actions.push({\n" +
            '        type: "TEST_ACTION_1",\n' +
            `        rulesetId: "${rulesetId}",\n` +
            '        ruleId: "NOTIFYTRANSACTIONPROPERTY",\n' +
            "        parameters: {\n" +
              '          "actionParameter": transactionProperty\n' +
              "        }\n" +
            "      });\n" +
          "      actions.push({\n" +
            '        type: "TEST_ACTION_2",\n' +
            `        rulesetId: "${rulesetId}",\n` +
            '        ruleId: "NOTIFYTRANSACTIONPROPERTY",\n' +
            "        parameters: {\n" +
              '          "actionParameter2": transactionAmountProperty\n' +
              "        }\n" +
            "      });\n" +
          "    }\n";

    const ast = createAst(
        ExecutionTarget.EVALUATION,
      ruleset,
      contextId,
      transactionContextParameterDefinitions,
      loggingContext,
    );
    const code = generateCode(ast);


    expect(code).toContain(expectedCode);
  });

test("Local Variable Assignment: It extracts local variables from the ruleset and creates assignments", async () => {
  const rulesetId = "LOCAL_VAR_ASSIGNMENT_TESTING";
  const contextId = "LOCAL_VAR_ASSIGNMENT_TESTING_CTX";
  const ruleset = {
    rulesetId,
    entityId,
    category: "UNIT_TESTING",
    name: "LocalVarAssignment",
    description: "for testing",
    schemaVersion: "1.0.0",
    version: 1,
    status: "ACTIVE",
    startDateTime: "1970-01-01T00:00:00Z",
    endDateTime: "2034-01-01T00:00:00Z",
    lastModifiedDateTime: "2024-01-01T00:00:00Z",
    collectionMappings: [],
    persistentVariables: [],
    contextId,
    outcomeRules: [],
    evaluationRules: [
      {
        ruleId: "NOTIFYTRANSACTIONPROPERTY",
        name: "NotifyTransactionProperty",
        description:
        "Notifies the caller of the value of the transactionProperty",
        priority: 0,
        condition: {
          conditionTypeId: "COMPARISON",
          operator: "==",
          parameters: {
            leftOperand: "TRUE",
            rightOperand: "TRUE",
          },
        },
        actions: [],
        variableAssignments: [],
      },
    ],
    localVariables: [
      {
        variableId: "transactionProperty",
        name: "transactionProperty",
        type: "string",
        description: "Some property of the transaction",
        defaultValue: "test",
      },
      {
        variableId: "amountProperty",
        name: "amountProperty",
        type: "number",
        description: "Amount of the transaction",
        defaultValue: 100.0,
      },
      {
        variableId: "integerProperty",
        name: "integerProperty",
        type: "number",
        description: "An integer property",
        defaultValue: 10,
      },
      {
        variableId: "booleanProperty",
        name: "booleanProperty",
        type: "boolean",
        description: "A boolean property",
        defaultValue: false,
      },
      {
        variableId: "dateProperty",
        name: "dateProperty",
        type: "date",
        description: "A date property",
        defaultValue: "1970-01-01",
      },
      {
        variableId: "floatProperty",
        name: "float property",
        type: "number",
        description: "A float property",
        defaultValue: 0.99,
      },
    ],
  };

  next = jest.fn();
  req = createMockReq(ruleset);
  res = createMockRes();
  validateRuleset(req, res, next);
  expect(next).toHaveBeenCalled();

  const ast = createAst( ExecutionTarget.EVALUATION, ruleset, contextId, [], loggingContext);
  const code = generateCode(ast);

  const expectedCode =
    '  let transactionProperty = "test";\n' +
    "  let amountProperty = 100;\n" +
    "  let integerProperty = 10;\n" +
    "  let booleanProperty = false;\n" +
    '  let dateProperty = new Date("1970-01-01");\n' +
    "  let floatProperty = 0.99;\n" ;
  expect(code).toContain(expectedCode);
});

test("Assignment: It extracts transaction parameters from the transactionData parameter", async () => {
  const rulesetId = "TRANSACTION_DATA_ASSIGNMENT_TESTING";
  const contextId = "TRANSACTION_DATA_ASSIGNMENT_TESTING_CTX";
  const ruleset = {
    name: "Local Variable Assignment",
    entityId,
    rulesetId,
    category: "UNIT_TESTING",
    description: "for testing",
    schemaVersion: "1.0.0",
    version: 1,
    status: "ACTIVE",
    startDateTime: "1970-01-01T00:00:00Z",
    endDateTime: "2034-01-01T00:00:00Z",
    lastModifiedDateTime: "2024-01-01T00:00:00Z",
    localVariables: [],
    collectionMappings: [],
    persistentVariables: [],
    contextId,
    outcomeRules: [],
    evaluationRules: [
      {
        ruleId: "NOTIFYTRANSACTIONPROPERTY",
        name: "NotifyTransactionProperty",
        description:
        "Notifies the caller of the value of the transactionProperty",
        priority: 0,
        condition: {
          conditionTypeId: "COMPARISON",
          operator: "==",
          parameters: {
            leftOperand: "{transactionProperty}",
            rightOperand: "TRANSACTION",
          },
        },
        actions: [],
        variableAssignments: [],
      },
    ]
  };

  next = jest.fn();
  req = createMockReq(ruleset);
  res = createMockRes();
  validateRuleset(req, res, next);
  expect(next).toHaveBeenCalled();

  const transactionContextParameterDefinitions = [
    {
      name: "transactionProperty",
      propertyId: "transactionProperty",
      type: "string",
      description: "Some property of the transaction",
    },
    {
      name: "amountProperty",
      propertyId: "amountProperty",
      type: "number",
      description: "Amount of the transaction",
    },
    {
      name: "integerProperty",
      propertyId: "integerProperty",
      type: "number",
      description: "An integer property",
    },
    {
      name: "booleanProperty",
      propertyId: "booleanProperty",
      type: "boolean",
      description: "A boolean property",
    },
    {
      name: "dateProperty",
      propertyId: "dateProperty",
      type: "date",
      description: "A date property",
    },
    {
      name: "arrayProperty",
      propertyId: "arrayProperty",
      type: "array",
      description: "An array property",
    },
    {
      name: "objectProperty",
      propertyId: "objectProperty",
      type: "object",
      description: "An object property",
    },
  ];

  const ast = createAst(
        ExecutionTarget.EVALUATION,
    ruleset,
    contextId,
    transactionContextParameterDefinitions,
    loggingContext,
  );
  const code = generateCode(ast);

  const expectedCode =
    '  const transactionProperty = transactionData["transactionProperty"];\n' +
    '  const amountProperty = transactionData["amountProperty"];\n' +
    '  const integerProperty = transactionData["integerProperty"];\n' +
    '  const booleanProperty = Boolean(transactionData["booleanProperty"]);\n' +
    '  const dateProperty = new Date(transactionData["dateProperty"]);\n' +
    '  const arrayProperty = transactionData["arrayProperty"];\n' +
    '  const objectProperty = transactionData["objectProperty"];\n';

  expect(code).toContain(expectedCode);
});


describe("Analytics Data Storage", () => {
  test("Analytics data is stored in Redis after ruleset evaluation", async () => {
    const rulesetId = "ANALYTICS_TEST_RULESET";
    const contextId = "ANALYTICS_TEST_CONTEXT";
    const ruleset = {
      rulesetId,
      entityId,
      category: "UNIT_TESTING",
      name: "Analytics Testing",
      description: "for testing analytics data storage",
      status: "ACTIVE", 
      schemaVersion: "1.0.0",
      version: 1,
      startDateTime: "1970-01-01T00:00:00Z",
      endDateTime: "2034-01-01T00:00:00Z",
      lastModifiedDateTime: "2024-01-01T00:00:00Z",
      collectionMappings: [
        {
          collectionId: "sellerArea",
          name: "sellerArea",
          keyMapping: {
            propertyId: "sellerId",
          }
        }
      ],
      persistentVariables: [
        {
          variableId: "sellerAreaCount",
          name: "Seller Area Count",
          description: "Counts transactions in seller area",
          type: "number",
          defaultValue: 0,
          collectionId: "sellerArea",
        }
      ],
      localVariables: [],
      contextId,
      outcomeRules: [],
      evaluationRules: [
        {
          ruleId: "RULE_1",
          name: "RULE_1",
          description: "Updates seller area count",
          priority: 0,
          condition: {
            conditionTypeId: "COMPARISON",
            operator: "==",
            parameters: {leftOperand: "TRUE", rightOperand: "TRUE"}
          },
          variableAssignments: [
            {
              variableId: "sellerAreaCount",
              assignmentTypeId: "ADD",
              value: 1,
            }
          ],
          actions: []
        }
      ]
    };

    const transactionContextParameterDefinitions = [
      {
        name: "sellerId",
        propertyId: "sellerId",
        type: "string",
        description: "the seller id",
      }
    ];

    const transactionData = {
      sellerId: "SELLER_001"
    };

    const timestamp = "2024-01-01T00:00:00.000Z";
    const transactionId = "TEST_TRANSACTION_001";

    // Execute the ruleset
   const response = await executeRulesetEvaluation(
      ruleset,
      entityId,
      transactionContextParameterDefinitions,
      contextId,
      transactionId,
      timestamp,
      transactionData,
      mockedRedisClient,
      mockedRedlock,
      loggingContext
    );


    // Get the first item from Redis list
    const queueKey = "analytics_queue:{common}";
    
    // Get the analytics data from Redis storage
    const analyticsData = JSON.parse(redisStorage[queueKey][0]);

    const pers_var_timestamp = analyticsData.collections[0].persistence[0].data[0].timestamp;

    // Verify the analytics data structure
    expect(analyticsData).toEqual({
      timestamp: timestamp,
      entity_id: entityId,
      rule_phase: "EVALUATION",
      context_id: contextId,
      transaction_id: transactionId,
      ruleset_id: rulesetId,
      ruleset_ver: 1,
      collections: 
      [
        {
          id: "sellerArea",
          key: "sellerId",
          value: "SELLER_001",
          persistence: [
            {
              id: "sellerAreaCount",
              data: [
                {
                  "ruleId": "RULE_1",
                  "timestamp": pers_var_timestamp,
                  "value": 1,
                }
              ]
            }
          ]
        }
      ]
    });
  });
});
});
