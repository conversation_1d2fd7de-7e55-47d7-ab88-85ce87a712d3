import { logger } from "../middleware/logger.js";

import { v4 as uuidv4 } from "uuid"; // For ES6 modules

function safeStringify(obj, indent = 2) {
  const seen = new WeakSet(); // Track circular references

  return JSON.stringify(
    obj,
    (key, value) => {
      // Skip circular references
      if (typeof value === "object" && value !== null) {
        if (seen.has(value)) return "[Circular]";
        seen.add(value);
      }

      // Handle complex objects (e.g., Buffer, Stream, etc.)
      if (value instanceof Buffer) return "[<PERSON>uffer]";
      if (value instanceof Error) return `[Error: ${value.message}]`;
      // if (typeof value === "function") return "[Function]";
      if (typeof value === "symbol") return "[Symbol]";

      return value;
    },
    indent,
  );
}

export function logFunctionCalls(fn) {
  const functionCallId = uuidv4();
  return function(...args) {
    logger.trace(
      [],
      JSON.stringify({
        calling: {
          functionCallId: functionCallId,
          functionName: fn.name,
          arguments: args,
        },
      }),
    );

    let callResult = fn.apply(this, args);

    logger.trace(
      [],
      JSON.stringify({
        returning: {
          functionCallId: functionCallId,
          functionName: fn.name,
          returnValue: callResult,
        },
      }),
    );
    return callResult;
  };
}

// Create a proxy to log function calls
export function createLoggingProxy(obj) {
  return new Proxy(obj, {
    get(target, prop) {
      const functionCallId = uuidv4();
      if (typeof target[prop] === "function") {
        return function(...args) {
          const context = args[args.length - 1];
          try {
            logger.info(
              context,
              JSON.stringify({
                calling: {
                  functionCallId: functionCallId,
                  className: target.constructor.name,
                  functionName: prop,
                },
              }),
            );
            logger.trace(
              context,
              JSON.stringify({
                calling: {
                  functionCallId: functionCallId,
                  className: target.constructor.name,
                  functionName: prop,
                  arguments: args,
                },
              }),
            );

            let callResult = target[prop].apply(this, args);

            logger.trace(
              context,
              JSON.stringify({
                returning: {
                  functionCallId: functionCallId,
                  className: target.constructor.name,
                  functionName: prop,
                  returnValue: callResult,
                },
              }),
            );
            logger.info(
              context,
              JSON.stringify({
                returning: {
                  functionCallId: functionCallId,
                  className: target.constructor.name,
                  functionName: prop,
                },
              }),
            );

            return callResult;
          } catch (error) {
            logger.error(context, error);
            throw error;
          }
        };
      }
      return target[prop];
    },
  });
}
