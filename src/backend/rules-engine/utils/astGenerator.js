/* eslint-disable no-unused-vars */
/**
 * astGenerator.js
 *
 * Generates, compiles, caches, and executes dynamic “evaluate” functions
 * for campaign rulesets.
 *
 * Workflow:
 *   1. createAst(...)       ⇒ Build a Babel AST for a given ruleset JSON
 *   2. generateCode(...)    ⇒ Emit JavaScript source from that AST
 *   3. createHelperFunctions() ⇒ Inject runtime helpers (locking, Redis, date utils)
 *   4. executeRulesetEvaluation(...) ⇒ Compile & cache the evaluator, then invoke it
 */
//;

import vm from "vm";
import * as t from "@babel/types";
import * as generator from "@babel/generator";
import { createRequire } from "module";
import AnalyticsService from "../services/analyticsService.js";
import StockApiService from "../services/stockApiService.js";

import { getEcdsApiToken } from "../connectors/ecdsApiTransfer.js";
import { logger } from "../middleware/logger.js";
import { numericLiteral } from "@babel/types";

const generate = generator.default.default;

const codeCache = new Map();

function isJson(str) {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
}

function logWithLineNumbers(text) {
  text
    .split(/\r?\n/)           // split on both LF and CRLF
    .forEach((line, index) => {
      console.log(`${index + 1}: ${line}`);
    });
}

/**
 * Normalize an input into an array.
 *
 * @param {*} value
 *   Any value. If it is an empty object ({}), returns []
 *   if falsy, returns []; otherwise returns value.
 * @returns {Array}
 */
//;
function normalizeArray(value) {
  if (
    value &&
    typeof value === "object" &&
    !Array.isArray(value) &&
    Object.keys(value).length === 0
  ) {
    return [];
  }
  return value || [];
}

/**
 * Returns a string of helper-function definitions to be prepended
 * to the generated code at runtime.  These include:
 *   - PropertyChangeLog class
 *   - acquireAllLocks / releaseAllLocks (Redlock)
 *   - isValueInList (Redis set lookup)
 *   - crediverseTransfer (external API call)
 *   - date‐utility functions (isDay, getDayOfWeek, etc.)
 *
 * @returns {string}
 *   JavaScript source defining all runtime helpers.
 */
//;
function createHelperFunctions() {
  return `

  const formatDate = (dateToFormat, dateFormat) => {
    // 1. Normalize/validate the input as a Date
    let date;
    if (dateToFormat instanceof Date) {
      date = dateToFormat;
    } else {
      date = new Date(dateToFormat);
    }
    if (!(date instanceof Date) || isNaN(date.getTime())) {
      throw new TypeError('Invalid date: ' + dateToFormat);
    }
  
    // 2. If requested format is yyyy-MM-dd, build that
    if (dateFormat === 'yyyy-MM-dd') {
      const year  = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day   = String(date.getDate()).padStart(2, '0');
      return \`\${year}-\${month}-\${day}\`;
    }
  
    // 3. Fallback to ISO 8601
    return date.toISOString();
  };

  class PropertyChangeLog{
    constructor(transactionId, rulesetId) {
      this.transactionId = transactionId;
      this.rulesetId = rulesetId;
      // Holds the modified properties as an object where each key is a property name.
        this.modifiedProperties = {};
    }

    addChangedProperty(ruleId, propertyName, originalValue, modifiedValue) {
      this.modifiedProperties[propertyName] = {
        original: originalValue,
        modified: modifiedValue,
        rulesetId: this.rulesetId,
        ruleId,
      };
    }

    serialize() {
      return this.modifiedProperties;
    }
  }

  async function acquireAllLocks(keys, callerContext,ttlMs = 15000) {
    const loggingContext = [...callerContext, {function: 'acquireAllLocks'} ]

    
    // Sort keys (optional, but sometimes good practice to keep ordering consistent)
    const sortedKeys = [...keys].sort();
    logger.trace(loggingContext, { keys_to_lock: sortedKeys });

    // Redlock acquire is all-or-nothing for these resources.
      // If it cannot lock all of them, it throws an error.

      try {
        logger.info(loggingContext,"Aquiring persistentVariablesLock");
        const lock = await persistentVariablesLock.acquire(sortedKeys, ttlMs);
        logger.info(loggingContext,"Aquired persistentVariablesLock");
        return lock;
      } catch (e) {


        const statsList = await Promise.all(e.attempts.map(p => p.catch(e => { 
          throw e; 
        })));
        statsList.forEach((stats, idx) => {
          if (stats.votesAgainst.size > 0) {
            stats.votesAgainst.forEach((error, client) => {
              logger.error(loggingContext,{
                nodeIndex: idx,
                reason: error.message
              });
            });
          }
        });

        throw e;
      }
  }

  /**
    * Releases the lock for all resources that were acquired together.
    *
    * @param {import('redlock').Lock} lock - The lock object returned by acquireAllLocks
    */
    //;
  async function releaseAllLocks(lock,callerContext) {
    const loggingContext = [...callerContext, {function: 'releaseAllLocks'} ]
    logger.info(loggingContext, "releasing persistent variable locks");
    if (!lock) {
      logger.warn(loggingContext, "lock not held.");
      return;
    }
    logger.info(loggingContext, "awaiting lock. " );
    await lock.release();
    logger.info(loggingContext, "lock released" );
  }

  const ECDS_API_PORT = process.env.ECDS_API_PORT || 9084;
  const ECDS_API_URL = process.env.ECDS_API_URL || "http://ecds-api";

  const ECDS_API_TRANSFER_PATH = "/api/account/transaction/transfer";

  const ECDS_API_TRANSFER_URI = ECDS_API_URL + ":" + ECDS_API_PORT + ECDS_API_TRANSFER_PATH;

  async function isValueInList(value, listName, callerContext) {

    const loggingContext = [...callerContext, {function: 'isValueInList'} ]
    try {
      const listDataString = await redisClient.hget("lists:{common}", listName);

      if (!listDataString) {
        return false;
      }

      const listData = JSON.parse(listDataString);

      const elementsKey = listData.elementsKey;  
      if (!elementsKey) {
        return false;
      }

      return await redisClient.sismember(elementsKey, value);
    } catch (err) {
      logger.error(loggingContext, err.message);
      return false;
    }
  }

  // Function to make the API request
  async function crediverseTransfer(apiCallName, amount, targetMsisdn, callerContext) {
    const loggingContext = [...callerContext, {function: 'crediverseTransfer'} ];

    logger.info(loggingContext,{ apiCallName, targetMsisdn, amount });

    const transferRequest = {
      amount: amount,
      targetMSISDN: targetMsisdn
    };


    try {
      const ecdsApiToken = apiCallTokenMap.get(apiCallName);

      // Make the request
      const response = await fetch(ECDS_API_TRANSFER_URI, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": "Bearer " + ecdsApiToken
        },
        body: JSON.stringify(transferRequest), // Convert data to JSON string
      });

      if (!response.ok) {
        const errorResponse = await response.json();
        const errorMessage = \` Crediverse transfer failed with status code \${response.status}: \${JSON.stringify(errorResponse)}\`;
        logger.error(loggingContext, errorMessage );
        return {apiCallName, apiCallType: "crediverseTransfer",request: transferRequest, response: errorResponse};
      } else {
        const transferResponse = await response.json();
        logger.trace(loggingContext , {transferResponse} );
        return {apiCallName, apiCallType: "crediverseTransfer",request: transferRequest, response: transferResponse};
      }

    } catch (error) {
      logger.error(loggingContext,error.message);

      return {apiCallName, apiCallType: "crediverseTransfer",request: transferRequest, response: error };
    }
  }

  // New Stock Transfer API function
  async function stockTransfer(apiCallName, fromAccount, toAccount, amount, stockType, apiId, rulesetId, accountRef, callerContext) {
    const loggingContext = [...callerContext, {function: 'stockTransfer'} ];

    logger.info(loggingContext, {
      apiCallName,
      fromAccount,
      toAccount,
      amount,
      stockType,
      apiId,
      rulesetId,
      accountRef
    });

    const transferRequest = {
      fromAccount,
      toAccount,
      amount,
      stockType: stockType || "default",
      apiId,
      rulesetId,
      accountRef: accountRef || "primary"
    };

    try {
      // Use the stockApiService to handle the transfer
      const result = await stockApiService.transferStock(transferRequest, loggingContext);

      return {
        apiCallName,
        apiCallType: "stockTransfer",
        request: transferRequest,
        response: result
      };

    } catch (error) {
      logger.error(loggingContext, error.message);

      return {
        apiCallName,
        apiCallType: "stockTransfer",
        request: transferRequest,
        response: { success: false, error: error.message }
      };
    }
  }
  ///

  

  // Helper function to determine if a date is on the specified day of the week
  function isDay(dateToCheck, day) {
    const daysOfWeek = ["SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"];
    const dayIndex = dateToCheck.getUTCDay(); 
    return daysOfWeek[dayIndex] === day.toUpperCase();
  }


  function dayOfWeek(date) {
    const days = ["SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"];
    return days[date.getUTCDay()];
  }

  function getDayOfMonth(date) {
    return date.getUTCDate();
  }

  function getWeekOfYear(date) {
    const tempDate = new Date(date.getUTCFullYear(), 0, 1);
    const dayOfYear = Math.floor((date - tempDate) / (24 * 60 * 60 * 1000)) + 1;
    return Math.ceil((dayOfYear + tempDate.getUTCDay() + 1) / 7);
  }

  function getMonthOfYear(date) {
    const months = [
      "JANUARY", "FEBRUARY", "MARCH", "APRIL", "MAY", "JUNE", 
      "JULY", "AUGUST", "SEPTEMBER", "OCTOBER", "NOVEMBER", "DECEMBER"
    ];
    return months[date.getUTCMonth()];
  }

  function getHourOfDay(date) {
    return date.getUTCHours();
  }
  `;
}

/**
 * Build AST nodes for common date‐related constants:
 *   dayOfWeek, dayOfMonth, weekOfYear, monthOfYear, hourOfDay
 *
 * @returns {Array<import('@babel/types').VariableDeclaration>}
 */
//;
function createDateConstants() {
  const dayOfWeekDeclaration = t.variableDeclaration("const", [
    t.variableDeclarator(
      t.identifier("DAY_OF_WEEK"),
      t.callExpression(t.identifier("dayOfWeek"), [t.identifier("timestamp")]),
    ),
  ]);

  const dayOfMonthDeclaration = t.variableDeclaration("const", [
    t.variableDeclarator(
      t.identifier("dayOfMonth"),
      t.callExpression(t.identifier("getDayOfMonth"), [
        t.identifier("timestamp"),
      ]),
    ),
  ]);

  const weekOfYearDeclaration = t.variableDeclaration("const", [
    t.variableDeclarator(
      t.identifier("weekOfYear"),
      t.callExpression(t.identifier("getWeekOfYear"), [
        t.identifier("timestamp"),
      ]),
    ),
  ]);

  const monthOfYearDeclaration = t.variableDeclaration("const", [
    t.variableDeclarator(
      t.identifier("monthOfYear"),
      t.callExpression(t.identifier("getMonthOfYear"), [
        t.identifier("timestamp"),
      ]),
    ),
  ]);

  const hourOfDayDeclaration = t.variableDeclaration("const", [
    t.variableDeclarator(
      t.identifier("hourOfDay"),
      t.callExpression(t.identifier("getHourOfDay"), [
        t.identifier("timestamp"),
      ]),
    ),
  ]);

  return [
    dayOfWeekDeclaration,
    dayOfMonthDeclaration,
    weekOfYearDeclaration,
    monthOfYearDeclaration,
    hourOfDayDeclaration,
  ];
}

/**
 * Declare `const apiCallResults = []` in the AST.
 *
 * @returns {import('@babel/types').VariableDeclaration}
 */
//;
function createApiResponseMapVariableDeclaration() {
  return t.variableDeclaration("const", [
    t.variableDeclarator(t.identifier("apiCallResults"), t.arrayExpression([])),
  ]);
}

/**
 * Given an array of API-call definitions, produce AST statements:
 *   apiCallResults.push(await <apiCall>(...params, callerContext));
 *
 * @param {Array<Object>} apiCallDefinitions
 * @param {Array<Object>} _localVariableDefinitions
 * @param {Array<Object>} _persistentVariableDefinitions
 * @param {Array<Object>} _transactionParameterDefinitions
 * @returns {Array<import('@babel/types').ExpressionStatement>}
 */
//;
function createApiCallStatements(
  apiCallDefinitions,
  _localVariableDefinitions,
  _persistentVariableDefinitions,
  _transactionParameterDefinitions,
) {
  return apiCallDefinitions.map((apiCallDefinition) => {
    // Handle different API types
    if (apiCallDefinition.type === "stockTransfer") {
      return createStockTransferStatement(apiCallDefinition);
    } else {
      // Legacy crediverseTransfer and other API types
      return t.expressionStatement(
        t.callExpression(
          t.memberExpression(
            t.identifier("apiCallResults"),
            t.identifier("push"),
          ),
          [
            t.awaitExpression(
              t.callExpression(t.identifier(apiCallDefinition.type), [
                t.stringLiteral(apiCallDefinition.name),
                ...apiCallDefinition.parameters.map((param) => {
                  const value = param.value;
                  if (value.startsWith("{") && value.endsWith("}")) {
                    const varName = value.slice(1, -1);
                    return t.identifier(varName);
                  } else {
                    return t.stringLiteral(value);
                  }
                }),
                t.identifier("callerContext"),
              ]),
            ),
          ],
        ),
      );
    }
  });
}

/**
 * Create AST statement for stock transfer API call
 * @param {Object} apiCallDefinition - API call definition with stockTransfer type
 * @returns {import('@babel/types').ExpressionStatement}
 */
function createStockTransferStatement(apiCallDefinition) {
  // Extract parameters for stock transfer
  const paramMap = {};
  apiCallDefinition.parameters.forEach(param => {
    paramMap[param.name] = param.value;
  });

  // Create arguments for stockTransfer function call
  const args = [
    t.stringLiteral(apiCallDefinition.name), // apiCallName
  ];

  // Add required parameters in order
  const requiredParams = ["fromAccount", "toAccount", "amount"];
  requiredParams.forEach(paramName => {
    const value = paramMap[paramName];
    if (value && value.startsWith("{") && value.endsWith("}")) {
      const varName = value.slice(1, -1);
      args.push(t.identifier(varName));
    } else {
      args.push(t.stringLiteral(value || ""));
    }
  });

  // Add optional parameters
  const stockType = paramMap["stockType"];
  if (stockType && stockType.startsWith("{") && stockType.endsWith("}")) {
    const varName = stockType.slice(1, -1);
    args.push(t.identifier(varName));
  } else {
    args.push(t.stringLiteral(stockType || "default"));
  }

  // Add API metadata
  args.push(t.stringLiteral(apiCallDefinition.apiId || ""));
  args.push(t.stringLiteral(apiCallDefinition.rulesetId || ""));
  args.push(t.stringLiteral(apiCallDefinition.accountRef || "primary"));
  args.push(t.identifier("callerContext"));

  return t.expressionStatement(
    t.callExpression(
      t.memberExpression(
        t.identifier("apiCallResults"),
        t.identifier("push"),
      ),
      [
        t.awaitExpression(
          t.callExpression(t.identifier("stockTransfer"), args)
        ),
      ],
    ),
  );
}

/**
 * Given action definitions and IDs, produce AST:
 *   actions.push({ type, rulesetId, ruleId, parameters: { ... } });
 * @param {Array<Object>} actionsDefinitions
 * @param {string} rulesetId
 * @param {string} ruleId
 * @returns {Array<import('@babel/types').ExpressionStatement>}
 */
//;
function createActionStatements(actionsDefinitions, rulesetId, ruleId) {
  const actionsArrayIdentifier = t.identifier("actions");

  // Generate actions.push(...) statements
  const actionPushStatements = actionsDefinitions.map((actionDef) => {
    const actionObjectProperties = [
      t.objectProperty(t.identifier("type"), t.stringLiteral(actionDef.type)),
      t.objectProperty(t.identifier("rulesetId"), t.stringLiteral(rulesetId)),
      t.objectProperty(t.identifier("ruleId"), t.stringLiteral(ruleId)),
    ];

    // Process parameters
    const parametersProperties = Object.entries(actionDef.parameters).map(
      ([key, value]) => {
        let propertyValue;
        if (
          typeof value === "string" &&
          value.startsWith("{") &&
          value.endsWith("}")
        ) {
          // Placeholder: use variable from scope
          const variableName = value.slice(1, -1);
          propertyValue = t.identifier(variableName);
        } else {
          // Literal value
          //
          if (typeof value === "number") {
            propertyValue = t.numericLiteral(value);
          } else {
            propertyValue = t.stringLiteral(value);
          }
        }

        return t.objectProperty(t.stringLiteral(key), propertyValue);
      },
    );

    const parametersObject = t.objectExpression(parametersProperties);
    actionObjectProperties.push(
      t.objectProperty(t.identifier("parameters"), parametersObject),
    );

    const actionObject = t.objectExpression(actionObjectProperties);

    // Generate: actions.push(actionObject);
    const pushStatement = t.expressionStatement(
      t.callExpression(
        t.memberExpression(actionsArrayIdentifier, t.identifier("push")),
        [actionObject],
      ),
    );

    return pushStatement;
  });

  // Return only the action push statements
  return actionPushStatements;
}

/**
 * From transactionParameterDefinitions, build AST variable declarations:
 *   const/let <propertyId> = <transactionData>.<propertyId>;
 *
 * @param {Array<{ propertyId: string, type: string, mutable: boolean }>} transactionParameterDefinitions
 * @returns {Array<import('@babel/types').VariableDeclaration>}
 */
//;
function createTransactionContextAssignments(transactionParameterDefinitions) {
  return transactionParameterDefinitions.map((param) => {
    const { propertyId, type } = param;

    const transactionDataMember = t.memberExpression(
      t.identifier("transactionData"),
      t.stringLiteral(propertyId),
      true,
    );

    let rightHandSide;

    switch (type) {
      case "string":
        rightHandSide = transactionDataMember;
        break;
      case "number":
        rightHandSide = transactionDataMember;
        break;
      case "boolean":
        rightHandSide = t.callExpression(t.identifier("Boolean"), [
          transactionDataMember,
        ]);
        break;
      case "date":
        rightHandSide = t.newExpression(t.identifier("Date"), [
          transactionDataMember,
        ]);
        break;
      case "array":
      case "object":
        rightHandSide = transactionDataMember;
        break;
      default:
        rightHandSide = transactionDataMember;
    }

    // Create const variableName = rightHandSide;
    const variableDeclarator = t.variableDeclarator(
      t.identifier(propertyId),
      rightHandSide,
    );

    let declarationKeyword = "";

    if (param.mutable === true) {
      declarationKeyword = "let";
    } else {
      declarationKeyword = "const";
    }

    return t.variableDeclaration(declarationKeyword, [variableDeclarator]);
  });
}

function createGetPersistentVariableCache(variableDefinition) {


  const castFunctionIdentifier = (variableType) => {
    switch(variableType) {
      case "number":
        return "Number";  
      case "string": 
        return "String";
      case "boolean":
        return "Boolean";
      case "date":
        return "Date";
      default:
        throw new Error("Unkown persistent variable type");
    }

  };
  const getValueFromCacheDeclaration = t.variableDeclaration("const", [
    t.variableDeclarator(
      t.identifier(`${variableDefinition.variableId}FromCache`),
      t.callExpression(
        t.identifier( castFunctionIdentifier(variableDefinition.type)), [
          t.awaitExpression(
            t.callExpression(
              t.memberExpression(
                t.identifier("redisClient"),
                t.identifier("hget"),
              ),
              [
                t.stringLiteral("persistentVariables:{common}"),
                t.callExpression(
                  t.memberExpression(
                    t.identifier("persistentVariableKeys"),
                    t.identifier("get"),
                  ),
                  [t.stringLiteral(variableDefinition.variableId)],
                ),
              ],
            ),
          ),
        ]),
    ),
  ]);

  const ifStatement = t.ifStatement(
    t.identifier(`${variableDefinition.variableId}FromCache`),
    t.blockStatement([
      t.expressionStatement(
        t.assignmentExpression(
          "=",
          t.identifier(variableDefinition.variableId),
          t.identifier(`${variableDefinition.variableId}FromCache`),
        ),
      ),
    ]),
  );

  return [getValueFromCacheDeclaration, ifStatement];
}

function createPersistentVariableCacheUpdate(variableId) {
  return t.expressionStatement(
    t.awaitExpression(
      t.callExpression(
        t.memberExpression(t.identifier("redisClient"), t.identifier("hset")),
        [
          t.stringLiteral("persistentVariables:{common}"),
          t.callExpression(
            t.memberExpression(
              t.identifier("persistentVariableKeys"),
              t.identifier("get"),
            ),
            [t.stringLiteral(variableId)],
          ),

          t.identifier(variableId),
        ],
      ),
    ),
  );
}

function createPersistentVariableDeclarations(persistentVariableDefinitions) {
  return persistentVariableDefinitions.flatMap(
    (persistentVariableDefinitions) => {
      return createVariableAssignment(persistentVariableDefinitions);
    },
  );
}

/**
 * From persistentVariableDefinitions, build AST to:
 *   1. Fetch Number(await redisClient.hget(...)) into `<var>FromCache`
 *   2. If present, assign it back to `<var>`
 *
 * @param {Array<{ variableId: string, type: string }>} persistentVariableDefinitions
 * @returns {Array<import('@babel/types').VariableDeclaration|import('@babel/types').IfStatement>}
 */
//;
function createPersistentVariableAssignments(persistentVariableDefinitions) {
  return persistentVariableDefinitions.flatMap(
    (persistentVariableDefinition) => {
      const getValueFromCacheDeclaration = createGetPersistentVariableCache(
        persistentVariableDefinition,
      );

      const persistentVariableAssignments = [...getValueFromCacheDeclaration];

      return persistentVariableAssignments;
    },
  );
}
/**
 * Create AST declaration for const persistentVariableLogs = []
 *
 * @returns {import('@babel/types').VariableDeclaration}
 */
function createPersistentVariableLogsDeclaration() {
  return t.variableDeclaration("const", [
    t.variableDeclarator(
      t.identifier("persistentVariableLogs"),
      t.arrayExpression([])
    ),
  ]);
}
/**
 * Create AST statement for persistentVariableLogs.push()
 *
 * @param {string} variableId - The variable ID
 * @param {string} ruleId - The rule ID
 * @returns {import('@babel/types').ExpressionStatement}
 */
function createPersistentVariableLogPush(variableId, ruleId) {
  return t.expressionStatement(
    t.callExpression(
      t.memberExpression(
        t.identifier("persistentVariableLogs"),
        t.identifier("push")
      ),
      [
        t.objectExpression([
          t.objectProperty(
            t.identifier("id"),
            t.stringLiteral(variableId)
          ),
          t.objectProperty(
            t.identifier("value"),
            t.identifier(variableId)
          ),
          t.objectProperty(
            t.identifier("timestamp"),
            t.callExpression(
              t.memberExpression(
                t.newExpression(t.identifier("Date"), []),
                t.identifier("toISOString")
              ),
              []
            )
          ),
          t.objectProperty(
            t.identifier("ruleId"),
            t.stringLiteral(ruleId)
          )
        ])
      ]
    )
  );
}

/**
 * Map ruleset.localVariables into AST `let <variableId> = <defaultValue>;`
 *
 * @param {Array<{ variableId: string, type: string, defaultValue: any }>} localVariableDefinitions
 * @returns {Array<import('@babel/types').VariableDeclaration>}
 */
//;
function createLocalVariableAssignments(localVariableDefinitions) {
  return localVariableDefinitions.map(createVariableAssignment);
}

function createVariableAssignment(param) {
  const { variableId, type, defaultValue } = param;

  let valueNode;

  switch (type) {
    case "string":
      valueNode = t.stringLiteral(String(defaultValue));
      break;
    case "number":
      valueNode = t.numericLiteral(defaultValue);
      break;
    case "boolean":
      valueNode = t.booleanLiteral(Boolean(defaultValue));
      break;
    case "date":
      valueNode = t.newExpression(t.identifier("Date"), [
        t.stringLiteral(defaultValue),
      ]);
      break;
    case "array":
    case "object":
      valueNode = t.valueToNode(defaultValue);
      break;
    default:
      valueNode = t.identifier("undefined");
  }

  const variableDeclarator = t.variableDeclarator(
    t.identifier(variableId),
    valueNode,
  );

  return t.variableDeclaration("let", [variableDeclarator]);
}

/**
 * Parse an operand (literal or "{variable}") to determine if it's a variable reference.
 *
 * @param {string|number|boolean} operand
 * @param {Array} transactionParameterDefinitions
 * @param {Array} localVariableDefinitions
 * @param {Array} persistentVariableDefinitions
 * @returns {{ isVariable: boolean, variableId: string|null, variablesType: string, value: any }}
 */
//;
function parseOperand(
  operand,
  transactionParameterDefinitions,
  localVariableDefinitions,
  persistentVariableDefinitions,
) {
  if (!operand) return t.nullLiteral();

  // might this operand, which is a string be a list refenece.
  // try to parse it if it cant parse it's not

  let variableId;

  if (typeof operand === "string" && isStringListDefinition(operand)) {
    let returnValue = {
      isVariable: false,
      variableId: null,
      variablesType: "listId",
      value: JSON.parse(operand).listId,
    };

    return returnValue;
  } else if (
    typeof operand === "string" &&
    operand.startsWith("{") &&
    operand.endsWith("}")
  ) {
    const builtInVars = [
      "callingEntityId",
      "transactionContextId",
      "originTransactionId",
      "timestamp",
      "modificationsApplied",
      "status",
    ];

    variableId = operand.slice(1, -1);

    if (builtInVars.includes(variableId)) {
      return {
        isVariable: true,
        variableId: variableId,
        variablesType: "string",
        value: variableId,
      };
    }

    let variableTypeAndNamespace = getTypeAndNameSpace(
      variableId,
      localVariableDefinitions,
      persistentVariableDefinitions,
      transactionParameterDefinitions,
    );

    if (variableTypeAndNamespace) {
      const retval = {
        isVariable: true,
        variableId,
        variablesType: variableTypeAndNamespace.variableType,
        value: variableId,
      };

      return retval;
    } else {
      throw new Error(
        `Variable ${variableId} is not a transaction parameter,local variable, or persistent variable`,
      );
    }
  } else {
    const retval = {
      isVariable: false,
      variableId: null,
      variablesType: typeof operand,
      value: operand,
    };
    return retval;
  }
}

/**
 * Given left/right operand types, decide the JS comparison type.
 *
 * @param {*} leftOperand
 * @param {*} rightOperand
 * @param {string|null} leftVarType
 * @param {string|null} rightVarType
 * @returns {string} e.g. "string", "literalNumber"
 */
//;
function determineVariablesType(
  leftOperand,
  rightOperand,
  leftVarType,
  rightVarType,
) {
  if (leftVarType) {
    return leftVarType;
  }
  if (rightVarType) {
    return rightVarType;
  }
  if (typeof leftOperand === "string" && typeof rightOperand === "string") {
    return "string";
  }
  if (typeof leftOperand === "number" && typeof rightOperand === "number") {
    return "literalNumber";
  }
  throw new Error("Unsupported variableType");
}

/**
 * Construct an AST node for a literal or identifier operand.
 *
 * @param {string|number|boolean} operand
 * @param {boolean} isVariable
 * @param {string} variableId
 * @param {string} variablesType
 * @returns {import('@babel/types').Expression}
 */
//;
function createOperandExpression(
  operand,
  isVariable,
  variableId,
  variablesType,
) {
  if (isVariable) {
    return t.identifier(variableId);
  } else {
    if (operand === null) {
      return t.nullLiteral();
    } else if (variablesType === "boolean") {
      return t.booleanLiteral(operand);
    } else if (
      variablesType === "string" ||
      variablesType === "enum" ||
      variablesType === "date"
    ) {
      return t.stringLiteral(operand);
    } else if (
      variablesType === "number" ||
      variablesType === "literalNumber"
    ) {
      return t.numericLiteral(operand);
    } else {
      throw new Error(`Unsupported variablesType: ${variablesType}`);
    }
  }
}

function createTestExpression(leftExpr, rightExpr, operator) {
  const operatorMap = {
    "==": "===",
    "!=": "!==",
    "<": "<",
    "<=": "<=",
    ">": ">",
    ">=": ">=",
  };

  const jsOperator = operatorMap[operator];

  if (!jsOperator) {
    throw new Error(`Unsupported operator: ${operator}`);
  }

  return t.binaryExpression(jsOperator, leftExpr, rightExpr);
}

/**
 * Recursively build an AST expression for a rule condition.
 *
 * @param {string} type
 * @param {string} operator
 * @param {Object} parameters
 * @param {Array} transactionParameterDefinitions
 * @param {Array} localVariableDefinitions
 * @param {Array} persistentVariableDefinitions
 * @returns {import('@babel/types').Expression}
 */
export function createConditionExpression(
  type,
  operator,
  parameters,
  transactionParameterDefinitions,
  localVariableDefinitions,
  persistentVariableDefinitions,
) {
  if (!type) {
    console.error("NO CONDITION TYPE");
    throw new Error("NO condition type!");
  }

  switch (type) {
    case "ALLWAYS":
      return handleAlwaysCondition();
    case "COMPARISON":
      return createComparisonCondition(
        operator,
        parameters,
        transactionParameterDefinitions,
        localVariableDefinitions,
        persistentVariableDefinitions,
      );
    case "LOGICAL":
      return handleLogicalCondition(
        operator,
        parameters,
        transactionParameterDefinitions,
        localVariableDefinitions,
        persistentVariableDefinitions,
      );
    case "DATE": {
      return handleDateCondition(parameters);
    }
    default:
      throw new Error(`Unsupported condition type: ${type}`);
  }
}

// ===== Branch Handlers =====

function handleAlwaysCondition() {
  return t.booleanLiteral(true);
}

const InConditionListReferenceType = Object.freeze({
  VARIABLE_REFERENCE: "VARIABLE_REFERENCE",
  LOOKUP_LIST_REFERENCE: "LOOKUP_LIST_REFERENCE",
  LITERAL_LIST: "LITERAL_LIST",
});

function determineInConditionListReferenceType(listOperandInfo) {
  if (listOperandInfo.isVariable) {
    return InConditionListReferenceType.VARIABLE_REFERENCE;
  }

  if (!listOperandInfo.isVariable && listOperandInfo.variablesType === "object" && isListDefinition(listOperandInfo.value)) {
    return InConditionListReferenceType.LOOKUP_LIST_REFERENCE;
  }

  return InConditionListReferenceType.LITERAL_LIST;
}

function createComparisonCondition(
  operator,
  { leftOperand, rightOperand },
  transactionDefs,
  localDefs,
  persistentDefs,
) {


  const leftInfo = parseOperand(
    leftOperand,
    transactionDefs,
    localDefs,
    persistentDefs,
  );
  const rightInfo = parseOperand(
    rightOperand,
    transactionDefs,
    localDefs,
    persistentDefs,
  );

  if (operator === "IN") {
    return createInListExpression(
      leftInfo,
      rightInfo,
      transactionDefs,
      localDefs,
      persistentDefs,
    );
  }


  const variablesType = determineVariablesType(
    leftOperand,
    rightOperand,
    leftInfo.variablesType,
    rightInfo.variablesType,
  );

  const leftExpr = createOperandExpression(
    leftOperand,
    leftInfo.isVariable,
    leftInfo.variableId,
    variablesType,
  );
  const rightExpr = createOperandExpression(
    rightOperand,
    rightInfo.isVariable,
    rightInfo.variableId,
    variablesType,
  );

  return createTestExpression(leftExpr, rightExpr, operator);
}

function handleLogicalCondition(
  operator,
  { conditions },
  transactionParameterDefinitions,
  localVariableDefinitions,
  peristentVariableDefinitions,
) {
  const conditionExpressions = conditions.map((condition) => {
    return createConditionExpression(
      condition.conditionTypeId,
      condition.operator,
      condition.parameters,
      transactionParameterDefinitions,
      localVariableDefinitions,
      peristentVariableDefinitions,
    );
  });

  const operatorMap = {
    AND: "&&",
    OR: "||",
  };
  const jsOperator = operatorMap[operator];

  let ruleConditionExpression = conditionExpressions.reduce(
    (accumulator, currentExpression) => {
      if (!accumulator) {
        // First expression, set it as the accumulator
        return currentExpression;
      } else {
        // Combine with the current expression using logical OR
        return t.logicalExpression(jsOperator, accumulator, currentExpression);
      }
    },
    null,
  );

  return t.parenthesizedExpression(ruleConditionExpression);
}

function handleDateCondition({ date, day }) {
  const dateNode = isVariable(date)
    ? t.identifier(date.slice(1, -1))
    : t.stringLiteral(date);
  const dayNode = isVariable(day)
    ? t.identifier(day.slice(1, -1))
    : t.stringLiteral(day);

  return t.callExpression(t.identifier("isDay"), [dateNode, dayNode]);
}

function createInArrayCondition(operator, valueInfo, arrayInfo) {


  const createNode = (val) => {
    if (val.isVariable) return t.identifier(val.variableId);

    switch (val.variablesType) {
      case "number":
        return t.numericLiteral(val.value);
      case "boolean":
        return t.booleanLiteral(val.value);
      case "string":
        return t.stringLiteral(val.value);
      case "object": {
        return t.arrayExpression(val.value.map((el) => t.stringLiteral(el)));
      }
      case "listId":
        return t.stringLiteral(val.value);

      default:
        throw new Error(`Unsupported value type: ${val.variablesType}`);
    }
  };

  const valueNode = createNode(valueInfo);

  const arrayNode = createNode(arrayInfo);

  const includesExpr = t.callExpression(
    t.memberExpression(arrayNode, t.identifier("includes")),
    [valueNode],
  );

  return operator === "IN"
    ? includesExpr
    : t.unaryExpression("!", includesExpr);
}

// Extracted utility for IN-list with Redis lookup
function createInListExpression(
  valueInfo,
  listInfo,
  _transactionDefs,
  _localDefs,
  _persistentDefs,
) {

  const listReferenceType = determineInConditionListReferenceType(listInfo)



  switch (listReferenceType) {
    case InConditionListReferenceType.VARIABLE_REFERENCE: {
      return createInArrayCondition(
        "IN",
        valueInfo,
        listInfo);
    }
    case InConditionListReferenceType.LOOKUP_LIST_REFERENCE: {

      const listReference = listInfo.value.listId;

      return t.awaitExpression(
        t.callExpression(t.identifier("isValueInList"), [
          t.identifier(valueInfo.variableId),
          t.stringLiteral(listReference),
          t.identifier("callerContext"),
        ]),
      );
    }
    case InConditionListReferenceType.LITERAL_LIST: {
      return createInArrayCondition("IN",
        valueInfo,
        listInfo,
      );
    }
    default: {
      throw new Error(`Unsupported right operand type: ${listReferenceType}`);
    }
  }
}


function createIfStatement(condition, consequent) {
  return t.ifStatement(condition, consequent);
}

/**
 * Combine a condition expression and a block of statements into an if-statement.
 *
 * @param {import('@babel/types').Expression} condition
 * @param {import('@babel/types').Statement[]} consequentStatements
 * @returns {import('@babel/types').IfStatement}
 */
//;
function createConditionalStatements(
  condition,
  actionDefinitions,
  apiCallDefinitions,
  localVariableDefinitions,
  _collectionMappings,
  persistentVariableDefinitions,
  variableAssignmentDefinitions,
  transactionParameterDefinitions,
  rulesetId,
  ruleId,
) {
  const { conditionTypeId, operator, parameters } = condition;

  const variableAssignmentStatements = createVariableAssignments(
    normalizeArray(localVariableDefinitions),
    normalizeArray(variableAssignmentDefinitions),
    normalizeArray(persistentVariableDefinitions),
    normalizeArray(transactionParameterDefinitions),
    ruleId,
  );

  // Create action statements
  const actionStatements = createActionStatements(
    normalizeArray(actionDefinitions),
    rulesetId,
    ruleId,
  );

  const apiCallStatements = createApiCallStatements(
    normalizeArray(apiCallDefinitions),
    normalizeArray(localVariableDefinitions),
    normalizeArray(persistentVariableDefinitions),
    normalizeArray(transactionParameterDefinitions),
  );

  let conditionExpression = createConditionExpression(
    conditionTypeId,
    operator,
    parameters,
    normalizeArray(transactionParameterDefinitions),
    normalizeArray(localVariableDefinitions),
    normalizeArray(persistentVariableDefinitions),
  );

  const consequentStatement = t.blockStatement([
    ...variableAssignmentStatements,
    ...apiCallStatements,
    ...actionStatements,
  ]);

  return createIfStatement(conditionExpression, consequentStatement);
}

function isVariable(value) {
  return (
    typeof value === "string" && value.startsWith("{") && value.endsWith("}")
  );
}

/**
 * Build template‐literal assignment AST from a string with `{var}` placeholders.
 *
 * @param {string} templateStr
 * @param {string} varName
 * @returns {import('@babel/types').ExpressionStatement}
 */
//;
function createTemplateAssignment(templateStr, varName) {
  const variablePattern = /\{([^}]+)\}/g;

  let match;
  let lastIndex = 0;
  const quasis = [];
  const expressions = [];

  // Iterate over all matches of {variable} in the templateStr.
  while ((match = variablePattern.exec(templateStr)) !== null) {
    const varStartIndex = match.index;
    const varNameInside = match[1].trim(); // Extracted variable name

    // Text before this variable placeholder
    const textBeforeVar = templateStr.slice(lastIndex, varStartIndex);
    quasis.push(
      t.templateElement({ raw: textBeforeVar, cooked: textBeforeVar }),
    );

    // Add the variable as an expression (identifier)
    expressions.push(t.identifier(varNameInside));

    // Update the position to after the variable
    lastIndex = variablePattern.lastIndex;
  }

  // After the loop, push the remaining text as the last quasi
  const remainingText = templateStr.slice(lastIndex);
  quasis.push(
    t.templateElement({ raw: remainingText, cooked: remainingText }, true),
  );

  // Create the template literal node
  const templateLiteralNode = t.templateLiteral(quasis, expressions);

  // Create the assignment expression: smsMessage = `...`;
  const assignmentExpressionNode = t.assignmentExpression(
    "=",
    t.identifier(varName),
    templateLiteralNode,
  );

  // Create the expression statement: smsMessage = `...`;
  const expressionStatementNode = t.expressionStatement(
    assignmentExpressionNode,
  );

  return expressionStatementNode;
}

const Namespaces = Object.freeze({
  LOCAL: "local",
  PERSISTENT: "persistent",
  TRANSACTION_PROPERTY: "transactionProperty",
  TRANSACTION_CONTEXT: "transactionContext",
});

/**
 * Resolve a variableId to its type & namespace among local, persistent, or transaction params.
 *
 * @param {string} variableId
 * @param {Array} localVariableDefinitions
 * @param {Array} persistentVariableDefinitions
 * @param {Array} transactionParameterDefinitions
 * @returns {{ variableType: string, namespace: string } | null}
 */
//;
function getTypeAndNameSpace(
  variableId,
  localVariableDefinitions,
  persistentVariableDefinitions,
  transactionParameterDefinitions,
) {
  const localVariableDefinition = localVariableDefinitions.find(
    (variable) => variable.variableId === variableId,
  );
  if (localVariableDefinition) {
    return {
      variableType: localVariableDefinition.type,
      namespace: Namespaces.PERSISTENT,
    };
  } else {
    const persistentVariableDefinition = persistentVariableDefinitions.find(
      (variableDefinition) => variableDefinition.variableId === variableId,
    );

    if (persistentVariableDefinition) {
      return {
        variableType: persistentVariableDefinition.type,
        namespace: Namespaces.PERSISTENT,
      };
    } else {
      const transactionParameterDefinition =
        transactionParameterDefinitions.find(
          (parameterDefinition) =>
            parameterDefinition.propertyId === variableId,
        );

      if (transactionParameterDefinition) {
        return {
          variableType: transactionParameterDefinition.type,
          namespace: Namespaces.TRANSACTION_PROPERTY,
        };
      } else {
        return null;
      }
    }
  }
}

/**
 * For a property‐assignment update, generate:
 *   const original_<name> = <name>;
 *   <mutation statement>;
 *   propertyChangeLog.addChangedProperty(...)
 *
 * @param {string} ruleId
 * @param {string} propertyName
 * @param {*} value
 * @param {import('@babel/types').ExpressionStatement} mutationStatement
 * @returns {import('@babel/types').Statement[]}
 */
//;
function createPropertyChangeSnippet(
  ruleId,
  propertyName,
  _value,
  mutationStatement,
) {
  // const original_purchaseAmount = purchaseAmount;
  //
  let originalValueName = `original_${propertyName}`;
  const originalPurchaseDeclaration = t.variableDeclaration("const", [
    t.variableDeclarator(
      t.identifier(originalValueName),
      t.identifier(propertyName),
    ),
  ]);

  const addChangedPropertyCall = t.expressionStatement(
    t.callExpression(
      t.memberExpression(
        t.identifier("propertyChangeLog"),
        t.identifier("addChangedProperty"),
      ),
      [
        t.stringLiteral(ruleId),
        t.stringLiteral(propertyName),
        t.identifier(originalValueName),
        t.identifier(propertyName),
      ],
    ),
  );

  return [
    originalPurchaseDeclaration,
    mutationStatement,
    addChangedPropertyCall,
  ];
}

/**
 * Returns {@code true} if the given value is an object with:
 * <ul>
 *   <li>a {@code functionId} property of type {@link String}</li>
 *   <li>an {@code args} property which is an array of {@link String}</li>
 * </ul>
 *
 * @param value the object to inspect
 * @return {@code true} if {@code value} has a {@code functionId} (String) and {@code args} (String[]); {@code false} otherwise
 */
function isFunctionCall(value) {
  return (
    value !== null &&
    typeof value === "object" &&
    !Array.isArray(value) &&
    typeof value.functionId === "string" &&
    Array.isArray(value.args) &&
    value.args.every(
      (arg) =>
        typeof arg === "string" ||
        typeof arg === "number" ||
        typeof arg === "boolean",
    )
  );
}

/**
 * Returns {@code true} if the given value is an object with:
 * <ul>
 *  <li>a {@code listId} property of type {@link String}</li>
 * </ul>
 */
function isStringListDefinition(value) {

  return value !== null && isJson(value) && isListDefinition(JSON.parse(value));
}

function isListDefinition(value) {
  if (value.listId) return true;
  else return false;
}

/**
 * Convert a JSON function descriptor into a Babel function CallExpression.
 *
 * @param {Object} descriptor
 * @param {string} descriptor.functionId – the name of the function to call
 * @param {string[]} descriptor.args – arguments; strings in `{…}` become identifiers
 * @returns {import('@babel/types').CallExpression}
 */
export function createFunctionCall({ functionId, args }) {
  // Build the callee identifier, e.g. formatDate
  const callee = t.identifier(functionId);

  // Map each arg: {foo} → identifier foo, otherwise string literal
  const callArgs = args.map((arg) => {
    if (typeof arg === "string" && arg.startsWith("{") && arg.endsWith("}")) {
      const name = arg.slice(1, -1);
      return t.identifier(name);
    }
    return t.stringLiteral(arg);
  });

  // Return the CallExpression node
  return t.callExpression(callee, callArgs);
}

/**
 * Turn an array of { variableId, assignmentTypeId, value } into AST assignments.
 * Honors namespace:
 *   - LOCAL   ⇒ direct mutation
 *   - PERSISTENT ⇒ mutate + hset update
 *   - TRANSACTION_PROPERTY ⇒ propertyChangeLog
 *
 * @param {Array} localVariableDefinitions
 * @param {Array} variableAssignmentDefinitions
 * @param {Array} persistentVariableDefinitions
 * @param {Array} transactionParameterDefinitions
 * @param {string} ruleId
 * @returns {Array<import('@babel/types').Statement>}
 */
//;
function createVariableAssignments(
  localVariableDefinitions,
  variableAssignmentDefinitions,
  persistentVariableDefinitions,
  transactionParameterDefinitions,
  ruleId,
) {
  return variableAssignmentDefinitions
    .map((variableAssignment) => {
      let { variableId, assignmentTypeId, value } = variableAssignment;

      let typeAndNamespace = getTypeAndNameSpace(
        variableId,
        localVariableDefinitions,
        persistentVariableDefinitions,
        transactionParameterDefinitions,
      );

      if (!typeAndNamespace) {
        throw new Error(
          `Variable ${variableId} not found in local or persistent variable definitions`,
        );
      }

      let { variableType, namespace } = typeAndNamespace;

      // get var type here
      const leftParameter = t.identifier(variableId);
      let rightParameter;

      let rhsVariableId;
      const righHandSideIsVariable = isVariable(value);

      if (righHandSideIsVariable) {
        rhsVariableId = value.slice(1, -1);
        rightParameter = t.identifier(rhsVariableId);
      } else if (isFunctionCall(value)) {
        rightParameter = createFunctionCall(value); //
      } else {
        if (variableType === "number") {
          rightParameter = t.numericLiteral(value);
        } else if (variableType === "boolean") {
          rightParameter = t.booleanLiteral(value);
        } else {
          rightParameter = t.stringLiteral(value);
        }
      }

      const identifiers = [leftParameter, rightParameter];

      if (
        (variableType === "boolean" ||
          variableType === "enum" ||
          variableType === "date" ||
          variableType === "string") &&
        assignmentTypeId !== "SET"
      ) {
        throw new Error(
          `${assignmentTypeId} is not implemented for Type ${variableType}`,
        );
      }

      let valueMutationStatement;

      switch (assignmentTypeId) {
        case "SET":
          if (isVariable(value)) {
            valueMutationStatement = t.expressionStatement(
              t.assignmentExpression("=", identifiers[0], identifiers[1]),
            );
          } else if (
            value.type === "string" &&
            value.includes("{") &&
            value.includes("}")
          ) {
            valueMutationStatement = createTemplateAssignment(
              value,
              identifiers[0].variableId,
            );
          } else {
            valueMutationStatement = t.expressionStatement(
              t.assignmentExpression("=", identifiers[0], identifiers[1]),
            );
          }
          break;
        case "ADD":
          valueMutationStatement = t.expressionStatement(
            t.assignmentExpression("+=", identifiers[0], identifiers[1]),
          );
          break;
        case "SUBTRACT":
          valueMutationStatement = t.expressionStatement(
            t.assignmentExpression("-=", identifiers[0], identifiers[1]),
          );
          break;
        case "MULTIPLY":
          valueMutationStatement = t.expressionStatement(
            t.assignmentExpression("*=", identifiers[0], identifiers[1]),
          );
          break;
        case "DIVIDE":
          valueMutationStatement = t.expressionStatement(
            t.assignmentExpression("/=", identifiers[0], identifiers[1]),
          );
          break;
        case "DECREASE_BY_PERCENTAGE":
          // newValue = oldValue * (1 - percent)
          valueMutationStatement = t.expressionStatement(
            t.assignmentExpression(
              "*=",
              identifiers[0],
              t.binaryExpression(
                "-", 
                t.numericLiteral(1), 
                t.binaryExpression(
                  "/",
                  identifiers[1],
                  t.numericLiteral(100))),
            ),
          );
          break;
        case "INCREASE_BY_PERCENTAGE":
          // newValue = oldValue * (1 + percent)
          valueMutationStatement = t.expressionStatement(
            t.assignmentExpression(
              "*=",
              identifiers[0],
              t.binaryExpression(
                "+", 
                t.numericLiteral(1), 
                t.binaryExpression(
                  "/",
                  identifiers[1],
                  t.numericLiteral(100))),
            ),
          );
          break;
        default:
          valueMutationStatement = null;
      }

      if (!valueMutationStatement) {
        throw new Error(`Unsupported assignmentTypeId: ${assignmentTypeId}`);
      }

      let variableAssignmentStatements = [];

      switch (namespace) {
        case Namespaces.LOCAL: {
          variableAssignmentStatements = [valueMutationStatement];
          break;
        }
        case Namespaces.PERSISTENT: {
          const updatePersistentVariableCacheDeclaration =
            createPersistentVariableCacheUpdate(variableId);
            const persistentVariableLogPush = createPersistentVariableLogPush(variableId, ruleId);

          variableAssignmentStatements = [
            valueMutationStatement,
            updatePersistentVariableCacheDeclaration,
            persistentVariableLogPush,
          ];
          break;
        }
        case Namespaces.TRANSACTION_PROPERTY: {
          variableAssignmentStatements = createPropertyChangeSnippet(
            ruleId,
            variableId,
            value,
            valueMutationStatement,
          );
          break;
        }
        case Namespaces.TRANSACTION_CONTEXT: {
          let errorMessage =
            "Transaction Context Variables can not be the target of a variable operation";
          throw new Error(errorMessage);
        }
      }
      return variableAssignmentStatements;
    })
    .flat();
}

/**
 * Ensure required transactionData properties exist and match expected types.
 *
 * @param {Array<{ propertyId: string, type: string }>} transactionParameterDefinitions
 * @returns {Array<import('@babel/types').IfStatement>}
 */
//;
function createTransactionContextValidationStatements(
  transactionParameterDefinitions,
) {
  return transactionParameterDefinitions
    .map((param) => {
      const { propertyId, type } = param;

      // Check for presence
      const hasPropertyCall = t.callExpression(
        t.memberExpression(
          t.identifier("transactionData"),
          t.identifier("hasOwnProperty"),
        ),
        [t.stringLiteral(propertyId)],
      );

      const notHasProperty = t.unaryExpression("!", hasPropertyCall);

      const errorMessage = `Missing required transaction data property: ${propertyId}`;
      const throwStatement = t.throwStatement(
        t.newExpression(t.identifier("Error"), [t.stringLiteral(errorMessage)]),
      );

      const presenceCheckIfStatement = t.ifStatement(
        notHasProperty,
        t.blockStatement([throwStatement]),
      );

      // Type validation
      const propertyAccess = t.memberExpression(
        t.identifier("transactionData"),
        t.stringLiteral(propertyId),
        true,
      );

      let typeCheckExpression;
      let expectedType;

      switch (type) {
        case "string":
          expectedType = "string";
          break;
        case "number":
          expectedType = "number";
          break;
        case "boolean":
          expectedType = "boolean";
          break;
        case "array":
          expectedType = "object";
          typeCheckExpression = t.unaryExpression(
            "!",
            t.callExpression(t.identifier("Array.isArray"), [propertyAccess]),
          );
          break;
        case "object":
          expectedType = "object";
          break;
        case "date":
          expectedType = "date";
          // Check if new Date(value) is a valid date
          typeCheckExpression = t.binaryExpression(
            "===",
            t.callExpression(
              t.memberExpression(
                t.newExpression(t.identifier("Date"), [propertyAccess]),
                t.identifier("toString"),
              ),
              [],
            ),

            t.stringLiteral("Invalid Date"),
          );
          break;
        default:
          expectedType = "undefined";
      }

      if (!typeCheckExpression && expectedType) {
        typeCheckExpression = t.binaryExpression(
          "!==",
          t.unaryExpression("typeof", propertyAccess),
          t.stringLiteral(expectedType),
        );
      }

      const typeErrorMessage = `Invalid type for property ${propertyId}: expected ${type}`;

      const typeThrowStatement = t.throwStatement(
        t.newExpression(t.identifier("Error"), [
          t.stringLiteral(typeErrorMessage),
        ]),
      );

      const typeCheckIfStatement = t.ifStatement(
        typeCheckExpression,
        t.blockStatement([typeThrowStatement]),
      );

      return [presenceCheckIfStatement, typeCheckIfStatement];
    })
    .flat();
}

function createPersistentVariableKey(
  collectionMappingKey,
  persistentVariableName,
) {
  return collectionMappingKey + persistentVariableName + ":";
}

function createPersistentVariableCollectionKey(
  rulesetId,
  rulesetVersion,
  _contextId,
  collectionMappingName,
) {
  const collectionKey = `${rulesetId}:${rulesetVersion}:{${rulesetId}_${rulesetVersion}}:${collectionMappingName}:`;
  // const collectionKey = `${rulesetId}:${rulesetVersion}:${collectionMappingName}:`;
  return collectionKey;
}

/**
 * Build a Map of Redis‐key templates for persistentVariables.
 *
 * @param {string} rulesetId
 * @param {number} rulesetVersion
 * @param {string} transactionContextId
 * @param {Array<{ variableId: string, collectionId: string }>} persistentVariableDefinitions
 * @param {Array<{ collectionId: string, keyMapping: { propertyId: string }[] }>} collectionMappings
 * @returns {Array<import('@babel/types').Statement>}
 */
//;
function createPersistentVariableKeys(
  rulesetId,
  rulesetVersion,
  transactionContextId,
  persistentVariableDefinitions,
  collectionMappings,
) {
  if (!persistentVariableDefinitions || !collectionMappings) return [];

  const mapExpression = t.newExpression(t.identifier("Map"), []);

  const persistentKeyVariableDeclaration = t.variableDeclaration("const", [
    t.variableDeclarator(t.identifier("persistentVariableKeys"), mapExpression),
  ]);

  const persistentVariableKeysSetStatements = persistentVariableDefinitions.map(
    (persistentVariableDefinition) => {
      let collectionMapping = collectionMappings.find(
        (mapping) =>
          mapping.collectionId === persistentVariableDefinition.collectionId,
      );

      let collectionMappingKey = createPersistentVariableCollectionKey(
        rulesetId,
        rulesetVersion,
        transactionContextId,
        collectionMapping.collectionId,
      );

      let persistentVariableKey = createPersistentVariableKey(
        collectionMappingKey,
        persistentVariableDefinition.variableId,
      );

      const keyMappings = [collectionMapping.keyMapping];

      let value = keyMappings.reduce((acc, keyMapping) => {
        acc = t.binaryExpression(
          "+",
          acc,
          t.binaryExpression(
            "+",
            t.identifier(keyMapping.propertyId),
            t.stringLiteral(":"),
          ),
        );
        return acc;
      }, t.stringLiteral(persistentVariableKey));

      const key = t.stringLiteral(persistentVariableDefinition.variableId);

      const mapSetExpression = t.callExpression(
        t.memberExpression(
          t.identifier("persistentVariableKeys"),
          t.identifier("set"),
        ),
        [key, value],
      );

      return t.expressionStatement(mapSetExpression);
    },
  );

  return [
    persistentKeyVariableDeclaration,
    ...persistentVariableKeysSetStatements,
  ];
}

/**
 * Create an const journalData AST statement so we can use it in the return statement
 *
 * @param {string} rulesetId
 * @param {number} rulesetVersion
 * @param {Array} transactionParameterDefinitions
 * @param {Array} localVariableDefinitions
 * @param {Array} persistentVariableDefinitions
 * @returns {import('@babel/types').ExpressionStatement}
 */
//;
function createJournalDataStatement(
  rulesetId,
  rulesetVersion,
  transactionParameterDefinitions,
  localVariableDefinitions,
  persistentVariableDefinitions,
  executionTarget,
) {
  return t.variableDeclaration("const", [
    t.variableDeclarator(
      t.identifier("journalData"),
      t.objectExpression([
        t.objectProperty(
          t.identifier("timestamp"),
          t.identifier("timestamp"),
        ),
        t.objectProperty(
          t.identifier("rulesetId"),
          t.stringLiteral(rulesetId),
        ),
        t.objectProperty(
          t.identifier("rulesetVersion"),
          t.numericLiteral(rulesetVersion),
        ),
        t.objectProperty(
          t.identifier("entityId"),
          t.identifier("callingEntityId"),
        ),
        t.objectProperty(
          t.identifier("rule_phase"),
          t.stringLiteral(executionTarget),
        ),
        t.objectProperty(
          t.identifier("transactionContextId"),
          t.identifier("transactionContextId"),
        ),
        // evaluationRecord:
        t.objectProperty(
          t.identifier("evaluationRecord"),
          t.objectExpression([
            t.objectProperty(
              t.identifier("evaluationParameters"),
              t.objectExpression([
                t.objectProperty(
                  t.identifier("callingEntityId"),
                  t.identifier("callingEntityId"),
                ),
                t.objectProperty(
                  t.identifier("transactionContextId"),
                  t.identifier("transactionContextId"),
                ),
                t.objectProperty(
                  t.identifier("originTransactionId"),
                  t.identifier("originTransactionId"),
                ),
                t.objectProperty(
                  t.identifier("timestamp"),
                  t.identifier("timestamp"),
                ),
              ]),
            ),
            t.objectProperty(
              t.identifier("transactionData"),
              t.objectExpression(
                transactionParameterDefinitions.map((parameterDefinition) => {
                  return t.objectProperty(
                    t.identifier(parameterDefinition.propertyId),
                    t.identifier(parameterDefinition.propertyId),
                  );
                }),
              ),
            ),
            t.objectProperty(
              t.identifier("localVariables"),
              t.objectExpression(
                localVariableDefinitions.map((variableDefinition) => {
                  return t.objectProperty(
                    t.identifier(variableDefinition.variableId),
                    t.identifier(variableDefinition.variableId),
                  );
                }),
              ),
            ),
            t.objectProperty(
              t.identifier("persistentVariables"),
              t.objectExpression(
                persistentVariableDefinitions.map((variableDefinition) => {
                  return t.objectProperty(
                    t.identifier(variableDefinition.variableId),
                    t.identifier(variableDefinition.variableId),
                  );
                }),
              ),
            ),
            t.objectProperty( 
              t.identifier("persistentVariableLogs"),
              t.identifier("persistentVariableLogs"),
            ),
            t.objectProperty(
              t.identifier("actions"),
              t.identifier("actions"),
            ),
            t.objectProperty(
              t.identifier("apiCallResults"),
              t.identifier("apiCallResults"),
            ),
          ]),
        ),
      ]),

    ),
  ]);
}



/**
 * Build the final return statement AST for evaluation calls:
 *   return {
 *     transactionId: originTransactionId,
 *     modifiedProperties: propertyChangeLog.serialize(),
 *     ...(actions && actions.length ? { actions } : {})
 *   };
 *
 * @returns {import('@babel/types').ReturnStatement}
 */
//;
function createOutcomeNotificationReturnStatement() {
  return t.returnStatement(
    t.objectExpression([
      // journalData: journalData
      t.objectProperty(
        t.identifier("journalData"),
        t.identifier("journalData"),
      ),
      t.objectProperty(
        t.identifier("result"),
        t.objectExpression([
          t.objectProperty(
            t.identifier("transactionId"),
            t.identifier("originTransactionId"),
          ),
          t.objectProperty(
            t.identifier("message"),
            t.stringLiteral("Outcome notification received"),
          ),
        ]),
      ),
    ]),
  );
}

/**
 * Build the final return statement AST for evaluation calls:
 *   return {
 *     transactionId: originTransactionId,
 *     modifiedProperties: propertyChangeLog.serialize(),
 *     ...(actions && actions.length ? { actions } : {})
 *   };
 *
 * @returns {import('@babel/types').ReturnStatement}
 */
//;
function createEvaluationReturnStatement() {
  return t.returnStatement(
    t.objectExpression([
      // journalData: journalData
      t.objectProperty(
        t.identifier("journalData"),
        t.identifier("journalData"),
      ),
      t.objectProperty(
        t.identifier("result"),
        t.objectExpression([
          t.objectProperty(
            t.identifier("transactionId"),
            t.identifier("originTransactionId"),
          ),
          t.objectProperty(
            t.identifier("modifiedProperties"),
            t.callExpression(
              t.memberExpression(
                t.identifier("propertyChangeLog"),
                t.identifier("serialize"),
              ),
              [],
            ),
          ),
          // ...(actions && actions.length ? { actions } : {})
          t.spreadElement(
            t.conditionalExpression(
              // Test: actions && actions.length
              t.logicalExpression(
                "&&",
                t.identifier("actions"),
                t.memberExpression(t.identifier("actions"), t.identifier("length")),
              ),
              // Consequent: { actions }
              t.objectExpression([
                t.objectProperty(
                  t.identifier("actions"),
                  t.identifier("actions"),
                  false,
                  true,
                ),
              ]),
              // Alternate: {}
              t.objectExpression([]),
            ),
          ),
        ]),
      ),


    ]),
  );
}

/**
 * Build a complete Babel Program AST for the evaluate(...) function
 * based on a ruleset definition.
 *
 * @param {Object} rulesetJson
 *   Ruleset JSON: { rulesetId, version, evaluationRules, localVariables, ... }
 * @param {string} transactionContextId
 *   Unique ID or falsy: if falsy, generates a stubbed evaluator returning empty.
 * @param {Array<Object>} transactionParameterDefinitions
 *   Definitions of transactionData properties to validate and assign.
 * @param {Array<Object>} callerContext
 *   Logging context passed into the generated function.
 * @returns {import('@babel/types').Program}
 *   A Babel AST Program containing the `evaluate` function declaration.
 */
//;
export function createAst(
  executionTarget,
  rulesetJson,
  transactionContextId,
  transactionParameterDefinitions,
  callerContext,
) {
  let loggingContext = [
    ...callerContext,
    { metodName: "createAst" },
    { transactionContextId },
  ];

  if (!rulesetJson) {
    logger.warn(loggingContext, "Empty ruleset JSON provided");
    return t.program([t.emptyStatement()]);
  }

  const ruleset = rulesetJson;
  const rulesetId = ruleset.rulesetId;
  const rulesetVersion = ruleset.version;

  const transactionContextAssignments = createTransactionContextAssignments(
    normalizeArray(transactionParameterDefinitions),
  );

  const localVariableAssignments = createLocalVariableAssignments(
    normalizeArray(ruleset.localVariables),
  );

  const persistentVariableAssignments = createPersistentVariableAssignments(
    normalizeArray(ruleset.persistentVariables),
  );

  const transactionContextValidationStatements =
    createTransactionContextValidationStatements(
      normalizeArray(transactionParameterDefinitions),
    );

  const journalData = createJournalDataStatement(
    rulesetId,
    rulesetVersion,
    normalizeArray(transactionParameterDefinitions),
    normalizeArray(ruleset.localVariables),
    normalizeArray(ruleset.persistentVariables),
    executionTarget
  );

  let returnStatement;
  if (executionTarget === ExecutionTarget.EVALUATION) {
    returnStatement = createEvaluationReturnStatement();
  } else if (executionTarget === ExecutionTarget.OUTCOME_NOTIFICATION) {
    returnStatement = createOutcomeNotificationReturnStatement();
  } else {
    throw new Error("Invalid execution target");
  }

  if (!transactionContextId) {
    ////////////////
    let evalFunctionDeclaration = t.functionDeclaration(
      t.identifier("evaluate"),
      [
        t.identifier("callingEntityId"),
        t.identifier("transactionContextId"),
        t.identifier("originTransactionId"),
        t.identifier("timestamp"),
        t.identifier("transactionData"),
        t.identifier("status"),
        t.identifier("modificationsApplied"),
        t.identifier("callerContext"),
      ],
      t.blockStatement([returnStatement]),
      false,
      true,
    );

    return evalFunctionDeclaration;
  }

  const rules = normalizeArray(((target) => {
    switch (target) {
      case ExecutionTarget.OUTCOME_NOTIFICATION:
        return ruleset.outcomeRules;
      case ExecutionTarget.EVALUATION:
        return ruleset.evaluationRules;
      default:
        throw new Error("Invalid execution target");
    }
  })(executionTarget));

  const conditionalActionStatements = rules
    .sort((rule1, rule2) => rule1.priority - rule2.priority)
    .flatMap((rule) => {
      const condition = rule.condition;
      const actionDefinitions = rule.actions;
      const ruleId = rule.ruleId;

      let statements = createConditionalStatements(
        condition,
        actionDefinitions,
        normalizeArray(rule.apiCalls),
        normalizeArray(ruleset.localVariables),
        normalizeArray(ruleset.collectionMappings),
        normalizeArray(ruleset.persistentVariables),
        normalizeArray(rule.variableAssignments),
        normalizeArray(transactionParameterDefinitions),
        rulesetId,
        ruleId,
      );

      return statements;
    });

  // Declare the actions array at the beginning of the function
  const actionsArrayIdentifier = t.identifier("actions");
  const actionsArrayDeclaration = t.variableDeclaration("const", [
    t.variableDeclarator(actionsArrayIdentifier, t.arrayExpression([])),
  ]);

  const persistentVariableKeys = createPersistentVariableKeys(
    ruleset.rulesetId,
    ruleset.version,
    transactionContextId,
    normalizeArray(ruleset.persistentVariables),
    normalizeArray(ruleset.collectionMappings),
  );

  let dateConstants = createDateConstants();

  let acquiredLocksDeclaration = t.variableDeclaration("let", [
    t.variableDeclarator(t.identifier("acquiredLock"), t.nullLiteral()),
  ]);

  // Create `persistentVariableKeys.values()` as a member expression
  const valuesCall = t.callExpression(
    t.memberExpression(
      t.identifier("persistentVariableKeys"),
      t.identifier("values"),
    ),
    [],
  );

  // Create `Array.from(...)` as a call expression
  const lockKeysArrayFrom = t.callExpression(
    t.memberExpression(t.identifier("Array"), t.identifier("from")),
    [valuesCall],
  );

  // Create `const lockKeys = Array.from(persistentVariableKeys.values());` as a variable declaration
  const lockKeysDeclaration = t.variableDeclaration("const", [
    t.variableDeclarator(t.identifier("lockKeys"), lockKeysArrayFrom),
  ]);

  // Create `acquireAllLocks(lockKeys)` as a call expression
  const acquireAllLocksCall = t.callExpression(
    t.identifier("acquireAllLocks"),
    [t.identifier("lockKeys"), t.identifier("callerContext")],
  );

  // Create `acquiredLocks = await acquireAllLocks(lockKeys);` as an assignment expression
  const acquiredLocksAssignment = t.expressionStatement(
    t.assignmentExpression(
      "=",
      t.identifier("acquiredLock"),
      t.awaitExpression(acquireAllLocksCall),
    ),
  );

  const lockedPersistentVariablesBlock = t.blockStatement([
    ...persistentVariableKeys,
    lockKeysDeclaration,
    acquiredLocksAssignment,
    ...persistentVariableAssignments,
    ...conditionalActionStatements,
  ]);

  // Create `await unlockAllLocks(acquiredLocks)` as an expression statement
  const unlockAllLocksCall = t.expressionStatement(
    t.callExpression(t.identifier("releaseAllLocks"), [
      t.identifier("acquiredLock"),
      t.identifier("callerContext"),
    ]),
  );

  // Create the body of the `finally` block
  const finallyBody = t.blockStatement([unlockAllLocksCall]);

  const consoleErrorCall = t.expressionStatement(
    t.callExpression(
      t.memberExpression(t.identifier("logger"), t.identifier("error")),
      [t.identifier("loggingContext"), t.identifier("e")],
    ),
  );

  // Create `throw e;` as a throw statement
  const throwStatement = t.throwStatement(t.identifier("e"));

  // Create the body of the `catch` block
  const catchBody = t.blockStatement([consoleErrorCall, throwStatement]);

  // Create the `catch(e)` clause
  const lockedPersistentVariablesCatchClause = t.catchClause(
    t.identifier("e"),
    catchBody,
  );

  let peristentVariableTryCatch = t.tryStatement(
    lockedPersistentVariablesBlock,
    lockedPersistentVariablesCatchClause,
    finallyBody,
  );

  const peristentVariableDeclarations = createPersistentVariableDeclarations(
    normalizeArray(ruleset.persistentVariables),
  );

  const loggingContextDeclaration = t.variableDeclaration("const", [
    t.variableDeclarator(
      t.identifier("loggingContext"),
      t.arrayExpression([
        t.spreadElement(t.identifier("callerContext")),
        t.objectExpression([
          t.objectProperty(
            t.identifier("function"),
            t.stringLiteral("evaluate"),
          ),
        ]),
      ]),
    ),
  ]);

  const propertyChangeLogConstructor = t.newExpression(
    t.identifier("PropertyChangeLog"),
    [t.identifier("originTransactionId"), t.stringLiteral(rulesetId)],
  );

  const propertyChangeLogDeclaration = t.variableDeclaration("const", [
    t.variableDeclarator(
      t.identifier("propertyChangeLog"),
      propertyChangeLogConstructor,
    ),
  ]);

  // 'declaration' now represents:
  // let propertyChangeLog = new PropertyChangeLog(originTransactionId, "AGENT_SALES_TARGET_2024", "ACCUMULATE_SALES");

  const functionBodyStatements = [
    loggingContextDeclaration,
    actionsArrayDeclaration,
    propertyChangeLogDeclaration,
    createApiResponseMapVariableDeclaration(),
    ...dateConstants,
    ...transactionContextValidationStatements,
    ...transactionContextAssignments,
    ...localVariableAssignments,
    acquiredLocksDeclaration,
    ...peristentVariableDeclarations,
    createPersistentVariableLogsDeclaration(),
    peristentVariableTryCatch,
    journalData,
    returnStatement,
  ];

  const mainFunction = t.functionDeclaration(
    t.identifier("evaluate"),
    [
      t.identifier("callingEntityId"),
      t.identifier("transactionContextId"),
      t.identifier("originTransactionId"),
      t.identifier("timestamp"),
      t.identifier("transactionData"),
      t.identifier("status"),
      t.identifier("modificationsApplied"),
      t.identifier("callerContext"),
    ],
    t.blockStatement(functionBodyStatements),
    false,
    true,
  );

  const program = t.program([mainFunction]);

  return program;
}

/**
 * Generate JavaScript source code from a Babel AST.
 *
 * @param {import('@babel/types').Program} ast
 * @param {Array<Object>} loggingContext
 * @returns {string}
 */
//;
export function generateCode(ast, loggingContext) {
  const { code } = generate(ast, loggingContext);
  return code;
}

const collectApiCalls = (ruleset) => {
  let result = [];
  if (Array.isArray(ruleset)) {
    // Traverse arrays
    result = ruleset.flatMap(collectApiCalls);
  } else if (typeof ruleset === "object" && ruleset !== null) {
    // Traverse objects
    for (const key in ruleset) {
      if (key === "apiCalls" && Array.isArray(ruleset[key])) {
        result.push(...ruleset[key]);
      } else {
        result.push(...collectApiCalls(ruleset[key]));
      }
    }
  }
  return result;
};

export const ExecutionTarget = Object.freeze({
  OUTCOME_NOTIFICATION: "OUTCOME_NOTIFICATION",
  EVALUATION: "EVALUATION",
});

export async function executeRulesetEvaluation(
  ruleset,
  callingEntityId,
  transactionContextParameterDefinitions,
  transactionContextId,
  originTransactionId,
  timestamp,
  transactionData,
  redisClient,
  persistentVariablesLock,
  callerContext,
) {
  return await executeRuleset(
    ExecutionTarget.EVALUATION,
    ruleset,
    callingEntityId,
    transactionContextParameterDefinitions,
    transactionContextId,
    originTransactionId,
    timestamp,
    transactionData,
    null,
    null,
    redisClient,
    persistentVariablesLock,
    callerContext,
  );
}

export async function executeRulesetOutcomeNotification(
  ruleset,
  callingEntityId,
  transactionContextParameterDefinitions,
  transactionContextId,
  originTransactionId,
  timestamp,
  transactionData,
  status,
  modificationsApplied,
  redisClient,
  persistentVariablesLock,
  callerContext,
) {
  

  return await executeRuleset(
    ExecutionTarget.OUTCOME_NOTIFICATION,
    ruleset,
    callingEntityId,
    transactionContextParameterDefinitions,
    transactionContextId,
    originTransactionId,
    timestamp,
    transactionData,
    status,
    modificationsApplied,
    redisClient,
    persistentVariablesLock,
    callerContext,
  );
}

/**
 * 
 * @param {*} executionTarget 
 * @param {*} ruleset 
 * @param {*} callingEntityId 
 * @param {*} transactionContextParameterDefinitions 
 * @param {*} transactionContextId 
 * @param {*} originTransactionId 
 * @param {*} timestamp 
 * @param {*} transactionData 
 * @param {*} status 
 * @param {*} modificationsApplied 
 * @param {*} redisClient 
 * @param {*} persistentVariablesLock 
 * @param {*} callerContext 
 * @returns {Promise<{result: any, journalData: any}>}
 */
async function executeRuleset(
  executionTarget,
  ruleset,
  callingEntityId,
  transactionContextParameterDefinitions,
  transactionContextId,
  originTransactionId,
  timestamp,
  transactionData,
  status,
  modificationsApplied,
  redisClient,
  persistentVariablesLock,
  callerContext,
) {
  const loggingContext = [
    ...callerContext,
    { rulesetId: ruleset.rulesetId },
    { callingEntityId },
    { transactionContextId },
    { originTransactionId },
  ];

  logger.info(loggingContext, "Evaluating Ruleset");

  const { rulesetId, rulesetVersion } = ruleset;

  const cacheKey = `${rulesetId}.${rulesetVersion}.${executionTarget}.${transactionContextId}`;


  if (!codeCache.has(cacheKey)) {
    logger.info(loggingContext, "Generating Ruleset code");

    const rulesetAst = createAst(
      executionTarget,
      ruleset,
      transactionContextId,
      transactionContextParameterDefinitions,
      loggingContext,
    );

    const code = generateCode(rulesetAst, loggingContext);

    if (!code) {
      logger.warn(
        loggingContext,
        `No ruleset code generated for rulesetId: ${rulesetId} `,
        loggingContext,
      );
      return [];
    }

    const helperFunctions = createHelperFunctions();

    const moduleCode = `${helperFunctions}\n${code}\nmodule.exports = { evaluate }`;

    logger.trace(loggingContext, moduleCode);

    //console.log("==================================================================================");
    //console.log("==================================================================================");
    //logWithLineNumbers(moduleCode);
    //console.log("==================================================================================");
    //console.log("==================================================================================");

    // Compile the script once and cache it
    let compiledScript = new vm.Script(moduleCode);

    // Create a context and run the compiled script to get the evaluate function
    const require = (moduleName) => {
      return createRequire(import.meta.url)(moduleName); // Default behavior
    };

    let apiCalls = collectApiCalls(ruleset);
    let apiCallTokens;
    try {
      apiCallTokens = await Promise.all(
        await apiCalls.map(async (apiCall) => {
          let apiToken = await getEcdsApiToken(
            apiCall.authentication.apiUsername,
            apiCall.authentication.apiSecret,
            apiCall.authentication.crediverseUsername,
            apiCall.authentication.crediversePassword,
          );
          return [apiCall.name, apiToken];
        }),
      );
    } catch (error) {
      const errorMessage = `Error during api token generation: ${error.message}`;
      logger.error(loggingContext, errorMessage);
      throw new Error(error);
    }

    const apiCallTokenMap = new Map(apiCallTokens);

    // Create stockApiService instance for the execution context
    const stockApiService = new StockApiService(redisClient, loggingContext);

    const codeContext = {
      require,
      console,
      process,
      fetch,
      apiCallTokenMap,
      redisClient,
      persistentVariablesLock,
      stockApiService,
      logger,
      module: {},
      exports: {},
    };
    vm.createContext(codeContext);

    compiledScript.runInContext(codeContext);
    let evaluateFunctionCache = codeContext.module.exports.evaluate;

    codeCache.set(cacheKey, evaluateFunctionCache);
  }

  try {
    let callResponse = await codeCache.get(cacheKey)(
      callingEntityId,
      transactionContextId,
      originTransactionId,
      new Date(timestamp),
      transactionData,
      status,
      modificationsApplied,
      loggingContext,
    );

    // Async insert of analytics data into Redis and logging
    if (callResponse.journalData) {
      logger.info([...loggingContext, { journalData: callResponse.journalData }], "Journal data received from ruleset evaluation");
      //console.log("==============================");
      //console.log("==============================");
      //console.log("==============================");
      //console.log({journalData:JSON.stringify(callResponse.journalData,null,2)});
      //console.log("==============================");
      //console.log("==============================");

      const analyticsService = new AnalyticsService(redisClient, callerContext);
      analyticsService.insertAnalyticsData(callResponse.journalData, ruleset);
    }

    return callResponse;
  } catch (error) {
    logger.error(loggingContext, error.message);
    throw new Error(error);
  }
}