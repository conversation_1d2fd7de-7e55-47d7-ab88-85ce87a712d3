import { Router } from "express";
import ListManagementController from "../controllers/listManagementController.js";
import createUploadMiddleware from "../middleware/uploadFileMiddleware.js";
import {
  jwtTokenAuthMiddleware,
  nonApiUserAuthMiddleware,
} from "../middleware/authMiddleware.js";

import { listValidation } from "../validations/listManagementValidation.js";

import { logger } from "../middleware/logger.js";

export default class ListManagementRouter {
  routes() {
    return this.router;
  }

  constructor() {
    this.componentName = "ListManagementRouter";
    this.componentContext = [{ componentName: this.componentName }];

    const loggingContext = [
      { componentName: this.componentName },
      { methodName: "constructor" },
    ];

    logger.info(loggingContext, "Creating ListManagementRouter");

    try {
      this.router = Router();

      this.uploadMiddleware = createUploadMiddleware(
        "text/csv",
        5 * 1024 * 1024
      ); // 5MB limit

      logger.info(loggingContext, "Creating new listManagementController");

      const controller = new ListManagementController(loggingContext);

      logger.info(loggingContext, "adding list management routes");

      // Register GET route for fetching lists
      this.router.get("/", jwtTokenAuthMiddleware, controller.getAllLists);
      // Register GET route for fetching a list by ID
      this.router.get("/:id", jwtTokenAuthMiddleware, controller.getListById);

      // Register GET route for downloading a list by ID
      this.router.get(
        "/:id/download",
        jwtTokenAuthMiddleware,
        controller.downloadListById
      );

      // Register delete route for deleting a list by ID
      this.router.delete(
        "/:id",
        nonApiUserAuthMiddleware,
        controller.deleteListById
      );

      // Register POST route for creating a list (with validation)
      this.router.post(
        "/",
        nonApiUserAuthMiddleware,
        this.uploadMiddleware,
        listValidation,
        controller.createList
      );

      logger.info(loggingContext, "Created ListManagementRouter");
    } catch (error) {
      logger.error(loggingContext, error);
      throw error;
    }
  }
}
