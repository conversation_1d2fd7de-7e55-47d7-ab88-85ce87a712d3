import express from "express";
import WebhookController from "../controllers/webhookController.js";
import { jwtTokenAuthMiddleware, nonApiUserAuthMiddleware } from "../middleware/authMiddleware.js";
import { logger } from "../middleware/logger.js";
import { validateGetWebhooksQuery, validateWebhook } from "../validations/webhookValidation.js";

export default class WebhookRoutes {
  constructor() {
    this.componentName = "WebhookRoutes";
    this.componentContext = [{ componentName: this.componentName }];

    this.router = express.Router();
    this.controller = new WebhookController();

    this.router.post(
      "/",
      jwtTokenAuthMiddleware,
      validateWebhook,
      this.controller.registerWebhook,
    );

    this.router.get(
      "/",
      jwtTokenAuthMiddleware,
      validateGetWebhooksQuery,
      this.controller.getWebhooks,
    );

    this.router.get(
      "/:webhookId",
      jwtTokenAuthMiddleware,
      this.controller.getWebhookById,
    );

    this.router.put(
      "/:webhookId",
      jwtTokenAuthMiddleware,
      validateWebhook,
      this.controller.updateWebhook,
    );

    this.router.delete(
      "/:webhookId",
      jwtTokenAuthMiddleware,
      this.controller.deleteWebhook,
    );
    this.router.post(
      "/:webhookId/test",
      jwtTokenAuthMiddleware,
      this.controller.testWebhook,
    );

    logger.info(
      [...this.componentContext, { methodName: "constructor" }],
      "Created WebhookRouter",
    );
  }

  routes() {
    return this.router;
  }
}
