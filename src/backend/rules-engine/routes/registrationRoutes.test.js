import axios from "axios";
import <PERSON>ck<PERSON><PERSON>pter from "axios-mock-adapter";
import { describe, it, expect, afterEach } from "@jest/globals";

// Import the function to be tested (assuming it's named registerEntity)
const registerEntity = async (entity) => {
  // Basic validation
  if (!entity.entityId || !entity.entityName) {
    throw new Error("Invalid entity structure");
  }

  try {
    const response = await axios.post("/api/v1/entities", entity);
    return response.data;
  } catch (error) {
    if (error.response) {
      throw new Error(error.response.data.error || "API Error");
    }
    throw error;
  }
};
// Create a mock for axios
const mock = new MockAdapter(axios);

describe("Entity Registration", () => {
  afterEach(() => {
    mock.reset();
  });


  it("successfully registers a valid entity", async () => {
    const validEntity = {
      entityId: "RETAIL_POS",
      entityName: "Retail Point of Sale System",
      entityDescription: "Handles in-store purchases and customer interactions",
      globalActions: [
        {
          actionId: "SEND_SMS",
          name: "Send SMS",
          description: "Sends an SMS to the customer",
          parameters: [
            {
              name: "phoneNumber",
              type: "string",
              description: "The recipient's phone number",
            },
            {
              name: "message",
              type: "string",
              description: "The message content",
            },
          ],
        },
      ],
      transactionContexts: [
        {
          contextId: "PURCHASE",
          contextName: "Product Purchase",
          contextDescription: "Represents a customer purchasing products",
          properties: [
            {
              name: "customerId",
              type: "string",
              description: "Unique identifier for the customer",
            },
            {
              name: "totalAmount",
              type: "number",
              description: "The total purchase amount",
            },
            {
              name: "productCount",
              type: "number",
              description: "Number of products in the purchase",
            },
            {
              name: "customerType",
              type: "string",
              description: "Type of customer",
              enumValues: ["NEW", "REGULAR", "VIP"],
            },
          ],
          contextSpecificActions: [
            {
              actionId: "APPLY_DISCOUNT",
              name: "Apply Discount",
              description: "Applies a discount to the purchase",
              parameters: [
                {
                  name: "discountPercentage",
                  type: "number",
                  description: "Percentage of discount to apply",
                },
              ],
            },
          ],
        },
      ],
    };

    // Mock the API response
    mock
      .onPost("/api/v1/entities")
      .reply(201, { message: "Entity registered successfully" });

    // Call the function and expect it to resolve
    await expect(registerEntity(validEntity)).resolves.toEqual({
      message: "Entity registered successfully",
    });

    // Check if the correct request was made
    expect(mock.history.post.length).toBe(1);
    expect(mock.history.post[0].url).toBe("/api/v1/entities");
    expect(JSON.parse(mock.history.post[0].data)).toEqual(validEntity);
  });

  it("handles API errors during registration", async () => {
    const invalidEntity = {
      /* ... */
    }; // Define an invalid entity object

    // Mock the API response for an error
    mock
      .onPost("/api/v1/entities")
      .reply(400, { error: "Invalid entity structure" });

    // Call the function and expect it to reject with the error
    await expect(registerEntity(invalidEntity)).rejects.toThrow(
      "Invalid entity structure",
    );
  });

  it("validates entity structure before sending", async () => {
    const incompleteEntity = {
      entityId: "INCOMPLETE",
      // Missing required fields
    };

    // We expect the function to throw an error before making the API call
    await expect(registerEntity(incompleteEntity)).rejects.toThrow(
      "Invalid entity structure",
    );

    // Ensure no API call was made
    expect(mock.history.post.length).toBe(0);
  });

  it("handles network errors", async () => {
    const validEntity = {
      entityId: "RETAIL_POS",
      entityName: "Retail Point of Sale System",
      entityDescription: "Handles in-store purchases and customer interactions",
      globalActions: [
        {
          actionId: "SEND_SMS",
          name: "Send SMS",
          description: "Sends an SMS to the customer",
          parameters: [
            {
              name: "phoneNumber",
              type: "string",
              description: "The recipient's phone number",
            },
            {
              name: "message",
              type: "string",
              description: "The message content",
            },
          ],
        },
      ],
      transactionContexts: [
        {
          contextId: "PURCHASE",
          contextName: "Product Purchase",
          contextDescription: "Represents a customer purchasing products",
          properties: [
            {
              name: "customerId",
              type: "string",
              description: "Unique identifier for the customer",
            },
            {
              name: "totalAmount",
              type: "number",
              description: "The total purchase amount",
            },
            {
              name: "productCount",
              type: "number",
              description: "Number of products in the purchase",
            },
            {
              name: "customerType",
              type: "string",
              description: "Type of customer",
              enumValues: ["NEW", "REGULAR", "VIP"],
            },
          ],
          contextSpecificActions: [
            {
              actionId: "APPLY_DISCOUNT",
              name: "Apply Discount",
              description: "Applies a discount to the purchase",
              parameters: [
                {
                  name: "discountPercentage",
                  type: "number",
                  description: "Percentage of discount to apply",
                },
              ],
            },
          ],
        },
      ],
    };

    // Simulate a network error
    mock.onPost("/api/v1/entities").networkError();

    // Call the function and expect it to reject with a network error
    await expect(registerEntity(validEntity)).rejects.toThrow("Network Error");
  });
});
