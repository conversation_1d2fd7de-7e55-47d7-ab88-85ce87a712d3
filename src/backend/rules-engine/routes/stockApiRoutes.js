import express from "express";
import stockApiController from "../controllers/stockApiController.js";
import { body, param, query, validationResult } from "express-validator";

const router = express.Router();

/**
 * Middleware to handle validation errors
 */
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: "Validation failed",
      details: errors.array(),
      requestId: req.headers["x-request-id"] || "unknown"
    });
  }
  next();
};

/**
 * @route POST /api/stock/register
 * @desc Register a new stock API
 * @access Public (should be protected in production)
 */
router.post(
  "/register",
  [
    body("apiId")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("apiId must be a string between 1 and 100 characters"),
    body("name")
      .isString()
      .isLength({ min: 1, max: 200 })
      .withMessage("name must be a string between 1 and 200 characters"),
    body("type")
      .isIn(["stockTransfer", "stockBalance", "stockHistory"])
      .withMessage("type must be one of: stockTransfer, stockBalance, stockHistory"),
    body("provider")
      .isString()
      .isLength({ min: 1, max: 50 })
      .withMessage("provider must be a string between 1 and 50 characters"),
    body("endpoints")
      .isObject()
      .withMessage("endpoints must be an object"),
    body("authentication")
      .isObject()
      .withMessage("authentication must be an object"),
    body("parameters")
      .isArray()
      .withMessage("parameters must be an array")
  ],
  handleValidationErrors,
  stockApiController.registerApi
);

/**
 * @route GET /api/stock/apis
 * @desc Get all registered stock APIs
 * @access Public (should be protected in production)
 */
router.get("/apis", stockApiController.getApis);

/**
 * @route GET /api/stock/apis/:apiId
 * @desc Get specific stock API by ID
 * @access Public (should be protected in production)
 */
router.get(
  "/apis/:apiId",
  [
    param("apiId")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("apiId must be a string between 1 and 100 characters")
  ],
  handleValidationErrors,
  stockApiController.getApiById
);

/**
 * @route POST /api/stock/credentials
 * @desc Store credentials for a ruleset
 * @access Public (should be protected in production)
 */
router.post(
  "/credentials",
  [
    body("rulesetId")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("rulesetId must be a string between 1 and 100 characters"),
    body("accountRef")
      .isString()
      .isLength({ min: 1, max: 50 })
      .withMessage("accountRef must be a string between 1 and 50 characters"),
    body("credentials")
      .isObject()
      .withMessage("credentials must be an object"),
    body("credentials.apiUsername")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("credentials.apiUsername must be a string between 1 and 100 characters"),
    body("credentials.apiSecret")
      .isString()
      .isLength({ min: 1, max: 200 })
      .withMessage("credentials.apiSecret must be a string between 1 and 200 characters"),
    body("credentials.accountUsername")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("credentials.accountUsername must be a string between 1 and 100 characters"),
    body("credentials.accountPassword")
      .isString()
      .isLength({ min: 1, max: 200 })
      .withMessage("credentials.accountPassword must be a string between 1 and 200 characters"),
    body("credentials.provider")
      .optional()
      .isString()
      .isLength({ min: 1, max: 50 })
      .withMessage("credentials.provider must be a string between 1 and 50 characters")
  ],
  handleValidationErrors,
  stockApiController.storeCredentials
);

/**
 * @route GET /api/stock/credentials/:rulesetId
 * @desc Get masked credentials for a ruleset
 * @access Public (should be protected in production)
 */
router.get(
  "/credentials/:rulesetId",
  [
    param("rulesetId")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("rulesetId must be a string between 1 and 100 characters"),
    query("accountRef")
      .optional()
      .isString()
      .isLength({ min: 1, max: 50 })
      .withMessage("accountRef must be a string between 1 and 50 characters")
  ],
  handleValidationErrors,
  stockApiController.getCredentials
);

/**
 * @route POST /api/stock/transfer
 * @desc Execute a stock transfer
 * @access Public (should be protected in production)
 */
router.post(
  "/transfer",
  [
    body("fromAccount")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("fromAccount must be a string between 1 and 100 characters"),
    body("toAccount")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("toAccount must be a string between 1 and 100 characters"),
    body("amount")
      .isNumeric()
      .isFloat({ min: 0.01 })
      .withMessage("amount must be a positive number"),
    body("apiId")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("apiId must be a string between 1 and 100 characters"),
    body("rulesetId")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("rulesetId must be a string between 1 and 100 characters"),
    body("accountRef")
      .optional()
      .isString()
      .isLength({ min: 1, max: 50 })
      .withMessage("accountRef must be a string between 1 and 50 characters"),
    body("stockType")
      .optional()
      .isString()
      .isLength({ min: 1, max: 50 })
      .withMessage("stockType must be a string between 1 and 50 characters")
  ],
  handleValidationErrors,
  stockApiController.transferStock
);

/**
 * @route PUT /api/stock/apis/:apiId
 * @desc Update stock API definition
 * @access Public (should be protected in production)
 */
router.put(
  "/apis/:apiId",
  [
    param("apiId")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("apiId must be a string between 1 and 100 characters"),
    body("name")
      .optional()
      .isString()
      .isLength({ min: 1, max: 200 })
      .withMessage("name must be a string between 1 and 200 characters"),
    body("type")
      .optional()
      .isIn(["stockTransfer", "stockBalance", "stockHistory"])
      .withMessage("type must be one of: stockTransfer, stockBalance, stockHistory"),
    body("provider")
      .optional()
      .isString()
      .isLength({ min: 1, max: 50 })
      .withMessage("provider must be a string between 1 and 50 characters"),
    body("endpoints")
      .optional()
      .isObject()
      .withMessage("endpoints must be an object"),
    body("authentication")
      .optional()
      .isObject()
      .withMessage("authentication must be an object"),
    body("parameters")
      .optional()
      .isArray()
      .withMessage("parameters must be an array")
  ],
  handleValidationErrors,
  async (req, res) => {
    // TODO: Implement update functionality
    res.status(501).json({
      error: "Update functionality not yet implemented",
      requestId: req.headers["x-request-id"] || "unknown"
    });
  }
);

/**
 * @route DELETE /api/stock/apis/:apiId
 * @desc Delete stock API definition
 * @access Public (should be protected in production)
 */
router.delete(
  "/apis/:apiId",
  [
    param("apiId")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("apiId must be a string between 1 and 100 characters")
  ],
  handleValidationErrors,
  async (req, res) => {
    // TODO: Implement delete functionality
    res.status(501).json({
      error: "Delete functionality not yet implemented",
      requestId: req.headers["x-request-id"] || "unknown"
    });
  }
);

/**
 * @route DELETE /api/stock/credentials/:rulesetId
 * @desc Delete credentials for a ruleset
 * @access Public (should be protected in production)
 */
router.delete(
  "/credentials/:rulesetId",
  [
    param("rulesetId")
      .isString()
      .isLength({ min: 1, max: 100 })
      .withMessage("rulesetId must be a string between 1 and 100 characters"),
    query("accountRef")
      .optional()
      .isString()
      .isLength({ min: 1, max: 50 })
      .withMessage("accountRef must be a string between 1 and 50 characters")
  ],
  handleValidationErrors,
  async (req, res) => {
    // TODO: Implement delete credentials functionality
    res.status(501).json({
      error: "Delete credentials functionality not yet implemented",
      requestId: req.headers["x-request-id"] || "unknown"
    });
  }
);

export default router;
