import { Router } from "express";
import createUploadMiddleware from "../middleware/uploadFileMiddleware.js";
import {
  jwtTokenAuthMiddleware,
  nonApiUserAuthMiddleware,
} from "../middleware/authMiddleware.js";
import BulkNotificationController from "../controllers/bulkNotificationController.js";
import { bulkNotificationValidationsMap } from "../validations/bulkNotificationValidation.js";

export default class BulkNotificationRouter {
  constructor() {
    this.router = Router();

    // Get the upload middleware for the csv file
    this.uploadMiddleware = createUploadMiddleware("text/csv", 5 * 1024 * 1024); // 5MB limit

    // Initiate the controller
    this.controller = new BulkNotificationController();

    // Get the validation middleware
    this.validateList = bulkNotificationValidationsMap;

    this.router.get("/", jwtTokenAuthMiddleware, this.controller.getAllLists);

    this.router.post(
      "/",
      nonApiUserAuthMiddleware,
      this.uploadMiddleware,
      this.validateList.CREATE_LIST,
      this.controller.createList,
    );

    this.router.delete(
      "/:id/",
      nonApiUserAuthMiddleware,
      this.controller.deleteListById,
    );

    this.router.get(
      "/:id/download",
      jwtTokenAuthMiddleware,
      this.controller.downloadListById,
    );

    this.router.get(
      "/:id/sendsms",
      jwtTokenAuthMiddleware,
      this.controller.sendBulkSms,
    );
  }
  routes() {
    return this.router;
  }
}
