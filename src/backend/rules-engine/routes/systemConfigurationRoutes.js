import express from "express";
import { jwtTokenAuthMiddleware } from "../middleware/authMiddleware.js";
import { logger } from "../middleware/logger.js";
import SystemConfigurationController from "../controllers/systemConfigurationController.js";

export default class SystemConfigurationRoutes {
    constructor() {
        this.componentName = "SystemConfigurationRoutes";
        this.componentContext = [{ componentName: this.componentName }]; 

        this.router = express.Router();
        this.controller = new SystemConfigurationController();

        /**
         * This route will get all condition types
         */
        this.router.get(
            "/conditionTypes",
            jwtTokenAuthMiddleware,
            this.controller.conditionTypes,
        );


        /**
         * This route will get all variable assignments
         */
        this.router.get(
            "/variableAssignments",
            jwtTokenAuthMiddleware,
            this.controller.variableAssignments,
        );


        /**
         * This route will get all functions
         */
        this.router.get(
            "/functions",
            jwtTokenAuthMiddleware,
            this.controller.functions,
        );


        /**
         * This route will get all entities
         */
        this.router.get(
            "/entities",
            jwtTokenAuthMiddleware,
            this.controller.entities,
        );

        /**
         * This route will get all standard properties
         */
        this.router.get(
            "/standardProperties",
            jwtTokenAuthMiddleware,
            this.controller.standardProperties,
        );
        
        
        /**
         * This route will get all config schema
         */
        this.router.get(
            "/config-schema",
            jwtTokenAuthMiddleware,
            this.controller.configSchema,
        );

        logger.info(
            [...this.componentContext, { methodName: "constructor" }],
            "Created WebhookRouter",
        );
    }

    routes() {
        return this.router;
    }
}
