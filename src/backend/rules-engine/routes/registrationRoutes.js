import express from "express";
import RegistrationController from "../controllers/registrationController.js";
import { validateRegistration } from "../validations/registrationValidation.js";
import { logger } from "../middleware/logger.js";

import {
  apiTokenAuthMiddleware,
  jwtTokenAuthMiddleware,
} from "../middleware/authMiddleware.js";

export default class RegistrationRouter {
  routes() {
    return this.router;
  }
  constructor() {
    this.router = express.Router();
    this.controller = new RegistrationController();
    this.componentName = "RegistrationRouter";
    this.componentContext = [{ componentName: this.componentName }];

    this.router.post(
      "/",
      apiTokenAuthMiddleware,
      validateRegistration,
      this.controller.registerEntity,
    );
    this.router.get("/", jwtTokenAuthMiddleware, this.controller.getEntities);
    this.router.get(
      "/:entityId",
      jwtTokenAuthMiddleware,
      this.controller.getEntity,
    );
    this.router.put(
      "/:entityId",
      jwtTokenAuthMiddleware,
      validateRegistration,
      this.controller.updateEntity,
    );

    this.router.delete(
      "/:entityId",
      jwtTokenAuthMiddleware,
      this.controller.deleteEntity,
    );

    logger.info(
      [{ componentName: this.componentName }, { methodName: "constructor" }],
      "Created RegistrationRouter",
    );
  }
}
