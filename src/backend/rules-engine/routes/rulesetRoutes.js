import { validateRuleset } from "../validations/rulesetValidation.js";

import express from "express";

import RulesetController from "../controllers/rulesetController.js";

import {
  jwtTokenAuthMiddleware,
  nonApiUserAuthMiddleware,
} from "../middleware/authMiddleware.js";

import { logger } from "../middleware/logger.js";

export default class RulesetRouter {
  routes() {
    return this.router;
  }

  constructor() {
    this.componentName = "RulesetRouter";
    this.componentContext = [{ componentName: this.componentName }];

    this.router = express.Router();
    this.controller = new RulesetController();

    this.router.post(
      "/",
      nonApiUserAuthMiddleware,
      validateRuleset,
      this.controller.saveRuleset,
    );
    this.router.get("/", jwtTokenAuthMiddleware, this.controller.getRulesets);
    this.router.get(
      "/:rulesetId",
      jwtTokenAuthMiddleware,
      this.controller.getRuleset,
    ); // Get a specific ruleset by ID
    this.router.delete(
      "/:rulesetId",
      nonApiUserAuthMiddleware,
      this.controller.deleteRuleset,
    );

    this.router.post(
      "/:rulesetId/:version/activate",
      nonApiUserAuthMiddleware,
      this.controller.activateRuleset,
    );

    this.router.post(
      "/:rulesetId/:version/deactivate",
      nonApiUserAuthMiddleware,
      this.controller.deactivateRuleset,
    );

    this.router.post(
      "/:existingRulesetId/:existingVersion/copy/:newRulesetId",
      nonApiUserAuthMiddleware,
      this.controller.copyRuleset,
    );
    logger.info(
      [...this.componentContext, { methodName: "constructor" }],
      "Created RulesetRouter",
    );
  }
}
