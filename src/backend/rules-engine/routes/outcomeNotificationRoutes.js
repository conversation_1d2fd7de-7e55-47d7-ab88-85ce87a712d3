import OutcomeNotificationController from "../controllers/outcomeNotificationController.js";

import { Router } from "express";
import { validateOutcomeNotification } from "../validations/outcomeNotificationValidation.js";

import { apiTokenAuthMiddleware } from "../middleware/authMiddleware.js";

import { logger } from "../middleware/logger.js";

export default class OutcomeNotificationRoutes {
  routes() {
    return this.router;
  }
  constructor() {
    this.componentName = "OutcomeNotificationRoutes";
    this.componentContext = [{ componentName: this.componentName }];

    this.router = Router();
    this.controller = new OutcomeNotificationController();
    this.router.post(
      "/",
      apiTokenAuthMiddleware,
      validateOutcomeNotification,
      this.controller.processOutcomeNotification,
    );

    logger.info(
      [...this.componentContext, { methodName: "constructor" }],
      "Created OutcomeNotificationRoutes",
    );
  }
}
