import { Router } from "express";
import { validateEvaluation } from "../validations/evaluationValidation.js";
import EvaluationController from "../controllers/evaluationController.js";

import { apiTokenAuthMiddleware } from "../middleware/authMiddleware.js";

import { logger } from "../middleware/logger.js";

export default class EvaluationRouter {
  routes() {
    return this.router;
  }
  constructor() {
    this.componentName = "EvaluationRouter";
    this.componentContext = [{ componentName: this.componentName }];

    this.router = Router();
    this.controller = new EvaluationController();
    this.router.post(
      "/",
      apiTokenAuthMiddleware,
      validateEvaluation,
      this.controller.evaluate,
    );

    logger.info(
      [...this.componentContext, { methodName: "constructor" }],
      "Created EvaluationRouter",
    );
  }
}
