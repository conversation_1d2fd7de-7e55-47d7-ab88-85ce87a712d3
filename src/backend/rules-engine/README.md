# Ruleforge-cre Developer instructions and documentation

## User management during testing

Ruleforge-cre can only be accesesd through users that were were created by the user management service.

For the moment the user_admin has the same access as the ruleset_admin on ruleforge.

The intended roles for the endpoints are:

| method | endpoint                   | allowed roles |
| ------ | -------------------------- | ------------- |
| POST   | /api/v1/entities           | api           |
| GET    | /api/v1/entities           | api           |
| PUT    | /api/v1/entities           | api           |
| DELETE | /api/v1/entities/:entityId | api           |

| method | endpoint                                                                     | allowed roles       |
| ------ | ---------------------------------------------------------------------------- | ------------------- |
| POST   | /api/v1/rulesets                                                            | ruleset_admin      |
| GET    | /api/v1/rulesets/                                                           | ruleset_admin, api |
| GET    | /api/v1/rulesets/:rulesetId                                                | ruleset_admin, api |
| DELETE | /api/v1/rulesets/:rulesetId                                                | ruleset_admin,     |
| POST   | /api/v1/rulesets/:rulesetId/:version/activate"                             | ruleset_admin,     |
| POST   | /api/v1/rulesets/:rulesetId/:version/deactivate"                           | ruleset_admin,     |
| POST   | /api/v1/rulesets/:existingRulesetId/:existingVersion/copy/:newRulesetId", | ruleset_admin,     |

| method | endpoint                   | allowed roles       |
| ------ | -------------------------- | ------------------- |
| GET    | /api/v1/lists/             | api, ruleset_admin |
| GET    | /api/v1/lists/download/:id | ruleset_admin      |
| DELETE | /api/v1/lists/:id          | ruleset_admin      |
| POST   | /api/v1/lists              | ruleset_admin      |

| method | endpoint                                  | allowed roles  |
| ------ | ----------------------------------------- | -------------- |
| GET    | /api/v1/analytics/getRulesetSalesReport/ | ruleset_admin |

| method | endpoint                                | allowed roles  |
| ------ | --------------------------------------- | -------------- |
| GET    | /api/v1/bulk-notification/'             | ruleset_admin |
| POST   | /api/v1/bulk-notification/'             | ruleset_admin |
| DELETE | /api/v1/bulk-notification/:id/'         | ruleset_admin |
| GET    | /api/v1/bulk-notification/:id/download' | ruleset_admin |
| GET    | /api/v1/bulk-notification/:id/sendsms   | ruleset_admin |

### Creating local users for testing.

#### api user

1. Start the user-management service as per it's README. [../user-management/README.md]
2. Run this command: `docker exec -i rulesforge_user_service node test/createApiUser.js`
3. It will aske for a user name and password, make something up.
4. Copy the token it returns and run `export API_TOKEN=COPIED_TOKEN` to insert it into the local environment

#### Ruleset user

For the moment the user_admin is standing in as a capaign_user for testing.

1. start the user-management service as per it's readme. [../user-management/README.md]
2. run [../user-management/getAdminToken.sh] like this:

```
export RULESET_ADMIN_TOKEN=`./getAdminToken.sh | jq -r '.data.token'`
```

after the above users are created the and their tokens inserted into the local environment using the scripts in the tests directory becomes possible, just make sure the script uses the correct token for the type of user that should run it.

## Exapmle console workflow using test scripts:

1. Get the ruleset admin token
```
export RULESET_ADMIN_TOKEN=`../user-management/getAdminToken.sh | jq -r '.data.token'`
```
1. Create an entity and get the entity id
```
export ENTITY_ID=`./tests/add_entity.sh  ./tests/scenarios/dev/register.json | awk '/^[{]/' | jq '.entityId'`; echo $ENTITY_ID
```
1.  Create the ruleset
```
./tests/add_ruleset.sh ./tests/scenarios/dev/ruleset.json
```

1.  Activate the ruleset 
```
./tests/activateRuleset.sh `cat tests/scenarios/dev/ruleset.json | sed "s/__ENTITY_ID__/$ENTITY_ID/g" | jq -r '.rulesetId'` 1
```

1.  Call evaluate
```
./tests/evaluate.sh ./tests/scenarios/dev/evaluate_post.json
```


## Crediverse Transfer API configuration.

For Ruleforge to call the transfer function to distribute bonuses and commissions there need to exist under the agent that owns the ruleset account on Crediverse.

The environment variables `ECDS_API_PORT` and `ECDS_API_URL` needs to be set to the address and port where ecds-api is running.

To use the Crediverse transfer functionality use in in a rule to be called if the rule condition is true.

every ruleset rule may have an `apiCalls` property:

```json
              "apiCalls": [
                {
                  "name": "payAirtimeSalesCommission",
                  "type": "crediverseTransfer",
                  "authentication": {
                    "apiUsername": "ruleforge9",
                    "apiSecret": "67c321580cd94076a6d1fa95f0833723",
                    "crediverseUsername": "ruleforge",
                    "crediversePassword": "97aaa688"
                  },
                  "parameters": [
                    {
                      "name": "amount",
                      "type": "number",
                      "value": "{salesCommission}"
                    },
                    {
                      "name": "targetMSISDN",
                      "type": "string",
                      "value": "{sellerAgentMsisdn}"
                    }
                  ]
                }
              ],

```

The associated API call will be made when the rule condition is true. The `crediverseTransfer` API call needs the information in it's authentication properties to get a token from ecds-api and then use it to affect the transfer call to Crediverse.

the `apiUsername` and `apiSecret` refers to a hard coded user on ecds-api. There are 10 such users: `ruleforge0` to `ruleforge9`. any one of them can be associated to the Crediverse Agent API User identified by the `crediverseUsername` and `crediversePassword`.

** Ruleforge ecds-api users and secrets**

| ecds-api username | ecds-api secret                  |
| ----------------- | -------------------------------- |
| ruleforge0        | 67c321580cd94076a6d1fa95f0833723 |
| ruleforge1        | 6aa7a656786c48c7b9745b0e2dfc42bb |
| ruleforge2        | 90d14d1e3e254d73ae44cbb40a5c168c |
| ruleforge3        | 6554187084e04eedb54c747c32ae2d28 |
| ruleforge4        | 7a361a9c878248a5a9cfba43429731af |
| ruleforge5        | 3950810fec6a4bd8a28f97b2fd54f8fe |
| ruleforge6        | 0dab2d618e814080bedab31f59f9f5b4 |
| ruleforge7        | bd17a2db06384759bab53fd4490c25fc |
| ruleforge8        | 870195d02e7b466fb9218a17db2a6579 |
| ruleforge9        | 762c05c5d90447c08cc2a467bc5a5ab2 |

> **NOTE: ** Sharing these here is actually unacceptable and these should only be available and valid for UAT.

** A complete ruleset example: **

```json
{
  "schemaVersion": "2.4.0",
  "rulesetId": "AGENT_SALES_TARGET_2024",
  "name": "Agent Sales Revenue Target 2024",
  "description": "Track agent sales revenue and notify when 1500 FCFA target is reached",
  "rulesetVersion": 1,
  "status": "ACTIVE",
  "startDateTime": "2024-01-01T00:00:00Z",
  "endDateTime": "2024-12-31T23:59:59Z",
  "lastModifiedDateTime": "2024-11-14T14:30:00Z",
  "localVariableDefinitions": [
    {
      "variableId": "salesCommission",
      "name": "Sales Commission",
      "description": "sales commission due the selling agent",
      "type": "number",
      "defaultValue": 0.0
    }
  ],
  "entities": [
    {
      "entityId": "CREDIVERSE",
      "entityName": "Crediverse EVD System",
      "transactionContexts": [
        {
          "contextId": "WHOLESALE_AIRTIME_PURCHASE_POST",
          "contextName": "Post Wholesale Airtime Credit Purchase",
          "rules": [
            {
              "ruleId": "PaySalesCommission",
              "name": "PaySalesCommission",
              "description": "Pays sales commission to the selling agent.",
              "priority": 1,
              "condition": {
                "type": "COMPARISON",
                "operator": ">=",
                "parameters": {
                  "leftOperand": "{purchaseAmount}",
                  "rightOperand": 0
                }
              },
              "variableOperations": [
                {
                  "variableId": "salesCommission",
                  "operation": "SET",
                  "value": "{purchaseAmount}"
                },
                {
                  "variableId": "salesCommission",
                  "operation": "DIVIDE",
                  "value": 10
                }
              ],
              "apiCalls": [
                {
                  "name": "payAirtimeSalesCommission",
                  "type": "crediverseTransfer",
                  "authentication": {
                    "apiUsername": "ruleforge9",
                    "apiSecret": "67c321580cd94076a6d1fa95f0833723",
                    "crediverseUsername": "ruleforge",
                    "crediversePassword": "97aaa688"
                  },
                  "parameters": [
                    {
                      "name": "amount",
                      "type": "number",
                      "value": "{salesCommission}"
                    },
                    {
                      "name": "targetMSISDN",
                      "type": "string",
                      "value": "{sellerAgentMsisdn}"
                    }
                  ]
                }
              ],
              "actions": []
            }
          ]
        }
      ]
    }
  ]
}
```

## Environment Variable Config

RuleForge centralizes all settings in a single `Config.js` module. On startup, each value is resolved in this order:

Environment variable (for example, `process.env.RF_PORT`)

`config.json` file (only for server port and SSL settings; see below)

Built-in default

Each load emits a `console.info()` (or your logger) message showing whether a setting came from the environment, the config file, or the default.

`config.json` support

If a file named config.json exists at the project root and contains an entry for the current NODE_ENV (“development” or “production”), its server section will override only these settings:

- server.port → RF_PORT
- server.ssl.enabled → RF_SSL_ENABLED
- server.ssl.cert → RF_SSL_CERT_PATH
- server.ssl.key → RF_SSL_KEY_PATH

Example config.json
``` json
{
    "development": {
        "server": {
            "port": 8080,
                "ssl": false
        }
    },
        "production": {
            "server": {
                "port": 8443,
                "ssl": {
                    "enabled": true,
                    "cert": "/path/to/cert.pem",
                    "key": "/path/to/key.pem"
                }
            }
        }
}
```

All other settings ignore config.json and fall back to environment variables or defaults.

### Available settings and their default values

These defaults are defined in `./config/Config.js`.

#### NODE_ENV: 
**default:** `development`

Whether executing in `development` or `production` mode. Will be used to load the correct section of `config.json` if it's present, othewise ignored. 

Handy to use to control SSL for a development docker instance by toggling it in the docker-compose.yaml, and having a config.json. 

#### RF_PORT: 
**default:** `3000`

The port where RuleForge listens for requests.

#### RF_SSL_ENABLED: 
**default:** `false `

Whether to serve HTTPS(true) or HTTP(false)

#### RF_SSL_CERT_PATH: 
**default:** (empty string)

Where to find the SSL certificatese when `RF_SSL_ENABLED` is `true`

#### RF_SSL_KEY_PATH: 
**default:** (empty string)

Where to find the SSL keys when `RF_SSL_ENABLED` is `true`


#### REDIS_HOSTS: 
**default:** 
```json
[{"host":"ruleforge_cre_db","port":6379}]
```

The connection information for connecting to Redis. 

The default connects to a single Redis node.  

To connect to multiple nodes supply an array of entries: 
``` json
[{"host":"ruleforge_cre_db_m1","port":6379},{"host":"ruleforge_cre_db_m2","port":6379},{"host":"ruleforge_cre_db_m2","port":6379}]
```
listing all of the cluster master nodes.


#### ECDS_API_PORT: 
**default:** `9084`

Where to call Crediverse.

#### ECDS_API_URL: 
**default:** `http://ecds-api`

Where to call Crediverse.

#### INSTANCE_NAME: 
**default:** `-`

Used for logging. Each RF instance should be configured with it's hostname in this value.

#### LOG_LEVEL: 
**default:** `info`

The level at which the appliaction should log. One of:
- fatal
- error
- warn
- info
- trace
- debug


#### JWT_PUBLIC_KEY: 
**default:** `"the-key" `

The key used for validating JWT tokens.  This vaule has to be retrieved from the 'user-managment' service and populated.

#### SMS_QUEUE_BATCH_SIZE: 

**default:** `100`


#### SMS_QUEUE_BATCH_DELAY: 

**default:** `1000`

#### SMS_QUEUE_DB_HOST: 

**default:** `ruleforge_cre_journal_db`

#### SMS_QUEUE_DB_NAME: 

**default:** `ruleforge_cre_journal_db`

#### SMS_QUEUE_DB_PASSWORD: 

**default:** `ussdgw`

#### SMS_QUEUE_DB_PORT: 

**default:** `3306`

#### SMS_QUEUE_DB_USERNAME: 

**default:** `root`

#### SMS_QUEUE_TABLE_NAME: 

**default:** `smsq_queue`

#### KEEP_ALIVE_TIMEOUT

**default:** `60000` 

the number of ms to keep connections alive for

## Minimal Example `dockercompose.yml'

```yaml
services:
  ruleforge_cre_db:
    image: redis/redis-stack:latest
    container_name: ruleforge_cre_db
    ports:
      - "6379:6379" # Redis port.
      - "8001:8001" # Redis Analytics port.  Maybe not needed for production.

  ruleforge_cre_service:
    build: .
    container_name: ruleforge_cre_service
    ports:
      - "3000:3000"
    depends_on:
      - ruleforge_cre_db
    volumes:
      - .:/usr/src/app
    environment:
      - NODE_ENV=development
      - REDIS_HOSTS='[{"host":"ruleforge_cre_db", "port":6379}]'
      - INSTANCE_NAME=DEVELOPMENT
      - PORT=3000
      - SECRET_KEY=your_secret_key
```

## HTTPS

The rules engine service is serving a HTTPS connection on port 3000. The certificates was signed by Let's Encrypt, so they just work, but they are going to expire in 90 days from Thu Oct 17 2024. At which point we will have to create new ones.

To create the server key:

```
openssl genpkey -algorithm RSA -out server.key
```

To create the server cert:

```
openssl req -x509 -new -nodes -key server.key -sha256 -days 365 -out server.cert -config openssl.cnf
```

> |
> | **NOTE:** `openssl.cnf` is included in the `certs` directory
> |

## API Token

### Ruleforge-cre client authorization

Clients of the Ruleforge-cre service needs an API token to be added to the http headers of all requests to prove that the client is authorized to address the service.

#### Development

While addressing a self hosted development instance clients can use this header:

```
"Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwiaWF0IjoxNzI5MTY2MjU1LCJleHAiOjE3Mjk3NzEwNTV9.ksJSyypb04f3bFzpZbjNNO7vn8vATqM1Jo4OR_somPk"
```

#### Live

For live instances of the service, a client user will register with the user management service and request an API token.

In order for Ruleforge-cre to validate this token it should be configured to use the secret string that the user management service used to create the token.

Put it in the `docker-compose.yaml` file that runs the rules engine service as a environment variable in the `environment:` block:

```
    - SECRET_KEY=__user_management_secret__
```

## Running in dev environment

Make sure to have this in your /etc/hosts file so that the default configuration just works. Otherwise make sure that you configure your environment to point to where your Redis and mariadb databases are running.

```
0.0.0.0 ruleforge_cre_db
```

then run:

```

node app.js

```

## Running with Docker compose

To run the rules engine service with docker run:

```

docker compose up --build -d

```

to bring it down use

```

docker compose down -v

```

## Logs

Three logs are written.

- The operational log which is not defined and consists of message and errors that the developer found useful is written to the console.
- The access log, showing what IP addresses accessed the system, what endpoint was accessed and what the HTTP response code was, is written to a file called access.log in the app directory.
- The API log, showing request response bodies and headers, is written to a file called api.log in the app directory.

## Testing Scripts

There are four testing scripts scripts in the `tests` directory, they are used like this;

```
sh ./tests/add_entity.sh ./tests/scenarios/crediverse_dev/register.json
```

```
sh ./tests/add_ruleset.sh ./tests/scenarios/crediverse_dev/add_ruleset.json
```

```
sh ./tests/evaluate.sh ./tests/scenarios/crediverse_dev/evaluate.json
```

```
sh ./tests/get_rulesets.sh
```
