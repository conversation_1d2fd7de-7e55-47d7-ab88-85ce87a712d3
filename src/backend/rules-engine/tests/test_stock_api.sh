#!/bin/bash

# Stock API Testing Script
# This script tests the stock API functionality including registration, 
# credential management, and transfer operations.

set -e  # Exit on any error

# Configuration
BASE_URL="http://localhost:3000"
API_BASE="${BASE_URL}/api/v1/stock"
REQUEST_ID=$(uuidgen 2>/dev/null || echo "test-$(date +%s)")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to make HTTP requests with proper headers
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=${4:-200}
    
    log_info "Making $method request to $endpoint"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" \
            -X "$method" \
            -H "Content-Type: application/json" \
            -H "X-Request-ID: $REQUEST_ID" \
            -d "$data" \
            "$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" \
            -X "$method" \
            -H "X-Request-ID: $REQUEST_ID" \
            "$endpoint")
    fi
    
    # Split response and status code
    body=$(echo "$response" | head -n -1)
    status=$(echo "$response" | tail -n 1)
    
    if [ "$status" -eq "$expected_status" ]; then
        log_success "Request successful (HTTP $status)"
        echo "$body" | jq . 2>/dev/null || echo "$body"
    else
        log_error "Request failed (HTTP $status, expected $expected_status)"
        echo "$body" | jq . 2>/dev/null || echo "$body"
        return 1
    fi
}

# Test 1: Register a stock API
test_register_api() {
    log_info "Test 1: Registering stock API"
    
    local api_data='{
        "apiId": "test-crediverse-api",
        "name": "Test Crediverse Stock API",
        "description": "Test API for Crediverse stock transfers",
        "type": "stockTransfer",
        "provider": "crediverse",
        "version": "1.0.0",
        "endpoints": {
            "transfer": "/api/account/transaction/transfer",
            "balance": "/api/account/balance"
        },
        "authentication": {
            "type": "oauth2",
            "tokenEndpoint": "/oauth/token",
            "requiredFields": ["apiUsername", "apiSecret", "accountUsername", "accountPassword"]
        },
        "parameters": [
            {
                "name": "fromAccount",
                "type": "string",
                "required": true,
                "description": "Source account identifier"
            },
            {
                "name": "toAccount",
                "type": "string", 
                "required": true,
                "description": "Target account identifier"
            },
            {
                "name": "amount",
                "type": "number",
                "required": true,
                "description": "Amount to transfer"
            },
            {
                "name": "stockType",
                "type": "string",
                "required": false,
                "default": "airtime",
                "description": "Type of stock to transfer"
            }
        ]
    }'
    
    make_request "POST" "${API_BASE}/register" "$api_data" 201
}

# Test 2: Get all registered APIs
test_get_apis() {
    log_info "Test 2: Getting all registered APIs"
    make_request "GET" "${API_BASE}/apis"
}

# Test 3: Get specific API by ID
test_get_api_by_id() {
    log_info "Test 3: Getting API by ID"
    make_request "GET" "${API_BASE}/apis/test-crediverse-api"
}

# Test 4: Store credentials for a ruleset
test_store_credentials() {
    log_info "Test 4: Storing credentials for ruleset"
    
    local cred_data='{
        "rulesetId": "test-ruleset-001",
        "accountRef": "primary",
        "credentials": {
            "apiUsername": "ruleforge9",
            "apiSecret": "67c321580cd94076a6d1fa95f0833723",
            "accountUsername": "test_account",
            "accountPassword": "test_password_123",
            "provider": "crediverse"
        }
    }'
    
    make_request "POST" "${API_BASE}/credentials" "$cred_data" 201
}

# Test 5: Get masked credentials
test_get_credentials() {
    log_info "Test 5: Getting masked credentials"
    make_request "GET" "${API_BASE}/credentials/test-ruleset-001?accountRef=primary"
}

# Test 6: Execute stock transfer (will fail without real ECDS API)
test_stock_transfer() {
    log_info "Test 6: Executing stock transfer"
    
    local transfer_data='{
        "fromAccount": "company-main-account",
        "toAccount": "+**********",
        "amount": 100,
        "stockType": "airtime",
        "apiId": "test-crediverse-api",
        "rulesetId": "test-ruleset-001",
        "accountRef": "primary"
    }'
    
    # This will likely fail due to authentication/network issues, but tests the endpoint
    log_warning "This test may fail due to external API dependencies"
    make_request "POST" "${API_BASE}/transfer" "$transfer_data" 200 || log_warning "Transfer failed as expected (external API not available)"
}

# Test 7: Test validation errors
test_validation_errors() {
    log_info "Test 7: Testing validation errors"
    
    # Test missing required fields for API registration
    log_info "Testing API registration with missing fields"
    local invalid_api='{"name": "Invalid API"}'
    make_request "POST" "${API_BASE}/register" "$invalid_api" 400 || true
    
    # Test missing required fields for credentials
    log_info "Testing credential storage with missing fields"
    local invalid_creds='{"rulesetId": "test"}'
    make_request "POST" "${API_BASE}/credentials" "$invalid_creds" 400 || true
    
    # Test missing required fields for transfer
    log_info "Testing transfer with missing fields"
    local invalid_transfer='{"amount": 100}'
    make_request "POST" "${API_BASE}/transfer" "$invalid_transfer" 400 || true
}

# Test 8: Test duplicate API registration
test_duplicate_api() {
    log_info "Test 8: Testing duplicate API registration"
    
    local api_data='{
        "apiId": "test-crediverse-api",
        "name": "Duplicate API",
        "type": "stockTransfer",
        "provider": "test"
    }'
    
    make_request "POST" "${API_BASE}/register" "$api_data" 409 || true
}

# Main test execution
main() {
    log_info "Starting Stock API Tests"
    log_info "Base URL: $BASE_URL"
    log_info "Request ID: $REQUEST_ID"
    echo
    
    # Check if server is running
    if ! curl -s "$BASE_URL" > /dev/null; then
        log_error "Server is not running at $BASE_URL"
        log_info "Please start the rules engine server first"
        exit 1
    fi
    
    # Run tests
    test_register_api
    echo
    
    test_get_apis
    echo
    
    test_get_api_by_id
    echo
    
    test_store_credentials
    echo
    
    test_get_credentials
    echo
    
    test_stock_transfer
    echo
    
    test_validation_errors
    echo
    
    test_duplicate_api
    echo
    
    log_success "All tests completed!"
    log_info "Note: Some tests may fail due to external dependencies (ECDS API, Redis, etc.)"
}

# Help function
show_help() {
    echo "Stock API Testing Script"
    echo
    echo "Usage: $0 [OPTIONS]"
    echo
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -u, --url URL  Set base URL (default: http://localhost:3000)"
    echo
    echo "Examples:"
    echo "  $0                           # Run all tests with default URL"
    echo "  $0 -u http://localhost:8080  # Run tests with custom URL"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -u|--url)
            BASE_URL="$2"
            API_BASE="${BASE_URL}/api/v1/stock"
            shift 2
            ;;
        *)
            log_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Check dependencies
if ! command -v curl &> /dev/null; then
    log_error "curl is required but not installed"
    exit 1
fi

if ! command -v jq &> /dev/null; then
    log_warning "jq is not installed - JSON output will not be formatted"
fi

# Run main function
main
