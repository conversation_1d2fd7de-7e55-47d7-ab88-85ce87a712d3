API_TOKEN=*******************************************************************************************************************************************************************************************************************************



# Check if a JSON file was provided
if [ $# -ne 1 ]; then
  echo "Usage: $0 <entity_json_file>"
  exit 1
fi

# Set the API endpoint (use HTTPS)
API_ENDPOINT="https://localhost:3000/api/v1/entities"

# Read the JSON file
ENTITY_JSON_FILE="$1"

if [ ! -f "$ENTITY_JSON_FILE" ]; then
  echo "Error: File '$ENTITY_JSON_FILE' not found."
  exit 1
fi

ENTITY_JSON=$(cat "$ENTITY_JSON_FILE")


# Make the POST request and store the response and status code
RESPONSE=$(curl --http2 -k -s -w "\nStatus Code: %{http_code}\n" -X POST "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_TOKEN" \
  -d "$ENTITY_JSON")

# Print the response (including the status code)
echo "Response from $API_ENDPOINT:"
echo "$RESPONSE"

# Extract and store just the status code
STATUS_CODE=$(echo "$RESPONSE" | grep "Status Code:" | awk '{print $3}')
echo "Status Code: $STATUS_CODE"

# Exit with the status code
exit 0
