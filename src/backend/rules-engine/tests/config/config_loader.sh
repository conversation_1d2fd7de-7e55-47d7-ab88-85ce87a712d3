#!/bin/bash

# Get the directory of the current script
SCRIPT_DIR=$(dirname "$(readlink -f "$0")")

# Define the path to the configuration file relative to the script
CONFIG_FILE="$SCRIPT_DIR/config/config.json"

# Check if the configuration file exists
if [ ! -f "$CONFIG_FILE" ]; then
  echo "Error: Configuration file '$CONFIG_FILE' not found."
  exit 1
fi

# Read the configuration values from the JSON file
API_TOKEN=$(jq -r '.API_TOKEN' "$CONFIG_FILE")
API_ENDPOINT_HOST=$(jq -r '.API_ENDPOINT_HOST' "$CONFIG_FILE")

# Validate that the required values are present
if [ -z "$API_TOKEN" ] || [ -z "$API_ENDPOINT_HOST" ]; then
  echo "Error: Missing 'API_TOKEN' or 'API_ENDPOINT_HOST' in configuration file."
  exit 1
fi

# Check if required tools are available
if ! command -v curl >/dev/null 2>&1; then
  echo "Error: curl is not installed. Please install it and try again."
  exit 1
fi

if ! command -v jq >/dev/null 2>&1; then
  echo "Error: jq is not installed. Please install it and try again."
  exit 1
fi