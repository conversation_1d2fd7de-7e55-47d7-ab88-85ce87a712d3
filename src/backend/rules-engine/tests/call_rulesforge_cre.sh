#!/bin/bash
API_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwiaWF0IjoxNzMwODA0MzI5LCJleHAiOjE3NDExNzIzMjl9.sLXVJtALE0wkBuqzoXnaUEBF3Hn_IUsKHPB2tx2QTbE

# Check if a url and JSON file was provided
if [ $# -ne 2 ]; then
  echo "Usage: $0  <http_metod> <url> [entity_json_file]"
  exit 1
fi

# Set the API endpoint (use HTTPS)


METHOD="$1"

API_ENDPOINT="$2"


ENTITY_JSON_FILE="$3"

if [ -f "$ENTITY_JSON_FILE" ]; then
  ENTITY_JSON=$(cat "$ENTITY_JSON_FILE")

  RESPONSE=$(curl -k -s -w "\nStatus Code: %{http_code}\n" -X "$METHOD" "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_TOKEN" \
  -d "$ENTITY_JSON")
else
  RESPONSE=$(curl -k -s -w "\nStatus Code: %{http_code}\n" -X "$METHOD" "$API_ENDPOINT" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $API_TOKEN" )
fi

# Make the POST request and store the response and status code
# Print the response (including the status code)
echo "Response from $API_ENDPOINT:"
echo "$RESPONSE"

# Extract and store just the status code
STATUS_CODE=$(echo "$RESPONSE" | grep "Status Code:" | awk '{print $3}')
echo "Status Code: $STATUS_CODE"

# Exit with the status code
exit 0

