#!/bin/bash

# URL of the outcomes endpoint
URL="https://localhost:3000/api/v1/outcomes"

# Sample JSON payload based on the Transaction Outcome Notification Schema.
read -r -d '' PAYLOAD << 'EOF'
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "PURCHASE",
  "transactionId": "TXN123456789",
  "timestamp": "2025-03-15T14:35:00Z",
  "status": "COMPLETED",
  "transactionData": {
    "customerId": "CUST123456",
    "productPrice": 89.99,
    "discountPercentage": 10,
    "shippingCost": 0,
    "totalAmount": 89.99
  },
  "modificationStatus": {
    "modificationsApplied": ["productPrice", "discountPercentage", "shippingCost"],
    "rejected": []
  },
  "additionalData": {
    "conversionTime": 280,
    "deviceType": "MOBILE",
    "paymentMethod": "CREDIT_CARD"
  }
}
EOF

# Execute the curl request
curl -k -X POST "$URL" \
  -H "Content-Type: application/json" \
  -d "$PAYLOAD"

echo ""

