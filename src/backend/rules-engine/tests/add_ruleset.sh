#!/bin/bash


# Check if a JSON file was provided
if [ $# -ne 1 ]; then
  echo "Usage: $0 <ruleset_json_file>"
  exit 1
fi

# Set the API endpoint
API_ENDPOINT="https://localhost:3000/api/v1/rulesets"

# Read the JSON file
RULESET_JSON_FILE="$1"

if [ ! -f "$RULESET_JSON_FILE" ]; then
  echo "Error: File '$RULESET_JSON_FILE' not found."
  exit 1
fi

RULESET_JSON=$(cat "$RULESET_JSON_FILE"| sed "s/__ENTITY_ID__/$ENTITY_ID/g")

# Make the POST request and store the response and status code
RESPONSE=$(curl -k -s -w "\nStatus Code: %{http_code}\n" -X POST "$API_ENDPOINT" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $RULESET_ADMIN_TOKEN" \
-d "$RULESET_JSON")

# Print the response (including the status code)
echo "Response from $API_ENDPOINT:"
echo "$RESPONSE"

# Extract and store just the status code
STATUS_CODE=$(echo "$RESPONSE" | grep "Status Code:" | awk '{print $3}')
echo "Status Code: $STATUS_CODE"

# Exit with the status code
exit 0

