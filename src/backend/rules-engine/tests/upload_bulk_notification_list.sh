#!/bin/bash


if [ $# -ne 2 ]; then
  echo "Usage: $0 <listname> <filename.csv>"
  exit 1
fi

CSV_LIST_FILE="$2"
echo $CSV_LIST_FILE

if [ ! -f "$CSV_LIST_FILE" ]; then
  echo "Error: File '$CSV_LIST_FILE' not found."
  exit 1
fi

API_ENDPOINT="https://localhost:3000/api/v1/bulk-notification"

RESPONSE=$(curl -k -s -w "\nStatus Code: %{http_code}\n" -X POST "$API_ENDPOINT" \
  -H "Authorization: Bearer $API_TOKEN" \
  -F "file=@$CSV_LIST_FILE;type=text/csv" \
  -F "name=$1" \
  -F "text=goGoGO"
  -F "type=string")
  

echo "Response from $API_ENDPOINT:"
echo "$RESPONSE"

STATUS_CODE=$(echo "$RESPONSE" | grep "Status Code:" | awk '{print $3}')
echo "Status Code: $STATUS_CODE"

exit 0

