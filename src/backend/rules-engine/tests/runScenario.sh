# Check if a scenario name was provided
if [ $# -ne 1 ]; then
  echo "Usage: $0 <scenario> - where <scenario> is one of the subdirectories of the tests/scenarios directory." 
  exit 1
fi

pwd


# 1. Get the ruleset admin token
echo "1. Get the ruleset admin token"
export RULESET_ADMIN_TOKEN=`../user-management/getAdminToken.sh | jq -r '.data.token'` ; 
echo RULESET_ADMIN_TOKEN=$RULESET_ADMIN_TOKEN

# 2. Create an entity and get the entity id
echo "2. Create an entity and get the entity id"
export ENTITY_ID=`./tests/add_entity.sh  ./tests/scenarios/$1/register.json | awk '/^[{]/' | jq '.entityId'`; 
echo ENTITY_ID=$ENTITY_ID

# 3.  Create the ruleset
echo "3.  Create the ruleset"
./tests/add_ruleset.sh ./tests/scenarios/$1/ruleset.json

# 4.  Activate the ruleset 
echo "4.  Activate the ruleset "
./tests/activateRuleset.sh `cat tests/scenarios/$1/ruleset.json | sed "s/__ENTITY_ID__/$ENTITY_ID/g" | jq -r '.rulesetId'` 1

# 5.  Call evaluate
echo "5.  Call evaluate"
./tests/evaluate.sh ./tests/scenarios/$1/evaluate_post.json

