#!/bin/bash

API_TOKEN=*******************************************************************************************************************************************************************************************************************************


# Function to display usage
usage() {
  echo "Usage: $0 <evaluation_json_file> "
  exit 1
}

# Check if at least one argument was provided
if [ $# -lt 1 ] || [ $# -gt 2 ]; then
  usage
fi

# Set the API endpoint URL
URL="https://localhost:3000/api/v1/outcomes"

# Read the evaluation JSON file
OUTCOME_JSON_FILE="$1"

if [ ! -f "$OUTCOME_JSON_FILE" ]; then
  echo "Error: File '$OUTCOME_JSON_FILE' not found."
  exit 1
fi

PAYLOAD=$(cat "$OUTCOME_JSON_FILE"| sed "s/__ENTITY_ID__/$ENTITY_ID/g")

echo $PAYLOAD

# Make the POST request and store the response and status code
HTTP_RESPONSE=$(curl -k -s -w "HTTPSTATUS:%{http_code}" -X POST "$URL" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $API_TOKEN" \
-d "$PAYLOAD")

# Extract the body and status
HTTP_BODY=$(echo "$HTTP_RESPONSE" | sed -e 's/HTTPSTATUS\:.*//g')
HTTP_STATUS=$(echo "$HTTP_RESPONSE" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')

# Print the response and status code
echo "Response from $URL:"
echo "$HTTP_BODY"
echo "Status Code: $HTTP_STATUS"

exit 0

