#!/bin/bash

# Function to display usage
usage() {
  echo "Usage: $0 "
  exit 1
}

# Set the API endpoint URL (use HTTPS)
URL="https://localhost:3000/api/v1/rulesets"

# Make the GET request and store the response and status code
HTTP_RESPONSE=$(curl -k -s -w "HTTPSTATUS:%{http_code}" -X GET "$URL" \
-H "Authorization: Bearer $RULESET_ADMIN_TOKEN" \
-H "Content-Type: application/json" )

# Extract the body and status
HTTP_BODY=$(echo "$HTTP_RESPONSE" | sed -e 's/HTTPSTATUS\:.*//g')
HTTP_STATUS=$(echo "$HTTP_RESPONSE" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')

# Print the response and status code
echo "$HTTP_BODY"

exit 0




