#!/bin/bash

# NOTE in the curl command below the username refers to the username of an API user in Crediverse.  
# Make sure that the password is url encoded: See: https://www.w3schools.com/tags/ref_urlencode.ASP
# The id and secret before the '@' sign must be hardcoded into  `crediverse/ecds-api/src/main/java/cs/config/spring/OAuth2AuthorizationConfig.java`
#
export ECDS_API_ACCESS_TOKEN=` curl ruleforge:7a361a9c87824855a9cfba43129731af@localhost:9084/oauth/token -d grant_type=password -d username=ruleforge -d password='0%26fjmoTt' | jq '.access_token' | tr -d '"' `

echo $ECDS_API_ACCESS_TOKEN


