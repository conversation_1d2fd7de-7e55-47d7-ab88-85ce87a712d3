#!/bin/bash


# NOTE in the curl command below the username refers to the username of an API user in Crediverse.  Make sure that the password is url encoded: See: https://www.w3schools.com/tags/ref_urlencode.ASP
ECDS_API_ACCESS_TOKEN=`curl ruleforge:7a361a9c87824855a9cfba43129731af@localhost:9084/oauth/token -d grant_type=password -d username=cc001 -d password='o0Ei5IJ!'  | jq '.access_token' | tr -d '"' `

echo $ECDS_API_ACCESS_TOKEN

# Check if a JSON file was provided
if [ $# -ne 2 ]; then
  echo "Usage: $0 <amount> <targetMSISDN> " 
  exit 1
fi

# Set the API endpoint
ECDS_API_TRANSFER_ENDPOINT="http://localhost:9084/api/account/transaction/transfer"


TRANSFER_JSON="{\"amount\": $1, \"targetMSISDN\": \"$2\"}"

echo "RULESET_JSON:" $TRANSFER_JSON
echo "API_ENDPOINT:" $ECDS_API_TRANSFER_ENDPOINT


# Make the POST request and store the response and status code
RESPONSE=$(curl -k -s -w "\nStatus Code: %{http_code}\n" -X POST "$ECDS_API_TRANSFER_ENDPOINT" \
 -H "Content-Type: application/json" \
 -H "Authorization: Bearer $ECDS_API_ACCESS_TOKEN" \
 -d "$TRANSFER_JSON")
 
 # Print the response (including the status code)
echo "Response from $API_ENDPOINT:"
echo "$RESPONSE"
 
 # Extract and store just the status code
STATUS_CODE=$(echo "$RESPONSE" | grep "Status Code:" | awk '{print $3}')
echo "Status Code: $STATUS_CODE"
 
 # Exit with the status code
exit 0

