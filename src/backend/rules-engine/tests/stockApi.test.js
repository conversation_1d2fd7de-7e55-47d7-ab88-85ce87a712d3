import { jest } from '@jest/globals';
import StockApiService from '../services/stockApiService.js';
import stockApiController from '../controllers/stockApiController.js';

// Mock dependencies
const mockRedisClient = {
  get: jest.fn(),
  set: jest.fn(),
  sadd: jest.fn(),
  smembers: jest.fn(),
  pipeline: jest.fn(() => ({
    exec: jest.fn()
  }))
};

const mockLogger = {
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  trace: jest.fn()
};

// Mock fetch for API calls
global.fetch = jest.fn();

describe('StockApiService', () => {
  let stockApiService;
  const callerContext = [{ test: 'context' }];

  beforeEach(() => {
    jest.clearAllMocks();
    stockApiService = new StockApiService(mockRedisClient, callerContext);
  });

  describe('registerApi', () => {
    it('should register a new API successfully', async () => {
      const apiDefinition = {
        apiId: 'test-api',
        name: 'Test API',
        type: 'stockTransfer',
        provider: 'test'
      };

      mockRedisClient.get.mockResolvedValue(null); // API doesn't exist
      mockRedisClient.set.mockResolvedValue('OK');
      mockRedisClient.sadd.mockResolvedValue(1);

      const result = await stockApiService.registerApi(apiDefinition, callerContext);

      expect(result).toEqual({ success: true, apiId: 'test-api' });
      expect(mockRedisClient.get).toHaveBeenCalledWith('stockApi:definitions:test-api');
      expect(mockRedisClient.set).toHaveBeenCalled();
      expect(mockRedisClient.sadd).toHaveBeenCalledWith('stockApi:definitions:index', 'test-api');
    });

    it('should throw error if API already exists', async () => {
      const apiDefinition = {
        apiId: 'existing-api',
        name: 'Existing API',
        type: 'stockTransfer'
      };

      mockRedisClient.get.mockResolvedValue('{"apiId":"existing-api"}');

      await expect(stockApiService.registerApi(apiDefinition, callerContext))
        .rejects.toThrow('API with ID existing-api already exists');
    });
  });

  describe('storeCredentials', () => {
    it('should store encrypted credentials successfully', async () => {
      const rulesetId = 'test-ruleset';
      const accountRef = 'primary';
      const credentials = {
        apiUsername: 'testuser',
        apiSecret: 'testsecret',
        accountUsername: 'account123',
        accountPassword: 'password123',
        provider: 'crediverse'
      };

      mockRedisClient.get.mockResolvedValue(null); // No existing credentials
      mockRedisClient.set.mockResolvedValue('OK');

      const result = await stockApiService.storeCredentials(rulesetId, accountRef, credentials, callerContext);

      expect(result).toEqual({ success: true });
      expect(mockRedisClient.set).toHaveBeenCalled();
      
      // Verify that the stored data contains encrypted secrets
      const setCall = mockRedisClient.set.mock.calls[0];
      const storedData = JSON.parse(setCall[1]);
      expect(storedData.accounts.primary.apiUsername).toBe('testuser');
      expect(storedData.accounts.primary.apiSecret).not.toBe('testsecret'); // Should be encrypted
      expect(storedData.accounts.primary.accountPassword).not.toBe('password123'); // Should be encrypted
    });
  });

  describe('getCredentials', () => {
    it('should retrieve and decrypt credentials successfully', async () => {
      const rulesetId = 'test-ruleset';
      const accountRef = 'primary';
      
      // Mock encrypted credentials data
      const encryptedData = {
        rulesetId,
        accounts: {
          primary: {
            apiUsername: 'testuser',
            apiSecret: stockApiService.encrypt('testsecret'),
            accountUsername: 'account123',
            accountPassword: stockApiService.encrypt('password123'),
            provider: 'crediverse'
          }
        }
      };

      mockRedisClient.get.mockResolvedValue(JSON.stringify(encryptedData));

      const result = await stockApiService.getCredentials(rulesetId, accountRef, callerContext);

      expect(result).toEqual({
        apiUsername: 'testuser',
        apiSecret: 'testsecret',
        accountUsername: 'account123',
        accountPassword: 'password123',
        provider: 'crediverse'
      });
    });

    it('should return null if credentials not found', async () => {
      mockRedisClient.get.mockResolvedValue(null);

      const result = await stockApiService.getCredentials('nonexistent', 'primary', callerContext);

      expect(result).toBeNull();
    });
  });

  describe('transferStock', () => {
    it('should execute stock transfer successfully', async () => {
      const transferRequest = {
        fromAccount: 'source123',
        toAccount: 'target456',
        amount: 100,
        stockType: 'airtime',
        apiId: 'test-api',
        rulesetId: 'test-ruleset',
        accountRef: 'primary'
      };

      // Mock API definition
      const apiDefinition = {
        apiId: 'test-api',
        provider: 'crediverse'
      };

      // Mock credentials
      const credentials = {
        apiUsername: 'testuser',
        apiSecret: 'testsecret',
        accountUsername: 'account123',
        accountPassword: 'password123'
      };

      // Mock successful transfer response
      const transferResponse = {
        transferId: 'txn_123',
        status: 'success'
      };

      mockRedisClient.get
        .mockResolvedValueOnce(JSON.stringify(apiDefinition)) // API definition
        .mockResolvedValueOnce(JSON.stringify({ // Credentials
          accounts: {
            primary: {
              apiUsername: 'testuser',
              apiSecret: stockApiService.encrypt('testsecret'),
              accountUsername: 'account123',
              accountPassword: stockApiService.encrypt('password123')
            }
          }
        }));

      // Mock getEcdsApiToken
      jest.doMock('../connectors/ecdsApiTransfer.js', () => ({
        getEcdsApiToken: jest.fn().mockResolvedValue('mock-token')
      }));

      // Mock fetch for transfer API
      global.fetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(transferResponse)
      });

      const result = await stockApiService.transferStock(transferRequest, callerContext);

      expect(result.success).toBe(true);
      expect(result.amount).toBe(100);
      expect(result.fromAccount).toBe('source123');
      expect(result.toAccount).toBe('target456');
    });

    it('should handle transfer failure gracefully', async () => {
      const transferRequest = {
        fromAccount: 'source123',
        toAccount: 'target456',
        amount: 100,
        apiId: 'nonexistent-api',
        rulesetId: 'test-ruleset',
        accountRef: 'primary'
      };

      mockRedisClient.get.mockResolvedValue(null); // API not found

      const result = await stockApiService.transferStock(transferRequest, callerContext);

      expect(result.success).toBe(false);
      expect(result.error).toContain('API definition not found');
    });
  });

  describe('encryption/decryption', () => {
    it('should encrypt and decrypt data correctly', () => {
      const originalText = 'sensitive-data-123';
      
      const encrypted = stockApiService.encrypt(originalText);
      expect(encrypted).not.toBe(originalText);
      
      const decrypted = stockApiService.decrypt(encrypted);
      expect(decrypted).toBe(originalText);
    });
  });
});

describe('Stock API Controller', () => {
  let req, res;

  beforeEach(() => {
    req = {
      headers: { 'x-request-id': 'test-request-123' },
      body: {},
      params: {},
      query: {}
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis()
    };
    jest.clearAllMocks();
  });

  describe('registerApi', () => {
    it('should register API successfully', async () => {
      req.body = {
        apiId: 'test-api',
        name: 'Test API',
        type: 'stockTransfer',
        provider: 'test'
      };

      // Mock successful registration
      jest.spyOn(StockApiService.prototype, 'registerApi')
        .mockResolvedValue({ success: true, apiId: 'test-api' });

      await stockApiController.registerApi(req, res);

      expect(res.status).toHaveBeenCalledWith(201);
      expect(res.json).toHaveBeenCalledWith({
        message: 'API registered successfully',
        apiId: 'test-api',
        requestId: 'test-request-123'
      });
    });

    it('should return 400 for missing required fields', async () => {
      req.body = { name: 'Test API' }; // Missing apiId and type

      await stockApiController.registerApi(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        error: 'Missing required fields: apiId, name, type',
        requestId: 'test-request-123'
      });
    });
  });

  describe('transferStock', () => {
    it('should execute transfer successfully', async () => {
      req.body = {
        fromAccount: 'source123',
        toAccount: 'target456',
        amount: 100,
        apiId: 'test-api',
        rulesetId: 'test-ruleset'
      };

      const transferResult = {
        success: true,
        transferId: 'txn_123',
        amount: 100,
        fromAccount: 'source123',
        toAccount: 'target456'
      };

      jest.spyOn(StockApiService.prototype, 'transferStock')
        .mockResolvedValue(transferResult);

      await stockApiController.transferStock(req, res);

      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({
        ...transferResult,
        requestId: 'test-request-123'
      });
    });

    it('should return 400 for missing required fields', async () => {
      req.body = { amount: 100 }; // Missing required fields

      await stockApiController.transferStock(req, res);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.stringContaining('Missing required field'),
          requestId: 'test-request-123'
        })
      );
    });
  });
});
