{"name": "SmartShop Bundle Delivery Service", "description": "Customizable bundles delivery service with agent network", "transactionContexts": [{"contextId": "BUNDLE_SELL", "name": "Bundle Sell", "description": "Represents an agent selling a bundle to a subscriber", "properties": [{"propertyId": "agentMs<PERSON>dn", "name": "Agent MSISD<PERSON>", "description": "The mobile number of the agent performing the transaction", "type": "string", "mutable": false}, {"propertyId": "agentCgi", "name": "Agent <PERSON>", "description": "The Cell Global Identity representing the agent's location", "type": "string", "mutable": false}, {"propertyId": "bundleRetailPrice", "name": "Bundle Retail Price", "description": "The retail price of the bundle being sold", "type": "number", "mutable": false}, {"propertyId": "agentPurchasePrice", "name": "Agent Purchase Price", "description": "The purchase price of the bundle, debited from the agent's wallet", "type": "number", "mutable": true, "constraints": {"min": 0}}, {"propertyId": "bundleId", "name": "Bundle ID", "description": "Identifier for the bundle being sold (format: SS-PP, where SS is service sequence number and PP is package sequence number)", "type": "string", "mutable": false}, {"propertyId": "subscriberMsisdn", "name": "Subscriber MSISDN", "description": "The mobile number of the subscriber receiving the bundle", "type": "string", "mutable": false}]}]}