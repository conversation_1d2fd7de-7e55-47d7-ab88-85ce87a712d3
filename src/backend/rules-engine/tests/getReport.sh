#!/bin/bash

API_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MSwiaWF0IjoxNzMwODA0MzI5LCJleHAiOjE3NDExNzIzMjl9.sLXVJtALE0wkBuqzoXnaUEBF3Hn_IUsKHPB2tx2QTbE

# Set the API endpoint
# API_ENDPOINT="https://localhost:3000/api/v1/analytics/getReport"
API_ENDPOINT="https://localhost:3000/api/v1/analytics/getRulesetSalesReport?rulesetId=$1&rulesetVersion=$2"
# API_ENDPOINT="https://localhost:3000/api/v1/analytics/getReport/$1/$2/$3/\"$4\"/\"$5\""
echo $API_ENDPOINT


# Make the POST request and store the response and status code
RESPONSE=$(curl -k -s -w "\nStatus Code: %{http_code}\n" -X GET "$API_ENDPOINT" \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $API_TOKEN" )

# Print the response (including the status code)
echo "Response from $API_ENDPOINT:"
echo "$RESPONSE"

# Extract and store just the status code
STATUS_CODE=$(echo "$RESPONSE" | grep "Status Code:" | awk '{print $3}')
echo "Status Code: $STATUS_CODE"

# Exit with the status code
exit 0

