import express from "express";
import bodyParser from "body-parser";
import http from "http";
// import { createSecureServer } from "http2";

import spdy from "spdy";


import { configureCors } from "./middleware/cors.js";
import { httpsOptions } from "./config/ssl.js";

import { v4 as uuidv4 } from "uuid";
import RedisClient from "./services/redisClient.js";

import Config from "./config/Config.js";

import { requestLogger, logger } from "./middleware/logger.js";

import RegistrationRouter from "./routes/registrationRoutes.js";
import RulesetRouter from "./routes/rulesetRoutes.js";
import EvaluationRouter from "./routes/evaluateRoutes.js";
import OutcomeNotificationRouter from "./routes/outcomeNotificationRoutes.js";
import ListManagementRouter from "./routes/listManagementRoutes.js";
import BulkNotificationRouter from "./routes/bulkNotificationRoutes.js";
import WebhookRoutes from "./routes/webhookRoutes.js";
import SystemConfigurationRoutes from "./routes/systemConfigurationRoutes.js";
import StockApiRoutes from "./routes/stockApiRoutes.js";

logger.info( [{ componentName: "app.js" }, { task: "creation" }], {CONFIGURATION:  Config.dump()});

const app = express();
const REDIS_HOSTS = Config.REDIS_HOSTS;

await RedisClient.configure(REDIS_HOSTS);

function injectRequestId(req, res, next) {
  const requestId = uuidv4();
  req.requestId = requestId;
  res.setHeader("X-Request-ID", requestId);
  next();
}

app.use(bodyParser.json({ limit: "2mb" }));
app.use(configureCors());


logger.info(
  [{ componentName: "app.js" }, { task: "creation" }],
  "Creating Server"
);

const RF_PORT = Config.RF_PORT;
let server;

try {
  if (Config.RF_SSL_ENABLED) {
    logger.info(
      [{ componentName: "app.js" }, { task: "creation" }],
      "RF_SSL_ENABLED=true → Starting HTTP/2 (With SSL) server"
    );
    try {
      server = spdy.createServer(httpsOptions, app);
    } catch (error) {
      logger.warn(
      [{ componentName: "app.js" }, { task: "creation" }],
        "HTTP/2 Server could not be started.  Disabling HTTP/2 (With SSL) and Starting HTTP/1.1 (No SSL) server instead: " + error
      );
      logger.info(
        [{ componentName: "app.js" }, { task: "creation" }],
        "RF_SSL_ENABLED=true, (Failed to start!) → Starting HTTP/1.1 (No SSL) server"
      );
      server = http.createServer(app);
    }
  } else {
    logger.info( 
      [{ componentName: "app.js" }, { task: "creation" }],
      "RF_SSL_ENABLED=false → Starting HTTP/1.1(No SSL) server"
    );
    server = http.createServer(app);
  }

  // ─── KEEP-ALIVE CONFIGURATION ─────────────────────────────────────────────
  // How long to keep the connection alive waiting for the next request (ms)
  const KEEP_ALIVE_TIMEOUT = Config.KEEP_ALIVE_TIMEOUT ?? 60_000;

  // HTTP-level keep-alive: how long a socket stays open awaiting another request
  server.keepAliveTimeout = KEEP_ALIVE_TIMEOUT;
  
  // Must be larger than keepAliveTimeout so header parsing can finish
  server.headersTimeout     = KEEP_ALIVE_TIMEOUT + 5_000;

  // TCP-level keep-alive: send OS-level probes on idle sockets
  server.on("connection", (socket) => {
    socket.setKeepAlive(true, KEEP_ALIVE_TIMEOUT);
  });
  // ─────────────────────────────────────────────────────────────────────────


  server.listen(RF_PORT, async () => {
    logger.info(
      [{ componentName: "app.js" }, { task: "creation" }],
      `RuleForge API listening on port ${RF_PORT}`
    );
    logger.info(
      [{ componentName: "app.js" }, { task: "creation" }],
      `Using Redis @ ${REDIS_HOSTS}`
    );

    // inject request‐id & logger middleware
    app.use(injectRequestId);
    app.use(requestLogger);

    // routes
    app.use("/api/v1/lists", new ListManagementRouter().routes());
    app.use("/api/v1/entities", new RegistrationRouter().routes());
    app.use("/api/v1/rulesets", new RulesetRouter().routes());
    app.use("/api/v1/evaluate", new EvaluationRouter().routes());
    app.use("/api/v1/outcomes", new OutcomeNotificationRouter().routes());
    app.use("/api/v1/bulk-notification", new BulkNotificationRouter().routes());
    app.use("/api/v1/webhooks", new WebhookRoutes().routes());
    app.use("/api/v1/stock", StockApiRoutes);
    app.use("/system", new SystemConfigurationRoutes().routes());

    // set‐log‐level endpoint
    app.get("/api/v1/set-log-level", (req, res) => {
      const newLevel = req.query.level;
      logger.info(
        [{ componentName: "app.js" }, { task: "set-log-level" }],
        `Changing log level to ${newLevel}`
      );
      logger.level = newLevel;
      res.send(`Log level changed to ${newLevel}`);
    });

    // 404 handler
    app.use((_req, res) => {
      logger.warn(
        [{ componentName: "app.js" }, { task: "requestHandling" }],
        "Endpoint not found"
      );
      res.status(404).send({ error: "Endpoint not found" });
    });
  });
} catch (error) {
  logger.fatal(
    [{ componentName: "app.js" }, { task: "app initialization" }],
    error
  );
}

// process handlers (uncaughtException, SIGINT, SIGTERM) as before…

export default app;

