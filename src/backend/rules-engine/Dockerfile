# Use an official Node.js runtime as a parent image
FROM node:18

# Set the working directory inside the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json
COPY package*.json ./

RUN npm install -g npm@9.8.1

RUN npm install

# RUN npm install -g nodemon

COPY . .

# Install dos2unix to convert line endings for scripts in /tests directory for Windows
RUN apt update && apt install -y jq redis-tools
RUN npm  -g install  nodemon

# Ensure scripts in /tests are converted and executable
#RUN dos2unix /usr/src/app/tests/*.sh && chmod +x /usr/src/app/tests/*.sh

# Expose the port on which the app will run
EXPOSE 3000

# Command to start the application
CMD ["./scripts/wait-for-redis.sh", "nodemon", "app.js"]

#CMD ["tail", "-f", "/dev/null"]

