// Define the API endpoint

import Config from '../config/Config.js'

const ECDS_API_PORT = Config.ECDS_API_PORT;
const ECDS_API_URL = Config.ECDS_API_URL;
const ECDS_API_TRANSFER_PATH = "/account/transaction/transfer";
const ECDS_API_TRANSFER_URI = `${ECDS_API_URL}:${ECDS_API_PORT}${ECDS_API_TRANSFER_PATH}`;

export async function getEcdsApiToken(
  apiUsername,
  apiSecret,
  ecdsUsername,
  ecdsPassword,
) {
  const url = `${ECDS_API_URL}:${ECDS_API_PORT}/oauth/token`;

  const bodyData = new URLSearchParams({
    grant_type: "password",
    username: ecdsUsername,
    password: ecdsPassword, // Include the percent-encoded character for &
  });

  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
      Authorization: `Basic ${btoa(`${apiUsername}:${apiSecret}`)}`,
    },
    body: bodyData.toString(),
  };

  try {
    let rawResponse = await fetch(url, options);
    let response = await rawResponse.json();
    return response.access_token;
  } catch (error) {
    const errorMessage = `Error during Crediverse api get token request: ${error}`;
    throw new Error(errorMessage);
  }
}

// Function to make the API request
export async function crediverseTransfer(targetMsisdn, amount) {
  const transferRequest = {
    amount: targetMsisdn,
    targetMSISDN: amount,
  };

  // Make the request
  const response = await fetch(ECDS_API_TRANSFER_URI, {
    method: "POST",
    headers: {
      "Content-Type": "application/json", // Inform the server you're sending JSON
      Authorization: `Bearer ${ECDS_API_ACCESS_TOKEN}`, // Add the bearer token
    },
    body: JSON.stringify(transferRequest), // Convert data to JSON string
  });

  // Parse and log the response
  const transferResponse = await response.json();

  return transferResponse;
}
