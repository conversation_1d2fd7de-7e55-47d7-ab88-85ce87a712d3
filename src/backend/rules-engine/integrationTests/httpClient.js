// httpClient.js
import fetch from 'node-fetch';
import https from 'https';

// Create a fresh HTTPS agent for each call to avoid reusing closed sockets
function makeAgent(url) {
  if (!url.startsWith('https:')) return undefined;
  return new https.Agent({
    rejectUnauthorized: false,  // trust self-signed
    keepAlive: false,          // no socket reuse
  });
}

async function safeFetch(url, opts = {}) {
  const agent = makeAgent(url);
  const headers = { Connection: 'close', ...(opts.headers || {}) };
  try {
    const res = await fetch(url, { ...opts, headers, agent });
    return res;
  } finally {
    if (agent) agent.destroy();
  }
}

// POST JSON and return parsed JSON
export async function postJson(url, body, token) {
  const res = await safeFetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    },
    body: JSON.stringify(body),
  });
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`POST ${url} failed ${res.status}: ${text}`);
  }
  return res.json();
}

// POST multipart/form-data and return parsed JSON
export async function postForm(url, form, token) {
  // ensure proper Content-Length to prevent chunked encoding
  const length = await new Promise((resolve, reject) => {
    form.getLength((err, len) => err ? reject(err) : resolve(len));
  });

  const res = await safeFetch(url, {
    method: 'POST',
    headers: {
      ...form.getHeaders(),
      'Content-Length': length,
      ...(token && { Authorization: `Bearer ${token}` }),
    },
    body: form,
  });
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`POST ${url} failed ${res.status}: ${text}`);
  }
  return res.json();
}


