export const ruleset = {
  schemaVersion: "7.0.0",
  rulesetId: "PERSISTENT_VAR_TEST",
  name: "Persistent Variable Test Rule Set",
  description:
    "Tests persistent variables by tracking agent transaction counts and awarding progressive discounts",
  entityId: "574e820e-6441-4b43-b41a-9c24bb35b7a2",
  contextId: "BUNDLE_SELL",
  version: 1,
  status: "ACTIVE",
  category: "TESTING",
  startDateTime: "2025-05-27T00:00:00Z",
  endDateTime: "2025-12-31T23:59:59Z",
  lastModifiedDateTime: "2025-05-27T16:30:00Z",
  collectionMappings: [
    {
      collectionId: "agentTransactionHistory",
      name: "Agent Transaction History",
      keyMapping: {
        propertyId: "agentMsisdn",
      },
    },
  ],
  persistentVariables: [
    {
      variableId: "totalTransactionCount",
      name: "Total Transaction Count",
      description: "Total number of completed transactions for this agent",
      type: "number",
      defaultValue: 0,
      collectionId: "agentTransactionHistory",
    },
    {
      variableId: "totalDiscountEarned",
      name: "Total Discount Earned",
      description:
        "Total discount amount earned by this agent across all transactions",
      type: "number",
      defaultValue: 0,
      collectionId: "agentTransactionHistory",
    },
    {
      variableId: "lastTransactionTimestamp",
      name: "Last Transaction Timestamp",
      description: "Timestamp of the agent's last transaction",
      type: "date",
      defaultValue: "1970-01-01T00:00:00Z",
      collectionId: "agentTransactionHistory",
    },
    {
      variableId: "loyaltyTier",
      name: "Loyalty Tier",
      description:
        "Agent's loyalty tier based on transaction count (BRONZE, SILVER, GOLD)",
      type: "string",
      defaultValue: "BRONZE",
      collectionId: "agentTransactionHistory",
    },
  ],
  localVariables: [
    {
      variableId: "currentDiscountRate",
      name: "Current Discount Rate",
      description:
        "Discount rate for current transaction based on loyalty tier",
      type: "number",
      defaultValue: 2,
    },
    {
      variableId: "calculatedDiscount",
      name: "Calculated Discount",
      description: "Actual discount amount for current transaction",
      type: "number",
      defaultValue: 0,
    },
    {
      variableId: "qualifiesForDiscount",
      name: "Qualifies For Discount",
      description: "Flag indicating if agent qualifies for loyalty discount",
      type: "boolean",
      defaultValue: false,
    },
  ],
  evaluationRules: [
    {
      ruleId: "APPLY_DISCOUNT",
      name: "Check Silver Tier Discount",
      description:
        "Awards 5% discount for Silver tier agents (5-9 transactions)",
      priority: 2,
      condition: {
        conditionTypeId: "LOGICAL",
        operator: "AND",
        parameters: {
          conditions: [
            {
              conditionTypeId: "COMPARISON",
              operator: "==",
              parameters: {
                leftOperand: 1,
                rightOperand: 1,
              },
            },
            {
              conditionTypeId: "COMPARISON",
              operator: "==",
              parameters: {
                leftOperand: 1,
                rightOperand: 1,
              },
            },
          ],
        },
      },
      variableAssignments: [
        {
          variableId: "agentPurchasePrice",
          assignmentTypeId: "DECREASE_BY_PERCENTAGE",
          value: "{currentDiscountRate}",
        },
      ],
      webhookCalls: [],
    },
  ],
  outcomeRules: [
    {
      ruleId: "INCREMENT_TRANSACTION_COUNT",
      name: "Increment Transaction Count",
      description:
        "Increments the agent's total transaction count for completed transactions",
      priority: 1,
      condition: {
        conditionTypeId: "COMPARISON",
        operator: "==",
        parameters: {
          leftOperand: "{status}",
          rightOperand: "COMPLETED",
        },
      },
      variableAssignments: [
        {
          variableId: "totalTransactionCount",
          assignmentTypeId: "ADD",
          value: 1,
        },
        {
          variableId: "lastTransactionTimestamp",
          assignmentTypeId: "SET",
          value: "{timestamp}",
        },
      ],
      webhookCalls: [],
    },
    {
      ruleId: "UPDATE_LOYALTY_TIER_BRONZE",
      name: "Update Loyalty Tier to Bronze",
      description:
        "Updates loyalty tier to Bronze for agents with 1-4 transactions",
      priority: 2,
      condition: {
        conditionTypeId: "LOGICAL",
        operator: "AND",
        parameters: {
          conditions: [
            {
              conditionTypeId: "COMPARISON",
              operator: "==",
              parameters: {
                leftOperand: "{status}",
                rightOperand: "COMPLETED",
              },
            },
            {
              conditionTypeId: "COMPARISON",
              operator: ">=",
              parameters: {
                leftOperand: "{totalTransactionCount}",
                rightOperand: 1,
              },
            },
            {
              conditionTypeId: "COMPARISON",
              operator: "<=",
              parameters: {
                leftOperand: "{totalTransactionCount}",
                rightOperand: 4,
              },
            },
          ],
        },
      },
      variableAssignments: [
        {
          variableId: "loyaltyTier",
          assignmentTypeId: "SET",
          value: "BRONZE",
        },
      ],
      webhookCalls: [],
    },
    {
      ruleId: "UPDATE_LOYALTY_TIER_SILVER",
      name: "Update Loyalty Tier to Silver",
      description:
        "Updates loyalty tier to Silver for agents with 5-9 transactions",
      priority: 3,
      condition: {
        conditionTypeId: "LOGICAL",
        operator: "AND",
        parameters: {
          conditions: [
            {
              conditionTypeId: "COMPARISON",
              operator: "==",
              parameters: {
                leftOperand: "{status}",
                rightOperand: "COMPLETED",
              },
            },
            {
              conditionTypeId: "COMPARISON",
              operator: ">=",
              parameters: {
                leftOperand: "{totalTransactionCount}",
                rightOperand: 5,
              },
            },
            {
              conditionTypeId: "COMPARISON",
              operator: "<=",
              parameters: {
                leftOperand: "{totalTransactionCount}",
                rightOperand: 9,
              },
            },
          ],
        },
      },
      variableAssignments: [
        {
          variableId: "loyaltyTier",
          assignmentTypeId: "SET",
          value: "SILVER",
        },
      ],
      webhookCalls: [],
    },
    {
      ruleId: "UPDATE_LOYALTY_TIER_GOLD",
      name: "Update Loyalty Tier to Gold",
      description:
        "Updates loyalty tier to Gold for agents with 10+ transactions",
      priority: 4,
      condition: {
        conditionTypeId: "LOGICAL",
        operator: "AND",
        parameters: {
          conditions: [
            {
              conditionTypeId: "COMPARISON",
              operator: "==",
              parameters: {
                leftOperand: "{status}",
                rightOperand: "COMPLETED",
              },
            },
            {
              conditionTypeId: "COMPARISON",
              operator: ">=",
              parameters: {
                leftOperand: "{totalTransactionCount}",
                rightOperand: 10,
              },
            },
          ],
        },
      },
      variableAssignments: [
        {
          variableId: "loyaltyTier",
          assignmentTypeId: "SET",
          value: "GOLD",
        },
      ],
      webhookCalls: [],
    },
    {
      ruleId: "TRACK_DISCOUNT_EARNINGS",
      name: "Track Discount Earnings",
      description:
        "Adds current discount to total discount earned if discount was applied",
      priority: 5,
      condition: {
        conditionTypeId: "LOGICAL",
        operator: "AND",
        parameters: {
          conditions: [
            {
              conditionTypeId: "COMPARISON",
              operator: "==",
              parameters: {
                leftOperand: "{status}",
                rightOperand: "COMPLETED",
              },
            },
            {
              conditionTypeId: "COMPARISON",
              operator: "IN",
              parameters: {
                leftOperand: "agentPurchasePrice",
                rightOperand: "{modificationsApplied}",
              },
            },
          ],
        },
      },
      variableAssignments: [
        {
          variableId: "calculatedDiscount",
          assignmentTypeId: "SET",
          value: "{bundleRetailPrice}",
        },
        {
          variableId: "calculatedDiscount",
          assignmentTypeId: "MULTIPLY",
          value: "{currentDiscountRate}",
        },
        {
          variableId: "calculatedDiscount",
          assignmentTypeId: "DIVIDE",
          value: 100,
        },
        {
          variableId: "totalDiscountEarned",
          assignmentTypeId: "ADD",
          value: "{calculatedDiscount}",
        },
      ],
      webhookCalls: [],
    },
  ],
};
