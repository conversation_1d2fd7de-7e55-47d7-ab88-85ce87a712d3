
import fs from "fs";
import path from "path";
import process from "process";
import FormData from "form-data";

import { entity } from "./entity.js";
import { ruleset } from "./ruleset.js";
import { evaluationPost } from "./evaluation_post.js";
import { outcomeNotificationPost } from "./outcomeNotification_post.js";
import { warn } from "console";

import fetch from 'node-fetch';

import https from "https";

import { setId, getId } from "./persistId.js";

export async function deleteItem(endpoint, token) {

  const agent = new https.Agent({ rejectUnauthorized: false });
  try{

    const res = await fetch(endpoint, {
      method:  'DELETE',
      headers: {
        'Content-Type':  'application/json',
        Authorization:   `Bearer ${token}`
      },
      agent              // ← node-fetch will use this for TLS
    });

    if (!res.ok) throw new Error(`HTTP ${res.status} – ${res.statusText}`);
    return res.json();
  } finally {
    agent.destroy(); // Clean up the agent after use
  }
}



export async function postItem(item, endpoint, token) {

  const agent = new https.Agent({ rejectUnauthorized: false });
  try{

    const res = await fetch(endpoint, {
      method:  'POST',
      headers: {
        'Content-Type':  'application/json',
        Authorization:   `Bearer ${token}`
      },
      body:    JSON.stringify(item || {}),
      agent              // ← node-fetch will use this for TLS
    });

    if (!res.ok) throw new Error(`HTTP ${res.status} – ${res.statusText}`);
    return res.json();
  } finally {
    agent.destroy(); // Clean up the agent after use
  }

}

function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

const API_TOKEN =
  "*******************************************************************************************************************************************************************************************************************************";




export async function login() {
  const body = { username: 'admin', password: 'password', tenant_id: 1 };
  const API_ENDPOINT = 'http://localhost:5000/api/v1/auth/login';

  const opts = {
    method:  'POST',
    headers: {
      'Content-Type': 'application/json',
      'Connection':    'close',
    },
    body:    JSON.stringify(body),
  };


  try {
    const res = await fetch(API_ENDPOINT, opts);
    if (!res.ok) throw new Error(`Login failed ${res.status} ${res.statusText}`);
    const { data } = await res.json();
    return data.token;
  } catch (err) {
    console.error('Login request failed:', err);
    process.exit(1);
  }
}


/**
  * Upload a CSV list via multipart/form-data, ensuring proper Content-Length.
  */
  export async function uploadList(csvPath, listName, url, token) {
    console.log({ Uploading: { csvPath, listName, url } });

    // Ensure file exists
    try {
      await fs.promises.access(csvPath, fs.constants.R_OK);
    } catch {
      console.error(`Error: File '${csvPath}' not found or unreadable.`);
      process.exit(1);
    }

    // Build form
    const form = new FormData();
    form.append('file', fs.createReadStream(csvPath), {
      contentType: 'text/csv',
      filename:    path.basename(csvPath),
    });
    form.append('name', listName);
    form.append('type', 'string');

    // Compute Content-Length to avoid chunked encoding
    const contentLength = await new Promise((resolve, reject) => {
      form.getLength((err, length) => {
        if (err) reject(err);
        else resolve(length);
      });
    });

    const headers = {
      ...form.getHeaders(),
      'Content-Length': contentLength,
      'Connection':     'close',
      'Authorization':  `Bearer ${token}`,
    };

    // Create a fresh agent per call
    const agent = new https.Agent({ rejectUnauthorized: false, keepAlive: false });

    try {
      const res = await fetch(url, {
        method:  'POST',
        headers,
        body:    form,
        agent,
      });

      if (!res.ok) {
        const text = await res.text();
        console.error(`Upload failed (${res.status}):`, text);
        process.exit(1);
      }

      console.log('Uploaded List');
      return res.json();
    } catch (err) {
      console.error('Upload request failed:', err);
      process.exit(1);
    } finally {
      agent.destroy();
    }
  }

async function createEntity() {
  const savedEntityId = "currentEntityId.json";
  let entityId = await getId(savedEntityId);

  if (!entityId) {
    console.log("Creating entity! ");
    console.log(`${entity.name}`);

    const registerUrl = "https://localhost:3000/api/v1/entities";
    const registrationResponse = await postItem(entity, registerUrl, API_TOKEN);

    entityId = registrationResponse.entityId;

    await setId(savedEntityId, entityId);
  }

  return entityId;
}
async function uploadListIfNeeded(listName, userToken) {
  const savedListName = "savedListName.json";

  const currentList = await getId(savedListName);

  if (!currentList) {
    const uploadListUrl = "https://localhost:3000/api/v1/lists";

    let uploadListResponse = await uploadList(
      "lome_area_cgi_list.csv",
      listName,
      uploadListUrl,
      userToken,
    );

    //console.log({ uploadListResponseStatus: uploadListResponse.status });

    await setId(savedListName, listName);
    return;
  } else {
    console.log(`${listName} list already upleaded`);
  }
}

async function createRuleset(userToken,entityId,contextId) {
  const savedRulesetId = "currentRulesetId.json";
  let rulesetId = await getId(savedRulesetId);

  if (!rulesetId) {
    ruleset.entityId = entityId;
    ruleset.contextId = contextId;

    const addRulesetUrl = "https://localhost:3000/api/v1/rulesets";

    const addRulesetResponse = await postItem(
      ruleset,
      addRulesetUrl,
      userToken,
    );

  } 
  await setId(savedRulesetId, ruleset.rulesetId);

  return [ruleset.rulesetId,ruleset.version];
}


async function deleteRuleset(userToken, rulesetId) {
  const deleteRulesetUrl = `https://localhost:3000/api/v1/rulesets/${rulesetId}`;
  console.log({deleteRulesetUrl })
  try {

    const deleteRulesetResponse = await deleteItem(
      deleteRulesetUrl,
      userToken,
    );
  console.log({
    deleteRulesetResponseStatus: deleteRulesetResponse.status,
  });

  }
  catch(e){
    console.error(e);
  }
}


async function activateRuleset(userToken, rulesetId,  rulesetVersion )
{

  // activate ruleset
  const activateRulesetUrl = `https://localhost:3000/api/v1/rulesets/${rulesetId}/${rulesetVersion}/activate`;

  console.log({activateRulesetUrl });

  const activateRulesetResponse = await postItem(
    null,
    activateRulesetUrl,
    userToken,
  );
  console.log({
    activateRulesetResponseStatus: activateRulesetResponse.status,
  });

}

async function test() {
  try {
    //   const contextId = "BUNDLE_SELL";
    const userToken = await login();

    const listName = "LOME_AREA_CGI_LIST";

    const entityId = await createEntity();

    console.log({entityId });

    await uploadListIfNeeded(listName, userToken);

    const contextId = "BUNDLE_SELL";

    const [rulesetId,rulesetVersion ] =  await createRuleset(userToken, entityId, contextId);

    console.log({rulesetId,rulesetVersion });

   await activateRuleset(userToken, rulesetId,  rulesetVersion );
   // sleep(2000);
   //await deleteRuleset(userToken, rulesetId,  rulesetVersion );
    

    const evaluateUrl = `https://localhost:3000/api/v1/evaluate`;

    let evaluationPostResponse;


    evaluationPost.entityId = entityId;
    evaluationPost.contextId = contextId;
    evaluationPost.transactionId = "1";
    evaluationPost.timestamp = new Date().toISOString();
    evaluationPost.transactionData.agentMsisdn = "0884440000";
    evaluationPost.transactionData.bundleId = "BUNDLE_1";
    evaluationPost.transactionData.bundleRetailPrice = 1000;
    evaluationPost.transactionData.agentPurchasePrice = 980;
    evaluationPost.transactionData.subscriberMsisdn = "0117770000";
    evaluationPost.transactionData.agentCgi = "62120-10015-856";

    evaluationPostResponse = await postItem(
      evaluationPost,
      evaluateUrl,
      API_TOKEN,
    );

    console.log(JSON.stringify({ evaluationPostResponse },null,2));
    /*
    const outcomeNotificationUrl = `https://localhost:3000/api/v1/outcomes`;
    let outcomeNotificationPostResponse;

    outcomeNotificationPost.entityId = entityId;
    outcomeNotificationPost.contextId = contextId;
    outcomeNotificationPost.transactionId = "1";
    outcomeNotificationPost.timestamp = new Date().toISOString();
    outcomeNotificationPost.transactionData.agentMsisdn = "0884440000";
    outcomeNotificationPost.transactionData.bundleId = "BUNDLE_1";
    outcomeNotificationPost.transactionData.bundleRetailPrice = 1000;
    outcomeNotificationPost.transactionData.agentPurchasePrice = 980;
    outcomeNotificationPost.transactionData.subscriberMsisdn = "0117770000";
    outcomeNotificationPost.transactionData.agentCgi = "62120-10015-856";
    outcomeNotificationPost.transactionData.benefitsMultiplier = 1.0;


    outcomeNotificationPostResponse = await postItem(
      outcomeNotificationPost,
      outcomeNotificationUrl,
      API_TOKEN,
    );

    console.log(JSON.stringify({ outcomeNotificationPostResponse},null,2));

    await sleep(1000);

    evaluationPost.entityId = entityId;
    evaluationPost.contextId = contextId;
    evaluationPost.transactionId = "2";
    evaluationPost.timestamp = new Date().toISOString();
    evaluationPost.transactionData.agentMsisdn = "0884440000";
    evaluationPost.transactionData.bundleId = "BUNDLE_1";
    evaluationPost.transactionData.bundleRetailPrice = 1000;
    evaluationPost.transactionData.agentPurchasePrice = 980;
    evaluationPost.transactionData.subscriberMsisdn = "0117770001";
    evaluationPost.transactionData.agentCgi = "62120-10015-856";


    evaluationPostResponse = await postItem(
      evaluationPost,
      evaluateUrl,
      API_TOKEN,
    );
    console.log(JSON.stringify({ evaluationPostResponse },null,2));


    outcomeNotificationPost.entityId = entityId;
    outcomeNotificationPost.contextId = contextId;
    outcomeNotificationPost.transactionId = "2";
    outcomeNotificationPost.timestamp = new Date().toISOString();
    outcomeNotificationPost.transactionData.agentMsisdn = "0884440000";
    outcomeNotificationPost.transactionData.bundleId = "BUNDLE_1";
    outcomeNotificationPost.transactionData.bundleRetailPrice = 1000;
    outcomeNotificationPost.transactionData.agentPurchasePrice = 980;
    outcomeNotificationPost.transactionData.subscriberMsisdn = "0117770001";
    outcomeNotificationPost.transactionData.agentCgi = "62120-10015-856";
    outcomeNotificationPost.transactionData.benefitsMultiplier = 1.0;


    outcomeNotificationPostResponse = await postItem(
      outcomeNotificationPost,
      outcomeNotificationUrl,
      API_TOKEN,
    );

    console.log(JSON.stringify({ outcomeNotificationPostResponse},null,2));





    // submit evaluation

    await sleep(500);

    evaluationPost.entityId = entityId;
    evaluationPost.contextId = contextId;
    evaluationPost.transactionId = "1";
    evaluationPost.timestamp = new Date().toISOString();
    evaluationPost.transactionData.agentMsisdn = "0884440000";
    evaluationPost.transactionData.bundleId = "BUNDLE_1";
    evaluationPost.transactionData.bundleRetailPrice = 1000;
    evaluationPost.transactionData.agentPurchasePrice = 1000;
    evaluationPost.transactionData.subscriberMsisdn = "0117770000";
    evaluationPost.transactionData.agentCgi = "CGI_2";

    const evaluateUrl = `https://localhost:3000/api/v1/evaluate`;

    const evaluationPostResponse = await postItem(
      evaluationPost,
      evaluateUrl,
      API_TOKEN,
    );
    // console.log({ evaluationPostResponseStatus: evaluationPostResponse.status });
    //
      // sumbit outcome notification
    //
    */

  } catch (err) {
    console.log({ err });
    process.exit(1);
  }
}

test();
