// idStore.js
import { promises as fs } from 'fs';
const ID_DIR = "/home/<USER>/tmp/";
/**
 * Persist a value to a file as JSON.
 *
 * @param {string} filename – Path to the file to write.
 * @param {*} value – Any JSON-serializable value to persist.
 * @returns {Promise<void>}
 * @throws Will throw if the file cannot be written.
 */
export async function setId(filename, value) {
  const data = JSON.stringify(value);
  await fs.writeFile(ID_DIR + filename, data, 'utf8');
}

/**
 * Retrieve a persisted value from a file.
 *
 * @param {string} filename – Path to the file to read.
 * @returns {Promise<*>} The parsed value if the file exists, or null if it does not.
 * @throws Will re-throw any error other than file-not-found.
 */
export async function getId(filename) {
  try {
    const data = await fs.readFile(ID_DIR +filename, 'utf8');
    return JSON.parse(data);
  } catch (err) {
    if (err.code === 'ENOENT') {
      // File doesn't exist
      return null;
    }
    throw err;
  }
}

