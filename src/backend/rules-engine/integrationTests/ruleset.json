{
  "schemaVersion": "6.0.0",
  "rulesetId": "LOME_BUNDLE_SALES_APR2025",
  "name": "Lomé Agent Bundle Sales Incentive - April 2025",
  "description": "Daily sales target incentive program for Lomé region agents with bonus discount rewards",
  "entityId": __ENTITY_ID__,
  "contextId": "AAA",
  "version": 1,
  "status": "ACTIVE",
  "category": "PRICING",
  "startDateTime": "2024-03-03T00:00:00Z",
  "endDateTime": "2044-03-07T23:59:59Z",
  "lastModifiedDateTime": "2024-02-24T12:00:00Z",
  "collectionMappings": [
    {
      "collectionId": "agentDailySales",
      "name": "Agent Daily Sales",
      "keyMapping": {
        "propertyId": "agentMsisdn"
      }
    }
  ],
  "persistentVariables": [
    {
      "variableId": "dailyBundleCount",
      "name": "Daily Bundle Sales Count",
      "description": "Number of qualifying bundles successfully sold by agent on current transaction date",
      "type": "number",
      "defaultValue": 0,
      "collectionId": "agentDailySales"
    },
    {
      "variableId": "lastTransactionDay",
      "name": "Last Transaction Day",
      "description": "Day of the agent's last transaction in YYYY-MM-DD format",
      "type": "string",
      "defaultValue": "",
      "collectionId": "agentDailySales"
    },
    {
      "variableId": "targetAchievedDay",
      "name": "Target Achieved Day",
      "description": "Stores the day (YYYY-MM-DD) when an agent last reached their daily sales target of 30 bundles. When this matches the current transaction day, the 2% discount is applied.",
      "type": "string",
      "defaultValue": "",
      "collectionId": "agentDailySales"
    },
    {
      "variableId": "campaignCosts",
      "name": "Campaign Costs",
      "description": "Tracks the total cost of additional 2% discounts granted by the campaign, calculated as the sum of all discount amounts applied when agents reach their daily targets.",
      "type": "number",
      "defaultValue": 0,
      "collectionId": "agentDailySales"
    }
  ],
  "localVariables": [
    {
      "variableId": "calculatedDiscount",
      "name": "Calculated Discount",
      "description": "The calculated 2% discount amount for the current transaction",
      "type": "number",
      "defaultValue": 0
    },
    {
      "variableId": "currentDay",
      "name": "Current Day",
      "description": "The current transaction day in YYYY-MM-DD format",
      "type": "string",
      "defaultValue": ""
    },
    {
      "variableId": "isLomeArea",
      "name": "Is Lomé Area",
      "description": "Flag indicating if the agent is in the Lomé area",
      "type": "boolean",
      "defaultValue": false
    },
    {
      "variableId": "currentDayOfWeek",
      "name": "Current Day of Week",
      "description": "The day of week for the current transaction",
      "type": "string",
      "defaultValue": ""
    },
    {
      "variableId": "isWeekday",
      "name": "Is Weekday",
      "description": "Flag indicating if the current transaction is on a weekday",
      "type": "boolean",
      "defaultValue": false
    }
  ],
  "evaluationRules": [
    {
      "ruleId": "EXTRACT_CURRENT_DAY",
      "name": "Extract Current Day",
      "description": "Extracts the current day from the timestamp",
      "priority": 1,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "!=",
        "parameters": {
          "leftOperand": "{timestamp}",
          "rightOperand": null
        }
      },
      "variableAssignments": [
        {
          "variableId": "currentDay",
          "assignmentTypeId": "SET",
          "value": {
            "functionId": "formatDate",
            "args": [
              "{timestamp}",
              "yyyy-MM-dd"
            ]
          }
        }
      ]
    },
    {
      "ruleId": "CHECK_REWARD_VALIDITY",
      "name": "Check Reward Validity",
      "description": "Clears the reward date if the current day is different",
      "priority": 2,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": "!=",
              "parameters": {
                "leftOperand": "{targetAchievedDay}",
                "rightOperand": ""
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "!=",
              "parameters": {
                "leftOperand": "{currentDay}",
                "rightOperand": "{targetAchievedDay}"
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "targetAchievedDay",
          "assignmentTypeId": "SET",
          "value": ""
        }
      ]
    },
    {
      "ruleId": "CHECK_LOME_AREA",
      "name": "Check Lomé Area",
      "description": "Determines if the agent is in the Lomé area",
      "priority": 3,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "!=",
        "parameters": {
          "leftOperand": "{agentCgi}",
          "rightOperand": null
        }
      },
      "variableAssignments": [
        {
          "variableId": "isLomeArea",
          "assignmentTypeId": "SET",
          "value": false
        }
      ]
    },
    {
      "ruleId": "SET_LOME_AREA_FLAG",
      "name": "Set Lomé Area Flag",
      "description": "Sets the Lomé area flag to true if the agent CGI is in the Lomé area list",
      "priority": 4,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "IN",
        "parameters": {
          "leftOperand": "{agentCgi}",
          "rightOperand": {
            "listId": "LOME_AREA_CGI_LIST"
          }
        }
      },
      "variableAssignments": [
        {
          "variableId": "isLomeArea",
          "assignmentTypeId": "SET",
          "value": true
        }
      ]
    },
    {
      "ruleId": "APPLY_BONUS_DISCOUNT",
      "name": "Apply Bonus Discount",
      "description": "Applies additional 2% discount when target is reached and the agent is in the Lomé area",
      "priority": 5,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{targetAchievedDay}",
                "rightOperand": "{currentDay}"
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{isLomeArea}",
                "rightOperand": true
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "calculatedDiscount",
          "assignmentTypeId": "SET",
          "value": "{agentPurchasePrice}"
        },
        {
          "variableId": "calculatedDiscount",
          "assignmentTypeId": "MULTIPLY",
          "value": 0.02
        },
        {
          "variableId": "agentPurchasePrice",
          "assignmentTypeId": "DECREASE_BY_PERCENTAGE",
          "value": 2
        }
      ]
    }
  ],
  "outcomeRules": [
    {
      "ruleId": "EXTRACT_CURRENT_DAY_OUTCOME",
      "name": "Extract Current Day for Outcome",
      "description": "Extracts the current day from the timestamp for outcome processing",
      "priority": 1,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "!=",
        "parameters": {
          "leftOperand": "{timestamp}",
          "rightOperand": null
        }
      },
      "variableAssignments": [
        {
          "variableId": "currentDay",
          "assignmentTypeId": "SET",
          "value": {
            "functionId": "formatDate",
            "args": [
              "{timestamp}",
              "yyyy-MM-dd"
            ]
          }
        }
      ]
    },
    {
      "ruleId": "RESET_DAILY_COUNTER",
      "name": "Reset Daily Counter",
      "description": "Resets an agent's daily bundle count when they process their first transaction of a new day",
      "priority": 2,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": "!=",
              "parameters": {
                "leftOperand": "{lastTransactionDay}",
                "rightOperand": ""
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "!=",
              "parameters": {
                "leftOperand": "{currentDay}",
                "rightOperand": "{lastTransactionDay}"
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "dailyBundleCount",
          "assignmentTypeId": "SET",
          "value": 0
        },
        {
          "variableId": "lastTransactionDay",
          "assignmentTypeId": "SET",
          "value": "{currentDay}"
        }
      ]
    },
    {
      "ruleId": "INITIALIZE_TRANSACTION_DAY",
      "name": "Initialize Transaction Day",
      "description": "Sets the current day as the last transaction day if no previous transactions",
      "priority": 3,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "==",
        "parameters": {
          "leftOperand": "{lastTransactionDay}",
          "rightOperand": ""
        }
      },
      "variableAssignments": [
        {
          "variableId": "lastTransactionDay",
          "assignmentTypeId": "SET",
          "value": "{currentDay}"
        }
      ]
    },
    {
      "ruleId": "SET_WEEKDAY_FLAG_OUTCOME",
      "name": "Set Weekday Flag for Outcome",
      "description": "Sets the current day of week and initializes the weekday flag to false for outcome processing",
      "priority": 4,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "!=",
        "parameters": {
          "leftOperand": "{timestamp}",
          "rightOperand": null
        }
      },
      "variableAssignments": [
        {
          "variableId": "currentDayOfWeek",
          "assignmentTypeId": "SET",
          "value": {
            "functionId": "dayOfWeek",
            "args": [
              "{timestamp}"
            ]
          }
        },
        {
          "variableId": "isWeekday",
          "assignmentTypeId": "SET",
          "value": false
        }
      ]
    },
    {
      "ruleId": "CHECK_IS_WEEKDAY_OUTCOME",
      "name": "Check If Weekday for Outcome",
      "description": "Sets the weekday flag to true if the current day is a weekday for outcome processing",
      "priority": 5,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "IN",
        "parameters": {
          "leftOperand": "{currentDayOfWeek}",
          "rightOperand": [
            "MONDAY",
            "TUESDAY",
            "WEDNESDAY",
            "THURSDAY",
            "FRIDAY"
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "isWeekday",
          "assignmentTypeId": "SET",
          "value": true
        }
      ]
    },
    {
      "ruleId": "CHECK_LOME_AREA_OUTCOME",
      "name": "Check Lomé Area for Outcome",
      "description": "Determines if the agent is in the Lomé area for outcome processing",
      "priority": 6,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "!=",
        "parameters": {
          "leftOperand": "{agentCgi}",
          "rightOperand": null
        }
      },
      "variableAssignments": [
        {
          "variableId": "isLomeArea",
          "assignmentTypeId": "SET",
          "value": false
        }
      ]
    },
    {
      "ruleId": "SET_LOME_AREA_FLAG_OUTCOME",
      "name": "Set Lomé Area Flag for Outcome",
      "description": "Sets the Lomé area flag to true if the agent CGI is in the Lomé area list for outcome processing",
      "priority": 7,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "IN",
        "parameters": {
          "leftOperand": "{agentCgi}",
          "rightOperand": {
            "listId": "LOME_AREA_CGI_LIST"
          }
        }
      },
      "variableAssignments": [
        {
          "variableId": "isLomeArea",
          "assignmentTypeId": "SET",
          "value": true
        }
      ]
    },
    {
      "ruleId": "TRACK_DISCOUNT_COSTS",
      "name": "Track Discount Costs",
      "description": "Tracks the cost of discounts applied for reporting",
      "priority": 8,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{status}",
                "rightOperand": "COMPLETED"
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "IN",
              "parameters": {
                "leftOperand": "agentPurchasePrice",
                "rightOperand": "{modificationsApplied}"
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "calculatedDiscount",
          "assignmentTypeId": "SET",
          "value": "{bundleRetailPrice}"
        },
        {
          "variableId": "calculatedDiscount",
          "assignmentTypeId": "MULTIPLY",
          "value": 0.02
        },
        {
          "variableId": "campaignCosts",
          "assignmentTypeId": "ADD",
          "value": "{calculatedDiscount}"
        }
      ]
    },
    {
      "ruleId": "INCREMENT_BUNDLE_COUNT",
      "name": "Increment Bundle Count",
      "description": "Increments daily bundle count for qualifying successfully completed sales",
      "priority": 9,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{status}",
                "rightOperand": "COMPLETED"
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": ">=",
              "parameters": {
                "leftOperand": "{bundleRetailPrice}",
                "rightOperand": 500
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{isLomeArea}",
                "rightOperand": true
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{isWeekday}",
                "rightOperand": true
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "dailyBundleCount",
          "assignmentTypeId": "ADD",
          "value": 1
        }
      ]
    },
    {
      "ruleId": "CHECK_TARGET_REACHED",
      "name": "Check Target Reached",
      "description": "Checks if daily target is reached and sets rewards flag",
      "priority": 10,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": ">=",
              "parameters": {
                "leftOperand": "{dailyBundleCount}",
                "rightOperand": 30
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{targetAchievedDay}",
                "rightOperand": ""
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "targetAchievedDay",
          "assignmentTypeId": "SET",
          "value": "{currentDay}"
        }
      ]
    }
  ]
}
