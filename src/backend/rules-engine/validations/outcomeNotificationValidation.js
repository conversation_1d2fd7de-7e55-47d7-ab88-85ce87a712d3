import Joi from "joi";

const outcomeNotificationSchema = Joi.object({
  entityId: Joi.string()
    .uuid()
    .required(),
  contextId: Joi.string()
    .max(50)
    .pattern(/^[A-Z0-9_]+$/)
    .required(),
  transactionId: Joi.string()
    .max(50)
    .required(),
  timestamp: Joi.string()
    .isoDate()
    .required(),
  status: Joi.string()
    .valid("COMPLETED", "FAILED", "ABANDONED")
    .required(),
  transactionData: Joi.object().required(),
  modificationStatus: Joi.object({
    modificationsApplied: Joi.array().items(Joi.string()).required(),
    modificationsRejected: Joi.array().items(
      Joi.object({
        propertyId: Joi.string().required(),
        reason: Joi.string().required(),
      })
    ).required(),
  }).required(),
  additionalData: Joi.object().optional(),
});

export const validateOutcomeNotification = (req, res, next) => {
  const { error } = outcomeNotificationSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ error: error.details[0].message });
  }
  next();
};

