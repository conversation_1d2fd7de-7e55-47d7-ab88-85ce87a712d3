import <PERSON><PERSON> from "joi";
import { helperFunctions } from "../utils/helper.js";

// Common validation schema for name, type, and csvFile
const createListSchema = Joi.object({
    name: Joi.string()
        .max(70)
        .required()
        .messages({
            'string.empty': 'Name is required',
            'string.max': 'Name must be less than or equal to 70 characters',
        }),

    type: Joi.string()
        .valid('string')
        .messages({
            'any.only': 'Invalid type',
        }),

    text: Joi.string()
        .max(160)
        .required()
        .messages({
            'string.empty': 'Text is required',
            'string.max': 'Text must be less than or equal to 160 characters',
        }),
});


// Mapper object with the middleware functions for validation schemas based on list type
export const bulkNotificationValidationsMap = {
    CREATE_LIST: helperFunctions.joiValidationMiddleware(createListSchema)
};
