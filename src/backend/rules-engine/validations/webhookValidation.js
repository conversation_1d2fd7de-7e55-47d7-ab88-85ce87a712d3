import Joi from 'joi';
import systemConfigurations from "../config/systemConfigurations.js";


// Common regex
const camelCaseRegex = /^[a-z][a-zA-Z0-9]*$/;

const validUrls = systemConfigurations.webhooksAllowedUrls;


// Schema
const webhookSchema = Joi.object({
  webhookId: Joi.string().max(50).pattern(camelCaseRegex).required().messages({
    'string.pattern.base': '"webhookId" must be in camelCase format (e.g., "webhookId")',
  }),
  name: Joi.string().max(100).required(),
  description: Joi.string().max(250).required(),
  url: Joi.string().uri().max(500).required(),
  method: Joi.string().valid('GET', 'POST', 'PUT', 'DELETE', 'PATCH').required(),

  headers: Joi.object().pattern(
    Joi.string().required(), // header name
    Joi.string().required()  // header value
  ).default({}),

  bodyTemplate: Joi.object().pattern(
    Joi.string().min(1),                  // key: non-empty string
    Joi.alternatives().try(              // value: string | number | boolean
      Joi.string(),
      Joi.number(),
      Joi.boolean()
    )
  ).default({}),

  timeout: Joi.number().integer().strict().min(0).default(5000),

  retryPolicy: Joi.object({
    maxRetries: Joi.number().integer().strict().min(0).default(3),
    initialDelaySeconds: Joi.number().strict().integer().min(0).default(30),
    backoffMultiplier: Joi.number().strict().min(1).default(2.0),
  }).default(),

  securityLevel: Joi.string().valid('SYSTEM', 'ORGANIZATION', 'ENTITY').required(),

  parameters: Joi.array().items(
    Joi.object({
      parameterId: Joi.string().max(50).pattern(camelCaseRegex).required().messages({
        'string.pattern.base': '"parameterId" must be in camelCase format (e.g., "parameterId")',
      }),
      name: Joi.string().max(100).required(),
      description: Joi.string().max(250).required(),
      type: Joi.string().valid('string', 'number', 'boolean', 'date', 'object', 'array').required(),
      required: Joi.boolean().strict().required()
    })
  ).required()
});



const querySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1)
    .messages({
      'number.base': '"page" must be a number',
      'number.min': '"page" must be at least 1',
      'number.integer': '"page" must be an integer'
    }),

  pageSize: Joi.number().integer().min(1).max(100).default(20)
    .messages({
      'number.base': '"pageSize" must be a number',
      'number.min': '"pageSize" must be at least 1',
      'number.max': '"pageSize" cannot be more than 100',
      'number.integer': '"pageSize" must be an integer'
    }),

  securityLevel: Joi.string().valid('SYSTEM', 'ORGANIZATION', 'ENTITY')
    .optional()
    .messages({
      'any.only': '"securityLevel" must be one of [SYSTEM, ORGANIZATION, ENTITY]',
      'string.base': '"securityLevel" must be a string'
    }),
});

/**
 * Validate the webhook request body and prevent webhookId change in PUT requests
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 * @returns {Object} - The response object
 */
export const validateWebhook = (req, res, next) => {
  const { error } = webhookSchema.validate(req.body, { abortEarly: false });

  if (error) {
    const invalidFields = error.details.map(detail => detail.path.join('.'));

    return res.status(400).json({
      error: {
        code: "INVALID_WEBHOOK_CONFIG",
        message: error.message,
        details: {
          invalidFields
        }
      },
      requestId: req.requestId || "UNKNOWN_REQUEST_ID"
    });
  }

 // Step 1: Prevent webhookId change in PUT requests
 if (req.method === "PUT") {
  const pathWebhookId = req.params?.webhookId;
  const bodyWebhookId = req.body?.webhookId;

  if (pathWebhookId && bodyWebhookId && pathWebhookId !== bodyWebhookId) {
    return res.status(400).json({
      error: {
        code: "WEBHOOK_ID_MISMATCH",
        message: "Webhook ID in the path and body must match. Webhook ID cannot be changed.",
        details: {
          pathId: pathWebhookId,
          bodyId: bodyWebhookId
        }
      },
      requestId: req.requestId || "UNKNOWN_REQUEST_ID"
    });
  }
}


// Step 2: URL domain whitelist check
const { url } = req.body;

try {
  // const parsed = new URL(url); // safely parse URL
  // const allowed = validUrls.some(valid => parsed.href.startsWith(valid));
  const parsed = new URL(url); // safely parse the URL
  const fullUrl = parsed.origin + parsed.pathname; // just scheme + host + path (ignores query/hash)

  const allowed = validUrls.includes(fullUrl);
  if (!allowed) {
    return res.status(403).json({
      error: {
        code: "URL_NOT_ALLOWED",
        message: "The specified URL is not in the allowed domains list",
        details: {
          url,
          allowedDomains: validUrls
        }
      },
      requestId: req.requestId || "UNKNOWN_REQUEST_ID"
    });
  }
} catch (e) {
  // Should never happen, since Joi already checks URL format
  return res.status(400).json({
    error: {
      code: "INVALID_WEBHOOK_CONFIG",
      message: "URL is not a valid format",
      details: {
        invalidFields: ["url"]
      }
    },
    requestId: req.requestId || "UNKNOWN_REQUEST_ID"
  });
}




  next();
};

/**
 * Validate the get webhooks query parameters
 * @param {Object} req - The request object
 * @param {Object} res - The response object
 * @param {Function} next - The next middleware function
 * @returns {Object} - The response object
 */
export const validateGetWebhooksQuery = (req, res, next) => {
  const { error, value } = querySchema.validate(req.query, { abortEarly: false });

  if (error) {
    const invalidFields = error.details.map(detail => detail.path.join('.'));

    return res.status(400).json({
      error: {
        code: "INVALID_WEBHOOK_QUERY",
        message: error.message,
        details: {
          invalidFields
        }
      },
      requestId: req.requestId || "UNKNOWN_REQUEST_ID"
    });
  }

  req.query = value; // coerce and normalize query param types
  next();
};