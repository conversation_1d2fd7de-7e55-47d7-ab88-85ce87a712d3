import { validateRuleset } from './rulesetValidation.js';
import { describe,expect,jest,beforeEach,it } from '@jest/globals';

// Helper functions to create mock req and res objects.
const createMockReq = (body) => ({ body });
const createMockRes = () => {
  const res = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

describe('validateRuleset middleware', () => {
  let req, res, next;

  // A valid payload as defined by the ruleset schema in rulesetValidation.js.
  const validPayload = {
    schemaVersion: "6.0.0",
    rulesetId: "PRICING_RULES_2025",
    copiedFrom: {
      rulesetId: "PRICING_RULES_2024",
      version: 1
    },
    name: "Dynamic Pricing Rules 2025",
    description: "Adaptive pricing rules based on demand and customer segments",
    entityId: "550e8400-e29b-41d4-a716-446655440000",
    contextId: "PURCHASE",
    version: 1,
    status: "ACTIVE",
    category: "PRICING",
    startDateTime: "2025-06-01T00:00:00Z",
    endDateTime: "2025-12-31T23:59:59Z",
    lastModifiedDateTime: "2025-04-03T14:30:00Z",
    collectionMappings: [
      {
        collectionId: "customers",
        name: "Customers",
        keyMapping: { propertyId: "customerId" }
      }
    ],
    persistentVariables: [
      {
        variableId: "totalPurchases",
        name: "Total Purchases",
        description: "The total amount of purchases made by a customer",
        type: "number",
        defaultValue: 0,
        collectionId: "customers"
      }
    ],
    localVariables: [
      {
        variableId: "discountAmount",
        name: "Discount Amount",
        description: "Calculated discount for the current transaction",
        type: "number",
        defaultValue: 0
      }
    ],
    evaluationRules: [
      {
        ruleId: "HIGH_VALUE_PRICING",
        name: "High Value Customer Pricing",
        description: "Apply special pricing for high value customers",
        priority: 1,
        condition: {
          conditionTypeId: "LOGICAL",
          operator: "AND",
          parameters: {
            conditions: [
              {
                conditionTypeId: "COMPARISON",
                operator: ">",
                parameters: { leftOperand: "{productPrice}", rightOperand: 100 }
              }
            ]
          }
        },
        variableAssignments: [
          {
            variableId: "discountAmount",
            assignmentTypeId: "SET",
            value: 10
          }
        ]
      }
    ],
    outcomeRules: [
      {
        ruleId: "LOYALTY_POINTS_AWARD",
        name: "Loyalty Points Award",
        description: "Award loyalty points for completed transactions",
        priority: 1,
        condition: {
          conditionTypeId: "COMPARISON",
          operator: "==",
          parameters: { leftOperand: "{status}", rightOperand: "COMPLETED" }
        },
        variableAssignments: [
          {
            variableId: "totalPurchases",
            assignmentTypeId: "ADD",
            value: "{productPrice}"
          }
        ]
      }
    ]
  };

  beforeEach(() => {
    req = createMockReq(validPayload);
    res = createMockRes();
    next = jest.fn();
  });

  it('calls next() for a valid ruleset', () => {
    validateRuleset(req, res, next);
    expect(next).toHaveBeenCalled();
    expect(res.status).not.toHaveBeenCalled();
  });

  it('returns 400 when a required field is missing', () => {
    const invalidPayload = { ...validPayload };
    delete invalidPayload.schemaVersion;
    req = createMockReq(invalidPayload);

    validateRuleset(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(next).not.toHaveBeenCalled();
    expect(res.json).toHaveBeenCalled();
  });

  it('returns 400 for an invalid ISO date format', () => {
    const invalidPayload = { ...validPayload, startDateTime: "invalid-date" };
    req = createMockReq(invalidPayload);

    validateRuleset(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(next).not.toHaveBeenCalled();
    expect(res.json).toHaveBeenCalled();
  });

  it('returns 400 for duplicate ruleId across evaluationRules and outcomeRules', () => {
    const invalidPayload = { ...validPayload };
    // Duplicate ruleId in both rule collections.
    invalidPayload.evaluationRules[0].ruleId = "DUPLICATE_RULE";
    invalidPayload.outcomeRules[0].ruleId = "DUPLICATE_RULE";
    req = createMockReq(invalidPayload);

    validateRuleset(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(next).not.toHaveBeenCalled();
    expect(res.json).toHaveBeenCalled();
  });

  it('returns 400 for duplicate variable identifiers between persistent and local variables', () => {
    const invalidPayload = { ...validPayload };
    // Duplicate variableId in persistentVariables and localVariables.
    invalidPayload.persistentVariables[0].variableId = "duplicateVar";
    invalidPayload.localVariables[0].variableId = "duplicateVar";
    req = createMockReq(invalidPayload);

    validateRuleset(req, res, next);
    expect(res.status).toHaveBeenCalledWith(400);
    expect(next).not.toHaveBeenCalled();
    expect(res.json).toHaveBeenCalled();
  });
});