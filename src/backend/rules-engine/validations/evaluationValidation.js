

import Joi from "joi";

const transactionRequestSchema = Joi.object({
  // entityId must be a valid UUID string.
  entityId: Joi.string().uuid().required(),

  // contextId must be a string with a maximum of 50 characters and in UPPER_SNAKE_CASE.
  contextId: Joi.string()
    .max(50)
    .pattern(/^[A-Z0-9_]+$/)
    .required(),

  // transactionId must be a string with a maximum of 50 characters.
  transactionId: Joi.string().max(50).required(),

  // timestamp must be a valid ISO 8601 date string.
  timestamp: Joi.date().iso().required(),

  // transactionData must be an object with at least one property.
  // We allow arbitrary keys (each a string) with any type of value.
  transactionData: Joi.object()
    .min(1)
    .pattern(Joi.string(), Joi.any())
    .required(),
}).required();

export const validateEvaluation = (req, res, next) => {
  const { error } = transactionRequestSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ error: error.details[0].message });
  }
  next();
};
