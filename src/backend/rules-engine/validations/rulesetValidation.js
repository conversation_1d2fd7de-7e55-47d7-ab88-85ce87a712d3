import Joi from 'joi';

// Regex patterns for naming conventions
const camelCaseRegex = /^[a-z][a-zA-Z0-9]*$/;
const upperSnakeCaseRegex = /^[A-Z]+(?:_[A-Z0-9]+)*$/;
const semanticVersionRegex = /^\d+\.\d+\.\d+$/;

// Custom schema for validating conditions to enforce a maximum of 10 nested conditions when applicable
const conditionSchema = Joi.object({
  conditionTypeId: Joi.string().required(),
  operator: Joi.string().required(),
  parameters: Joi.object().custom((params, helpers) => {
    if (params.conditions && Array.isArray(params.conditions) && params.conditions.length > 10) {
      return helpers.error("any.custom", { message: "A maximum of 10 conditions are allowed per rule" });
    }
    return params;
  }).required()
}).required();

// Schema for variable assignments used in both rule collections
const variableAssignmentSchema = Joi.object({
  variableId: Joi.string().required(),
  assignmentTypeId: Joi.string().valid(
    "SET",
    "ADD",
    "SUBTRACT",
    "MULTIPLY",
    "DIVIDE",
    "INCREASE_BY_PERCENTAGE",
    "DECREASE_BY_PERCENTAGE"
  ).required(),
  value: Joi.any().required()
});
// Regex pattern for matching parameter values (allowing literals or property references like {propertyId})
const parameterValuePattern = Joi.alternatives().try(
  Joi.string().pattern(/^{[^{}]+}$/), // property reference like {propertyId}
  Joi.string(),                       // plain string literal
  Joi.number(),                       // number literal
  Joi.boolean(),                      // boolean literal
  Joi.object({                        // structured function call object
    functionId: Joi.string().required(),
    args: Joi.array().items(
      Joi.alternatives().try(
        Joi.string().pattern(/^{[^{}]+}$/), // property ref
        Joi.string(),
        Joi.number(),
        Joi.boolean()
      )
    ).required()
  })
);


// Schema for a single webhook call
const webhookCallSchema = Joi.object({
  webhookId: Joi.string().required(), // must be a valid webhook identifier
  parameters: Joi.object().pattern(
    Joi.string(),                    // key: parameterId (any string)
    parameterValuePattern            // value: string literal or {propertyId}
  ).required()
});
// Schema for a single rule inside evaluationRules and outcomeRules arrays
const ruleSchema = Joi.object({
  ruleId: Joi.string().max(50).pattern(upperSnakeCaseRegex).required(),
  name: Joi.string().max(100).required(),
  description: Joi.string().max(500).required(),
  priority: Joi.number().integer().required(),
  condition: conditionSchema,
  variableAssignments: Joi.array().items(variableAssignmentSchema).max(10).required(),
  actions: Joi.array().optional(),
  webhookCalls: Joi.array().items(webhookCallSchema).optional()
});

// Main schema for the rule set JSON
const rulesetSchema = Joi.object({
  schemaVersion: Joi.string().pattern(semanticVersionRegex).required(),
  rulesetId: Joi.string().max(50).pattern(upperSnakeCaseRegex).required(),
  copiedFrom: Joi.object({
    rulesetId: Joi.string().max(50).pattern(upperSnakeCaseRegex).required(),
    version: Joi.number().required()
}).optional(),
  name: Joi.string().max(100).required(),
  description: Joi.string().max(500).required(),
  entityId: Joi.string().guid({ version: ['uuidv4', 'uuidv5'] }).required(),
  contextId: Joi.string().max(50).pattern(upperSnakeCaseRegex).required(),
  version: Joi.number().integer().required(),
  status: Joi.string().valid("DRAFT", "ACTIVE", "PAUSED", "ARCHIVED").required(),
  category: Joi.string().max(50).required(),
  startDateTime: Joi.date().iso().required(),
  endDateTime: Joi.string().isoDate().required(),
  lastModifiedDateTime: Joi.string().isoDate().required(),
  collectionMappings: Joi.array().items(
    Joi.object({
      collectionId: Joi.string().max(50).pattern(camelCaseRegex).required(),
      name: Joi.string().max(100).required(),
      keyMapping: Joi.object({
        propertyId: Joi.string().max(50).pattern(camelCaseRegex).required()
      }).required()
    })
  ).required(),
  persistentVariables: Joi.array().items(
    Joi.object({
      variableId: Joi.string().max(50).pattern(camelCaseRegex).required(),
      name: Joi.string().max(100).required(),
      description: Joi.string().max(250).required(),
      type: Joi.string().valid("number", "string", "boolean", "date", "enum", "array").required(),
      defaultValue: Joi.any(),
      collectionId: Joi.string().required()
    })
  ).required(),
  localVariables: Joi.array().items(
    Joi.object({
      variableId: Joi.string().max(50).pattern(camelCaseRegex).required(),
      name: Joi.string().max(100).required(),
      description: Joi.string().max(250).required(),
      type: Joi.string().valid("number", "string", "boolean", "date", "enum", "array").required(),
      defaultValue: Joi.any()
    })
  ).required(),
  evaluationRules: Joi.array().items(ruleSchema).max(30).required(),
  outcomeRules: Joi.array().items(ruleSchema).max(30).required()
})
  // Custom validation to ensure ruleId uniqueness across evaluationRules and outcomeRules
  .custom((value, helpers) => {
    const evalRuleIds = value.evaluationRules.map(rule => rule.ruleId);
    const outcomeRuleIds = value.outcomeRules.map(rule => rule.ruleId);
    const allRuleIds = [...evalRuleIds, ...outcomeRuleIds];
    const uniqueRuleIds = new Set(allRuleIds);
    if (allRuleIds.length !== uniqueRuleIds.size) {
      return helpers.error("any.custom", { message: "ruleId must be unique across evaluationRules and outcomeRules" });
    }
    return value;
  })
  // Custom validation to ensure variableId uniqueness between persistent and local variables.
  .custom((value, helpers) => {
    const persistentIds = value.persistentVariables.map(variable => variable.variableId);
    const localIds = value.localVariables.map(variable => variable.variableId);
    const allVariableIds = [...persistentIds, ...localIds];
    const uniqueVariableIds = new Set(allVariableIds);
    if (allVariableIds.length !== uniqueVariableIds.size) {
      return helpers.error("any.custom", { message: "Variable identifiers must be unique across persistent and local variables" });
    }
    return value;
  });


export const validateRuleset = (req, res, next) => {
  const { error } = rulesetSchema.validate(req.body);
  if (error) {
    return res.status(400).json({ error: error.details[0].message });
  }
  next();
};
