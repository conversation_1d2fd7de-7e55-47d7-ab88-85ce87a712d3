import Joi from "joi";

const actionSchema = Joi.object({
  type: Joi.string().max(50).required(),
  name: Joi.string().max(100).required(),
  description: Joi.string().max(250).required(),
  parameters: Joi.array().items(
    Joi.object({
      name: Joi.string().max(50).required(),
      type: Joi.string()
        .valid("string", "number", "boolean", "date", "object", "array", "enum")
        .required(),
      description: Joi.string().max(250).required(),
      defaultValue: Joi.any().optional(),
      values: Joi.array().items(Joi.string()).optional(),
    }),
  ),
});

const propertySchema = Joi.object({
  propertyId: Joi.string().max(100).required(),
  name: Joi.string().max(100).required(),
  type: Joi.string()
    .valid("number", "string", "boolean", "date", "object", "array", "enum")
    .required(),
  description: Joi.string().max(250).required(),
  values: Joi.array().items(Joi.string()).optional(),
  mutable: Joi.boolean().required(),
  constraints: Joi.object().required().when("mutable", {
    is: true,
    then: Joi.required(),
    otherwise: Joi.forbidden(),
  }),
});

/* 
  *
  *
 Joi.object({
  mutable: Joi.boolean().required(),
  constraints: Joi.object({
    min: Joi.number().required(),
    max: Joi.number().required()
  }).when('mutable', {
    is: true,
    then: Joi.required(),
    otherwise: Joi.forbidden()
  })
}); 

  */

const transactionContextSchema = Joi.object({
  contextId: Joi.string().max(50).required(),
  name: Joi.string().max(100).required(),
  description: Joi.string().max(250).optional(),
  properties: Joi.array().items(propertySchema).max(50).required()
});

const entitySchema = Joi.object({
  name: Joi.string().max(100).required(),
  description: Joi.string().max(500).optional(),
  globalActions: Joi.array().items(actionSchema).max(50).optional(),
  transactionContexts: Joi.array()
    .items(transactionContextSchema)
    .max(20)
    .required(),
}).max(2 * 1024 * 1024); // 2MB size limit

export const validateRegistration = (req, res, next) => {
  const { error } = entitySchema.validate(req.body);
  if (error) {
    return res.status(400).json({ error: error.details[0].message });
  }
  next();
};
