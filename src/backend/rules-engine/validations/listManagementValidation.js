import Joi from "joi";

// Common validation schema for name, type, and csvFile
const commonValidationSchema = Joi.object({
  name: Joi.string().max(70).required().messages({
    "string.empty": "Name is required",
    "string.max": "Name must be less than or equal to 70 characters",
  }),

  type: Joi.string().valid("string", "number").required().messages({
    "string.empty": "Type is required",
    "any.only": "Invalid type",
  }),
});

/**
 * Validation schema for regular list (Uses common schema)
 */
const validateLookupList = commonValidationSchema;

/**
 * Factory function to create Joi validation middleware.
 * It returns middleware that validates using the passed Joi schema.
 */
const factoryMiddleware = (validationSchema) => {
  return (req, res, next) => {
    const { error } = validationSchema.validate(req.body, {
      abortEarly: false,
    }); // Validate the body with <PERSON><PERSON>
    if (error) {
      // Aggregate error details into a structured format
      const errors = error.details.map((err) => err.message);
      return res.status(400).json({ error: errors[0] });
    }
    next(); // Proceed to the next middleware if validation passes
  };
};

// Mapper object with the middleware functions for validation schemas based on list type
export const listValidation = factoryMiddleware(validateLookupList);
