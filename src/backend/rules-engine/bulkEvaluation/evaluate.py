import datetime
import json
import random
import uuid
import requests
import urllib3
import time
import concurrent.futures
import csv

num_requests = 10000
max_workers = 8


# Replace with your actual API token
API_TOKEN = (
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9."
    "eyJpZCI6MSwiaWF0IjoxNzMwODA0MzI5LCJleHAiOjE3NDExNzIzMjl9."
    "sLXVJtALE0wkBuqzoXnaUEBF3Hn_IUsKHPB2tx2QTbE"
)

# API endpoint URL
URL = "https://localhost:3000/api/v1/evaluate"

# Base JSON template
base_json = {
    "entityId": "CREDIVERSE",
    "transactionContextId": "WHOLESALE_AIRTIME_PURCHASE_POST",
    "transactionId": "",  # Will be filled with a unique value
    "timestamp": "",      # Will be filled with a random timestamp
    "transactionData": {
        "buyerAgentId": 0,   # Random between 8000 and 9000
        "buyerAgentMsisdn": "",
        "buyerAgentTierName": "",
        "buyerAgentGroups": [],
        "buyerAgentCgi": "62001",
        "buyerAgentZones": [],
        "buyerAgentLanguage": "en",
        "sellerAgentId": 0,  # Random between 6000 and 7000
        "sellerAgentMsisdn": "",
        "sellerAgentTierName": "",
        "sellerAgentGroups": [],
        "sellerAgentCgi": "62002",
        "sellerAgentZones": [],
        "sellerAgentLanguage": "en",
        "purchaseAmount": 0,            # Random between 100 and 1000
        "currentTradeBonusAmount": 0,   # 10% of purchaseAmount
        "adjustmentAccountDeduction": 0,
        "transactionChannel": "USSD",
    },
}

# Random tier names
tier_names = ["GOLD", "SILVER", "PLATINUM"]

# Start date for random timestamps
start_date = datetime.datetime(2024, 1, 1)
# End date is 40 days after start_date
end_date = start_date + datetime.timedelta(days=40)

# Number of requests to send

# Suppress warnings for insecure HTTPS requests (since we're using https://localhost)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


def send_request(i):
    """
    Constructs the JSON, sends the POST request,
    and returns (start_time, elapsed_time, status_code, response_text).
    """
    # Deep copy the base JSON template
    request_json = json.loads(json.dumps(base_json))

    # Generate random buyerAgentId and buyerAgentMsisdn
    buyer_agent_id = random.randint(8000, 9000)
    request_json["transactionData"]["buyerAgentId"] = buyer_agent_id
    request_json["transactionData"]["buyerAgentMsisdn"] = "091000" + str(buyer_agent_id)

    # Generate random sellerAgentId and sellerAgentMsisdn
    seller_agent_id = random.randint(6000, 7000)
    request_json["transactionData"]["sellerAgentId"] = seller_agent_id
    request_json["transactionData"]["sellerAgentMsisdn"] = "091000" + str(seller_agent_id)

    # Random purchaseAmount between 100 and 1000
    purchase_amount = random.randint(100, 1000)
    request_json["transactionData"]["purchaseAmount"] = purchase_amount

    # currentTradeBonusAmount is 10% of purchaseAmount
    request_json["transactionData"]["currentTradeBonusAmount"] = purchase_amount * 0.10
    request_json["transactionData"]["adjustmentAccountDeduction"] = (
        purchase_amount * random.uniform(0, 0.1)
    )

    # Randomly assign tier names
    request_json["transactionData"]["buyerAgentTierName"] = random.choice(tier_names)
    request_json["transactionData"]["sellerAgentTierName"] = random.choice(tier_names)

    # Generate random timestamp between start_date and end_date
    random_timestamp = start_date + (end_date - start_date) * random.random()
    request_json["timestamp"] = random_timestamp.strftime("%Y-%m-%dT%H:%M:%SZ")

    # Generate unique transactionId
    request_json["transactionId"] = str(uuid.uuid4())

    # Headers for the HTTP request
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {API_TOKEN}",
    }

    # Capture the "wall-clock" start time (for logging)
    wall_clock_start = datetime.datetime.now()

    # Measure the start time (for performance)
    perf_start = time.perf_counter()

    # Make the POST request
    response = requests.post(
        URL,
        headers=headers,
        json=request_json,
        verify=False,  # Skip SSL verification for localhost
    )

    # Calculate elapsed time
    elapsed_time = time.perf_counter() - perf_start

    # Return all relevant info
    return (
        wall_clock_start,       # start_time
        elapsed_time,           # how long the request took
        response.status_code,   # HTTP status
        response.text,          # response body
    )


def main():
    # We'll store tuples (start_time, elapsed_time, status_code, response_text) here
    transaction_times = []

    # Number of concurrent workers

    # Create a ThreadPoolExecutor to manage parallel calls
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit tasks and collect futures
        futures = [executor.submit(send_request, i) for i in range(num_requests)]

        # As each future completes, retrieve the results
        for future in concurrent.futures.as_completed(futures):
            try:
                start_time, elapsed_time, status_code, response_text = future.result()
                transaction_times.append((start_time, elapsed_time, status_code, response_text))
            except Exception as e:
                print(f"Request generated an exception: {e}")

    # Write all data to a CSV file
    # Columns: start_time (ISO8601), elapsed_time (seconds), status_code, response_body
    with open("transaction_times.csv", "w", encoding="utf-8", newline="") as f:
        writer = csv.writer(f)
        # Write CSV header
        writer.writerow(["start_time", "elapsed_time", "status_code", "response_body"])

        for (st, et, sc, rb) in transaction_times:
            writer.writerow([st.isoformat(), f"{et:.4f}", sc, rb])

    # Compute and print the summary stats (including TPS)
    if transaction_times:
        elapsed_times = [et for (_, et, _, _) in transaction_times]
        average_time = sum(elapsed_times) / len(elapsed_times)
        min_time = min(elapsed_times)
        max_time = max(elapsed_times)

        print(f"Average response time: {average_time:.4f} seconds")
        print(f"Minimum response time: {min_time:.4f} seconds")
        print(f"Maximum response time: {max_time:.4f} seconds")

        # Calculate transactions per second (TPS)
        # Using earliest start time and latest end time across all requests
        earliest_start_time = min(st for (st, _, _, _) in transaction_times)
        latest_end_time = max(st + datetime.timedelta(seconds=et) for (st, et, _, _) in transaction_times)
        total_duration = (latest_end_time - earliest_start_time).total_seconds()
        tps = len(transaction_times) / total_duration if total_duration > 0 else 0

        print(f"Transactions per second (TPS): {tps:.4f}")
    else:
        print("No valid response times were recorded.")


if __name__ == "__main__":
    main()

