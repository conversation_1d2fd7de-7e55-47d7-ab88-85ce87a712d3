services:
  ruleforge_cre_service:
    build: .
    container_name: ruleforge_cre_service
    extra_hosts:
      - "ecds-api:host-gateway"
    ports:
      - "3000:3000"
    depends_on:
      - redis_cluster_create
    volumes:
      - .:/usr/src/app
      - ./logs:/logs
    environment:
      - RF_SSL_ENABLED=true
      - REDIS_HOSTS=[{"host":"rf_redis_master_01","port":6379},{"host":"rf_redis_master_02","port":6379},{"host":"rf_redis_master_03","port":6379}]
      - LOG_LEVEL=INFO
      - INSTANCE_NAME=LC_DEV
      - JWT_PUBLIC_KEY=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUZrd0V3WUhLb1pJemowQ0FRWUlLb1pJemowREFRY0RRZ0FFaHRGQ0U3blo0RlhvZzMvN3lGRG5velRCSlhLdQpoNWpYWDI5T1NYOW0xbWxseWNMTlNzdE8vWWRDTk1oQmxmdXZnWjNuSWluUlNyWlBYQTRmQmZ1Wmd3PT0KLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg==
      - NOTIFICATION_SERVICE_URL=http://localhost:6000
    networks:
      - cluster

  rf_redis_master_01:
    image: redis/redis-stack:latest
    ports:
      - "6379:6379"
    healthcheck:
      test:
        ["CMD", "sh", "-c", "redis-cli cluster info | grep 'cluster_state:ok'"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      redis-server --port 6379 --cluster-enabled yes --cluster-config-file nodes.conf
      --cluster-node-timeout 5000 --appendonly yes --protected-mode no
      --cluster-announce-port 6379 --cluster-announce-bus-port 16379
    networks:
      cluster:
        aliases:
          - rf_redis_master_01

  rf_redis_master_02:
    image: redis/redis-stack:latest
    healthcheck:
      test:
        ["CMD", "sh", "-c", "redis-cli cluster info | grep 'cluster_state:ok'"]
      interval: 10s
      timeout: 5s
      retries: 5

    command: >
      redis-server --port 6379 --cluster-enabled yes --cluster-config-file nodes.conf
      --cluster-node-timeout 5000 --appendonly yes --protected-mode no
      --cluster-announce-port 6379 --cluster-announce-bus-port 16379
    networks:
      cluster:
        aliases:
          - rf_redis_master_02

  rf_redis_master_03:
    image: redis/redis-stack:latest
    healthcheck:
      test:
        ["CMD", "sh", "-c", "redis-cli cluster info | grep 'cluster_state:ok'"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      redis-server --port 6379 --cluster-enabled yes --cluster-config-file nodes.conf
      --cluster-node-timeout 5000 --appendonly yes --protected-mode no
      --cluster-announce-port 6379 --cluster-announce-bus-port 16379
    networks:
      cluster:
        aliases:
          - rf_redis_master_03

  rf_redis_slave_01:
    image: redis/redis-stack:latest
    healthcheck:
      test:
        ["CMD", "sh", "-c", "redis-cli -p 6380 cluster info | grep 'cluster_state:ok'"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      redis-server --port 6380 --cluster-enabled yes --cluster-config-file nodes.conf
      --cluster-node-timeout 5000 --appendonly yes --protected-mode no
      --cluster-announce-port 6380 --cluster-announce-bus-port 16380
    networks:
      cluster:
        aliases:
          - rf_redis_slave_01

  rf_redis_slave_02:
    image: redis/redis-stack:latest
    healthcheck:
      test:
        ["CMD", "sh", "-c", "redis-cli -p 6380 cluster info | grep 'cluster_state:ok'"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      redis-server --port 6380 --cluster-enabled yes --cluster-config-file nodes.conf
      --cluster-node-timeout 5000 --appendonly yes --protected-mode no
      --cluster-announce-port 6380 --cluster-announce-bus-port 16380
    networks:
      cluster:
        aliases:
          - rf_redis_slave_02

  rf_redis_slave_03:
    image: redis/redis-stack:latest
    healthcheck:
      test:
        ["CMD", "sh", "-c", "redis-cli -p 6380 cluster info | grep 'cluster_state:ok'"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: >
      redis-server --port 6380 --cluster-enabled yes --cluster-config-file nodes.conf
      --cluster-node-timeout 5000 --appendonly yes --protected-mode no
      --cluster-announce-port 6380 --cluster-announce-bus-port 16380
    networks:
      cluster:
        aliases:
          - rf_redis_slave_03

  redis_cluster_create:
    image: redis/redis-stack:latest
    depends_on:
      - rf_redis_master_01
      - rf_redis_master_02
      - rf_redis_master_03
      - rf_redis_slave_01
      - rf_redis_slave_02
      - rf_redis_slave_03
    entrypoint: >
      sh -c "sleep 10 &&
      echo yes | redis-cli --cluster create
      rf_redis_master_01:6379 \
      rf_redis_master_02:6379 \
      rf_redis_master_03:6379 \
      rf_redis_slave_01:6380 \
      rf_redis_slave_02:6380 \
      rf_redis_slave_03:6380 \
      --cluster-replicas 1"
    networks:
      - cluster

networks:
  cluster:
    external: true

