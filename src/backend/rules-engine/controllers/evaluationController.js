import EvaluationService from "../services/evaluationService.js";
import RulesetService from "../services/rulesetService.js";

import { logger } from "../middleware//logger.js";

import RedisClient from "../services/redisClient.js";
import { createLoggingProxy } from "../utils/loggingProxy.js";

export default class EvaluationController {
  constructor() {
    this.componentName = "EvaluationController";
    this.componentContext = [{ componentName: this.componentName }];

    this.service = createLoggingProxy(
      new EvaluationService(
        RedisClient.redisClient,
        new RulesetService(RedisClient.redisClient, this.componentContext)),
    );

    logger.info(this.componentContext, "Created the EvaluationController");
  }

  evaluate = async (req, res) => {
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "evaluate" },
    ];

    const evaluationService = new EvaluationService(
      RedisClient.redisClient,
      new RulesetService(RedisClient.redisClient, loggingContext),
    );

    try {
      const {
        entityId,
        contextId,
        transactionId,
        timestamp,
        transactionData,
      } = req.body;

      // Call the service to evaluate the transaction context
      const evalResponse = await evaluationService.evaluate(
        entityId,
        contextId,
        transactionId,
        new Date(timestamp),
        transactionData,
        loggingContext,
      );

      return res.status(200).json(evalResponse);
    } catch (error) {
      logger.error(loggingContext, error);
      return res.status(500).json({ error: `Internal Server Error: ${error}` });
    }
  };
}
