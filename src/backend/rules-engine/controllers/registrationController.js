import RegistrationService from "../services/registrationService.js";
import RedisClient from "../services/redisClient.js";

import { createLoggingProxy } from "../utils/loggingProxy.js";
import { logger } from "../middleware//logger.js";

export default class RegistrationController {
  constructor() {
    this.componentName = "RegistrationController";
    this.componentContext = [{ componentName: this.componentName }];

    this.service = createLoggingProxy(
      new RegistrationService(RedisClient.redisClient, this.componentContext),
    );

    logger.info(
      [...this.componentContext, { methodName: "constructor" }],
      "created the RegistrationController",
    );
  }

  registerEntity = async (req, res) => {
    const requestContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "registerEntity" },
    ];

    try {
      const entityData = req.body;
      const result = await this.service.registerEntity(
        entityData,
        requestContext,
      );
      res.status(201).json(result);
    } catch (error) {
      logger.error(requestContext, error);
      res.status(400).json({ error: error.message });
    }
  };

  getEntity = async (req, res) => {
    const requestContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "getEntity" },
    ];

    try {
      const { entityId } = req.params;
      const result = await this.service.getEntity(entityId, requestContext);
      res.status(200).json(result);
    } catch (error) {
      res.status(404).json({ error: error.message });
      logger.error(requestContext, error);
    }
  };

  getEntities = async (req, res) => {
    const requestContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "getEntities" },
    ];
    try {
      const result = await this.service.getEntities(requestContext);
      res.status(200).json(result);
    } catch (error) {
      res.status(404).json({ error: error.message });
      logger.warn(requestContext, error);
    }
  };

  updateEntity = async (req, res) => {
    const requestContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "updateEntity" },
    ];
    try {
      const { entityId } = req.params;
      const entityData = req.body;
      const result = await this.service.updateEntity(
        entityId,
        entityData,
        requestContext,
      );
      res.status(200).json(result);
    } catch (error) {
      res.status(404).json({ error: error.message });
      logger.warn(requestContext, error);
    }
  };

  deleteEntity = async (req, res) => {
    const requestContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "deleteEntity" },
    ];
    try {
      const { entityId } = req.params;
      const result = await this.service.deleteEntity(entityId, requestContext);
      res.status(200).json(result);
    } catch (error) {
      res.status(404).json({ error: error.message });
      logger.warn(requestContext, error);
    }
  };
}
