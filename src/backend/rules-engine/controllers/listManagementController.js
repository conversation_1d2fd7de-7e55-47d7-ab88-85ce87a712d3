import ListManagementService from "../services/listManagementService.js";
import RedisClient from "../services/redisClient.js";
import CSVProcessingService from "../utils/csvProcessing.js";
import createCsvStream from "../utils/csvStreamService.js";
import { createLoggingProxy } from "../utils/loggingProxy.js";

import { logger } from "../middleware/logger.js";

export default class ListManagementController {
  /**
   *
   * @param {import('../services/listManagementService').ListTypesEnum} listType
   */
  constructor(callerContext) {
    this.componentName = "ListManagementController";
    this.componentContext = [
      ...callerContext,
      { coponentName: this.componentName },
    ];

    // Ready the list management service based on the list type
    /**
     * @type {ListManagementService}
     */
    this.service = createLoggingProxy(
      new ListManagementService(RedisClient.redisClient, this.componentContext)
    );
  }

  /**
   * Route: Creates a list based on the provided request data.
   *
   * @param {import('express').Request<{}, {}, RegularListBody>} req - The request object containing the list details.
   * @param {import('express').Response} res - The response object used to send back the HTTP response.
   *
   * @description
   * Extracts the name, type, and CSV file from the request body, processes the CSV file,
   * and saves the list to the database. Responds with a success message upon completion.
   */
  createList = async (req, res) => {
    logger.info(
      [...this.componentContext, { action: "createList" }],
      "creating a new list"
    );
    const listParams = req.body;

    logger.info([...this.componentContext, { action: "createList" }], {
      listParams,
    });
    // Check for duplicate list name
    try {
      const isDuplicate = await this.service.checkDuplicateListName(
        listParams.name
      );
      if (isDuplicate) {
        return res.status(409).json({
          error: {
            code: "DUPLICATE_LIST_NAME",
            message: "A list with this name already exists",
            details: {
              name: listParams.name,
            },
          },
          requestId: req.requestId || "unknown",
        });
      }
    } catch (error) {
      logger.error([...this.componentContext, { action: "createList" }], error);
      return res.status(500).json({
        error: `Failed to check for duplicate list name: ${error.message}`,
      });
    }
    const csvFileBuffer = req.file.buffer;
    /** Csv rows */
    let elements = [];

    // Process the CSV file
    try {
      const listData = await CSVProcessingService.extractColumnData(
        csvFileBuffer
      );
      elements = listData;

      // Add the count of elements to the list params
      listParams.elemsCount = elements.length;
    } catch (error) {
      logger.info([...this.componentContext, { action: "createList" }], error);

      return res
        .status(400)
        .json({ error: `Failed to process CSV file: ${error.message}` });
    }

    // Call the service to save the list
    try {
      const createdList = await this.service.createList(listParams, elements);
      res.status(201).json({
        listId: createdList.listId,
        name: createdList.name,
        type: createdList.type || "string",
        elementsCount: createdList.elemsCount,
        message: "List successfully created",
      });
    } catch (error) {
      console.error(error);
      return res
        .status(500)
        .json({ error: `Failed to save list: ${error.message}` });
    }
  };

  /**
   * Route: Get all lists from the database based on listType. Sends a JSON response containing the list of lists.
   * @param {import('express').Request} req
   * @param {import('express').Response} res
   *
   */
  getAllLists = async (req, res) => {
    try {
      const lists = await this.service.getAllLists();
      res.status(200).json(lists);
    } catch (error) {
      console.error(`Error fetching ${this.listType} lists from Redis:`, error);
      res.status(500).json({
        error: `Failed to fetch ${this.listType} lists: ${error.message}`,
      });
    }
  };

  /**
   * Get list details by the specified ID.
   * @param {import('express').Request} req - The request object containing the list ID in params.
   * @param {import('express').Response} res - The response object used to send the list data.
   */
  getListById = async (req, res) => {
    const listId = req.params.id;

    try {
      const listData = await this.service.getListById(listId);
      if (!listData) {
        return res.status(404).json({
          error: {
            code: "LIST_NOT_FOUND",
            message: "The specified list was not found",
            details: {
              listId: listId,
            },
          },
          requestId: req.requestId || "unknown",
        });
      }

      res.status(200).json(listData);
    } catch (error) {
      logger.error(
        [...this.componentContext, { action: "getListById" }],
        error
      );
      res.status(500).json({
        error: `Failed to fetch list: ${error.message}`,
      });
    }
  };

  /**
   * Delete the list with the specified ID.
   * @param {import('express').Request} req
   * @param {import('express').Response} res
   */
  deleteListById = async (req, res) => {
    const listId = req.params.id;

    try {
      await this.service.deleteListById(listId);
      res
        .status(200)
        .json(`${this.listType} list with ID ${listId} deleted successfully`);
    } catch (error) {
      console.error(
        `Error deleting ${this.listType} list with ID ${listId} from Redis:`,
        error
      );
      res
        .status(500)
        .json(`Failed to delete ${this.listType} list: ${error.message}`);
    }
  };

  /**
   * Download the csv file for the list with the specified ID.
   * @param {import('express').Request} req
   * @param {import('express').Response} res
   * @returns
   */
  downloadListById = async (req, res) => {
    try {
      const listId = req.params.id;
      const listData = await this.service.getListById(listId, true);

      if (!listData) {
        return res.status(404).json({
          error: {
            code: "LIST_NOT_FOUND",
            message: "The specified list was not found",
            details: {
              listId: listId,
            },
          },
          requestId: req.requestId || "unknown",
        });
      }
      // Set the response headers
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=${listData.name}.csv`
      );

      const headers = [];
      /** @type {[Array.<string>]} */
      const rows = listData.elements;

      // Create csv stream and pipe it to the response
      const csvStream = createCsvStream(rows, headers);
      csvStream.pipe(res);
    } catch (error) {
      console.error(error);
      res.status(500).send("Error generating CSV");
    }
  };
}

// ------ Doc ------ //

/**
 * @typedef {"string"} paramType
 */

/** The request body params for creating a regular list.
 * @typedef {Object} RegularListBody
 * @property {string} name - The name of the regular list.
 * @property {paramType} type - The type of the list
 */

/**
 * @typedef {Object} BulkNotificationListBody
 * @property {string} name - The name of the regular list.
 * @property {paramType} type - The type of the list
 * @property {string} msgText - The message text for the bulk notification.
 */

/**
 * @typedef {{lists: {listId: string,name: string,type: paramType,elementCount: number}[],totalCount: number,page: number,pageSize: number}} RegularListObject
 */

/**
 * @typedef {{listId,name,type,elementCount}[]} RegularListArray number,page: number,pageSize: number} RegularListArray
 */

/**
 * @typedef {{id: string, name: string, type: paramType, msgText: string}[]} BulkNotificationListArray
 */
