import RulesetService from "../services/rulesetService.js";
import RedisClient from "../services/redisClient.js";

import { createLoggingProxy } from "../utils/loggingProxy.js";
import { logger } from "../middleware//logger.js";

export default class RulesetController {
  constructor() {
    this.componentName = "RulesetController";
    this.componentContext = [{ componentName: this.componentName }];

    /** @type {RulesetService} */
    this.service = createLoggingProxy(
      new RulesetService(RedisClient.redisClient, this.componentContext),
    );
  }

  activateRuleset = async (req, res) => {
    const { rulesetId, version } = req.params;

    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "activateRuleset" },
      { rulesetId },
      { version },
    ];

    this.service
      .activateRuleset(rulesetId, version, loggingContext)
      .then((result) => {
        res.status(200).json(result);
      })
      .catch((error) => {
        logger.error(loggingContext, JSON.stringify(error));
        res.status(404).json({ error: error.message });
      });
  };

  deactivateRuleset = async (req, res) => {
    const { rulesetId, version } = req.params;

    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "deactivateRuleset" },
      { rulesetId },
      { version },
    ];
    this.service
      .deactivateRuleset(rulesetId, version, loggingContext)
      .then((result) => {
        res.status(200).json(result);
      })
      .catch((error) => {
        logger.error(loggingContext, JSON.stringify(error));
        res.status(404).json({ error: error.message });
      });
  };

  getRuleset = async (req, res) => {
    const { rulesetId } = req.params;
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "getRuleset" },
      { rulesetId },
    ];

    try {
      const ruleset = await this.service.getRuleset(
        rulesetId,
        null,
        loggingContext,
      );
      return res.status(200).json(ruleset);
    } catch (error) {
      logger.error(loggingContext, JSON.stringify(error));
      return res.status(404).json({ error: error.message });
    }
  };

  copyRuleset = async (req, res) => {
    const { existingRulesetId, existingVersion, newRulesetId } = req.params;
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "getRuleset" },
      { existingRulesetId },
      { existingVersion },
      { newRulesetId },
    ];

    try {
      const result = await this.service.copyRuleset(
        existingRulesetId,
        existingVersion,
        newRulesetId,
        loggingContext,
      );
      return res.status(200).json(result);
    } catch (error) {
      logger.error(loggingContext, JSON.stringify(error));
      return res.status(404).json({ error: error.message });
    }
  };

  saveRuleset = async (req, res) => {
    const rulesetData = req.body;

    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "saveRuleset" },
      { rulesetId: rulesetData.rulesetId },
      { version: rulesetData.version },
    ];

    try {
      // Process and save the ruleset data using the service
      const result = await this.service.saveRuleset(
        rulesetData,
        loggingContext,
      );

      return res.status(201).json(result);
    } catch (error) {
      logger.error(loggingContext, JSON.stringify(error));
      return res.status(500).json({ error: `Internal Server Error: ${error}` });
    }
  };

  getRulesets = async (req, res) => {
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "getRulesets" },
    ];

    try {
      const rulesets = await this.service.getAllRulesets(loggingContext);

      return res.status(200).json(rulesets);
    } catch (error) {
      logger.error(loggingContext, JSON.stringify(error));
      return res.status(500).json({ error: `Internal Server Error: ${error}` });
    }
  };

  deleteRuleset = async (req, res) => {
    const { rulesetId } = req.params;
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "deleteRulesets" },
    ];

    try {
      const result = await this.service.deleteRuleset(
        rulesetId,
        loggingContext,
      );
      return res.status(200).json(result);
    } catch (error) {
      logger.error(loggingContext, JSON.stringify(error));
      return res.status(404).json({ error: error.message });
    }
  };
}
