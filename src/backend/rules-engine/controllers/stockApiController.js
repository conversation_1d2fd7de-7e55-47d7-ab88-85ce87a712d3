import { logger } from "../middleware/logger.js";
import StockApiService from "../services/stockApiService.js";
import redisClient from "../services/redisClient.js";

/**
 * Controller for Stock API operations
 */
class StockApiController {
  constructor() {
    this.componentName = "StockApiController";
    this.componentContext = [{ componentName: this.componentName }];
  }

  /**
   * Register a new stock API
   */
  async registerApi(req, res) {
    const requestId = req.headers["x-request-id"] || "unknown";
    const loggingContext = [
      ...this.componentContext,
      { functionName: "registerApi" },
      { requestId }
    ];

    try {
      const apiDefinition = req.body;
      
      // Validate required fields
      if (!apiDefinition.apiId || !apiDefinition.name || !apiDefinition.type) {
        return res.status(400).json({
          error: "Missing required fields: apiId, name, type",
          requestId
        });
      }

      const stockApiService = new StockApiService(redisClient, loggingContext);
      const result = await stockApiService.registerApi(apiDefinition, loggingContext);

      logger.info(loggingContext, `API registered successfully: ${apiDefinition.apiId}`);
      
      res.status(201).json({
        message: "API registered successfully",
        apiId: result.apiId,
        requestId
      });

    } catch (error) {
      logger.error(loggingContext, `Failed to register API: ${error.message}`);
      
      if (error.message.includes("already exists")) {
        return res.status(409).json({
          error: error.message,
          requestId
        });
      }

      res.status(500).json({
        error: "Internal server error",
        requestId
      });
    }
  }

  /**
   * Get all registered APIs
   */
  async getApis(req, res) {
    const requestId = req.headers["x-request-id"] || "unknown";
    const loggingContext = [
      ...this.componentContext,
      { functionName: "getApis" },
      { requestId }
    ];

    try {
      const stockApiService = new StockApiService(redisClient, loggingContext);
      
      // Get all API IDs from index
      const apiIds = await redisClient.smembers("stockApi:definitions:index");
      
      // Get all API definitions
      const apis = await Promise.all(
        apiIds.map(async (apiId) => {
          return await stockApiService.getApiDefinition(apiId, loggingContext);
        })
      );

      // Filter out null results
      const validApis = apis.filter(api => api !== null);

      res.json({
        apis: validApis,
        count: validApis.length,
        requestId
      });

    } catch (error) {
      logger.error(loggingContext, `Failed to get APIs: ${error.message}`);
      res.status(500).json({
        error: "Internal server error",
        requestId
      });
    }
  }

  /**
   * Get specific API by ID
   */
  async getApiById(req, res) {
    const requestId = req.headers["x-request-id"] || "unknown";
    const { apiId } = req.params;
    const loggingContext = [
      ...this.componentContext,
      { functionName: "getApiById" },
      { requestId },
      { apiId }
    ];

    try {
      const stockApiService = new StockApiService(redisClient, loggingContext);
      const apiDefinition = await stockApiService.getApiDefinition(apiId, loggingContext);

      if (!apiDefinition) {
        return res.status(404).json({
          error: `API not found: ${apiId}`,
          requestId
        });
      }

      res.json({
        api: apiDefinition,
        requestId
      });

    } catch (error) {
      logger.error(loggingContext, `Failed to get API: ${error.message}`);
      res.status(500).json({
        error: "Internal server error",
        requestId
      });
    }
  }

  /**
   * Store credentials for a ruleset
   */
  async storeCredentials(req, res) {
    const requestId = req.headers["x-request-id"] || "unknown";
    const loggingContext = [
      ...this.componentContext,
      { functionName: "storeCredentials" },
      { requestId }
    ];

    try {
      const { rulesetId, accountRef, credentials } = req.body;
      
      // Validate required fields
      if (!rulesetId || !accountRef || !credentials) {
        return res.status(400).json({
          error: "Missing required fields: rulesetId, accountRef, credentials",
          requestId
        });
      }

      // Validate credentials structure
      const requiredCredFields = ["apiUsername", "apiSecret", "accountUsername", "accountPassword"];
      for (const field of requiredCredFields) {
        if (!credentials[field]) {
          return res.status(400).json({
            error: `Missing required credential field: ${field}`,
            requestId
          });
        }
      }

      const stockApiService = new StockApiService(redisClient, loggingContext);
      await stockApiService.storeCredentials(rulesetId, accountRef, credentials, loggingContext);

      logger.info(loggingContext, `Credentials stored for ruleset: ${rulesetId}`);
      
      res.status(201).json({
        message: "Credentials stored successfully",
        rulesetId,
        accountRef,
        requestId
      });

    } catch (error) {
      logger.error(loggingContext, `Failed to store credentials: ${error.message}`);
      res.status(500).json({
        error: "Internal server error",
        requestId
      });
    }
  }

  /**
   * Get masked credentials for a ruleset
   */
  async getCredentials(req, res) {
    const requestId = req.headers["x-request-id"] || "unknown";
    const { rulesetId } = req.params;
    const { accountRef = "primary" } = req.query;
    const loggingContext = [
      ...this.componentContext,
      { functionName: "getCredentials" },
      { requestId },
      { rulesetId },
      { accountRef }
    ];

    try {
      const credentialsKey = `stockApi:credentials:${rulesetId}`;
      const credentialsData = await redisClient.get(credentialsKey);
      
      if (!credentialsData) {
        return res.status(404).json({
          error: `Credentials not found for ruleset: ${rulesetId}`,
          requestId
        });
      }

      const parsed = JSON.parse(credentialsData);
      const accountCredentials = parsed.accounts?.[accountRef];
      
      if (!accountCredentials) {
        return res.status(404).json({
          error: `Credentials not found for account: ${accountRef}`,
          requestId
        });
      }

      // Return masked credentials (don't expose secrets)
      const maskedCredentials = {
        apiUsername: accountCredentials.apiUsername,
        apiSecret: "***masked***",
        accountUsername: accountCredentials.accountUsername,
        accountPassword: "***masked***",
        provider: accountCredentials.provider
      };

      res.json({
        rulesetId,
        accountRef,
        credentials: maskedCredentials,
        createdAt: parsed.createdAt,
        updatedAt: parsed.updatedAt,
        requestId
      });

    } catch (error) {
      logger.error(loggingContext, `Failed to get credentials: ${error.message}`);
      res.status(500).json({
        error: "Internal server error",
        requestId
      });
    }
  }

  /**
   * Execute a stock transfer
   */
  async transferStock(req, res) {
    const requestId = req.headers["x-request-id"] || "unknown";
    const loggingContext = [
      ...this.componentContext,
      { functionName: "transferStock" },
      { requestId }
    ];

    try {
      const transferRequest = req.body;
      
      // Validate required fields
      const requiredFields = ["fromAccount", "toAccount", "amount", "apiId", "rulesetId"];
      for (const field of requiredFields) {
        if (transferRequest[field] === undefined || transferRequest[field] === null) {
          return res.status(400).json({
            error: `Missing required field: ${field}`,
            requestId
          });
        }
      }

      // Set default account reference if not provided
      if (!transferRequest.accountRef) {
        transferRequest.accountRef = "primary";
      }

      const stockApiService = new StockApiService(redisClient, loggingContext);
      const result = await stockApiService.transferStock(transferRequest, loggingContext);

      if (result.success) {
        logger.info(loggingContext, `Stock transfer completed: ${result.transferId}`);
        res.status(200).json({
          ...result,
          requestId
        });
      } else {
        logger.warn(loggingContext, `Stock transfer failed: ${result.error}`);
        res.status(400).json({
          ...result,
          requestId
        });
      }

    } catch (error) {
      logger.error(loggingContext, `Stock transfer error: ${error.message}`);
      res.status(500).json({
        error: "Internal server error",
        requestId
      });
    }
  }
}

export default new StockApiController();
