import systemConfigurations from "../config/systemConfigurations.js";
import RegistrationController from "./registrationController.js";

import RegistrationService from "../services/registrationService.js";
import RedisClient from "../services/redisClient.js";

import { createLoggingProxy } from "../utils/loggingProxy.js";
import { logger } from "../middleware//logger.js";

export default class SystemConfigurationController {
    constructor() {
        this.componentName = "SystemConfigurationController";
        this.componentContext = [{ componentName: this.componentName }];
        this.registrationController = new RegistrationController();

        /** @type {RegistrationService} */
        this.registrationService = createLoggingProxy(
            new RegistrationService(RedisClient.redisClient, this.componentContext),
          );
      
          logger.info(
            [...this.componentContext, { methodName: "constructor" }],
            "created the SystemConfigurationController",
          );

    }


    /**
     * This Function will get all condition types
     * @param {Object} req 
     * @param {Object} res 
     * @returns 
     */
    conditionTypes = async (req, res) => {
        const result =  {
            "conditionTypes": systemConfigurations.conditionTypes
        }
        return res.status(200).json(result);
    }


    /**
     * This Function will get all variable assignments
     * @param {Object} req 
     * @param {Object} res 
     * @returns 
     */
    variableAssignments = async (req, res) => {
        const result = {
            "variableAssignments": systemConfigurations.variablesAssignments
        }
        return res.status(200).json(result);
    }

    /**
     * This Function will get all functions
     * @param {Object} req 
     * @param {Object} res 
     * @returns 
     */
    functions = async (req, res) => {
        const result = {
            "functions": systemConfigurations.functions
        }
        return res.status(200).json(result);
    }


    /**
     * This Function will get all standard properties
     * @param {Object} req 
     * @param {Object} res 
     * @returns 
     */
    standardProperties = async (req, res) => {
        const result = {
            "standardProperties": systemConfigurations.standardProperties
        }
        return res.status(200).json(result);
    }

    /**
     * This Function will get all entities
     * @param {Object} req 
     * @param {Object} res 
     * @returns 
     */
    entities = async (req, res) => {


        const requestContext = [
            { requestId: req.requestId },
            ...this.componentContext,
            { functionName: "getEntities" },
          ];
          try {
            const entities   = await this.registrationService.getEntities(requestContext);
            const result = {
                "entities": entities,
                "totalCount": entities.length,
                "page": req.query.page || 1,
                "pageSize": req.query.pageSize || entities.length
            }
            res.status(200).json(result);
            
          } catch (error) {
            res.status(404).json({ error: error.message });
            logger.warn(requestContext, error);
          }

      
    }

    /**
     * This Function will get all config schema
     * @param {Object} req 
     * @param {Object} res 
     * @returns 
     */
    configSchema = async (req, res) => {
        const result = {
            "conditionTypes": systemConfigurations.conditionTypes,
            "variableAssignments": systemConfigurations.variablesAssignments,
            "functions": systemConfigurations.functions,
            "standardProperties": systemConfigurations.standardProperties,
            "webhooksAllowedUrls": systemConfigurations.webhooksAllowedUrls,
            "systemVersion": systemConfigurations.systemVersion
        }
        return res.status(200).json(result);
    }

    

}