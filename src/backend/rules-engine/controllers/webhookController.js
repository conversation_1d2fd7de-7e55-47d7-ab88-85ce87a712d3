import WebhookService from "../services/webhookService.js";
import RedisClient from "../services/redisClient.js";
import { createLoggingProxy } from "../utils/loggingProxy.js";
import { logger } from "../middleware/logger.js";

export default class WebhookController {
  constructor() {
    this.componentName = "WebhookController";
    this.componentContext = [{ componentName: this.componentName }];

    this.service = createLoggingProxy(
      new WebhookService(RedisClient.redisClient, this.componentContext)
    );
  }
  /**
   * This Function will register a webhook in the Redis database
   * @param {Object} req The request object
   * @param {Object} res The response object
   * @returns {Object}
   */
  registerWebhook = async (req, res) => {
    const webhook = req.body;
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "registerWebhook" },
      { webhookId: webhook.webhookId },
    ];

    try {
      const result = await this.service.registerWebhook(
        webhook,
        req.requestId,
        loggingContext
      );
      
      return res.status(201).json(result);
    } catch (error) {
      logger.error(loggingContext, error);
      if (error.isCustomError) {
        return res.status(error.statusCode).json(error.error);
      }
      return res.status(500).json({ error: error.message });
    }
  };
  /**
   * This Function will get all webhooks from the Redis database
   * @param {Object} req The request object
   * @param {Object} res The response object
   * @returns {Object}
   */
  getWebhooks = async (req, res) => {
    const { page = 1, pageSize = 20, securityLevel } = req.query;
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "getWebhooks" },
    ];

    try { 
      const result = await this.service.getWebhooks(
        +page,
        +pageSize,
        securityLevel,
        req.requestId,
        loggingContext
      );
      return res.status(200).json(result);
    } catch (error) {
      logger.error(loggingContext, error);
      if (error.isCustomError) {
        return res.status(error.statusCode).json(error.error);
      }
      return res.status(500).json({ error: error.message });
    }
  };
  /**
   * This Function will get a webhook by its ID from the Redis database
   * @param {Object} req The request object
   * @param {Object} res The response object
   * @returns {Object}
   */
  getWebhookById = async (req, res) => {
    const { webhookId } = req.params;
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "getWebhookById" },
      { webhookId },
    ];

    try {
      const result = await this.service.getWebhookById(
        webhookId,
        req.requestId,
        loggingContext
      );
      return res.status(200).json(result);
    } catch (error) {
      logger.error(loggingContext, error);
      console.log("error", error);
      if (error.isCustomError) {
        return res.status(error.statusCode).json(error.error);
      }
      return res.status(500).json({ error: error.message });
    }
  };
  /**
   * This Function will update a webhook in the Redis database
   * @param {Object} req The request object
   * @param {Object} res The response object
   * @returns {Object}
   */
  updateWebhook = async (req, res) => {
    const { webhookId } = req.params;
    const webhook = req.body;
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "updateWebhook" },
      { webhookId },
    ];

    try {
      const result = await this.service.updateWebhook(
        webhookId,
        webhook,
        req.requestId,
        loggingContext
      );
      return res.status(200).json(result);
    } catch (error) {
      logger.error(loggingContext, error);
      if (error.isCustomError) {
        return res.status(error.statusCode).json(error.error);
      }
      return res.status(500).json({ error: error.message });
    }
  };
  /**
   * This Function will delete a webhook from the Redis database
   * @param {Object} req The request object
   * @param {Object} res The response object
   * @returns {Object}
   */
  deleteWebhook = async (req, res) => {
    const { webhookId } = req.params;
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "deleteWebhook" },
      { webhookId },
    ];

    try {
      const result = await this.service.deleteWebhook(
        webhookId,
        req.requestId,
        loggingContext
      );
      return res.status(204).send();
    } catch (error) {
      logger.error(loggingContext, error);
      if (error.isCustomError) {
        return res.status(error.statusCode).json(error.error);
      }
      return res.status(500).json({ error: error.message });
    }
  };
  /**
   * This Function will test a webhook
   * @param {Object} req The request object
   * @param {Object} res The response object
   * @returns {Object}
   */
  testWebhook = async (req, res) => {
    const webhookData = req.body;

    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "testWebhook" },
      { webhookId: webhookData.webhookId },
    ];

    try {
      // Process and save the ruleset data using the service
      const result = await this.service.testWebhook(
        webhookData,
        loggingContext
      );

      return res.status(201).json(result);
    } catch (error) {
      logger.error(loggingContext, JSON.stringify(error));
      return res.status(500).json({ error: `Internal Server Error: ${error}` });
    }
  };
}

/**
 * @param {Object} error
 * @param {number} error.statusCode The HTTP status code to return
 * @param {string} error.code The error code to return
 * @param {string} error.message The error message to return
 * @param {string} error.requestId The request ID to return
 * @param {Object} error.details The details to return
 * @returns {Object}
 */
export const formatErrorResponse = ({
  statusCode = 500,
  code = "INTERNAL_SERVER_ERROR",
  message = "Something went wrong",
  requestId = "UNKNOWN_REQUEST_ID",
  details = {},
}) => {
  return {
    statusCode,
    isCustomError: true,
    error: {
      error: {
        code,
        message,
        details,
      },
      requestId,
    },
  };
};
