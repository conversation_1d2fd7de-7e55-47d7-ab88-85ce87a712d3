import OutcomeNotificationService from "../services/outcomeNotificationService.js";
import RulesetService from "../services/rulesetService.js";


import RedisClient from "../services/redisClient.js";
import { createLoggingProxy } from "../utils/loggingProxy.js";
import { logger } from "../middleware/logger.js";

export default class OutcomesController {
  constructor() {
    this.componentName = "OutcomesController";
    this.componentContext = [{ componentName: this.componentName }];

    // Wrap the service with the logging proxy for consistent logging
    this.service = createLoggingProxy(
      new OutcomeNotificationService(
        RedisClient.redisClient, 
        new RulesetService(RedisClient.redisClient, this.componentContext),
        this.componentContext));


    logger.info(
      [...this.componentContext, { methodName: "constructor" }],
      "created the OutcomesController"
    );
  }

  // Handles the processing of an outcome notification
  processOutcomeNotification = async (req, res) => {
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { functionName: "processOutcomeNotification evaluate" },
    ];

    try {
      const {
        entityId,
        contextId,
        transactionId,
        timestamp,
        transactionData,
        status,
        modificationStatus
      } = req.body;


      // Call the service to evaluate the transaction context
      this.service.processOutcomeNotification(
        entityId,
        contextId,
        transactionId,
        new Date(timestamp),
        transactionData,
        status,
        modificationStatus.modificationsApplied,
        loggingContext,
      ).then((results)=>{
        logger.info(loggingContext,results);
      }).catch((error)=>{
        logger.error(loggingContext, error);
      });

      return res.status(202).json({
        "transactionId": transactionId,
        "message": "Outcome notification received"
      });
    } catch (error) {
      logger.error(loggingContext, error);
      return res.status(500).json({ error: `Internal Server Error: ${error}` });
    }
  };
}

