import settings from "../config/settings.js";
import BulkNotificationService from "../services/bulkNotificationService.js";
import RedisClient from "../services/redisClient.js";
import SmsQueueService from "../services/smsQueueService.js";
import CSVProcessingService from "../utils/csvProcessing.js";
import createCsvStream from "../utils/csvStreamService.js";
import { createLoggingProxy } from "../utils/loggingProxy.js";
import { logger } from "../middleware//logger.js";

class BulkNotificationController {
  constructor() {
    this.componentName = "BulkNotificationController";
    this.componentContext = [{ componentName: this.componentName }];
    /**
     * Bulk notification service instance
     * @type {BulkNotificationService}
     */
    this.service = createLoggingProxy(
      new BulkNotificationService(
        RedisClient.redisClient,
        this.componentContext,
      ),
    );

    /**
     * SMS queue service instance
     * @type {SmsQueueService}
     */
    this.SmsQueueService = new SmsQueueService();
  }

  /**
   * Route: Creates a list based on the provided request data.
   *
   * @param {import('express').Request<{}, {}, BulkNotificationListBody>} req - The request object containing the list details.
   * @param {import('express').Response} res - The response object used to send back the HTTP response.
   *
   * @description
   * Extracts the name, type, and CSV file from the request body, processes the CSV file,
   * and saves the list to the database. Responds with a success message upon completion.
   */
  createList = async (req, res) => {
    const listParams = req.body;
    const csvFileBuffer = req.file.buffer;
    /** Csv rows */
    let elements = [];

    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { method: "createList" },
      { listName: listParams.name },
    ];

    // Process the CSV file
    try {
      const listData =
        await CSVProcessingService.extractColumnData(csvFileBuffer);
      elements = listData;

      // Add the count of elements to the list params
      listParams.elemsCount = elements.length;
    } catch (error) {
      logger.error(loggingContext`Error processing CSV file:`, error);
      return res
        .status(400)
        .json({ error: `Failed to process CSV file: ${error.message}` });
    }

    // Call the service to save the list
    try {
      await this.service.createList(listParams, elements, loggingContext);
    } catch (error) {
      logger.error(loggingContext, error);
      return res
        .status(500)
        .json({ error: `Failed to save list: ${error.message}` });
    }

    res.status(201).json({ message: `List created successfully` });
  };

  /**
   * Route: Get all lists from the database based on. Sends a JSON response containing the list of lists.
   * @param {import('express').Request} req
   * @param {import('express').Response} res
   *
   */
  getAllLists = async (req, res) => {
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { method: "getAllLists" },
    ];
    logger.info(loggingContext, "getting all lists");
    try {
      const lists = await this.service.getAllLists(loggingContext);
      res.status(200).json(lists);
    } catch (error) {
      logger.error(loggingContext, error);
      res
        .status(500)
        .json({ error: `Failed to fetch lists: ${error.message}` });
    }
  };

  /**
   * Delete the list with the specified ID.
   * @param {import('express').Request} req
   * @param {import('express').Response} res
   */
  deleteListById = async (req, res) => {
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { method: "deleteListById" },
    ];
    const listId = req.params.id;

    try {
      await this.service.deleteListById(listId, loggingContext);
      res.status(200).json(`List with ID ${listId} deleted successfully`);
    } catch (error) {
      logger.error(loggingContext, error);
      res.status(500).json(`Failed to delete list: ${error.message}`);
    }
  };

  /**
   * Download the csv file for the list with the specified ID.
   * @param {import('express').Request} req
   * @param {import('express').Response} res
   * @returns
   */
  downloadListById = async (req, res) => {
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { method: "downloadListById" },
    ];
    try {
      const listId = req.params.id;
      const listData = await this.service.getListById(
        listId,
        true,
        loggingContext,
      );

      if (!listData) {
        return res.status(404).send(`List with ID ${listId} not found`);
      }

      // Set the response headers
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename=${listData.name}.csv`,
      );

      const headers = [];
      /** @type {[Array.<string>]} */
      const rows = listData.elements;

      // Create csv stream and pipe it to the response
      const csvStream = createCsvStream(rows, headers);
      csvStream.pipe(res);
    } catch (error) {
      logger.error(loggingContext, error);
      res.status(500).send("Error generating CSV");
    }
  };

  /**
   * Function to send the bulk SMS to the list
   * @param {import('express').Request} req
   * @param {import('express').Response} res
   * @returns
   */
  sendBulkSms = async (req, res) => {
    const loggingContext = [
      { requestId: req.requestId },
      ...this.componentContext,
      { method: "sendBulkSms" },
    ];
    try {
      // Get the list ID from the request
      const listId = req.params.id;

      // Get the data from the database
      const dataT = await this.service.getListById(
        listId,
        true,
        loggingContext,
      );

      // The msidin data
      const msidinData = dataT.elements;

      // If the data is empty, return an error
      if (!Array.isArray(msidinData) || msidinData.length === 0) {
        return res.status(404).json({ message: "No data found" });
      }

      // Prepare the message queue entries
      const smsQueueEntries = prepareSmsQueueEntries(msidinData, dataT.text);

      // Get the sms queue settings
      const batchSize = settings.smsQueue.batchSize;
      const batchDelay = settings.smsQueue.batchDelay;

      // Prepare the batches
      const batches = prepareBatchesData(smsQueueEntries, batchSize);

      // Process the batches with delay
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        const resultT =
          await this.SmsQueueService.bulkCreateQueueEntries(batch);
        
        await new Promise((resolve) => setTimeout(resolve, batchDelay));
      }

      // All good so return the success response
      return res
        .status(200)
        .json({ message: "Bulk notification sent successfully" });
    } catch (error) {
      logger.error(loggingContext, error);
      return res.status(500).json({ message: error.message });
    }
  };
}

export default BulkNotificationController;

// ------ Doc ------ //

/**
 * @typedef {"string"} paramType
 */

/**
 * @typedef {Object} BulkNotificationListBody
 * @property {string} name - The name of the regular list.
 * @property {paramType} type - The type of the list
 * @property {string} text - The message text for the bulk notification.
 */

/**
 * @typedef {{id: string, name: string, type: paramType, text: string}[]} BulkNotificationListArray
 */

// ======================== Sms notifications Helper functions ========================

/**
 * Prepare the sms queue entries array
 * @param {String[]} msidins
 * @param {string} messageText
 * @returns {import("../models/smsQueue/SmsQueueModel.js").SmsQueueModel[]}
 */
function prepareSmsQueueEntries(msidins, messageText) {
  return msidins.map(
    /**
     * @param {string} msidin - The msidin to process.
     * @returns {import("../models/smsQueue/SmsQueueModel.js").SmsQueueModel} An object containing the phone number and the message.
     */
    (msidin) => ({
      destination_msisdn: msidin,
      message: messageText,
      // Generate unique id for the transaction by timestamp and msidin number
      transaction_id: generateTid(msidin),
    }),
  );
}

/**
 * Prepare the array of batches. For example, if `smsQueueEntries` has 1000 elements and `batchSize` is 100,
 * the result will be an array of 10 arrays, each with 100 elements.
 *
 * @param {import("../models/smsQueue/SmsQueueModel.js").SmsQueueModel[]} smsQueueEntries - The array of SMS queue entries to be split into batches.
 * @param {number} batchSize - The number of elements in each batch.
 * @returns {Array<Array.<import("../models/smsQueue/SmsQueueModel.js").SmsQueueModel>>} An array of batches, where each batch is an array of `batchSize` elements.
 */
function prepareBatchesData(smsQueueEntries, batchSize) {
  // Validate inputs
  if (!Array.isArray(smsQueueEntries)) {
    throw new Error("smsQueueEntries must be an array.");
  }
  if (typeof batchSize !== "number" || batchSize <= 0) {
    throw new Error("batchSize must be a positive number.");
  }

  // Prepare batches
  const batches = [];
  for (let i = 0; i < smsQueueEntries.length; i += batchSize) {
    const batch = smsQueueEntries.slice(i, i + batchSize);
    batches.push(batch);
  }

  return batches;
}

/**
 * Generate a unique transaction ID based on the current timestamp and the last 4 digits of the MSISDN.
 *
 * @param {string|number} msisdn - The MSISDN (phone number) to use for generating the transaction ID.
 * @returns {number} The generated transaction ID.
 */
function generateTid(msisdn) {
  const timestamp = Date.now(); // Current timestamp in milliseconds
  const lastDigits = String(msisdn).slice(-4); // Extract the last 4 digits of the MSISDN
  return Number(`${timestamp}${lastDigits}`); // Concatenate and return as a number
}
