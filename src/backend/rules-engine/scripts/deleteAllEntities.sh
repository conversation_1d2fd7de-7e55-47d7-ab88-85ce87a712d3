#!/bin/bash

# Usage: ./delete_entities.sh [entityId]

REDIS_HOST="localhost" # Change if your Redis host is different
REDIS_PORT=6379        # Change if your Redis port is different

if [ -z "$1" ]; then
  # No entityId provided, delete all entities
  echo "Deleting all entities..."

  # Get all keys (assuming all entity keys match a specific pattern, e.g., "ENTITY:*")
  keys=$(redis-cli -h $REDIS_HOST -p $REDIS_PORT KEYS "*")

  if [ -z "$keys" ]; then
    echo "No entities found."
  else
    # Delete all keys
    echo "$keys" | xargs redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL
    echo "All entities deleted."
  fi
else
  # Delete a specific entity
  entityId=$1
  echo "Deleting entity with ID: $entityId"

  redis-cli -h $REDIS_HOST -p $REDIS_PORT DEL $entityId

  if [ $? -eq 0 ]; then
    echo "Entity $entityId deleted."
  else
    echo "Failed to delete entity $entityId."
  fi
fi
