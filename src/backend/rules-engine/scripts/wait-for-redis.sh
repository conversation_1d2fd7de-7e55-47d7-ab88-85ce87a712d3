#!/bin/bash
# wait-for-redis.sh
#
# Usage: ./wait-for-redis.sh <command to run when ready>
# Expects REDIS_HOSTS env variable set to a JSON array of objects with "host" and "port"
#
# Example REDIS_HOSTS:
#   [{"host":"rf_redis_master_01","port":6379},{"host":"rf_redis_master_02","port":6379},{"host":"rf_redis_master_03","port":6379}]

CMD="$@"
TIMEOUT=120

echo "Checking if Redis cluster is ready for all hosts specified in REDIS_HOSTS..."

# Ensure jq is available
if ! command -v jq >/dev/null 2>&1; then
  echo "jq is required but not installed. Please install jq."
  exit 1
fi

# Ensure REDIS_HOSTS is set
if [ -z "$REDIS_HOSTS" ]; then
  echo "REDIS_HOSTS is not set."
  exit 1
fi

# Read the hosts into an array so that each line (host and port) is a single element.
readarray -t nodes <<< "$(echo "$REDIS_HOSTS" | jq -r '.[] | "\(.host) \(.port)"')"

while [ $TIMEOUT -gt 0 ]; do
  ALL_READY=true
  for node in "${nodes[@]}"; do
    # Split the node string into host and port
    host=$(echo "$node" | awk '{print $1}')
    port=$(echo "$node" | awk '{print $2}')
    echo "Checking ${host}:${port}..."
    output=$(redis-cli -h "$host" -p "$port" cluster info 2>/dev/null)
    echo "Output from ${host}:${port}: $output"
    if echo "$output" | grep -q "cluster_state:ok"; then
      echo "${host}:${port} is ready."
    else
      echo "${host}:${port} is not ready."
      ALL_READY=false
    fi
  done

  if [ "$ALL_READY" = true ]; then
    echo "All Redis nodes are ready."
    exec $CMD
  fi

  sleep 1
  TIMEOUT=$((TIMEOUT - 1))
done

echo "Timed out waiting for all Redis nodes."
exit 1
