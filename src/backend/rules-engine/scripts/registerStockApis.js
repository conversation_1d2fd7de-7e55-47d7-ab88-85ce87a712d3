#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to register default stock APIs
 * 
 * This script demonstrates how to register stock APIs that can be used
 * by the GUI to compose rules with stock transfer operations.
 */

import StockApiService from "../services/stockApiService.js";
import redisClient from "../services/redisClient.js";
import { logger } from "../middleware/logger.js";

const callerContext = [{ componentName: "registerStockApis" }];

/**
 * Default stock API definitions
 */
const defaultStockApis = [
  {
    apiId: "crediverse-stock-transfer",
    name: "Crediverse Stock Transfer",
    description: "Transfer stock/airtime using Crediverse platform",
    type: "stockTransfer",
    provider: "crediverse",
    version: "1.0.0",
    endpoints: {
      transfer: "/api/account/transaction/transfer",
      balance: "/api/account/balance",
      history: "/api/account/history"
    },
    authentication: {
      type: "oauth2",
      tokenEndpoint: "/oauth/token",
      requiredFields: [
        "apiUsername",
        "apiSecret", 
        "accountUsername",
        "accountPassword"
      ]
    },
    parameters: [
      {
        name: "fromAccount",
        type: "string",
        required: true,
        description: "Source account identifier"
      },
      {
        name: "toAccount", 
        type: "string",
        required: true,
        description: "Target account identifier (MSISDN)"
      },
      {
        name: "amount",
        type: "number",
        required: true,
        description: "Amount to transfer",
        validation: {
          min: 0.01,
          max: 10000
        }
      },
      {
        name: "stockType",
        type: "string",
        required: false,
        default: "airtime",
        description: "Type of stock to transfer",
        options: ["airtime", "data", "voice", "sms"]
      }
    ],
    capabilities: [
      "transfer",
      "balance_check",
      "transaction_history"
    ],
    limits: {
      maxTransferAmount: 10000,
      dailyTransferLimit: 100000,
      rateLimitPerMinute: 60
    }
  },
  {
    apiId: "generic-stock-transfer",
    name: "Generic Stock Transfer",
    description: "Generic stock transfer API for multiple providers",
    type: "stockTransfer",
    provider: "generic",
    version: "1.0.0",
    endpoints: {
      transfer: "/api/stock/transfer",
      balance: "/api/stock/balance"
    },
    authentication: {
      type: "bearer_token",
      requiredFields: ["apiKey"]
    },
    parameters: [
      {
        name: "fromAccount",
        type: "string", 
        required: true,
        description: "Source account identifier"
      },
      {
        name: "toAccount",
        type: "string",
        required: true,
        description: "Target account identifier"
      },
      {
        name: "amount",
        type: "number",
        required: true,
        description: "Amount to transfer"
      },
      {
        name: "stockType",
        type: "string",
        required: false,
        default: "default",
        description: "Type of stock to transfer"
      },
      {
        name: "currency",
        type: "string",
        required: false,
        default: "USD",
        description: "Currency for the transfer"
      }
    ],
    capabilities: [
      "transfer",
      "balance_check"
    ]
  }
];

/**
 * Sample credentials for demonstration
 */
const sampleCredentials = [
  {
    rulesetId: "sample-ruleset-001",
    accountRef: "primary",
    credentials: {
      apiUsername: "ruleforge9",
      apiSecret: "67c321580cd94076a6d1fa95f0833723",
      accountUsername: "ruleforge",
      accountPassword: "97aaa688",
      provider: "crediverse"
    }
  },
  {
    rulesetId: "sample-ruleset-002", 
    accountRef: "primary",
    credentials: {
      apiUsername: "ruleforge8",
      apiSecret: "another-secret-key-here",
      accountUsername: "test_account",
      accountPassword: "test_password",
      provider: "crediverse"
    }
  }
];

/**
 * Register all default stock APIs
 */
async function registerDefaultApis() {
  logger.info(callerContext, "Starting stock API registration");
  
  try {
    const stockApiService = new StockApiService(redisClient, callerContext);
    
    for (const apiDef of defaultStockApis) {
      try {
        await stockApiService.registerApi(apiDef, callerContext);
        logger.info(callerContext, `Successfully registered API: ${apiDef.apiId}`);
      } catch (error) {
        if (error.message.includes("already exists")) {
          logger.warn(callerContext, `API already exists: ${apiDef.apiId}`);
        } else {
          logger.error(callerContext, `Failed to register API ${apiDef.apiId}: ${error.message}`);
        }
      }
    }
    
    logger.info(callerContext, "Stock API registration completed");
    
  } catch (error) {
    logger.error(callerContext, `Stock API registration failed: ${error.message}`);
    throw error;
  }
}

/**
 * Store sample credentials for testing
 */
async function storeSampleCredentials() {
  logger.info(callerContext, "Storing sample credentials");
  
  try {
    const stockApiService = new StockApiService(redisClient, callerContext);
    
    for (const credData of sampleCredentials) {
      try {
        await stockApiService.storeCredentials(
          credData.rulesetId,
          credData.accountRef,
          credData.credentials,
          callerContext
        );
        logger.info(callerContext, `Successfully stored credentials for: ${credData.rulesetId}`);
      } catch (error) {
        logger.error(callerContext, `Failed to store credentials for ${credData.rulesetId}: ${error.message}`);
      }
    }
    
    logger.info(callerContext, "Sample credentials storage completed");
    
  } catch (error) {
    logger.error(callerContext, `Sample credentials storage failed: ${error.message}`);
    throw error;
  }
}

/**
 * List all registered APIs
 */
async function listRegisteredApis() {
  logger.info(callerContext, "Listing registered APIs");
  
  try {
    const apiIds = await redisClient.smembers("stockApi:definitions:index");
    
    if (apiIds.length === 0) {
      logger.info(callerContext, "No APIs registered");
      return;
    }
    
    const stockApiService = new StockApiService(redisClient, callerContext);
    
    for (const apiId of apiIds) {
      const apiDef = await stockApiService.getApiDefinition(apiId, callerContext);
      if (apiDef) {
        logger.info(callerContext, {
          apiId: apiDef.apiId,
          name: apiDef.name,
          type: apiDef.type,
          provider: apiDef.provider,
          version: apiDef.version,
          createdAt: apiDef.createdAt
        });
      }
    }
    
  } catch (error) {
    logger.error(callerContext, `Failed to list APIs: ${error.message}`);
    throw error;
  }
}

/**
 * Main execution function
 */
async function main() {
  const command = process.argv[2] || "register";
  
  try {
    switch (command) {
      case "register":
        await registerDefaultApis();
        break;
      case "credentials":
        await storeSampleCredentials();
        break;
      case "list":
        await listRegisteredApis();
        break;
      case "all":
        await registerDefaultApis();
        await storeSampleCredentials();
        await listRegisteredApis();
        break;
      default:
        logger.info(callerContext, "Usage: node registerStockApis.js [register|credentials|list|all]");
        break;
    }
    
    logger.info(callerContext, "Script completed successfully");
    process.exit(0);
    
  } catch (error) {
    logger.error(callerContext, `Script failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { registerDefaultApis, storeSampleCredentials, listRegisteredApis };
