{"name": "rules-engine-service", "version": "1.0.0", "type": "module", "main": "app.mjs", "directories": {"test": "tests"}, "scripts": {"test": "node --experimental-vm-modules node_modules/jest/bin/jest.js --detectOpenHandles"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@babel/parser": "^7.25.6", "@babel/traverse": "^7.25.6", "bignumber.js": "^9.1.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "crypto": "^1.0.1", "csv-parser": "^3.0.0", "eslint": "^9.19.0", "express": "^4.21.0", "express-validator": "^7.2.0", "ioredis": "^5.4.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.11.4", "node": "^18.20.6", "node-fetch": "^3.3.2", "redlock": "^5.0.0-beta.2", "sequelize": "^6.37.5", "spdy": "^4.0.2", "uuid": "^11.0.5", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/generator": "^7.25.6", "@babel/preset-env": "^7.26.7", "@babel/types": "^7.25.6", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.14.0", "axios": "^1.7.7", "axios-mock-adapter": "^2.0.0", "babel-jest": "^29.7.0", "globals": "^15.12.0", "ioredis-mock": "^8.9.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "joi": "^17.13.3", "supertest": "^7.0.0", "testcontainers": "^10.16.0"}}