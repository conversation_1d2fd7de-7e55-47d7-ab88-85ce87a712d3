import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";


// Load SSL certificates
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const privateKey = fs.readFileSync(path.join(__dirname, "../certs", "server.key"), "utf8");
const certificate = fs.readFileSync(path.join(__dirname, "../certs", "server.crt"), "utf8");
// const ca = fs.readFileSync(path.join(__dirname, "../certs", "ca.crt"), "utf8"); // Optional, for certificate chain

export const httpsOptions = {
  key: privateKey,
  cert: certificate,
  allowHTTP1: true,    // fallback for older clients
  // ca: ca, // Include CA if using a certificate chain
};
