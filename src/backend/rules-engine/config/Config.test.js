import { jest, expect, describe, beforeEach, afterAll,it } from "@jest/globals";

// __tests__/config.test.js
import fs from "fs";
import path from "path";

const CONFIG_PATH = path.resolve(process.cwd(), "config.json");

describe("Config.js — full variable set", () => {
  const ALL_KEYS = [
    "NODE_ENV",
    "RF_PORT",
    "RF_SSL_ENABLED",
    "RF_SSL_CERT_PATH",
    "RF_SSL_KEY_PATH",
    "REDIS_HOSTS",
    "ECDS_API_PORT",
    "ECDS_API_URL",
    "INSTANCE_NAME",
    "LOG_LEVEL",
    "JWT_PUBLIC_KEY",
    "SMS_QUEUE_BATCH_SIZE",
    "SMS_QUEUE_BATCH_DELAY",
    "SMS_QUEUE_DB_HOST",
    "SMS_QUEUE_DB_NAME",
    "SMS_QUEUE_DB_PASSWORD",
    "SMS_QUEUE_DB_PORT",
    "SMS_QUEUE_DB_USERNAME",
    "SMS_QUEUE_TABLE_NAME",
  ];

  beforeEach(() => {
    jest.resetModules();
    ALL_KEYS.forEach((k) => delete process.env[k]);
    if (fs.existsSync(CONFIG_PATH)) fs.unlinkSync(CONFIG_PATH);
  });

  afterAll(() => {
    if (fs.existsSync(CONFIG_PATH)) fs.unlinkSync(CONFIG_PATH);
  });

  it(" defaults for everything else", async () => {

    const { default: C } = await import("../config/Config.js");

    expect(C.JWT_PUBLIC_KEY).toBe("the-key");
    expect(C.NODE_ENV).toBe("development");
    expect(C.RF_PORT).toBe(3000);
    expect(C.RF_SSL_ENABLED).toBe(false);
    expect(C.RF_SSL_CERT_PATH).toBe("");
    expect(C.RF_SSL_KEY_PATH).toBe("");
    expect(C.REDIS_HOSTS).toBe('[{"host":"ruleforge_cre_db","port":6379}]');
    expect(C.ECDS_API_PORT).toBe(9084);
    expect(C.ECDS_API_URL).toBe("http://ecds-api");
    expect(C.INSTANCE_NAME).toBe("-");
    expect(C.LOG_LEVEL).toBe("info");
    expect(C.SMS_QUEUE_BATCH_SIZE).toBe(100);
    expect(C.SMS_QUEUE_BATCH_DELAY).toBe(1000);
    expect(C.SMS_QUEUE_DB_HOST).toBe("ruleforge_cre_journal_db");
    expect(C.SMS_QUEUE_DB_NAME).toBe("ruleforge_cre_journal_db");
    expect(C.SMS_QUEUE_DB_PASSWORD).toBe("ussdgw");
    expect(C.SMS_QUEUE_DB_PORT).toBe(3306);
    expect(C.SMS_QUEUE_DB_USERNAME).toBe("root");
    expect(C.SMS_QUEUE_TABLE_NAME).toBe("smsq_queue");
  });

  it("env vars override defaults", async () => {
    process.env.JWT_PUBLIC_KEY = Buffer.from("foo").toString("base64");
    process.env.NODE_ENV = "production";
    process.env.RF_PORT = "4000";
    process.env.RF_SSL_ENABLED = "true";
    process.env.RF_SSL_CERT_PATH = "/a.cert";
    process.env.RF_SSL_KEY_PATH = "/a.key";
    process.env.REDIS_HOSTS = "[]";
    process.env.ECDS_API_PORT = "1234";
    process.env.ECDS_API_URL = "https://xyz";
    process.env.INSTANCE_NAME = "my-inst";
    process.env.LOG_LEVEL = "debug";
    process.env.SMS_QUEUE_BATCH_SIZE = "55";
    process.env.SMS_QUEUE_BATCH_DELAY = "2000";
    process.env.SMS_QUEUE_DB_HOST = "db-host";
    process.env.SMS_QUEUE_DB_NAME = "db-name";
    process.env.SMS_QUEUE_DB_PASSWORD = "pw";
    process.env.SMS_QUEUE_DB_PORT = "123";
    process.env.SMS_QUEUE_DB_USERNAME = "admin";
    process.env.SMS_QUEUE_TABLE_NAME = "tbl";

    const { default: C } = await import("../config/Config.js");

    expect(C.NODE_ENV).toBe("production");
    expect(C.RF_PORT).toBe(4000);
    expect(C.RF_SSL_ENABLED).toBe(true);
    expect(C.RF_SSL_CERT_PATH).toBe("/a.cert");
    expect(C.RF_SSL_KEY_PATH).toBe("/a.key");
    expect(C.REDIS_HOSTS).toBe("[]");
    expect(C.ECDS_API_PORT).toBe(1234);
    expect(C.ECDS_API_URL).toBe("https://xyz");
    expect(C.INSTANCE_NAME).toBe("my-inst");
    expect(C.LOG_LEVEL).toBe("debug");
    expect(C.SMS_QUEUE_BATCH_SIZE).toBe(55);
    expect(C.SMS_QUEUE_BATCH_DELAY).toBe(2000);
    expect(C.SMS_QUEUE_DB_HOST).toBe("db-host");
    expect(C.SMS_QUEUE_DB_NAME).toBe("db-name");
    expect(C.SMS_QUEUE_DB_PASSWORD).toBe("pw");
    expect(C.SMS_QUEUE_DB_PORT).toBe(123);
    expect(C.SMS_QUEUE_DB_USERNAME).toBe("admin");
    expect(C.SMS_QUEUE_TABLE_NAME).toBe("tbl");
  });

  it("config.json only affects RF_PORT and SSL settings", async () => {
    process.env.JWT_PUBLIC_KEY = Buffer.from("ok").toString("base64");
    process.env.NODE_ENV = "production";
    fs.writeFileSync(
      CONFIG_PATH,
      JSON.stringify({
        production: {
          server: {
            port: 8443,
            ssl: { enabled: true, cert: "/c.crt", key: "/c.key" }
          }
        }
      }),
      "utf8"
    );

    const { default: C } = await import("../config/Config.js");

    expect(C.RF_PORT).toBe(8443);
    expect(C.RF_SSL_ENABLED).toBe(true);
    expect(C.RF_SSL_CERT_PATH).toBe("/c.crt");
    expect(C.RF_SSL_KEY_PATH).toBe("/c.key");

    // Other values unchanged from defaults
    expect(C.ECDS_API_PORT).toBe(9084);
    expect(C.SMS_QUEUE_DB_HOST).toBe("ruleforge_cre_journal_db");
  });
});

