const systemVersion = "1.0.0";

// ============ Condition Types ============

const conditionTypes = [
  {
    conditionTypeId: "COMPARISON",
    name: "Comparison",
    description: "Compares two values using a specified operator",
    applicableTypes: ["string", "number", "boolean", "date", "enum"],
    parameters: [
      {
        parameterId: "leftOperand",
        name: "Left Operand",
        type: "any",
        description: "The left-hand side of the comparison",
        required: true,
      },
      {
        parameterId: "rightOperand",
        name: "Right Operand",
        type: "any",
        description: "The right-hand side of the comparison",
        required: true,
      },
    ],
    operators: [
      "==",
      "!=",
      "<",
      "<=",
      ">",
      ">=",
      "IN",
      "NOT_IN",
      "CONTAINS",
      "STARTS_WITH",
      "ENDS_WITH",
    ],
  },
  {
    conditionTypeId: "LOGICAL",
    name: "Logical",
    description: "Combines multiple conditions with a logical operator",
    applicableTypes: ["boolean"],
    parameters: [
      {
        parameterId: "conditions",
        name: "Conditions",
        type: "array",
        description: "The conditions to combine",
        required: true,
      },
    ],
    operators: ["AND", "OR", "NOT"],
  },
];

// ============ End Condition Types ============

// ============= Variables Assignments =============

const variablesAssignments = [
  {
    assignmentTypeId: "SET",
    name: "Set Value",
    description: "Sets the variable to the specified value",
    applicableTypes: ["string", "number", "boolean", "date", "enum"],
  },
  {
    assignmentTypeId: "ADD",
    name: "Add",
    description: "Adds the specified value to the variable",
    applicableTypes: ["number"],
  },
  {
    assignmentTypeId: "SUBTRACT",
    name: "Subtract",
    description: "Subtracts the specified value from the variable",
    applicableTypes: ["number"],
  },
  {
    assignmentTypeId: "MULTIPLY",
    name: "Multiply",
    description: "Multiplies the variable by the specified value",
    applicableTypes: ["number"],
  },
  {
    assignmentTypeId: "DIVIDE",
    name: "Divide",
    description: "Divides the variable by the specified value",
    applicableTypes: ["number"],
  },
  {
    assignmentTypeId: "INCREASE_BY_PERCENTAGE",
    name: "Increase by Percentage",
    description: "Increases the variable by the specified percentage",
    applicableTypes: ["number"],
  },
  {
    assignmentTypeId: "DECREASE_BY_PERCENTAGE",
    name: "Decrease by Percentage",
    description: "Decreases the variable by the specified percentage",
    applicableTypes: ["number"],
  },
];

// ============= End Variables Assignments =============

// ============== Functions ==============

const functions = [
  {
    functionId: "dayOfWeek",
    name: "Day of Week",
    description: "Returns the day of the week for a given date",
    parameters: [
      {
        parameterId: "date",
        name: "Date",
        type: "date",
        description: "The date to get the day of week for",
      },
    ],
    returnType: "string",
    returnValues: [
      "MONDAY",
      "TUESDAY",
      "WEDNESDAY",
      "THURSDAY",
      "FRIDAY",
      "SATURDAY",
      "SUNDAY",
    ],
    examples: [
      {
        reference: {
          functionId: "dayOfWeek",
          args: ["2025-03-28T00:00:00Z"],
        },
        result: "FRIDAY",
      },
    ],
  },
  {
    functionId: "formatDate",
    name: "Format Date",
    description: "Formats a date according to the specified pattern",
    parameters: [
      {
        parameterId: "date",
        name: "Date",
        type: "date",
        description: "The date to format",
      },
      {
        parameterId: "pattern",
        name: "Pattern",
        type: "string",
        description:
          "The format pattern (follows Java SimpleDateFormat syntax)",
      },
    ],
    returnType: "string",
    examples: [
      {
        reference: {
          functionId: "formatDate",
          args: ["2025-03-28T00:00:00Z", "yyyy-MM-dd"],
        },
        result: "2025-03-28",
      },
    ],
  },
];

// ============== End Functions ==============

// ============== Webhooks Allowed Urls ==============

const webhooksAllowedUrls = [
  "https://api.sendgrid.com/v3/mail/send",
  "https://api.mailgun.net/v3/concurrent-systems/messages",
  "http://localhost:6000/api/v1/sms/create/single",
];

// ============== End Webhooks Allowed Urls ==============

// ============== Standard Properties ==============

const standardProperties = [
  {
    propertyId: "timestamp",
    name: "Transaction Timestamp",
    description: "Date and time when the transaction occurred",
    type: "date",
    availablePhases: ["evaluation", "outcome"],
  },
  {
    propertyId: "transactionId",
    name: "Transaction ID",
    description: "Unique identifier for the transaction",
    type: "string",
    availablePhases: ["evaluation", "outcome"],
  },
  {
    propertyId: "entityId",
    name: "Entity ID",
    description: "Identifier of the entity sending the transaction",
    type: "string",
    availablePhases: ["evaluation", "outcome"],
  },
  {
    propertyId: "contextId",
    name: "Context ID",
    description: "Identifier of the transaction context",
    type: "string",
    availablePhases: ["evaluation", "outcome"],
  },
  {
    propertyId: "status",
    name: "Transaction Status",
    description:
      "Indicates whether the transaction was completed, failed, or abandoned",
    type: "enum",
    values: ["COMPLETED", "FAILED", "ABANDONED"],
    availablePhases: ["outcome"],
  },
  {
    propertyId: "modificationsApplied",
    name: "Applied Modifications",
    description:
      "Array of property identifiers that were successfully modified",
    type: "array",
    itemType: "string",
    availablePhases: ["outcome"],
  },
  {
    propertyId: "modificationsRejected",
    name: "Rejected Modifications",
    description: "Array of property identifiers that couldn't be modified",
    type: "array",
    itemType: "string",
    availablePhases: ["outcome"],
  },
];

const systemConfigurations = {
  systemVersion,
  conditionTypes,
  variablesAssignments,
  functions,
  standardProperties,
  webhooksAllowedUrls,
};

export default systemConfigurations;
