import fs from "fs";
import path from "path";


const DEFAULTS = {
  NODE_ENV: "development",

  // HTTP / HTTPS
  RF_PORT: 3000,
  RF_SSL_ENABLED: false,
  RF_SSL_CERT_PATH: "",
  RF_SSL_KEY_PATH: "",

  // Redis
  REDIS_HOSTS: '[{"host":"ruleforge_cre_db","port":6379}]',

  // ECDS API
  ECDS_API_PORT: 9084,
  ECDS_API_URL: "http://ecds-api",

  // Instance & Logging
  INSTANCE_NAME: "-",
  LOG_LEVEL: "info",

  JWT_PUBLIC_KEY: "the-key",

  // SMS Queue
  SMS_QUEUE_BATCH_SIZE: 100,
  SMS_QUEUE_BATCH_DELAY: 1000,
  SMS_QUEUE_DB_HOST: "ruleforge_cre_journal_db",
  SMS_QUEUE_DB_NAME: "ruleforge_cre_journal_db",
  SMS_QUEUE_DB_PASSWORD: "ussdgw",
  SMS_QUEUE_DB_PORT: 3306,
  SMS_QUEUE_DB_USERNAME: "root",
  SMS_QUEUE_TABLE_NAME: "smsq_queue",

  // Keep alive
  KEEP_ALIVE_TIMEOUT: 60000,

  // Stock API
  STOCK_API_ENCRYPTION_KEY: "change-this-key-in-production-use-32-chars",

};

function parseBool(val) {
  return val === true || (typeof val === "string" && val.toLowerCase() === "true");
}

function parseValue(key, val) {
  if (["KEEP_ALIVE_TIMEOUT","RF_PORT", "ECDS_API_PORT", "SMS_QUEUE_BATCH_SIZE", "SMS_QUEUE_BATCH_DELAY", "SMS_QUEUE_DB_PORT"].includes(key)) {
    return parseInt(val, 10);
  }
  if (key === "RF_SSL_ENABLED") {
    return parseBool(val);
  }
  return val;
}

function logSource(key, source) {
  console.log(
    [{ componentName: "Config.js" }, { variable: key }, { source }],
    `Loaded config ${key} from ${source}`
  );
}

function loadServerConfig(env) {
  const cfgPath = path.resolve(process.cwd(), "config.json");
  if (!fs.existsSync(cfgPath)) return {};
  try {
    const raw = fs.readFileSync(cfgPath, "utf8");
    const parsed = JSON.parse(raw);
    return parsed[env]?.server ?? {};
  } catch (err) {
    console.error( 
      [{ componentName: "Config.js" }, { task: "loadConfigFile" }],
      `Could not parse config.json: ${err.message}`
    );
    return {};
  }
}

// NODE_ENV
const NODE_ENV = process.env.NODE_ENV || DEFAULTS.NODE_ENV;
logSource("NODE_ENV", process.env.NODE_ENV ? "environment" : "default");

const serverCfg = loadServerConfig(NODE_ENV);
const sslCfg = typeof serverCfg.ssl === "object" ? serverCfg.ssl : {};

const Config = {
  NODE_ENV,

  RF_PORT: (() => {
    const key = "RF_PORT";
    if (process.env.RF_PORT != null) {
      logSource(key, "environment");
      return parseValue(key, process.env.RF_PORT);
    }
    if (serverCfg.port != null) {
      logSource(key, "config file");
      return parseValue(key, serverCfg.port);
    }
    logSource(key, "default");
    return DEFAULTS.RF_PORT;
  })(),

  RF_SSL_ENABLED: (() => {
    const key = "RF_SSL_ENABLED";
    if (process.env.RF_SSL_ENABLED != null) {
      logSource(key, "environment");
      return parseValue(key, process.env.RF_SSL_ENABLED);
    }
    if (sslCfg.enabled != null) {
      logSource(key, "config file");
      return parseValue(key, sslCfg.enabled);
    }
    logSource(key, "default");
    return DEFAULTS.RF_SSL_ENABLED;
  })(),

  RF_SSL_CERT_PATH: (() => {
    const key = "RF_SSL_CERT_PATH";
    if (process.env.RF_SSL_CERT_PATH) {
      logSource(key, "environment");
      return process.env.RF_SSL_CERT_PATH;
    }
    if (sslCfg.cert) {
      logSource(key, "config file");
      return sslCfg.cert;
    }
    logSource(key, "default");
    return DEFAULTS.RF_SSL_CERT_PATH;
  })(),

  RF_SSL_KEY_PATH: (() => {
    const key = "RF_SSL_KEY_PATH";
    if (process.env.RF_SSL_KEY_PATH) {
      logSource(key, "environment");
      return process.env.RF_SSL_KEY_PATH;
    }
    if (sslCfg.key) {
      logSource(key, "config file");
      return sslCfg.key;
    }
    logSource(key, "default");
    return DEFAULTS.RF_SSL_KEY_PATH;
  })(),

  REDIS_HOSTS: (() => {
    const key = "REDIS_HOSTS";
    if (process.env.REDIS_HOSTS) {
      logSource(key, "environment");
      return process.env.REDIS_HOSTS;
    }
    logSource(key, "default");
    return DEFAULTS.REDIS_HOSTS;
  })(),

  ECDS_API_PORT: (() => {
    const key = "ECDS_API_PORT";
    if (process.env.ECDS_API_PORT != null) {
      logSource(key, "environment");
      return parseValue(key, process.env.ECDS_API_PORT);
    }
    logSource(key, "default");
    return DEFAULTS.ECDS_API_PORT;
  })(),

  ECDS_API_URL: (() => {
    const key = "ECDS_API_URL";
    if (process.env.ECDS_API_URL) {
      logSource(key, "environment");
      return process.env.ECDS_API_URL;
    }
    logSource(key, "default");
    return DEFAULTS.ECDS_API_URL;
  })(),

  INSTANCE_NAME: (() => {
    const key = "INSTANCE_NAME";
    if (process.env.INSTANCE_NAME) {
      logSource(key, "environment");
      return process.env.INSTANCE_NAME;
    }
    logSource(key, "default");
    return DEFAULTS.INSTANCE_NAME;
  })(),

  LOG_LEVEL: (() => {
    const key = "LOG_LEVEL";
    if (process.env.LOG_LEVEL) {
      logSource(key, "environment");
      return process.env.LOG_LEVEL;
    }
    logSource(key, "default");
    return DEFAULTS.LOG_LEVEL;
  })(),


   JWT_PUBLIC_KEY: (() => {
    const key = "JWT_PUBLIC_KEY";
    if (process.env.JWT_PUBLIC_KEY) {
      logSource(key, "environment");
      return process.env.JWT_PUBLIC_KEY;
    }
    logSource(key, "default");
    return DEFAULTS.JWT_PUBLIC_KEY;
  })(),


  SMS_QUEUE_BATCH_SIZE: (() => {
    const key = "SMS_QUEUE_BATCH_SIZE";
    if (process.env.SMS_QUEUE_BATCH_SIZE != null) {
      logSource(key, "environment");
      return parseValue(key, process.env.SMS_QUEUE_BATCH_SIZE);
    }
    logSource(key, "default");
    return DEFAULTS.SMS_QUEUE_BATCH_SIZE;
  })(),

  SMS_QUEUE_BATCH_DELAY: (() => {
    const key = "SMS_QUEUE_BATCH_DELAY";
    if (process.env.SMS_QUEUE_BATCH_DELAY != null) {
      logSource(key, "environment");
      return parseValue(key, process.env.SMS_QUEUE_BATCH_DELAY);
    }
    logSource(key, "default");
    return DEFAULTS.SMS_QUEUE_BATCH_DELAY;
  })(),

  SMS_QUEUE_DB_HOST: (() => {
    const key = "SMS_QUEUE_DB_HOST";
    if (process.env.SMS_QUEUE_DB_HOST) {
      logSource(key, "environment");
      return process.env.SMS_QUEUE_DB_HOST;
    }
    logSource(key, "default");
    return DEFAULTS.SMS_QUEUE_DB_HOST;
  })(),

  SMS_QUEUE_DB_NAME: (() => {
    const key = "SMS_QUEUE_DB_NAME";
    if (process.env.SMS_QUEUE_DB_NAME) {
      logSource(key, "environment");
      return process.env.SMS_QUEUE_DB_NAME;
    }
    logSource(key, "default");
    return DEFAULTS.SMS_QUEUE_DB_NAME;
  })(),

  SMS_QUEUE_DB_PASSWORD: (() => {
    const key = "SMS_QUEUE_DB_PASSWORD";
    if (process.env.SMS_QUEUE_DB_PASSWORD) {
      logSource(key, "environment");
      return process.env.SMS_QUEUE_DB_PASSWORD;
    }
    logSource(key, "default");
    return DEFAULTS.SMS_QUEUE_DB_PASSWORD;
  })(),

  SMS_QUEUE_DB_PORT: (() => {
    const key = "SMS_QUEUE_DB_PORT";
    if (process.env.SMS_QUEUE_DB_PORT != null) {
      logSource(key, "environment");
      return parseValue(key, process.env.SMS_QUEUE_DB_PORT);
    }
    logSource(key, "default");
    return DEFAULTS.SMS_QUEUE_DB_PORT;
  })(),

  SMS_QUEUE_DB_USERNAME: (() => {
    const key = "SMS_QUEUE_DB_USERNAME";
    if (process.env.SMS_QUEUE_DB_USERNAME) {
      logSource(key, "environment");
      return process.env.SMS_QUEUE_DB_USERNAME;
    }
    logSource(key, "default");
    return DEFAULTS.SMS_QUEUE_DB_USERNAME;
  })(),

  SMS_QUEUE_TABLE_NAME: (() => {
    const key = "SMS_QUEUE_TABLE_NAME";
    if (process.env.SMS_QUEUE_TABLE_NAME) {
      logSource(key, "environment");
      return process.env.SMS_QUEUE_TABLE_NAME;
    }
    logSource(key, "default");
    return DEFAULTS.SMS_QUEUE_TABLE_NAME;
  })(),

  KEEP_ALIVE_TIMEOUT: (() => {
    const key = "KEEP_ALIVE_TIMEOUT";
    if (process.env.KEEP_ALIVE_TIMEOUT) {
      logSource(key, "environment");
      return parseValue(key, process.env.KEEP_ALIVE_TIMEOUT);
    }
    logSource(key, "default");
      return DEFAULTS.KEEP_ALIVE_TIMEOUT;
  })(),
};

Config.dump = function() {
  // grab only the real config entries (no methods)
  const data = Object.entries(this).reduce((acc, [key, val]) => {
    if (typeof val !== "function") acc[key] = val;
    return acc;
  }, {});

  // pretty-print with 2-space indent
  return JSON.stringify(data, null, 2);
};



export default Config;

