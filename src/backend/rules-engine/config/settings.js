import {logger} from "../middleware/logger.js";

import Config from "../config/Config.js"

// If the JWT_PUBLIC_KEY is not set, throw an error
if (!Config.JWT_PUBLIC_KEY) {
    logger.error(
        [{ componentName: "settings.js" }, { task: "initializeJwtPublicKey" }],
        "JWT_PUBLIC_KEY is not set",
      );
    process.exit(1);
}

const settings = {

    smsQueue: {
        /**
         * The maximum number of SMS queue entries to process in a single batch.
         */
        batchSize: Config.SMS_QUEUE_BATCH_SIZE ,
        /**
         * The delay (in milliseconds) between processing each batch.
         */
        batchDelay: Config.SMS_QUEUE_BATCH_DELAY,
    },

    // Decode the JWT_PUBLIC_KEY from base64 to utf8
    jwtPublicKey: Buffer.from(Config.JWT_PUBLIC_KEY, 'base64').toString('utf8'),
}


export default settings;
