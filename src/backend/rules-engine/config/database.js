
// File containing the all db configurations

import Config from '../config/Config.js'
/**
 * Database configuration of the SmsQueue database
 */
export const smsQueueDbConfig = {
    host: Config.SMS_QUEUE_DB_HOST ,
    port: Config.SMS_QUEUE_DB_PORT ,
    user: Config.SMS_QUEUE_DB_USERNAME ,
    password: Config.SMS_QUEUE_DB_PASSWORD ,
    database: Config.SMS_QUEUE_DB_NAME ,
    /** Table name for inserting the queue rows */
    tableName: Config.SMS_QUEUE_TABLE_NAME ,
    /** Since we have the mysql2 module already installed so we will use the mysql dialect so the the Sequelize will use it for db interaction on maria db */
    dialect: 'mysql',
    /** Log SQL queries in development mode */
    logging: Config.NODE_ENV === 'development',
    /** Pool object */
    pool: {
        /** Maximum number of connections in the pool */
      max: 1,
      /** Minimum number of connections in the pool */
      min: 0,
      /** Maximum time (ms) to acquire a connection */
      acquire: 30000,
      /** Maximum time (ms) a connection can remain idle */
      idle: 10000,
    },
    

}
