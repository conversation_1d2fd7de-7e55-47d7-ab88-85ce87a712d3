import cors from "cors";

const allowedOrigins = [
  "http://**************:8080",
  "https://**************:8080",
  "https://cs.ruleforge.net",
  "https://moovtogo.ruleforge.net",
];

export const configureCors = () => {
  return cors({
    origin: (origin, callback) => {
      if (!origin) {
        // Allow requests with no origin (e.g., curl, Postman)
        return callback(null, true);
      }
      if (allowedOrigins.includes(origin)) {
        // Allow requests from allowed origins
        return callback(null, true);
      } else {
        // For non-allowed origins, skip adding CORS headers
        return callback(null, false); // Let the request proceed without CORS headers
      }
    },
    credentials: true, // Allow credentials (cookies, authorization headers)
  });
};
