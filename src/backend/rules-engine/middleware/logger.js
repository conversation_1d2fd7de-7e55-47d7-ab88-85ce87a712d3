import winston from "winston";
import morgan from "morgan"; 

import Config from '../config/Config.js'

// Define custom log levels and colors.
const customLevels = {
  levels: {
    fatal: 0,
    error: 1,
    warn: 2,
    info: 3,
    trace: 4,
    debug: 5,
  },
  colors: {
    fatal: "red",
    error: "red",
    warn: "yellow",
    info: "green",
    trace: "gray",
    debug: "blue",
  },
};

function serializeError(error) {
  if (!(error instanceof Error)) {
    throw new TypeError("Input must be an Error object");
  }

  const errorObject = {
    name: error.name,
    message: error.message,
    stack: error.stack,
  };

  // Add custom properties if they exist
  for (const key in error) {
    if (Object.prototype.hasOwnProperty.call(error, key)) {
      errorObject[key] = error[key];
    }
  }

  return errorObject; // Pretty-print JSON
}

// Get the instance name from environment variable (or default to "-")
const instanceName = Config.INSTANCE_NAME; 

// Ensure the /logs directory exists
//const logDirectory = path.join("/", "logs");
//if (!fs.existsSync(logDirectory)) {
//  fs.mkdirSync(logDirectory, { recursive: true });
//}

// Create a custom CSV formatter. It outputs:
// timestamp,level,requestId,instance,"message"
const csvFormat = winston.format.printf(
  ({ level, message, timestamp, context, ...metadata }) => {
    let requestId = "-";
    if (context) {
      if (Array.isArray(context)) {
        const ctxWithReq = context.find((ctx) => ctx.requestId);
        if (ctxWithReq && ctxWithReq.requestId) {
          requestId = ctxWithReq.requestId;
        }
      } else if (typeof context === "object" && context.requestId) {
        requestId = context.requestId;
      }
    }

    let msg;
    if (message instanceof Error) {
      msg = serializeError(message);
    } else {
      msg = message;
    }

    let contextualMessage = { msg, context };
    contextualMessage = JSON.stringify(contextualMessage);

    contextualMessage = contextualMessage.replace(/"/g, '""');

    return `${timestamp},${String(level).toUpperCase()},${requestId},${instanceName},"${contextualMessage}"`;
  },
);

const LOG_LEVEL = Config.LOG_LEVEL || 'info'; 

const lowerCaseLogLevel = LOG_LEVEL.toLowerCase();

  console.log({loglevel: lowerCaseLogLevel });

// Create the Winston logger.
export const logger = winston.createLogger({
  levels: customLevels.levels,
  level: lowerCaseLogLevel , // Default log level 
  format: winston.format.combine(
    // Set the timestamp format to match your sample: "YYYY-MM-DD HH:mm:ss.SSS"
    winston.format.timestamp({ format: "YYYY-MM-DD HH:mm:ss.SSS" }),
    csvFormat,
  ),
  transports: [
    new winston.transports.Console(), // Log to console
    //   new DailyRotateFile({
    //     filename: path.join(logDirectory, "ruleforge-api-%DATE%.log"),
    //     datePattern: "YYYY-MM-DD",
    //     maxSize: "2g",
    //     maxFiles: "14d",
    //   }),
  ],
});

// Add custom colors (for potential console output styling)
winston.addColors(customLevels.colors);

// Override the default logger.log method so it accepts a context and a message.
const originalLog = logger.log.bind(logger);
logger.log = function (level, context, message) {
  if (typeof context === "string") {
    // If only a message is provided, use an empty object for context.
    message = context;
    context = {};
  }
  originalLog(level, { context, message });
};

// Create shorthand methods (logger.info, logger.error, etc.)
Object.keys(customLevels.levels).forEach((level) => {
  logger[level] = (context, message) => logger.log(level, context, message);
});

// Morgan request logger. It attaches the requestId to the context.
export const requestLogger = morgan(
  (tokens, req, res) => {
    let requestLogMessage = {
      context: [{ requestId: req.requestId }],
      message: {
        method: tokens.method(req, res),
        url: tokens.url(req, res),
        status: tokens.status(req, res),
        content_length: tokens.res(req, res, "content-length"),
        response_time: tokens["response-time"](req, res) + "ms",
      },
    };
    return JSON.stringify(requestLogMessage);
  },
  {
    stream: {
      write: (message) => {
        let msgAsJson = JSON.parse(message);
        logger.info(msgAsJson.context, msgAsJson.message);
      },
    },
  },
);
