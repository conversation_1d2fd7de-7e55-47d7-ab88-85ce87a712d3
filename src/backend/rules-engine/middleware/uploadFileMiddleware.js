import multer from "multer";
import { logger } from "../middleware/logger.js";

/**
 * Factory function to create a Multer middleware for file uploads.
 *
 * @param {string} mimeType - The allowed MIME type for the file (e.g., 'text/csv').
 * @param {number} maxSize - The maximum file size allowed (in bytes).
 * @param {string} [fieldName='file'] - The field name for the file upload.
 * @param {string} errorMessage - The error message to return if the validation fails.
 * @returns {Function} - Returns the Multer middleware function.
 */
function createUploadMiddleware(
  mimeType,
  maxSize,
  errorMessage = "Invalid file",
  fieldName = "file",
) {
  try {
    // Set up storage configuration (store the file in memory or on disk). As we are dealing with the CSV file, we are using memory storage and the concurrent requests are not high, hardly 1-2 requests at a time so it's safe to use memory storage.
    const storage = multer.memoryStorage(); // You can change this to diskStorage if needed

    // Set up file filter to allow only the specified MIME type
    const fileFilter = (req, file, cb) => {
      try {
        if (file.mimetype === mimeType) {
          logger.info(
            [{ component: "uploadFileMiddleware" }, { action: "fileFilter" }],
            "accepting file",
          );

          try {
            cb(null, true); // Accept the file if it matches the mimeType
          } catch (error) {
            logger.error(
              [{ component: "uploadFileMiddleware" }, { action: "cb" }],
              error,
            );
          }
        } else {
          logger.error(
            [{ component: "uploadFileMiddleware" }, { action: "fileFilter" }],
            errorMessage,
          );
          cb(new Error(errorMessage), false); // Reject if not the specified MIME type
        }
      } catch (error) {
        logger.error(
          [{ component: "uploadFileMiddleware" }, { action: "fileFilter" }],
          error,
        );
        throw error;
      }
    };

    let upload;

    // Initialize Multer with the file filter and file size limit
    try {
      upload = multer({
        storage: storage,
        fileFilter: fileFilter,
        limits: {
          fileSize: maxSize, // Set max file size
        },
      }).single(fieldName);
    } catch (error) {
      logger.error(
        [{ component: "uploadFileMiddleware" }, { action: "creating multer" }],
        error,
      );
    }

    // Multer error handling middleware
    return (req, res, next) => {
      try {
        upload(req, res, (err) => {
          try {
            // As the filter have only single file with the name of the fieldname and so if the file is not uploaded or not valid file, we can check it here as inside here if it have the file then we can get by req.file, even though if the fieldname is different we can still access the file by the req.file

            // Verify if the file not uploaded or not valid file fieldName
            if (!req.file) {
              logger.error(
                [{ component: "uploadFileMiddleware" }],
                `File not specified!`,
              );
              return res.status(400).json({ error: "No file uploaded" });
            }

            if (err) {
              // Send the error message from Multer if file validation fails
              logger.error([{ component: "uploadFileMiddleware" }], err);

              return res.status(400).json({ error: err.message });
            }
            next(); // Proceed to the next middleware if everything is valid
          } catch (error) {
            logger.error(
              [
                { component: "uploadFileMiddleware" },
                { action: "upload function" },
              ],
              error,
            );
          }
        });
      } catch (error) {
        logger.error(
          [
            {
              component: "uploadFileMiddleware",
              action: "handling file upload",
            },
          ],
          error,
        );
      }
    };
  } catch (error) {
    logger.error(
      [
        { component: "uploadFileMiddleware" },
        { action: "createUploadMiddleware" },
      ],
      error,
    );
  }
}
export default createUploadMiddleware;
