import { executeRulesetEvaluation } from "../utils/astGenerator.js";

import { logger } from "../middleware/logger.js";
import { combineEvaluationResults}  from "./combineEvaluationResults.js"

const { default: Redlock } = await import("redlock");

class EvaluationService {
  static componentName = "EvaluationService";
  constructor(redisClient, rulesetService) {
    this.redisClient = redisClient;
    this.rulesetService = rulesetService;

    this.componentContext = [
      { componentName: EvaluationService.componentName },
    ];
  }

  async evaluate(
    callingEntityId,
    transactionContextId,
    originTransactionId,
    timeStamp,
    transactionData,
    callerContext,
  ) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "evaluate" },
      { callingEntityId },
      { transactionContextId },
      { originTransactionId },
    ];

    logger.info(loggingContext, "evaluating request");

    try {
      const entity = JSON.parse(
        await this.redisClient.hget(
          "entities:{common}",
          `entity:${callingEntityId}`,
        ),
      );

      if (!entity) {
        const error = new Error("Entity not found for Id: " + callingEntityId);
        logger.error(loggingContext, error);
        throw error;
      }

      const transactionContext = entity.transactionContexts.find(
        (transactionContext) =>
          transactionContext.contextId === transactionContextId,
      );

      if (!transactionContext) {
        throw new Error(
          `Transaction context id "${transactionContextId}" not found for entity id "${callingEntityId}"`,
        );
      }

      const transactionContextParameterDefinitions =
        transactionContext?.properties;

      const rulesets = await this.rulesetService.findRulesetsByEntityIdAndContextId(
        callingEntityId,
        transactionContextId,
        loggingContext,
      );

      logger.info(loggingContext, { rulesets });

      let collatedRulesetsResults = {
        transactionId: originTransactionId,
      };

      const persistentVariablesLock = new Redlock(
        [this.redisClient], 
        {
          retryCount: 20,
          retryDelay: 100,      // 100 ms between tries
          retryJitter: 50,      // ±50 ms random
          driftFactor: 0.01
        });

      for (const ruleset of rulesets) {
        collatedRulesetsResults = combineEvaluationResults(
          collatedRulesetsResults,
          (await executeRulesetEvaluation(
            ruleset,
            callingEntityId,
            transactionContextParameterDefinitions,
            transactionContextId,
            originTransactionId,
            timeStamp,
            transactionData,
            this.redisClient,
            persistentVariablesLock,
            loggingContext,
          ))?.result,
        );
      }

      return collatedRulesetsResults;
    } catch (error) {
      logger.error(loggingContext, error);
      throw error;
    }
  }
}

export default EvaluationService;
