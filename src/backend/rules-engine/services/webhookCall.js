import axios from "axios";
import Redis from "ioredis";

/**
 * Webhook Caller Class - Handles webhook execution with method-specific parameter handling
 */
class WebhookCaller {
  constructor(redisClient, callerContext) {
    this.redisClient = redisClient;
    this.webhooks = [];
    this.callerContext = callerContext;
  }
  /**
   * Initializes the webhook caller and loads the webhooks from Redis
   * @returns {Object} The webhook caller
   */
  async init() {
    this.webhooks = await loadWebhooks(this.redisClient);
    return this; // allow chaining
  }

  /**
   * Replaces the placeholders in the template with the actual values
   * @param {Object} template - The template object
   * @param {Object} parameters - The parameters object
   * @returns {Object} The replaced template
   */
  #replacePlaceholders(template, parameters) {
    if (typeof template !== "string") {
      return template;
    }
    const result = template.replace(
      /^(\{(\w+)\})$/,
      (match, fullMatch, param) => {
        if (parameters[param] !== undefined) {
          return parameters[param];
        }
        return match;
      }
    );
    return result;
  }

  /**
   * Validates the parameters for the webhook
   * @param {Object} webhook - The webhook object
   * @param {Object} parameters - The parameters object
   * @returns {Object} The validation result
   */
  #validateParameters(webhook, parameters) {
    if (!webhook.parameters || !Array.isArray(webhook.parameters)) {
      return { valid: true }; // No parameters defined to validate
    }

    const missingParams = webhook.parameters
      .filter(
        (p) =>
          p.required &&
          (parameters[p.parameterId] === undefined ||
            parameters[p.parameterId] === null)
      )
      .map((p) => p.parameterId);

    if (missingParams.length > 0) {
      console.error("Missing required parameters:", missingParams.join(", "));
    }
    return missingParams.length > 0
      ? {
          valid: false,
          message: `Missing required parameters: ${missingParams.join(", ")}`,
          errorType: "MISSING_PARAMETERS",
          errorDetails: {
            message: `Missing required parameters: ${missingParams.join(", ")}`,
            code: "MISSING_PARAMETERS",
            suggestion: "Verify the required parameters are provided",
          },
        }
      : { valid: true };
  }

  /**
   * Builds the request configuration for the webhook
   * @param {Object} webhook - The webhook object
   * @param {Object} parameters - The parameters object
   * @returns {Object} The request configuration
   */
  #buildRequestConfig(webhook, parameters) {
    const method = webhook.method.toUpperCase();
    const isQueryParamMethod = ["GET", "DELETE"].includes(method);
    // Process URL with placeholders
    let processedUrl = webhook.url;
    // Handle query parameters for GET/DELETE
    if (isQueryParamMethod) {
      let queryParams = Object.fromEntries(
        Object.entries(webhook.bodyTemplate).map(([key, value]) => [
          key,
          this.#replacePlaceholders(value, parameters),
        ])
      );
      const urlObj = new URL(processedUrl);
      for (const [key, value] of Object.entries(queryParams)) {
        urlObj.searchParams.append(key, String(value));
      }
      processedUrl = urlObj.toString();
    }
    // Prepare headers
    let headers = {
      "Content-Type": "application/json",
      // "Authorization": `Bearer ${"provider-token"}`,
      ...webhook.headers,
    };
    // Build base config
    const config = {
      method,
      url: processedUrl,
      headers,
      timeout: webhook.timeout || 5000,
    };

    // Add body for non-query methods
    if (!isQueryParamMethod && webhook.bodyTemplate) {
      config.data = Object.fromEntries(
        Object.entries(webhook.bodyTemplate).map(([key, value]) => [
          key,
          this.#replacePlaceholders(value, parameters),
        ])
      );
    }
    return config;
  }

  /**
   * Executes the webhook with retry logic
   * @param {Object} webhook - The webhook object
   * @param {Object} parameters - The parameters object
   * @param {Number} attempt - The attempt number
   * @returns {Object} The execution result
   */
  async #executeWebhook(webhook, parameters, attempt = 1) {
    try {
      const config = this.#buildRequestConfig(webhook, parameters);

      const response = await axios(config);

      return {
        success: true,
        status: response.status,
        data: response.data,
        attempt,
      };
    } catch (error) {
      if (attempt >= (webhook.retryPolicy?.maxRetries || 0)) {
        let errorType = "UNKNOWN_ERROR";
        let errorDetails = {
          message: error.message || "An unknown error occurred",
          code: "WEBHOOK_ERROR",
        };
        // Handle HTTP status code errors with detailed information
        if (error.response) {
          const status = error.status || error.response.status;
          if (status >= 500) {
            console.error("The server error is:", error);
            errorType = "SERVER_ERROR";
            errorDetails = {
              ...errorDetails,
              code: `SERVER_ERROR_${status}`,
              message: `Server error (${status}): The external service is experiencing issues`,
              suggestion:
                "Please try again later or contact the service provider",
            };
          } else if (status >= 400) {
            errorType = "CLIENT_ERROR";

            // More specific client error handling
            if (status === 400) {
              console.error(
                "Client Error: The server could not process the request due to invalid syntax ",
                error
              );
              errorDetails = {
                ...errorDetails,
                code: "BAD_REQUEST",
                message:
                  "Bad Request: The server could not process the request due to invalid syntax",
                suggestion: "Verify the request parameters and format",
              };
            } else if (status === 401) {
              console.error(
                "Authentication is required or credentials are invalid:",
                error
              );
              errorDetails = {
                ...errorDetails,
                code: "UNAUTHORIZED",
                message:
                  "Unauthorized: Authentication is required or credentials are invalid",
                suggestion: "Check API keys or authentication tokens",
              };
            } else if (status === 403) {
              console.error(
                "Forbidden: The server understood the request but refuses to authorize it",
                error
              );
              errorDetails = {
                ...errorDetails,
                code: "FORBIDDEN",
                message:
                  "Forbidden: The server understood the request but refuses to authorize it",
                suggestion: "Verify your access permissions for this resource",
              };
            } else if (status === 404) {
              console.error(
                "The requested resource could not be found:",
                error
              );
              errorDetails = {
                ...errorDetails,
                code: "NOT_FOUND",
                message: "Not Found: The requested resource could not be found",
                suggestion:
                  "Verify the webhook URL is correct and the resource exists",
              };
            } else if (status === 429) {
              console.error("Too Many Requests: Rate limit exceeded:", error);
              errorDetails = {
                ...errorDetails,
                code: "RATE_LIMITED",
                message: "Too Many Requests: Rate limit exceeded",
                suggestion:
                  "Implement rate limiting or retry after the specified time",
              };
            } else {
              console.error(
                "Client Error: The request was rejected by the server",
                error
              );
              errorDetails = {
                ...errorDetails,
                code: `CLIENT_ERROR_${status}`,
                message: `Client error (${status}): The request was rejected by the server`,
                suggestion: "Check the request format and parameters",
              };
            }
          }

          // Include response data if available for better debugging
          if (error.response.data) {
            errorDetails.responseData =
              typeof error.response.data === "object"
                ? error.response.data
                : { raw: String(error.response.data).substring(0, 200) };
          }
        } else if (error.request) {
          // Request was made but no response received
          console.error(
            "Connection Error: The request was sent but no response was received",
            error
          );
          errorType = "CONNECTION_ERROR";
          errorDetails = {
            ...errorDetails,
            code: "NO_RESPONSE",
            message:
              "Connection Error: The request was sent but no response was received",
            suggestion: "Check network connectivity or service availability",
          };
        } else if (error.code === "ECONNABORTED") {
          console.error(
            "Timeout Error: The request took longer than expected",
            error
          );
          errorType = "TIMEOUT_ERROR";
          errorDetails = {
            ...errorDetails,
            code: "REQUEST_TIMEOUT",
            message: `Timeout Error: The request took longer than ${
              webhook.timeout || 5000
            }ms to complete`,
            suggestion:
              "Consider increasing the timeout value or optimizing the endpoint",
          };
        }
        return {
          success: false,
          status: error.response?.status,
          error: error.message,
          attempt,
          errorType,
          errorDetails,
        };
      }

      const delay =
        (webhook.retryPolicy?.initialDelaySeconds || 1) *
        Math.pow(webhook.retryPolicy?.backoffMultiplier || 2, attempt - 1) *
        1000;
      await new Promise((resolve) => setTimeout(resolve, delay));
      return this.#executeWebhook(webhook, parameters, attempt + 1);
    }
  }

  /**
   * Calls the webhook with the given webhookId and parameters
   * @param {String} webhookId - The webhookId
   * @param {Object} parameters - The parameters object
   * @returns {Object} The execution result
   */
  async call(webhookId, parameters = {}) {
    const webhook = this.webhooks.find((wh) => wh.webhookId === webhookId);

    if (!webhook) {
      console.error("Webhook not found:", webhookId);
      return {
        success: false,
        message: `Webhook ${webhookId} not found`,
        code: "WEBHOOK_NOT_FOUND",

        suggestion: "Verify the webhookId is correct and exists in the system",
      };
    }

    const validation = this.#validateParameters(webhook, parameters);
    if (!validation.valid) return { success: false, ...validation };
    const result = await this.#executeWebhook(webhook, parameters);
    return {
      success: result.success,
      status: result.status,
      data: result.data,
      attempt: result.attempt,
      error: result.errorDetails ? result.errorDetails : result.error || null,
      errorType: result.errorType ? result.errorType : null,
      message: result.success
        ? `Webhook ${webhookId} executed successfully`
        : `Webhook ${webhookId} failed after ${result.attempt} attempts: ${
            result.error?.message || "Unknown error"
          }`,
    };
  }
}

export default WebhookCaller;

/**
 * Load all webhook configurations from Redis hash: webhooks:{common}
 * @returns {Promise<Array>} List of webhook objects
 */
async function loadWebhooks(redisClient) {
  const hashKey = "webhooks:{common}";

  try {
    // Get all field-value pairs from the hash
    const rawWebhooks = await redisClient.hgetall(hashKey);

    // Convert each JSON string into an object
    const webhooks = Object.entries(rawWebhooks)
      .map(([field, value]) => {
        try {
          return JSON.parse(value);
        } catch (err) {
          console.warn(`Skipping invalid JSON in field "${field}"`);
          return null;
        }
      })
      .filter(Boolean);
    return webhooks;
  } catch (error) {
    console.error("Failed to load webhooks from Redis:", error);
    return [];
  }
}
