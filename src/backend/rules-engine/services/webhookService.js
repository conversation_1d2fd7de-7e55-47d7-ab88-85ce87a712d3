import { logger } from "../middleware/logger.js";
import { formatErrorResponse } from "../controllers/webhookController.js";

const TAG = "{common}";

// this script will check if the webhook already exists and if it does, it will return an error
// const SAVE_WEBHOOK_LUA = `
//   local tag = KEYS[1]
//   local webhookJson = ARGV[1]
//   local now = ARGV[2]

//   local webhook = cjson.decode(webhookJson)
//   local webhookId = webhook.webhookId
//   local fieldKey = "webhook:" .. webhookId

//   -- Check if it already exists
//   if redis.call("HEXISTS", "webhooks:" .. tag, fieldKey) == 1 then
//     return { err = "Webhook with ID " .. webhookId .. " already exists" }
//   end

//   webhook.createdAt = now
//   webhook.updatedAt = now

//   local finalJson = cjson.encode(webhook)

//   redis.call("HSET", "webhooks:" .. tag, fieldKey, finalJson)
//   redis.call("ZADD", "webhooks:" .. tag .. ":index", now, webhookId)

//   if webhook.securityLevel then
//     redis.call("ZADD", "webhooks:" .. tag .. ":security:" .. webhook.securityLevel, now, webhookId)
//   end

//   return now

// `;

const SAVE_WEBHOOK_LUA = `
local tag = KEYS[1]
local webhookJson = ARGV[1]
local isoNow = ARGV[2] -- ISO timestamp passed from JS

local webhook = cjson.decode(webhookJson)
local webhookId = webhook.webhookId
local fieldKey = "webhook:" .. webhookId

-- Check if webhook already exists
if redis.call("HEXISTS", "webhooks:" .. tag, fieldKey) == 1 then
  return { err = "Webhook with ID " .. webhookId .. " already exists" }
end

-- Get Redis server time in ms
local timeParts = redis.call("TIME")
local nowSec = tonumber(timeParts[1])
local nowMs = nowSec * 1000 + math.floor(tonumber(timeParts[2]) / 1000)

-- Set timestamps
webhook.createdAt = isoNow
webhook.updatedAt = isoNow

-- Save to Redis
local finalJson = cjson.encode(webhook)
redis.call("HSET", "webhooks:" .. tag, fieldKey, finalJson)
redis.call("ZADD", "webhooks:" .. tag .. ":index", nowMs, webhookId)

if webhook.securityLevel then
  redis.call("ZADD", "webhooks:" .. tag .. ":security:" .. webhook.securityLevel, nowMs, webhookId)
end

return isoNow

`;

export default class WebhookService {
  constructor(redisClient, callerContext) {
    this.componentName = "WebhookService";
    this.componentContext = [{ componentName: this.componentName }];
    this.redisClient = redisClient;

    logger.info(
      [
        ...callerContext,
        ...this.componentContext,
        { functionName: "constructor" },
      ],
      "Created WebhookService"
    );
  }
  /**
   * This Function will register a webhook in the Redis database
   * @param {Object} webhook The webhook object to register
   * @param {string} requestId The request ID
   * @param {Object} callerContext The caller context
   * @returns {Object}
   */
  async registerWebhook(webhook, requestId, callerContext) {
    const isoNow = new Date().toISOString().replace(/\.\d{3}Z$/, "Z");
    const context = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "registerWebhook" },
    ];

    try {
      await this.redisClient.eval(
        SAVE_WEBHOOK_LUA,
        1,
        TAG,
        JSON.stringify(webhook),
        isoNow
      );
      return {
        webhookId: webhook.webhookId,
        message: "Webhook successfully registered",
        createdAt: isoNow,
      };
    } catch (error) {
      logger.error(context, error);

      // Check if the error was returned from the Lua script as a "duplicate" message
      const isDuplicate = error?.message?.includes("already exists");

      if (isDuplicate) {
        throw formatErrorResponse({
          statusCode: 409,
          code: "DUPLICATE_WEBHOOK_ID",
          message: "A webhook with this ID already exists",
          details: {
            webhookId: webhook.webhookId,
          },
        });
      }
      throw formatErrorResponse({
        statusCode: 500,
        code: "INTERNAL_SERVER_ERROR",
        message: `Failed to register webhook: ${error}`,
        details: {
          webhookId: webhook.webhookId,
        },
        requestId: requestId || "UNKNOWN_REQUEST_ID",
      });
    }
  }
  /**
   * This Function will get a webhook by its ID from the Redis database
   * @param {string} webhookId The webhook ID
   * @param {string} requestId The request ID
   * @param {Object} callerContext The caller context
   * @returns {Object}
   */
  async getWebhookById(webhookId, requestId, callerContext) {
    const context = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "getWebhookById" },
    ];
    try {
      const key = `webhook:${webhookId}`;

      const webhookStr = await this.redisClient.hget(`webhooks:${TAG}`, key);
      if (!webhookStr) {
        throw formatErrorResponse({
          statusCode: 404,
          code: "WEBHOOK_NOT_FOUND",
          message: `Webhook with ID ${webhookId} not found`,
          details: {
            webhookId: webhookId,
          },
          requestId: requestId || "UNKNOWN_REQUEST_ID",
        });
      }
      return JSON.parse(webhookStr);
    } catch (error) {
      logger.error(context, error);
      if (error.isCustomError) {
        throw error;
      }
      throw formatErrorResponse({
        statusCode: 500,
        code: "INTERNAL_SERVER_ERROR",
        message: `Failed to get webhook by ID: ${error}`,
        details: {
          webhookId: webhookId,
        },
        requestId: requestId,
      });
    }
  }
  /**
   * This Function will get all webhooks from the Redis database
   * @param {number} page The page number
   * @param {number} pageSize The page size
   * @param {string} securityLevel The security level
   * @param {string} requestId The request ID
   * @param {Object} callerContext The caller context
   * @returns {Object}
   */
  async getWebhooks(page, pageSize, securityLevel, requestId, callerContext) {
    const context = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "getWebhooks" },
    ];
    try {
      const start = (page - 1) * pageSize;
      const end = start + pageSize - 1;
      const indexKey = securityLevel
        ? `webhooks:${TAG}:security:${securityLevel}`
        : `webhooks:${TAG}:index`;

      const webhookIds = await this.redisClient.zrange(indexKey, start, end);
      if (webhookIds.length === 0) {
        return {
          webhooks: [],
          totalCount: 0,
          page,
          pageSize,
        };
      }

      // Step 2: Use a Redis pipeline for multiple hgets
      const pipeline = this.redisClient.pipeline();
      webhookIds.forEach((id) => {
        pipeline.hget(`webhooks:${TAG}`, `webhook:${id}`);
      });
      const responses = await pipeline.exec();

      // Step 3: Safely parse JSON
      const webhooks = responses
        .map(([error, data], i) => {
          if (error) {
            logger.warn(
              context,
              `Failed to fetch webhook ${webhookIds[i]}: ${error.message}`
            );
            return null;
          }
          try {
            return JSON.parse(data);
          } catch (parseError) {
            logger.warn(
              context,
              `Invalid JSON for webhook ${webhookIds[i]}: ${parseError.message}`
            );
            return null;
          }
        })
        .filter(Boolean); // Remove nulls

      // Step 4: Get total count
      const totalCount = await this.redisClient.zcard(indexKey);

      return {
        webhooks,
        totalCount,
        page,
        pageSize,
      };
    } catch (error) {
      logger.error(context, error);
      throw formatErrorResponse({
        statusCode: 500,
        code: "INTERNAL_SERVER_ERROR",
        message: `Failed to get webhooks: ${error}`,
        details: {},
        requestId: requestId || "UNKNOWN_REQUEST_ID",
      });
    }
  }

  /**
   * This Function will update a webhook in the Redis database
   * @param {string} webhookId The webhook ID
   * @param {Object} webhook The webhook object to update
   * @param {string} requestId The request ID
   * @param {Object} callerContext The caller context
   * @returns {Object}
   */
  async updateWebhook(webhookId, webhook, requestId, callerContext) {
    const context = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "updateWebhook" },
    ];
    try {
      const key = `webhook:${webhookId}`;

      const existing = await this.redisClient.hget(`webhooks:${TAG}`, key);
      if (!existing) {
        throw formatErrorResponse({
          statusCode: 404,
          code: "WEBHOOK_NOT_FOUND",
          message: `Webhook with ID ${webhookId} not found`,
          details: {
            webhookId: webhookId,
          },
          requestId: requestId || "UNKNOWN_REQUEST_ID",
        });
      }

      const now = new Date().toISOString().replace(/\.\d{3}Z$/, "Z");
      const parsed = JSON.parse(existing);
      const updated = {
        ...parsed,
        ...webhook,
        updatedAt: now,
      };

      await this.redisClient.hset(
        `webhooks:${TAG}`,
        key,
        JSON.stringify(updated)
      );
      return {
        webhookId,
        message: "Webhook successfully updated",
        updatedAt: now,
      };
    } catch (error) {
      logger.error(context, error);
      if (error.isCustomError) {
        throw error;
      }
      throw formatErrorResponse({
        statusCode: 500,
        code: "INTERNAL_SERVER_ERROR",
        message: `Failed to update webhook ${webhookId} : ${error}`,
        details: {
          webhookId: webhookId,
        },
        requestId: requestId || "UNKNOWN_REQUEST_ID",
      });
    }
  }
  /**
   * This Function will delete a webhook from the Redis database
   * @param {string} webhookId The webhook ID
   * @param {string} requestId The request ID
   * @param {Object} callerContext The caller context
   * @returns {Object}
   */
  async deleteWebhook(webhookId, requestId, callerContext) {
    const context = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "deleteWebhook" },
    ];

    try {
      const key = `webhook:${webhookId}`;
      const raw = await this.redisClient.hget(`webhooks:${TAG}`, key);

      if (!raw) throw new Error(`Webhook with ID ${webhookId} not found`);

      const { securityLevel } = JSON.parse(raw);

      await this.redisClient.hdel(`webhooks:${TAG}`, key);
      await this.redisClient.zrem(`webhooks:${TAG}:index`, webhookId);

      if (securityLevel) {
        await this.redisClient.zrem(
          `webhooks:${TAG}:security:${securityLevel}`,
          webhookId
        );
      }

      return {
        webhookId,
        message: "Webhook successfully deleted",
      };
    } catch (error) {
      logger.error(context, error);

      throw formatErrorResponse({
        statusCode: 500,
        code: "INTERNAL_SERVER_ERROR",
        message: `Failed to delete webhook ${webhookId} : ${error}`,
        details: {
          webhookId: webhookId,
        },
        requestId: requestId || "UNKNOWN_REQUEST_ID",
      });
    }
  }
  /**
   * This Function will test a webhook
   * @param {Object} webhookData The webhook data
   * @param {Object} callerContext The caller context
   * @returns {Object}
   */
  async testWebhook(webhookData, callerContext) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "testWebhook" },
    ];

    try {
      return {
        message: "Webhook successfully tested",
      };
    } catch (error) {
      logger.error(loggingContext, error);
      throw error;
    }
  }
}
