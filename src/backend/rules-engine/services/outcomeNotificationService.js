import { logger } from "../middleware/logger.js";
import { executeRulesetOutcomeNotification } from "../utils/astGenerator.js";

const { default: Redlock } = await import("redlock");


class OutcomeNotificationService {
  constructor(redisClient, rulesetService, callerContext) {
    this.componentName = "OutcomeNotificationService";
    this.rulesetService = rulesetService;
    this.componentContext = [{ componentName: this.componentName }];

    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "constructor" }
    ];

    if (!redisClient) {
      logger.error(loggingContext, "Redis not configured");
      throw new Error("Redis not configured");
    }

    logger.info(loggingContext, "creating OutcomeNotificationService");
    this.redisClient = redisClient;
  }



  // Processes a transaction outcome notification.
  async processOutcomeNotification(
    callingEntityId,
    transactionContextId,
    originTransactionId,
    timeStamp,
    transactionData,
    status,
    modificationsApplied,
    callerContext,
  ) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "evaluate" },
      { callingEntityId },
      { transactionContextId },
      { originTransactionId },
    ];

    logger.info(loggingContext, "processing outcome notification");

    try {
      const entity = JSON.parse(
        await this.redisClient.hget(
          "entities:{common}",
          `entity:${callingEntityId}`,
        ),
      );

      if (!entity) {
        const error = new Error("Entity not found for Id: " + callingEntityId);
        logger.error(loggingContext, error);
        throw error;
      }

      const transactionContext = entity.transactionContexts.find(
        (transactionContext) =>
          transactionContext.contextId === transactionContextId,
      );

      if (!transactionContext) {
        throw new Error(
          `Transaction context id "${transactionContextId}" not found for entity id "${callingEntityId}"`,
        );
      }

      const transactionContextParameterDefinitions =
        transactionContext?.properties;

      const rulesets = await this.rulesetService.findRulesetsByEntityIdAndContextId(
        callingEntityId,
        transactionContextId,
       loggingContext,
      );

      logger.info(loggingContext, { rulesets });

      const persistentVariablesLock = new Redlock([this.redisClient], {
        retryCount: 10,
        retryDelay: 50, // time in ms
      });
      let results = []; 

      for (const ruleset of rulesets) {
        results.push(
          (await executeRulesetOutcomeNotification(
            ruleset,
            callingEntityId,
            transactionContextParameterDefinitions,
            transactionContextId,
            originTransactionId,
            timeStamp,
            transactionData,
            status,
            modificationsApplied,
            this.redisClient,
            persistentVariablesLock,
            loggingContext,
          ))?.result
        );
      }
      return results;
    } catch (error) {
      logger.error(loggingContext, error);
      throw error;
    }
  }
}

export default OutcomeNotificationService;

