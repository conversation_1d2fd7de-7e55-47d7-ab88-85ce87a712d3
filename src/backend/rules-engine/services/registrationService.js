import { logger } from "../middleware/logger.js";
import { generateSecureGUID } from "../utils/guidGenerator.js"
import {transformEmptyObjectsToArrays} from "../utils/transformEmptyObjectsToArrays.js"

const entityArrayFields = [
  "transactionContexts",
  "transactionContexts.properties",
  "transactionContexts.properties.values",
  "transactionContexts.constraints.validValues",
  
];

const REGISTER_ENTITY_LUA = `
  local existingEntity = redis.call("HGET", KEYS[1], ARGV[1])
  if existingEntity then
  return redis.error_reply("Entity already exists")
  end
  redis.call("HSET", KEYS[1], ARGV[1], ARGV[2])
  return "Entity created successfully"
`;
const UPDATE_ENTITY_LUA = `
  local tag = "{common}"
  local existingEntity = redis.call("HGET", KEYS[1], ARGV[1])
  if not existingEntity then
    return redis.error_reply("Entity not found")
  end
  redis.call("HSET", KEYS[1], ARGV[1], ARGV[2])
  return "Entity updated successfully"
`;

class RegistrationService {
  constructor(redisClient, callerContext) {
    this.componentName = "RegistrationService";
    this.componentContext = [{ componentName: this.componentName }];

    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "constructor" },
    ];

    if (!redisClient) {
      logger.error(loggingContext, "Redis not configured");
      throw new Error("Redis not configured");
    }

    logger.info(loggingContext, "creating RegistrationService ");
    this.redisClient = redisClient;
  }

  async registerEntity(entityData, callerContext) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "registerEntity" },
    ];

    const entityId = generateSecureGUID();


    loggingContext.push({ entityId: entityId });

    logger.info(loggingContext, { Hi: "testing the logging context" });

    const redisKey = "entities:{common}"; // the name of the hash
    const redisField = `entity:${entityId}`;
    const entityString = JSON.stringify(entityData);

    // Perform the Lua script EVAL with one key (the "entities" hash),
    // and two arguments (the field name and the JSON data).
    logger.debug(loggingContext, "Calling LUA REGISTER_ENTITY_LUA");
    let result;
    try {
      result = await this.redisClient.eval(
        REGISTER_ENTITY_LUA,
        1,
        redisKey,
        redisField,
        entityString,
      );
    } catch (error) {
      logger.error(loggingContext, result);
      throw error;
    }

    if (result === "Entity created successfully") {
      return { entityId, message: result };
    } else if (result === "Entity already exists") {
      logger.warn(loggingContext, "Entity already exists");
      throw new Error(result);
    } else {
      logger.error(loggingContext, result);
      throw new Error(result);
    }
  }

  async getEntity(entityId, callerContext) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "getEntity" },
      { entityId: entityId },
    ];

    try {
      const entityData = await this.redisClient.hget(
        "entities:{common}",
        `entity:${entityId}`,
      );
      if (!entityData) {
        const errorMessage = "Entity not found";
        logger.warn(loggingContext, errorMessage);
        throw new Error(errorMessage);
      }
      return transformEmptyObjectsToArrays(JSON.parse(entityData),entityArrayFields );
    } catch (error) {
      logger.error(loggingContext, error);
      throw new Error(error);
    }
  }

  async getEntities(callerContext) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "getEntities" },
    ];

    try {
      
      // Fetch all rulesets from the Redis hash "rulesets"
      const entitiesData = await this.redisClient.hgetall("entities:{common}");

      // Convert each entity from JSON string to an object and add entityId
      const entities = Object.entries(entitiesData).map(([field, value]) => {

        const entity = JSON.parse(value);

        // Strip "entity:" prefix from the field to get the entityId
        entity.entityId = field.replace('entity:', '');
        return transformEmptyObjectsToArrays(entity,entityArrayFields);
      });

      return entities;
      
    } catch (error) {
      logger.error(loggingContext, error);
      throw new Error(`Failed to fetch entities: ${error}`);
    }
  }

  async updateEntity(entityId, entityData, callerContext) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "updateEntity" },
    ];

    const redisKey = "entities:{common}"; // Hash name
    const redisField = `entity:${entityId}`;
    const entityString = JSON.stringify(entityData);

    try {
      // Perform the Lua script EVAL with one key and two arguments.
      const result = await this.redisClient.eval(
        UPDATE_ENTITY_LUA,
        1,
        redisKey,
        redisField,
        entityString,
      );

      // If Lua script returns an error_reply, node-redis will throw an error.
      // If it returns "Entity updated successfully", we can safely return the updated data.
      if (result === "Entity updated successfully") {
        return entityData;
      } else {
        throw new Error(result);
      }
    } catch (error) {
      logger.error(loggingContext, error);
      throw new Error(error.message);
    }
  }

  async deleteEntity(entityId, callerContext) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "deleteEntity" },
    ];

    const result = await this.redisClient.hdel(
      "entities:{common}",
      `entity:${entityId}`,
    );
    if (result === 0) {
      const errorMessage = "Entity not found";
      logger.error(loggingContext, errorMessage);
      throw new Error(errorMessage);
    }
    return { message: "Entity deleted successfully" };
  }
}

export default RegistrationService;
