import SmsQueueModel from "../models/smsQueue/SmsQueueModel.js";

/**
 * Service class for SMS queue operations.
 */

class SmsQueueService {

    /**
     * 
     * @param {import("../models/smsQueue/SmsQueueModel").SmsQueueModel[]} dataArray 
     * @returns 
     */
  async bulkCreateQueueEntries(dataArray) {
    if (!Array.isArray(dataArray) || dataArray.length === 0) {
      throw new Error('Input data must be a non-empty array.');
    }

    try {
      return await SmsQueueModel.bulkCreate(dataArray, {
        validate: true, // Validates each entry before inserting
        ignoreDuplicates: false, // Adjust if you want to ignore duplicate entries
      });
    } catch (error) {
      throw new Error(`Failed to add entries to the SMS queue: ${error.message}`);
    }


  }

}

export default SmsQueueService;