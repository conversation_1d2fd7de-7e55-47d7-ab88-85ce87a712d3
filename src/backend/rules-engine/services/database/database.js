// File containing all db connections

import { Sequelize } from "sequelize";
import { smsQueueDbConfig } from "../../config/database.js";


// Initialize Sequelize instance for sms_queue db
export const SmsQueueDb = new Sequelize(
    smsQueueDbConfig.database,
    smsQueueDbConfig.user,
    smsQueueDbConfig.password,
    {
      host: smsQueueDbConfig.host,
      dialect: smsQueueDbConfig.dialect,
      logging: smsQueueDbConfig.logging,
      pool: smsQueueDbConfig.pool,
    }
  );
  