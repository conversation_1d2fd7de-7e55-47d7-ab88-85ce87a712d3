import { logger } from "../middleware/logger.js";
import <PERSON>is from "ioredis";

class RedisClient {
  static redisClient = null;

  static getConnection() {
    return this.redisClient;
  }

  static validateRedisHosts(hostsJson) {
    let hosts;
    try {
      hosts = JSON.parse(hostsJson);
    } catch (error) {
      throw new Error(
        `Invalid JSON format for REDIS_HOSTS: ${hostsJson}: `,
        error,
      );
    }
    if (!Array.isArray(hosts)) {
      throw new Error("REDIS_HOSTS must be an array");
    }
    hosts.forEach((item, index) => {
      if (typeof item !== "object" || item === null) {
        throw new Error(`Item at index ${index} is not a valid object`);
      }
      if (typeof item.host !== "string") {
        throw new Error(
          `The "host" property at index ${index} must be a string`,
        );
      }
      if (typeof item.port !== "number") {
        throw new Error(
          `The "port" property at index ${index} must be a number`,
        );
      }
    });
    return hosts;
  }

  /**
   * Asynchronously configures the Redis client.
   * When a single host/port pair is provided, a standalone Redis instance is created.
   * When multiple host/port pairs are provided, a Redis Cluster is created.
   * @param {string} hostsJson - JSON string of an array with host and port for each node.
   */
  static async configure(hostsJson) {
    try {
      if (!this.redisClient) {
        const nodes = this.validateRedisHosts(hostsJson);
        if (!nodes || !nodes.length) {
          logger.fatal(
            [{ component: "RedisClient" }, { method: "configure" }],
            "No Redis nodes specified",
          );
          process.exit(1);
        }

        if (nodes.length === 1) {
          logger.info(
            [{ component: "RedisClient" }, { method: "configure" }],
            `Configuring RedisClient for single instance with node: ${JSON.stringify(
              nodes[0],
            )}`,
          );
          // Create a standalone Redis client
          this.redisClient = new Redis(nodes[0].port, nodes[0].host, {
            retryStrategy: (times) => {
              if (times > 10) {
                return null;
              }
              return Math.min(times * 100, 3000);
            },
          });
        } else {
          logger.info(
            [{ component: "RedisClient" }, { method: "configure" }],
            `Configuring RedisClient for cluster with nodes: ${JSON.stringify(
              nodes,
            )}`,
          );
          // Create a Redis Cluster client
          this.redisClient = new Redis.Cluster(nodes, {
            clusterRetryStrategy: (times) => {
              if (times > 10) {
                return null;
              }
              return Math.min(times * 100, 3000);
            },
            retryDelayOnFailover: 1000,
          });
        }

        // Handle errors and connection events for both single instance and cluster.
        this.redisClient.on("error", (err) => {
          logger.error(
            [
              { component: "RedisClient" },
              { method: "configure" },
              { eventHandler: "redisClient on error" },
            ],
            err,
          );
        });

        this.redisClient.on("connect", (connectionInfo) => {
          const mode = nodes.length === 1 ? "single instance" : "cluster";
          logger.info(
            [
              { component: "RedisClient" },
              { method: "configure" },
              { eventHandler: "redisClient on connect" },
            ],
            `Connected to Redis ${mode} (connection established).`,
          );
          if (connectionInfo) {
            logger.info(
              [
                { component: "RedisClient" },
                { method: "configure" },
                { eventHandler: "redisClient on connect" },
              ],
              { connectionInfo },
            );
          }
        });
      }

      // For cluster mode, force a refresh of the slots cache so that master nodes are discovered.
      if (this.redisClient instanceof Redis.Cluster) {
        logger.info(
          [{ component: "RedisClient" }, { method: "configure" }],
          "Awaiting slot refresh",
        );
        await this.redisClient.refreshSlotsCache();
        logger.info(
          [{ component: "RedisClient" }, { method: "configure" }],
          "Slots refreshed",
        );
      }

      // Wait for the client to become "ready" (i.e. connection established).
      if (this.redisClient.status !== "ready") {
        const mode =
          this.redisClient instanceof Redis.Cluster
            ? "cluster"
            : "single instance";
        logger.info(
          [{ component: "RedisClient" }, { method: "configure" }],
          `Waiting for Redis ${mode} to become ready...`,
        );
        await new Promise((resolve) => {
          this.redisClient.once("ready", () => {
            logger.info(
              [{ component: "RedisClient" }, { method: "configure" }],
              `Redis ${mode} is ready (ready event received).`,
            );
            resolve();
          });
        });
      } else {
        const mode =
          this.redisClient instanceof Redis.Cluster
            ? "cluster"
            : "single instance";
        logger.info(
          [{ component: "RedisClient" }, { method: "configure" }],
          `Redis ${mode} is already ready.`,
        );
      }

      // For cluster mode, log master nodes from the cluster.
      if (this.redisClient instanceof Redis.Cluster) {
        const masters = this.redisClient.nodes("master");
        if (masters.length === 0) {
          logger.warn(
            [{ component: "RedisClient" }, { method: "configure" }],
            "No master nodes found in the cluster slots cache.",
          );
        } else {
          logger.info(
            [{ component: "RedisClient" }, { method: "configure" }],
            "Master nodes available:",
            masters.map((node) => `${node.options.host}:${node.options.port}`),
          );
        }
      }
    } catch (error) {
      logger.fatal(
        [
          { component: "RedisClient" },
          { method: "configure" },
          { errorHandler: "catch" },
        ],
        error,
      );
      process.exit(1);
    }
  }
}

// Uncomment and adjust the following for graceful shutdown if needed:
// process.on("SIGINT", async () => {
//   await RedisClient.redisClient.quit();
// });

export default RedisClient;
