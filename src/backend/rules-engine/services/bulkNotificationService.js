import { helperFunctions } from "../utils/helper.js";

import { logger } from "../middleware//logger.js";

export default class BulkNotificationService {
  /**
   * Constructor function
   * @param {import("./redisClient.js").RedisClientJsDoc} redisClient
   */
  constructor(redisClient, callerContext) {
    /** @type {import("./redisClient.js").RedisClientJsDoc} */
    this.componentName = "BulkNotificationService";
    this.loggingContext = [
      ...callerContext,
      { componentName: this.componentName },
    ];

    this.redisClient = redisClient;

    if (!this.redisClient) {
      logger.fatal(this.loggingContext, "Where is the redis client!!!!");
      process.exit(1);
    }
  }

  /**
   * Checks if a list of a specific type and ID exists in Redis.
   *
   * @param {string} listId - The ID of the list to check.
   * @returns {Promise<boolean>} - A promise that resolves to true if the list exists, false otherwise.
   * @throws {Error} - Throws an error if the check operation fails.
   */
  _checkListExists = async (listId) => {
    try {
      const exists = await this.redisClient.hexists(`bulk_lists:{common}`, listId);
      return exists == true; // Returns true if key exists, false if it does not
    } catch (error) {
      console.error(
        `Error checking if bulk notification list with ID ${listId} exists in Redis:`,
        error,
      );
      throw new Error(
        `Failed to check if bulk notification list exists: ${error.message}`,
      );
    }
  };

  _generateElementsKey = (id) => {
    return `bulk_list:{common}:elems:${id}`;
  };

  /**
   * Generates a unique identifier by list name and appending a counter to the slug of the list name if necessary.
   * Checks the existence of the ID in Redis using checkListExists and increments the counter until a unique ID is found.
   *
   * @param {string} name - The list name.
   * @returns {Promise<string>} - A promise that resolves to a unique identifier.
   */
  _generateUniqueId = async (name) => {
    // Generate a base ID from the list name
    const baseId = helperFunctions.generateUnderscoreSlug(name);

    let uniqueId = baseId;
    let counter = 1;

    // Check if the key exists using checkListExists
    while (await this._checkListExists(uniqueId)) {
      uniqueId = `${baseId}${counter}`; // Append the counter to the baseId
      counter += 1; // Increment the counter for the next check
    }

    return uniqueId; // Return the unique ID once a non-existing ID is found
  };

  /**
   * Retrieves a list by the id from the Redis based on the specified type and ID along with the elements if required.
   *
   * @param {string} listId - The unique identifier of the list.
   * @param {boolean} [getElements=false] - A flag to indicate if the elements should be retrieved.
   * @returns {Promise.<ListsObject>} The parsed list data from Redis.
   * @throws {Error} If the list is not found or if there is an error during retrieval.
   */
  getListById = async (listId, getElements, callerContext) => {
    const loggingContext = [
      ...callerContext,
      ...this.loggingContext,
      { method: "getListById" },
    ];
    try {
      const listData = await this.redisClient.hget(`bulk_lists:{common}`, listId);
      if (!listData) {
        let error = new Error(
          `bulk notification list with ID ${listId} not found`,
        );
        logger.error(loggingContext, error);
        throw error;
      }

      const formatedObject = this._formatListObjectFromRedis(
        JSON.parse(listData),
        listId,
      );

      // If the elements are requested, fetch them from Redis
      if (getElements) {
        const elementsJson = await this.redisClient.get(
          formatedObject.elementsKey,
        ); // Fetch the JSON string
        if (elementsJson) {
          const elements = JSON.parse(elementsJson); // Parse the JSON string into an array or object
          formatedObject.elements = elements;
        } else {
          formatedObject.elements = []; // Default to an empty array if no data exists
        }
      }

      return formatedObject;
    } catch (error) {
      logger.error(loggingContext, error);
      throw new Error(error.message);
    }
  };

  /**
   * Asynchronously creates a new list in Redis under the specified list type.
   *
   * @param {ListsObject} newList - The list object to be stored, which must include a unique 'id' property.
   * @param {Array.<string | number>} elements - The csv file rows data.
   * @throws {Error} Throws an error if the list creation in Redis fails.
   */
  createList = async (newList, elements, callerContext) => {
    const loggingContext = [
      ...callerContext,
      this.loggingContext,
      { method: "createList" },
    ];
    const multi = this.redisClient.multi(); // Initialize the Redis transaction

    try {
      // If type is not provided then set to the string
      if (!newList.type) newList.type = "string";

      // Generate the unique ID for the list
      const listId = await this._generateUniqueId(newList.name);

      const elementsKey = this._generateElementsKey(listId);

      /**
       * Format the list object to be stored in Redis. We are using the name, type etc as object properties so that we can easily modify the list object in the future if needed with better redis performance
       */
      const formattedList = {
        ...newList,
        elementsKey: elementsKey,
      };

      // Add the hash command to store the list object under a key
      multi.hset(`bulk_lists:{common}`, listId, JSON.stringify(formattedList)); // Store the list object

      // Elements is an array that needs to be stored as a JSON string
      multi.set(elementsKey, JSON.stringify(elements)); // Store the elements as a JSON string

      // Execute the transaction
      await multi.exec(); // Execute both commands in the transaction

      logger.trace(
        loggingContext,
        `bulk notification list with ID ${listId} created successfully`,
      );
      return formattedList;
    } catch (error) {
      logger.error(loggingContext, error);

      throw error;
    }
  };

  /**
   * Deletes a list by the ID from the Redis.
   * @param {string} listId - The ID of the list to delete.
   */
  deleteListById = async (listId, callerContext) => {
    const loggingContext = [
      ...callerContext,
      this.loggingContext,
      { method: "deleteListById" },
    ];
    try {
      // Get the elements key for the list
      const listData = await this.getListById(listId, true, callerContext);
      const elementsKey = listData.elementsKey;

      // Start a Redis transaction
      const transaction = this.redisClient.multi();

      // Add the commands to the transaction
      transaction.hdel(`bulk_lists:{common}`, listId);
      transaction.del(elementsKey);

      // Execute the transaction
      const results = await transaction.exec();

      // Check the result of hDel
      const hDelResult = results[0]; // The result of the first command (hDel)
      if (hDelResult === 0) {
        const error = new Error(
          `bulk notification list with ID ${listId} not found`,
        );

        logger.error(loggingContext, error);
        throw error;
      }

      return true;
    } catch (error) {
      logger.error(loggingContext, error);
      throw error;
    }
  };

  /**
   * Get all lists without elements from the Redis.
   * @returns {Promise<import("../controllers/bulkNotificationController.js").BulkNotificationListArray>}
   */
  getAllLists = async (callerContext) => {
    const loggingContext = [
      ...callerContext,
      this.loggingContext,
      { method: "getAllLists" },
    ];
    try {
      const listsData = await this.redisClient.hgetall(`bulk_lists:{common}`);

      if (!listsData) {
        const error = new Error(`No bulk notification lists found`);

        logger.error(loggingContext, error);
        throw error;
      }

      // Format the list
      const lists = Object.entries(listsData).map(([listId, listData]) => {
        // Return the list object with the `id`, `name`, `type`, elemeCount
        return this._formatListObjectFromRedis(JSON.parse(listData), listId);
      });

      return lists;
    } catch (error) {
      logger.error(loggingContext, error);
      throw error;
    }
  };

  /**
   * Format the list object which came from the Redis.
   * @param {{name: string, type: string, elemsCount: number}} listObj
   * @param {string} key - The key of the list object in Redis.
   * @returns {import("../controllers/bulkNotificationController.js").BulkNotificationListArray[0] & {elementsKey: string}}
   */
  _formatListObjectFromRedis = (listObj, key) => {
    return {
      id: key,
      ...listObj,
    };
  };
}

/**
 * @typedef {import("../controllers/bulkNotificationController.js").BulkNotificationListArray[0] & {elementsKey: string, elements: Array.<string>}} ListsObject
 */
