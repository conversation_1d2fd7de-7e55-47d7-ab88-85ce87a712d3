import { logger } from "../middleware/logger.js";
import { getEcdsApiToken } from "../connectors/ecdsApiTransfer.js";
import Config from "../config/Config.js";
import crypto from "crypto";

/**
 * Stock API Service
 * 
 * Manages stock transfer operations with secure credential handling
 * and support for multiple providers.
 */
export default class StockApiService {
  constructor(redisClient, callerContext) {
    this.componentName = "StockApiService";
    this.componentContext = [{ componentName: this.componentName }];
    this.redisClient = redisClient;
    this.encryptionKey = Config.STOCK_API_ENCRYPTION_KEY || "default-key-change-in-production";
    
    logger.info(
      [...callerContext, ...this.componentContext, { functionName: "constructor" }],
      "Created StockApiService"
    );
  }

  /**
   * Transfer stock between accounts
   * @param {Object} transferRequest - Transfer details
   * @param {string} transferRequest.fromAccount - Source account identifier
   * @param {string} transferRequest.toAccount - Target account identifier  
   * @param {number} transferRequest.amount - Amount to transfer
   * @param {string} transferRequest.stockType - Type of stock (optional)
   * @param {string} transferRequest.apiId - Registered API identifier
   * @param {string} transferRequest.rulesetId - Ruleset owning the credentials
   * @param {string} transferRequest.accountRef - Account reference (e.g., "primary")
   * @param {Array} callerContext - Logging context
   * @returns {Object} Transfer result
   */
  async transferStock(transferRequest, callerContext) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "transferStock" }
    ];

    const { fromAccount, toAccount, amount, stockType = "default", apiId, rulesetId, accountRef } = transferRequest;

    logger.info(loggingContext, { 
      fromAccount, 
      toAccount, 
      amount, 
      stockType, 
      apiId, 
      rulesetId, 
      accountRef 
    });

    try {
      // Get API definition
      const apiDefinition = await this.getApiDefinition(apiId, loggingContext);
      if (!apiDefinition) {
        throw new Error(`API definition not found for apiId: ${apiId}`);
      }

      // Get credentials for the ruleset
      const credentials = await this.getCredentials(rulesetId, accountRef, loggingContext);
      if (!credentials) {
        throw new Error(`Credentials not found for ruleset: ${rulesetId}, account: ${accountRef}`);
      }

      // Execute transfer based on provider
      const result = await this.executeTransfer(
        apiDefinition,
        credentials,
        { fromAccount, toAccount, amount, stockType },
        loggingContext
      );

      logger.info(loggingContext, "Stock transfer completed successfully");
      return {
        success: true,
        transferId: result.transferId || `transfer_${Date.now()}`,
        amount,
        fromAccount,
        toAccount,
        stockType,
        timestamp: new Date().toISOString(),
        providerResponse: result
      };

    } catch (error) {
      logger.error(loggingContext, `Stock transfer failed: ${error.message}`);
      return {
        success: false,
        error: error.message,
        amount,
        fromAccount,
        toAccount,
        stockType,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Execute transfer based on provider type
   * @private
   */
  async executeTransfer(apiDefinition, credentials, transferData, callerContext) {
    const { provider } = apiDefinition;
    
    switch (provider) {
      case "crediverse":
        return await this.executeCrediverseTransfer(credentials, transferData, callerContext);
      default:
        throw new Error(`Unsupported provider: ${provider}`);
    }
  }

  /**
   * Execute Crediverse transfer
   * @private
   */
  async executeCrediverseTransfer(credentials, transferData, callerContext) {
    const loggingContext = [...callerContext, { functionName: "executeCrediverseTransfer" }];
    
    try {
      // Get access token
      const accessToken = await getEcdsApiToken(
        credentials.apiUsername,
        credentials.apiSecret,
        credentials.accountUsername,
        credentials.accountPassword
      );

      // Prepare transfer request
      const transferRequest = {
        amount: transferData.amount,
        targetMSISDN: transferData.toAccount
      };

      // Execute transfer
      const ECDS_API_PORT = Config.ECDS_API_PORT || 9084;
      const ECDS_API_URL = Config.ECDS_API_URL || "http://ecds-api";
      const ECDS_API_TRANSFER_PATH = "/api/account/transaction/transfer";
      const ECDS_API_TRANSFER_URI = `${ECDS_API_URL}:${ECDS_API_PORT}${ECDS_API_TRANSFER_PATH}`;

      const response = await fetch(ECDS_API_TRANSFER_URI, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${accessToken}`
        },
        body: JSON.stringify(transferRequest)
      });

      if (!response.ok) {
        const errorResponse = await response.json();
        throw new Error(`Crediverse transfer failed: ${JSON.stringify(errorResponse)}`);
      }

      const transferResponse = await response.json();
      logger.trace(loggingContext, { transferResponse });
      
      return transferResponse;

    } catch (error) {
      logger.error(loggingContext, `Crediverse transfer error: ${error.message}`);
      throw error;
    }
  }

  /**
   * Store encrypted credentials for a ruleset
   */
  async storeCredentials(rulesetId, accountRef, credentials, callerContext) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "storeCredentials" }
    ];

    try {
      const credentialsKey = `stockApi:credentials:${rulesetId}`;
      
      // Get existing credentials or create new structure
      let existingData = {};
      const existing = await this.redisClient.get(credentialsKey);
      if (existing) {
        existingData = JSON.parse(existing);
      }

      // Encrypt sensitive fields
      const encryptedCredentials = {
        apiUsername: credentials.apiUsername,
        apiSecret: this.encrypt(credentials.apiSecret),
        accountUsername: credentials.accountUsername,
        accountPassword: this.encrypt(credentials.accountPassword),
        provider: credentials.provider || "crediverse"
      };

      // Update credentials structure
      const credentialsData = {
        ...existingData,
        rulesetId,
        accounts: {
          ...existingData.accounts,
          [accountRef]: encryptedCredentials
        },
        updatedAt: new Date().toISOString()
      };

      if (!existingData.createdAt) {
        credentialsData.createdAt = new Date().toISOString();
      }

      await this.redisClient.set(credentialsKey, JSON.stringify(credentialsData));
      
      logger.info(loggingContext, `Credentials stored for ruleset: ${rulesetId}, account: ${accountRef}`);
      return { success: true };

    } catch (error) {
      logger.error(loggingContext, `Failed to store credentials: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get decrypted credentials for a ruleset
   */
  async getCredentials(rulesetId, accountRef, callerContext) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "getCredentials" }
    ];

    try {
      const credentialsKey = `stockApi:credentials:${rulesetId}`;
      const credentialsData = await this.redisClient.get(credentialsKey);
      
      if (!credentialsData) {
        logger.warn(loggingContext, `No credentials found for ruleset: ${rulesetId}`);
        return null;
      }

      const parsed = JSON.parse(credentialsData);
      const accountCredentials = parsed.accounts?.[accountRef];
      
      if (!accountCredentials) {
        logger.warn(loggingContext, `No credentials found for account: ${accountRef}`);
        return null;
      }

      // Decrypt sensitive fields
      return {
        apiUsername: accountCredentials.apiUsername,
        apiSecret: this.decrypt(accountCredentials.apiSecret),
        accountUsername: accountCredentials.accountUsername,
        accountPassword: this.decrypt(accountCredentials.accountPassword),
        provider: accountCredentials.provider
      };

    } catch (error) {
      logger.error(loggingContext, `Failed to get credentials: ${error.message}`);
      return null;
    }
  }

  /**
   * Register a new stock API definition
   */
  async registerApi(apiDefinition, callerContext) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "registerApi" }
    ];

    try {
      const { apiId } = apiDefinition;
      const apiKey = `stockApi:definitions:${apiId}`;
      
      // Check if API already exists
      const existing = await this.redisClient.get(apiKey);
      if (existing) {
        throw new Error(`API with ID ${apiId} already exists`);
      }

      const apiData = {
        ...apiDefinition,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      await this.redisClient.set(apiKey, JSON.stringify(apiData));
      
      // Add to index
      await this.redisClient.sadd("stockApi:definitions:index", apiId);
      
      logger.info(loggingContext, `API registered: ${apiId}`);
      return { success: true, apiId };

    } catch (error) {
      logger.error(loggingContext, `Failed to register API: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get API definition by ID
   */
  async getApiDefinition(apiId, callerContext) {
    const loggingContext = [
      ...callerContext,
      ...this.componentContext,
      { functionName: "getApiDefinition" }
    ];

    try {
      const apiKey = `stockApi:definitions:${apiId}`;
      const apiData = await this.redisClient.get(apiKey);
      
      if (!apiData) {
        logger.warn(loggingContext, `API definition not found: ${apiId}`);
        return null;
      }

      return JSON.parse(apiData);

    } catch (error) {
      logger.error(loggingContext, `Failed to get API definition: ${error.message}`);
      return null;
    }
  }

  /**
   * Encrypt sensitive data
   * @private
   */
  encrypt(text) {
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }

  /**
   * Decrypt sensitive data
   * @private
   */
  decrypt(encryptedText) {
    const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
    let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
}
