import { <PERSON><PERSON> } from "sequelize/lib/utils";
import { logger } from "../middleware/logger.js";

class AnalyticsService {
  constructor(redisClient, callerContext) {
    this.componentName = "AnalyticsService";
    this.redisClient = redisClient;
    this.componentContext = [{ componentName: this.componentName }];
    this.callerContext = callerContext;
  }
  /**
     * Deduplicates persistent variable logs array by keeping only the last occurrence
     * of entries with the same id and ruleId combination.
     * 
     * @private
     * @param {Array} logs - Array of persistent variable log objects
     * @returns {Array} Deduplicated array with only the last occurrence of each id-ruleId combination
     */
  _deduplicatePersistentLogs(logs) {
    const seen = new Map();
    const result = [];

    // Iterate through logs to find the last occurrence of each id-ruleId combination
    logs.forEach((log, index) => {
      const key = `${log.id}_${log.ruleId}`;
      seen.set(key, index);
    });

    // Collect only the entries that are the last occurrence of their id-ruleId combination
    logs.forEach((log, index) => {
      const key = `${log.id}_${log.ruleId}`;
      if (seen.get(key) === index) {
        result.push(log);
      }
    });

    return result;
  }

  /**
     * Formats and stores analytics data in Redis queue for later processing.
     * 
     * @async
     * @param {Object} journalData - The evaluation journalData data containing transaction details
     * @param {Object} journalData.timestamp - Timestamp of the evaluation
     * @param {string} journalData.entityId - Entity identifier
     * @param {string} journalData.transactionContextId - Context identifier for the transaction
     * @param {string} journalData.rulesetId - Ruleset identifier
     * @param {number} journalData.rulesetVersion - Version of the ruleset
     * @param {Array} journalData.evaluationRecord.persistentVariableLogs - Definitions of persistent variable logs
     * @param {Object} journalData.evaluationRecord - Detailed evaluation results
     * @param {Object} journalData.evaluationRecord.evaluationParameters - Parameters used during evaluation
     * @param {string} journalData.evaluationRecord.evaluationParameters.originTransactionId - Original transaction identifier
     * @param {Object} journalData.evaluationRecord.persistentVariables - State variables that persist between evaluations
     * @param {Array} callerContext - Logging context information
     * @param {Object} ruleSetJson - Complete ruleset configuration
     * @param {Array} ruleSetJson.persistentVariables - Definitions of persistent variables
     * @param {Array} ruleSetJson.collectionMappings - Mappings of collections to their keys
     * @returns {Promise<number>} Result of the Redis push operation
     */
  async insertAnalyticsData(journalData, ruleSetJson) {

    const loggingContext = [
      ...this.callerContext,
      ...this.componentContext,
      { functionName: "insertAnalyticsData" },
    ];

    try {

      logger.info(loggingContext, "inserting analytics data into Redis from the JournalData", journalData);

      const queueKey = "analytics_queue:{common}";

      // If no journalData is provided, return null
      if (!journalData) {
        logger.error(loggingContext, "journalData is not provided, skipping analytics data insertion");
        return null;
      }

      // Format data according to the required structure
      const formattedData = {
        timestamp: journalData.timestamp,
        entity_id: journalData.entityId,
        context_id: journalData.transactionContextId,
        transaction_id: journalData.evaluationRecord.evaluationParameters.originTransactionId,
        ruleset_id: journalData.rulesetId,
        ruleset_ver: journalData.rulesetVersion,
        rule_phase: journalData.rule_phase,
        collections: []
      };

      // Process persistent variables
      const persistentVariables = journalData.evaluationRecord.persistentVariables;

      // Extract transaction data from journalData (assuming it's available in the evaluation record)
      const transactionData = journalData.evaluationRecord.transactionData || {};


      // If ruleSetJson is not provided or persistentVariables is not available, return null
      if (!ruleSetJson || !persistentVariables) {
        logger.error(loggingContext, `${ruleSetJson ? "persistentVariables" : "ruleSetJson"} is not available for transaction ${formattedData.transaction_id}, skipping analytics data insertion`);
        return null;
      }


      let hasPeristenceData = false;

      // Find all persistent variables of type number and their collection mappings
      ruleSetJson.persistentVariables.forEach(variable => {

        // Skip if the variable is not a number or if it doesn't exist in the persistentVariables object
        if (variable.type !== 'number' || !Object.prototype.hasOwnProperty.call(persistentVariables, variable.variableId)) {
          return;
        }

        const collectionId = variable.collectionId;

        // Find the collection mapping
        const collectionMapping = ruleSetJson.collectionMappings.find(
          mapping => mapping.collectionId === collectionId
        );

        // If no collection mapping is found, skip  
        if (!collectionMapping) {
          return;
        }

        // Extract the key and value from transaction data
        const keyPropertyId = collectionMapping.keyMapping.propertyId;
        const actualValue = transactionData[keyPropertyId] || null; // Actual value from transaction data

        // Find existing collection in array or create new one
        let collection = formattedData.collections.find(col => col.id === collectionId);

        if (!collection) {
          collection = {
            id: collectionId,
            key: keyPropertyId, // Property name (e.g., "bundleId")
            value: actualValue, // Actual value from transaction data (e.g., "1-2")
            persistence: []
          };
          formattedData.collections.push(collection);
        }

        // Add the variable to the collection's persistence array
        const persistentLogs = this._deduplicatePersistentLogs(
          journalData.evaluationRecord.persistentVariableLogs.filter(log => log.id === variable.variableId)
        );

        // Only add to persistence array if there's actual data
        if (persistentLogs.length > 0) {
          collection.persistence.push({
            id: variable.variableId,
            data: persistentLogs.map(log => ({
              value: log.value,
              timestamp: log.timestamp,
              ruleId: log.ruleId
            }))
          });

          hasPeristenceData = true;
        }

      });

      // Only store in Redis if there's persistence data available
      if (!hasPeristenceData) {
        logger.info(loggingContext, "No persistence data available, skipping Redis storage");
        return null;
      }

      const serialized = JSON.stringify(formattedData);

      const data = await this.redisClient.lpush(queueKey, serialized);

      return data;
    } catch (error) {
      logger.error(loggingContext, "error inserting analytics data into Redis", error.stack || error);

    }

    return null;
  }
}

export default AnalyticsService;