// evaluationService.test.js

import { jest, describe, test, beforeEach, expect } from "@jest/globals";
import EvaluationService from "./evaluationService.js";
import Config from '../config/Config.js'

const loggingContext = [{ unitTestingModule: "evaluationService.test.js" }];

describe("EvaluationService", () => {
  let evaluationService;
  let mockerRedisClient;
  let rulesetServiceMock;

  const redisStorage = {};

  console.log(Config);

  // Create a mocked Redis client
  const mockedRedisClient = {
    connect: jest.fn().mockResolvedValue(undefined),
    on: jest.fn(),
    hset: jest.fn(async (key, field, value) => {
      if (!redisStorage[key]) {
        redisStorage[key] = {};
      }
      redisStorage[key][field] = value;
      return 1; // Return 1 field added
    }),
    hget: jest.fn(async (key, field) => {
      if (redisStorage[key] && redisStorage[key][field] !== undefined) {
        return redisStorage[key][field];
      } else {
        return null; // Field does not exist
      }
    }),
  };

  const mockedRedis = {
    createClient: jest.fn().mockReturnValue(mockedRedisClient),
  };

  beforeEach(() => {
    mockerRedisClient = {
      hget: jest.fn(),
      hset: jest.fn(),
    };

    rulesetServiceMock = {
      findRulesetsByEntityIdAndContextId: jest.fn(),
    };

    evaluationService = new EvaluationService(
      mockerRedisClient,
      rulesetServiceMock,
    );
    jest.clearAllMocks();
  });

  test("should return empty actions when no ruleset is found", async () => {
    // Set up the entity
    const entity = {
      entityId: "testEntity",
      transactionContexts: [
        {
          contextId: "testContext",
          properties: [
            {
              name: "transactionProperty",
              type: "string",
              description: "Some property of the transaction",
            },
          ],
        },
      ],
    };

    // Mock redisClient.hget
    mockerRedisClient.hget.mockImplementation(async (hash, key) => {
      if (key === `entity:testEntity` && hash === "entities:{common}") {
        return JSON.stringify(entity);
      }
      return null;
    });

    // Set up the ruleset
    // Mock RulesetService.findRulesetsByEntityIdAndContextId
    rulesetServiceMock.findRulesetsByEntityIdAndContextId.mockResolvedValue([]);

    // Now call evaluationService.evaluate with data that meets the condition
    const transactionDataMatching = {
      transactionProperty: "TRANSACTION",
    };

    await expect(
      evaluationService.evaluate(
        "testEntity", // callingEntityId
        "no_such_testContext", // transactionContextId
        "testTransaction_001",
        new Date().toISOString(), // timeStamp
        transactionDataMatching,
        loggingContext,
      ),
    ).rejects.toThrow(
      `Transaction context id "no_such_testContext" not found for entity id "testEntity"`,
    );
  });
});
