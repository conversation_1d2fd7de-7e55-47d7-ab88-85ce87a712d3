import { logger } from "../middleware/logger.js";
import {transformEmptyObjectsToArrays} from "../utils/transformEmptyObjectsToArrays.js"

const rulesetArrayPaths = [
  "collectionMappings",
  "persistentVariables",
  "localVariables",
  "evaluationRules",
  "outcomeRules",
  "evaluationRules.variableAssignments",
  "evaluationRules.webhookCalls",
  "outcomeRules.variableAssignments",
  "outcomeRules.webhookCalls",
  "outcomeRules.condition.parameters.conditions.parameters.conditions.parameters.conditions",
]
// ————————————————————————————————————————————————
// 1) SAVE
// ————————————————————————————————————————————————
const SAVE_RULESET_LUA_CLUSTER = `
  local tag = "{common}"
  local updatedRulesetStr = ARGV[1]
  local updatedRuleset = cjson.decode(updatedRulesetStr)

  local rulesetId            = updatedRuleset.rulesetId
  local entityId             = updatedRuleset.entityId
  local contextId = updatedRuleset.contextId

  -- 1) ensure entity exists
  local existingEntity = redis.call(
    "HGET",
    "entities:"..tag,
    "entity:"..entityId
  )
  if not existingEntity then
    return redis.error_reply(
      "Entity with ID "..entityId.." not found"
    )
  end

  -- 2) figure out new version
  local latestVersionStr = redis.call(
    "HGET",
    "rulesets:"..tag,
    "ruleset:"..rulesetId..":latestVersion"
  )
  local latestVersion = latestVersionStr and tonumber(latestVersionStr) or nil

  if not latestVersion then
    updatedRuleset.version= 1
    updatedRuleset.status = "DRAFT"
  else
    local prevKey = "ruleset:"..rulesetId..":"..latestVersion 

    local prevData = redis.call("HGET","rulesets:"..tag, prevKey)
    if not prevData then
      return redis.error_reply(
        "No previous ruleset for ID "..rulesetId.." v"..latestVersion
      )
    end
    local prev = cjson.decode(prevData)
    updatedRuleset.version= 
      (prev.status == "DRAFT") and latestVersion or latestVersion + 1
    updatedRuleset.status = "DRAFT"
  end

  -- 3) store new ruleset JSON (preserving empty arrays)
  local baseJson = updatedRulesetStr
    :gsub('"version"%s*:%s*[%d]+,?', '')
    :gsub('"status"%s*:%s*"[^"]+",?', '')
    :gsub(',%s*%}', '}')
    :sub(1,-2)

  local newJson = baseJson
    .. ', "version":'..updatedRuleset.version
    .. ', "status":"'..updatedRuleset.status..'"}'

  local newKey = "ruleset:"..rulesetId..":"..updatedRuleset.version


  redis.call("HSET","rulesets:"..tag,newKey,newJson)

  -- 4) bump latestVersion pointer
  redis.call(
    "HSET",
    "rulesets:"..tag,
    "ruleset:"..rulesetId..":latestVersion",
    updatedRuleset.version
  )

  -- 5) track versions in a set
  local versionsSet = "ruleset:"..tag..":"..rulesetId..":versions"


  redis.call("SADD",versionsSet, tostring(updatedRuleset.version))

  -- 6) global set per txContext
  redis.call(
    "SADD",
    "rulesetSet:"..tag,
    "ruleset:"..rulesetId..":"..updatedRuleset.version
  )

  -- 7) per-entity membership
  redis.call(
    "SADD",
    "entity:"..tag..":"..entityId..":"..contextId
      ..":rulesets",
    rulesetId..":"..updatedRuleset.version
  )

  return "Ruleset successfully created"
`;

// ————————————————————————————————————————————————
// 2) DELETE
// ————————————————————————————————————————————————
const DELETE_RULESET_LUA = `
  local tag = "{common}"
  local rulesetId = ARGV[1]

  -- find latest
  local latestStr = redis.call(
    "HGET",
    "rulesets:"..tag,
    "ruleset:"..rulesetId..":latestVersion"
  )
  if not latestStr then
    return redis.error_reply(
      "No latestVersion for "..rulesetId
    )
  end
  local latest = tonumber(latestStr)

  -- fetch it
  local dataKey = "ruleset:"..rulesetId..":"..latest
  local dataStr = redis.call("HGET", "rulesets:"..tag, dataKey)
  if not dataStr then
    return redis.error_reply(
      "No ruleset "..rulesetId.." v"..latest.." for key = "..dataKey 
    )
  end
  local data = cjson.decode(dataStr)
  if data.status ~= "DRAFT" then
    return redis.error_reply("Only rulesets in DRAFT status can be deleted")
  end

  -- remove hash entry
  redis.call("HDEL","rulesets:"..tag,dataKey)

  -- remove from version‐set
  local versionsSet = "ruleset:"..tag..":"..rulesetId..":versions"
  redis.call("SREM",versionsSet,tostring(latest))

  -- remove from global set
  redis.call(
    "SREM",
    "rulesetSet:"..tag,
    "ruleset:"..rulesetId..":"..latest
  )

  -- fix latestVersion pointer or drop it
  local rem = redis.call("SMEMBERS",versionsSet)
  if #rem>0 then
    local maxV=0
    for _,v in ipairs(rem) do
      v=tonumber(v)
      if v>maxV then maxV=v end
    end
    redis.call(
      "HSET",
      "rulesets:"..tag,
      "ruleset:"..rulesetId..":latestVersion",
      maxV
    )
  else
    redis.call(
      "HDEL",
      "rulesets:"..tag,
      "ruleset:"..rulesetId..":latestVersion"
    )
  end

  -- remove per-entity
  redis.call(
    "SREM",
    "entity:"..tag..":"..data.entityId..":"..data.contextId..":rulesets",
    rulesetId..":"..latest
  )

  return "Ruleset deleted successfully"
`;

// ————————————————————————————————————————————————
// 3) ACTIVATE
// ————————————————————————————————————————————————
const ACTIVATE_RULESET_LUA = `
  local tag = "{common}"
  local rulesetId            = ARGV[1]
  local version = tonumber(ARGV[2])

  local key = "ruleset:"..rulesetId..":"..version
  local str = redis.call("HGET","rulesets:"..tag,key)
  if not str then
    return redis.error_reply("Not found "..rulesetId.." v"..version )
  end
  local obj = cjson.decode(str)
  if obj.status ~= "DRAFT" then
    return redis.error_reply("Only rulesets in DRAFT status can be activated")
  end
  if not obj.startDateTime or not obj.endDateTime then
    return redis.error_reply("Missing start/end dates")
  end

  -- flip this one to ACTIVE
  obj.status = "ACTIVE"
  redis.call("HSET","rulesets:"..tag,key,cjson.encode(obj))

  -- any others in ACTIVE → COMPLETED 
  local versionsSet = "ruleset:"..tag..":"..rulesetId..":versions"
  for _,v in ipairs(redis.call("SMEMBERS",versionsSet)) do
    local vnum = tonumber(v)
    if vnum~=version then
      local k2 = "ruleset:"..rulesetId..":"..vnum
      local s2 = redis.call("HGET","rulesets:"..tag,k2)
      if s2 then
        local o2 = cjson.decode(s2)
        if o2.status=="ACTIVE" then
          o2.status="COMPLETED"
          redis.call("HSET","rulesets:"..tag,k2,cjson.encode(o2))
        end
      end
    end
  end

  return "Ruleset activated successfully"
`;

// ————————————————————————————————————————————————
// 4) DEACTIVATE (complete)
// ————————————————————————————————————————————————
const DEACTIVATE_RULESET_LUA = `
  local tag = "{common}"
  local rulesetId            = ARGV[1]
  local version       = tonumber(ARGV[2])

  local key = "ruleset:"..rulesetId..":"..version
  local str = redis.call("HGET","rulesets:"..tag,key)
  if not str then
    return redis.error_reply("Not found "..rulesetId.." v"..version)
  end
  local obj = cjson.decode(str)
  if obj.status ~= "ACTIVE" then
    return redis.error_reply("Only rulesets in an ACTIVE state can be completed")
  end

  obj.status = "COMPLETED"
  redis.call("HSET","rulesets:"..tag,key,cjson.encode(obj))

  return "Ruleset completed successfully"
`;

class RulesetService {
  constructor(redisClient, callerContext) {
    this.redisClient   = redisClient;
    this.componentName = "RulesetService";
    this.componentContext = [{ componentName: this.componentName }];
    logger.info(
      [...callerContext, ...this.componentContext, { functionName: "ctor" }],
      "Creating RulesetService"
    );
  }

  // —————————————————————————
  // getLatest version in a given txContext
  // —————————————————————————
  async getLatestRulesetVersion(rulesetId, callerContext) {
    const logCtx = [
      ...callerContext, ...this.componentContext,
      { functionName: "getLatestRulesetVersion" },
      { rulesetId }, 
    ];
    const latestVersionHash = "rulesets:{common}";
    const latestVersionKey = `ruleset:${rulesetId}:latestVersion`;

    const latest = await this.redisClient.hget( latestVersionHash, latestVersionKey);
    
     if (!latest) {
      logger.warn(logCtx,"no latestVersion found");
      return null;
    }
    return parseInt(latest,10);
  }

  // —————————————————————————
  // fetch one ruleset
  // —————————————————————————
  async getRuleset(
    rulesetId,
    version,
    callerContext
  ) {
    const logCtx = [
      ...callerContext, ...this.componentContext,
      { functionName: "getRuleset" },
      { rulesetId }
    ];
    logger.info(logCtx,"Fetching ruleset");

    let ver = version
      ? parseInt(version,10)
      : await this.getLatestRulesetVersion(
          rulesetId,[...logCtx]
        );

    if (!ver) {
      const e = new Error(`No version found for Ruleset ${rulesetId}`);
      logger.error(logCtx,e);
      throw e;
    }

    const key = `ruleset:${rulesetId}:${ver}`;

    const data = await this.redisClient.hget("rulesets:{common}", key);
    if (!data) {
      const e = new Error(`Ruleset ${rulesetId} v${ver} `);
      logger.error(logCtx,e);
      throw e;
    }
    return transformEmptyObjectsToArrays(JSON.parse(data),rulesetArrayPaths);
  }

  // —————————————————————————
  // save (create or update draft)
  // —————————————————————————
  async saveRuleset(updatedRuleset, callerContext) {
    const { rulesetId, version } = updatedRuleset;
    const logCtx = [
      ...callerContext, ...this.componentContext,
      { functionName: "saveRuleset" },
      { rulesetId },{ version }
    ];
    try {
      const str = JSON.stringify(updatedRuleset);
      const result = await this.redisClient.eval(
        SAVE_RULESET_LUA_CLUSTER,
        1,
        "rulesets:{common}",
        str
      );
      return { message: result, rulesetId, version };
    } catch (err) {
      logger.error(logCtx,err);
      throw new Error(`Failed to save ruleset: ${err}`);
    }
  }

  // —————————————————————————
  // copy into a new rulesetId under same txContext
  // —————————————————————————
  async copyRuleset(
    existingRulesetId,
    existingVersion,
    newRulesetId,
    callerContext
  ) {
    const logCtx = [
      ...callerContext, ...this.componentContext,
      { functionName: "copyRuleset" },
      { existingRulesetId },{ existingVersion },
      { newRulesetId },
    ];
    try {
      const srcKey = `ruleset:${existingRulesetId}:${parseInt(existingVersion,10)}`;
      const srcStr = await this.redisClient.hget("rulesets:{common}", srcKey);
      if (!srcStr) {
        const e = new Error(`Source ${existingRulesetId} v${existingVersion} not found`);
        logger.error(logCtx,e);
        throw e;
      }
      const src = JSON.parse(srcStr);
      const copy = {
        ...src,
        rulesetId: newRulesetId,
        version: 1,
        status: "DRAFT",
        contextId: src.contextId,
        name: src.name + " copy",
        copiedFrom: { rulesetId: existingRulesetId, version: existingVersion }
      };
      delete copy.createdAt;
      delete copy.updatedAt;
      delete copy.metrics;

      await this.saveRuleset(copy, logCtx);
      return {
        message: `Copied to ${newRulesetId}`,
        rulesetId: newRulesetId,
        rulesetVersion: 1
      };
    } catch (err) {
      logger.error(logCtx,err);
      throw new Error(`Failed to copy ruleset: ${err}`);
    }
  }

  // —————————————————————————
  // list all active for entity + txContext
  // —————————————————————————
  async findRulesetsByEntityIdAndContextId(
    entityId,
    contextId,
    callerContext
  ) {
    const logCtx = [
      ...callerContext, ...this.componentContext,
      { functionName: "findRulesetsByEntityIdAndContextId" },
      { entityId }, {contextId}
    ];
    logger.info(logCtx,`Listing rulesets for ${entityId}:${contextId}`);

    const setKey = `entity:{common}:${entityId}:${contextId}:rulesets`;
    const members = await this.redisClient.smembers(setKey);

    const all = await Promise.all(
      members.map(async mv => {
        const [rulesetId, version] = mv.split(":");
        const data = await this.redisClient.hget(
          "rulesets:{common}",
          `ruleset:${rulesetId}:${version}`
        );
        return transformEmptyObjectsToArrays(JSON.parse(data),rulesetArrayPaths);
      })
    );

    const now = Date.now();
    return all.filter(r => {
      if (!r || r.status!=="ACTIVE") return false;
      const start = new Date(r.startDateTime).getTime();
      const end   = new Date(r.endDateTime).getTime();
      return now>=start && now<=end;
    });
  }

  // —————————————————————————
  // delete a draft
  // —————————————————————————
  async deleteRuleset(
    rulesetId,
    callerContext
  ) {
    const logCtx = [
      ...callerContext, ...this.componentContext,
      { functionName: "deleteRuleset" },
      { rulesetId }
    ];
    try {
      const result = await this.redisClient.eval(
        DELETE_RULESET_LUA,
        1,
        "rulesets:{common}",
        rulesetId
      );
      return { message: result };
    } catch (err) {
      logger.error(logCtx,err);
      throw new Error(`Failed to delete ruleset: ${err}`);
    }
  }

  // —————————————————————————
  // list all 
  // —————————————————————————
  async getAllRulesets(callerContext) {
    const logCtx = [
      ...callerContext, ...this.componentContext,
      { functionName: "getAllRulesets" },
    ];

    const members = await this.redisClient.smembers(
      `rulesetSet:{common}`
    );

    const all = await Promise.all(
      members.map(async mv => {
        const [rid, ver] = mv.split(":").slice(1);

        const data = await this.redisClient.hget(
          "rulesets:{common}",
          `ruleset:${rid}:${ver}`
        );
        return transformEmptyObjectsToArrays(JSON.parse(data),rulesetArrayPaths);
      })
    );
    return all;
  }

  // —————————————————————————
  // activate
  // —————————————————————————
  async activateRuleset(
    rulesetId,
    rulesetVersion,
    callerContext
  ) {
    const logCtx = [
      ...callerContext, ...this.componentContext,
      { functionName: "activateRuleset" },
      { rulesetId },{ rulesetVersion }
    ];
    try {
      const res = await this.redisClient.eval(
        ACTIVATE_RULESET_LUA,
        1,
        "rulesets:{common}",
        rulesetId, // Arg[1] in LUA
        rulesetVersion.toString() // Arg[2] in LUA
      );
      return { message: res };
    } catch (err) {
      logger.error(logCtx,err);
      throw new Error(`Failed to activate ruleset: ${err}`);
    }
  }

  // —————————————————————————
  // deactivate (complete)
  // —————————————————————————
  async deactivateRuleset(
    rulesetId,
    rulesetVersion,
    callerContext
  ) {
    const logCtx = [
      ...callerContext, ...this.componentContext,
      { functionName: "deactivateRuleset" },
      { rulesetId },{ rulesetVersion }
    ];
    try {
      const res = await this.redisClient.eval(
        DEACTIVATE_RULESET_LUA,
        1,
        "rulesets:{common}",
        rulesetId,
        rulesetVersion.toString()
      );
      return { message: res };
    } catch (err) {
      logger.error(logCtx,err);
      throw new Error(`Failed to deactivate ruleset: ${err}`);
    }
  }
}

export default RulesetService;

