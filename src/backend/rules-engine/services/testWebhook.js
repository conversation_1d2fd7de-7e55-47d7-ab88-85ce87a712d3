import WebhookCaller from "./webhookCall.js";

import RedisClient from "./redisClient.js";

const REDIS_HOSTS = '[{"host":"127.0.0.1","port":6389}]';

await RedisClient.configure(REDIS_HOSTS);

const webhooks = [
  {
    webhookId: "smsNotification",
    name: "SMS Notification",
    description: "Sends SMS notifications via the corporate SMS gateway",
    url: "https://api.sms-gateway.com/send",
    method: "POST",
    headers: {
      Authorization: "Bearer ${SECRET:sms_api_key}",
      "Content-Type": "application/json",
    },
    bodyTemplate: {
      to: "{recipient}",
      message: "{message}",
      sender: "RuleForge",
    },
    timeout: 3000,
    retryPolicy: {
      maxRetries: 3,
      initialDelaySeconds: 60,
      backoffMultiplier: 2,
    },
    securityLevel: "SYSTEM",
    parameters: [
      {
        parameterId: "recipient",
        name: "Recipient",
        description: "The phone number to send the SMS to",
        type: "string",
        required: true,
      },
      {
        parameterId: "message",
        name: "Message",
        description: "The content of the SMS message",
        type: "string",
        required: true,
      },
    ],
  },
  {
    webhookId: "emailNotification",
    name: "Email Notification",
    description: "Sends email notifications to users",
    url: "https://api.email-service.com/send",
    method: "POST",
    headers: {
      Authorization: "Bearer ${SECRET:email_api_key}",
      "Content-Type": "application/json",
    },
    bodyTemplate: {
      to: "{email}",
      subject: "{subject}",
      body: "{bodyText}",
    },
    timeout: 5000,
    retryPolicy: {
      maxRetries: 5,
      initialDelaySeconds: 30,
      backoffMultiplier: 1.5,
    },
    securityLevel: "ORGANIZATION",
    parameters: [
      {
        parameterId: "email",
        name: "Email",
        description: "Recipient's email address",
        type: "string",
        required: true,
      },
      {
        parameterId: "subject",
        name: "Subject",
        description: "Email subject line",
        type: "string",
        required: true,
      },
      {
        parameterId: "bodyText",
        name: "Body",
        description: "Email body content",
        type: "string",
        required: true,
      },
    ],
  },
  {
    webhookId: "slackAlert",
    name: "Slack Alert",
    description: "Posts alerts to a Slack channel",
    url: "https://hooks.slack.com/services/T000/B000/XXX",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    bodyTemplate: {
      text: "{alertMessage}",
    },
    timeout: 4000,
    retryPolicy: {
      maxRetries: 2,
      initialDelaySeconds: 10,
      backoffMultiplier: 2,
    },
    securityLevel: "ENTITY",
    parameters: [
      {
        parameterId: "alertMessage",
        name: "Alert Message",
        description: "Message to post in Slack",
        type: "string",
        required: true,
      },
    ],
  },
  {
    webhookId: "userRegistrationWebhook",
    name: "User Registration Webhook",
    description: "Fires when a new user registers",
    url: "https://api.internal-service.com/user/registration",
    method: "DELETE",
    headers: {
      Authorization: "Bearer ${SECRET:internal_service_key}",
      "Content-Type": "application/json",
    },
    bodyTemplate: {
      userId: "{userId}",
      userName: "{userName}",
      signupDate: "2021-01-01",
      phoneNumber: "{phoneNumber}",
    },
    timeout: 6000,
    retryPolicy: {
      maxRetries: 3,
      initialDelaySeconds: 20,
      backoffMultiplier: 1.5,
    },
    securityLevel: "SYSTEM",
    parameters: [
      {
        parameterId: "userId",
        name: "User ID",
        description: "New user's ID",
        type: "string",
        required: true,
      },
      {
        parameterId: "userName",
        name: "User Name",
        description: "New user's name",
        type: "string",
        required: true,
      },
      {
        parameterId: "phoneNumber",
        name: "Phone Number",
        description: "New user's phone number",
        type: "string",
        required: false,
      },
    ],
  },
  {
    webhookId: "paymentReceived",
    name: "Payment Received",
    description: "Triggered when a payment is received",
    url: "https://api.payments.com/notify",
    method: "POST",
    headers: {
      Authorization: "Bearer ${SECRET:payments_api_key}",
      "Content-Type": "application/json",
    },
    bodyTemplate: {
      transactionId: "{transactionId}",
      amount: "{amount}",
      currency: "{currency}",
      receivedAt: "{receivedAt}",
    },
    timeout: 3000,
    retryPolicy: {
      maxRetries: 5,
      initialDelaySeconds: 15,
      backoffMultiplier: 2,
    },
    securityLevel: "ORGANIZATION",
    parameters: [
      {
        parameterId: "transactionId",
        name: "Transaction ID",
        description: "ID of the transaction",
        type: "string",
        required: true,
      },
      {
        parameterId: "amount",
        name: "Amount",
        description: "Payment amount",
        type: "number",
        required: true,
      },
      {
        parameterId: "currency",
        name: "Currency",
        description: "Currency code (e.g., USD)",
        type: "string",
        required: true,
      },
      {
        parameterId: "receivedAt",
        name: "Received At",
        description: "Timestamp of payment",
        type: "date",
        required: true,
      },
    ],
  },
  {
    webhookId: "inventoryLow",
    name: "Inventory Low Notification",
    description: "Triggered when inventory falls below threshold",
    url: "https://api.warehouse.com/inventory/low",
    method: "POST",
    headers: {
      Authorization: "Bearer ${SECRET:warehouse_api_key}",
      "Content-Type": "application/json",
    },
    bodyTemplate: {
      productId: "{productId}",
      productName: "{productName}",
      remainingStock: "{remainingStock}",
    },
    timeout: 4500,
    retryPolicy: {
      maxRetries: 3,
      initialDelaySeconds: 45,
      backoffMultiplier: 2,
    },
    securityLevel: "ENTITY",
    parameters: [
      {
        parameterId: "productId",
        name: "Product ID",
        description: "ID of the product",
        type: "string",
        required: true,
      },
      {
        parameterId: "productName",
        name: "Product Name",
        description: "Name of the product",
        type: "string",
        required: true,
      },
      {
        parameterId: "remainingStock",
        name: "Remaining Stock",
        description: "Number of items left in stock",
        type: "number",
        required: true,
      },
    ],
  },
];

const webhookCaller = await new WebhookCaller(
  RedisClient.redisClient,
  []
).init();

/**
 * Handles webhook execution using the first webhook in the array (SMS Notification)
 * This function demonstrates how to call a webhook with the appropriate parameters
 * @returns {Promise<void>}
 */
// const handleWebhook = async () => {
//   // Using the first webhook (smsNotification) which requires recipient and message parameters
//   const result = await webhookCaller.call("smsNotification", {
//     recipient: "+1234567890", // Phone number to send SMS to
//     message: "Inventory alert: Stock is running low", // SMS content
//   });
//   console.log("Webhook execution result: ", result);
// };
// const handleWebhook = async () => {
//   const result = await webhookCaller.call("notificationServier", {
//     param1: "data",
//     param2: "testing webhook 2",
//   });
//   console.log("the result is: ", result);
// };

// handleWebhook();

// manualWebhookTest.js

// Configuration
const TEST_WEBHOOK_ID = "notification2";

// Test parameters to send
const testParams = {
  msisdn1: "1235948",
  message: "This is a test webhook message",
};
// Configuration
// const TEST_WEBHOOK_ID = "notificationServier";

// // Test parameters to send
// const testParams = {
//   msisdn: "1235948",
//   message1: "This is a test webhook message",
// };

async function runTest() {
  try {
    // 1. Configure Redis client
    console.log("Connecting to Redis...");
    await RedisClient.configure(REDIS_HOSTS);
    const redisClient = RedisClient.redisClient;

    // 2. Create and initialize webhook caller
    const webhookCaller = await new WebhookCaller(redisClient, []).init();

    const result = await webhookCaller.call(TEST_WEBHOOK_ID, testParams);
    console.log("the result is: ", result);

    // 6. Close Redis connection
    await redisClient.quit();
    console.log("Test completed.");
  } catch (error) {
    console.error("Test failed with error:", error);
    process.exit(1);
  }
}

// Run the test
runTest();
// Add this to your testWebhook.js file at the end
