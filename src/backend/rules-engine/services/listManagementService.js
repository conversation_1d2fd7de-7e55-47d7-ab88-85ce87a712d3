// listManagementService.js
import { logger } from "../middleware/logger.js";

class ListManagementService {
  /**
   * Constructor function
   * @param {import("./redisClient.js").RedisClientJsDoc} redisClient
   * @param {ListTypes} listType
   */
  constructor(redisClient, callerContext) {
    /** @type {import("./redisClient.js").RedisClientJsDoc} */
    this.redisClient = redisClient;

    this.componentName = "ListManagementService";
    this.loggingContext = [
      ...callerContext,
      { componentName: this.componentName },
    ];
  }

  /**
   * Checks if a list of a specific type and ID exists in Redis.
   *
   * @param {string} listId - The ID of the list to check.
   * @returns {Promise<boolean>} - A promise that resolves to true if the list exists, false otherwise.
   * @throws {Error} - Throws an error if the check operation fails.
   */
  _checkListExists = async (listId) => {
    try {
      const exists = await this.redisClient.hexists(`lists:{common}`, listId);
      return exists == true; // Returns true if key exists, false if it does not
    } catch (error) {
      logger.error(
        [
          ...this.loggingContext,
          `checking if list with ID ${listId} exists in Redis:`,
        ],
        error
      );
      throw new Error(`Failed to check if list exists: ${error.message}`);
    }
  };

  _generateElementsKey = (id) => {
    return `lst:{common}:elems:${id}`;
  };

  /**
   * Generates a unique identifier by list name and appending a counter to the slug of the list name if necessary.
   * Checks the existence of the ID in Redis using checkListExists and increments the counter until a unique ID is found.
   *
   * @param {string} name - The list name.
   * @returns {Promise<string>} - A promise that resolves to a unique identifier.
   */
  _generateUniqueId = async (name) => {
    // Generate a base ID from the list name
    const baseId = name;

    let uniqueId = baseId;
    let counter = 1;

    // Check if the key exists using checkListExists
    while (await this._checkListExists(uniqueId)) {
      uniqueId = `${baseId}${counter}`; // Append the counter to the baseId
      counter += 1; // Increment the counter for the next check
    }

    return uniqueId; // Return the unique ID once a non-existing ID is found
  };

  /**
   * Retrieves a list by the id from the Redis based on the specified type and ID along with the elements if required.
   *
   * @param {string} listId - The unique identifier of the list.
   * @param {boolean} [getElements=false] - A flag to indicate if the elements should be retrieved.
   * @returns {Promise.<ListsObject>} The parsed list data from Redis.
   * @throws {Error} If the list is not found or if there is an error during retrieval.
   */
  getListById = async (listId, getElements = false) => {
    try {
      const listData = await this.redisClient.hget(`lists:{common}`, listId);
      if (!listData) {
        return null;
      }

      const formatedObject = this._formatListObjectFromRedis(
        JSON.parse(listData),
        listId
      );

      // If the elements are requested, fetch them from Redis
      if (getElements) {
        const elements = await this.redisClient.smembers(
          formatedObject.elementsKey
        );
        formatedObject.elements = elements;
      }

      return formatedObject;
    } catch (error) {
      logger.error(
        [...this.loggingContext, { function: "getListById" }, { listId }],
        error
      );

      //console.error(`Error fetching ${this.listType} list with ID ${listId} from Redis:`, error);
      throw new Error(error);
    }
  };

  /**
   * Asynchronously creates a new list in Redis under the specified list type.
   *
   * @param {ListsObject} newList - The list object to be stored, which must include a unique 'id' property.
   * @param {Array.<string | number>} elements - The csv file rows data.
   * @throws {Error} Throws an error if the list creation in Redis fails.
   */
  createList = async (newList, elements) => {
    logger.info([...this.loggingContext, { action: "creating list" }]);
    const multi = this.redisClient.multi(); // Initialize the Redis transaction

    try {
      // If type is not provided then set to the string
      if (!newList.type) newList.type = "string";

      // Generate the unique ID for the list
      const listId = await this._generateUniqueId(newList.name);

      const elementsKey = this._generateElementsKey(listId);

      // Format the list object to be stored in Redis. 
      // We are using the name, type etc as object properties 
      // so that we can easily modify the list object in the future 
      // if needed with better redis performance
      const formattedList = {
        ...newList,
        elementsKey: elementsKey,
      };

      // Add the hash command to store the list object under a key
      multi.hset(`lists:{common}`, listId, JSON.stringify(formattedList)); // Store the list object

      // Add the set command to store the elements (e.g., CSV rows) under a key
      // Elements is an array, and we'll store it as a set to ensure uniqueness
      multi.sadd(elementsKey, elements); // Store the elements

      // Execute the transaction
      await multi.exec(); // Execute both commands in the transaction

      return formattedList;
    } catch (error) {
      logger.error(
        [
          ...this.loggingContext,
          { function: "createList" },
          { listType: this.listType },
        ],
        error
      );

      throw new Error(error);
    }
  };

  /**
   * Checks if a list with the given name already exists.
   * @param {string} name - The name of the list to check.
   * @returns {Promise<boolean>} - A promise that resolves to true if a duplicate exists, false otherwise.
   */
  checkDuplicateListName = async (name) => {
    try {
      const listsData = await this.redisClient.hgetall(`lists:{common}`);
      return Object.values(listsData).some((listData) => {
        const list = JSON.parse(listData);
        return list.name === name;
      });
    } catch (error) {
      logger.error(
        [...this.loggingContext, { function: "checkDuplicateListName" }],
        error
      );
      throw new Error(
        `Failed to check for duplicate list name: ${error.message}`
      );
    }
  };
  /**
   * Deletes a list by the ID from the Redis.
   * @param {string} listId - The ID of the list to delete.
   */
  deleteListById = async (listId) => {
    try {
      // Get the elements key for the list
      const listData = await this.getListById(listId, true);
      const elementsKey = listData.elementsKey;

      // Start a Redis transaction
      const transaction = this.redisClient.multi();

      // Add the commands to the transaction
      transaction.hdel(`lists:{common}`, listId);
      transaction.del(elementsKey);

      // Execute the transaction
      const results = await transaction.exec();

      // Check the result of hDel
      const hDelResult = results[0]; // The result of the first command (hDel)
      if (hDelResult === 0) {
        throw new Error(`list with ID ${listId} not found`);
      }

      return true;
    } catch (error) {
      // Log and rethrow the error
      console.error(`Error deleting list with ID ${listId} from Redis:`, error);
      throw new Error(error.message);
    }
  };

  /**
   * Get all lists without elements from the Redis.
   * @returns {Promise<import("../controllers/listManagementController").RegularListObject>}
   */
  getAllLists = async () => {
    try {
      const listsData = await this.redisClient.hgetall(`lists:{common}`);

      if (!listsData) {
        throw new Error(`No lists found`);
      }

      // Format the list
      const lists = Object.entries(listsData).map(([listId, listData]) => {
        // Return the list object with the `id`, `name`, `type`, elemeCount
        return this._formatListObjectFromRedis(JSON.parse(listData), listId);
      });

      return {
        lists: lists,
        totalCount: 0,
        page: 1,
        pageSize: 20,
      };
    } catch (error) {
      logger.error(
        [...this.loggingContext, { function: "getAllLists" }],
        error
      );

      throw new Error(error.message);
    }
  };

  /**
   * Format the list object which came from the Redis.
   * @param {{name: string, type: string, elemsCount: number}} listObj
   * @param {string} key - The key of the list object in Redis.
   * @returns {import("../controllers/listManagementController").RegularListArray[0] & {elementsKey: string}}
   */
  _formatListObjectFromRedis = (listObj, key) => {
    return {
      listId: key,
      ...listObj,
    };
  };
}

export default ListManagementService;

/**
 * @typedef {"lookup"} ListTypesEnum
 */

/**
 * @typedef {import("../controllers/listManagementController").RegularListArray[0] & {elementsKey: string, elements: Array.<string>} | import("../controllers/listManagementController").BulkNotificationListArray[0] & {elementsKey: string, elements: Array.<string>}} ListsObject
 */
