export function combineEvaluationResults(currentResults, newResults) {
  // handle the simple pass-through cases
  if (currentResults === undefined && newResults === undefined) {
    return undefined;
  }
  if (currentResults === undefined) {
    return newResults;
  }
  if (newResults === undefined) {
    return currentResults;
  }

  // IDs must match
  if (currentResults.transactionId !== newResults.transactionId) {
    throw new Error("Transaction IDs do not match");
  }
  const transactionId = currentResults.transactionId;
  if (!transactionId) {
    throw new Error("No Transaction ID in context");
  }

  // merge the two modifiedProperties objects (defaults to {} if missing)
  const mergedModifiedProperties = {
    ...(currentResults.modifiedProperties || {}),
    ...(newResults.modifiedProperties || {}),
  };

  // concat the two actions arrays (defaults to [] if missing or not an array)
  const actions = [
    ...(Array.isArray(currentResults.actions) ? currentResults.actions : []),
    ...(Array.isArray(newResults.actions) ? newResults.actions : []),
  ];

  // build the result, only adding keys if they have content
  const result = { transactionId };

  if (Object.keys(mergedModifiedProperties).length > 0) {
    result.modifiedProperties = mergedModifiedProperties;
  }

  if (actions.length > 0) {
    result.actions = actions;
  }

  return result;
}


