import RedisClient from "../services/redisClient.js";
import { GenericContainer } from "testcontainers";
import RulesetService from "../services/rulesetService"; // Adjust the path as needed
import {
  describe,
  it,
  beforeAll,
  beforeEach,
  afterAll,
  expect,
  jest,
} from "@jest/globals";

const loggingContext = [{ unitTestingModule: "rulesetService.test.js" }];

jest.setTimeout(30000); // 30 seconds (or more)

describe("RulesetService Integration (real Redis via testcontainers)", () => {

  const rulesetContextId = "TESTING_CONTEXT";

  let redisContainer;
  let redisClient;
  let rulesetService;

  // Spin up Redis in Docker before all tests
  beforeAll(async () => {
    try {
      redisContainer = await new GenericContainer("redis:7-alpine")
        .withExposedPorts(6379)
        .start();

      const host = redisContainer.getHost();
      const port = redisContainer.getMappedPort(6379);
      const hostsJson = JSON.stringify([{ host, port }]);

      await RedisClient.configure(hostsJson);

      redisClient = RedisClient.redisClient;
      rulesetService = new RulesetService(redisClient, [{ note: "TESTING" }]);
    } catch (error) {
      console.error(error);
    }
  });

  // Flush Redis before each test so we start clean
  beforeEach(async () => {
    // node-redis v4: flushall -> sendCommand(['FLUSHALL'])
    await redisClient.flushall();
  });

  // After all tests, stop the container and close the client
  afterAll(async () => {
    if (redisClient) {
      await redisClient.quit();
    }
    if (redisContainer) {
      await redisContainer.stop();
    }
  });

  // ---------------------------------------------------------------------------
  // saveRuleset tests
  // ---------------------------------------------------------------------------
  describe("saveRuleset", () => {
    it("creates a new ruleset with version=1 if none exists", async () => {
      const rulesetId = "testRuleset";

      // Seed required entities in the entities hash (namespaced with {common})
      await redisClient.hset(
        "entities:{common}",
        "entity:ent1",
        JSON.stringify({}),
      );
      await redisClient.hset(
        "entities:{common}",
        "entity:ent2",
        JSON.stringify({}),
      );

      const newRuleset = {
        rulesetId: rulesetId ,
        entityId: "ent1" ,
        contextId: rulesetContextId,
      };

      const result = await rulesetService.saveRuleset(
        newRuleset,
        loggingContext,
      );
      expect(result.message).toBe("Ruleset successfully created");
      expect(result.rulesetId).toBe("testRuleset");

      let rulesetHash  = `ruleset:${rulesetId}:1`;

      // Verify the ruleset is stored in the rulesets hash
      const storedRulesetStr = await redisClient.hget(
        "rulesets:{common}",
        rulesetHash  
      );
      expect(storedRulesetStr).not.toBeNull();

      const storedRuleset = JSON.parse(storedRulesetStr);

      expect(storedRuleset.rulesetId).toBe("testRuleset");

      expect(storedRuleset.version).toBe(1);
      expect(storedRuleset.status).toBe("DRAFT");

      // Verify that latestVersion is set in the rulesets hash

      const latestVersion = await redisClient.hget(
        "rulesets:{common}",
        `ruleset:${rulesetId}:latestVersion`,
      );
      expect(latestVersion).toBe("1");

      // For saveRuleset, the Lua script adds the ruleset versions set and global ruleset set using:
      //   "ruleset:" + "{common}" + ":" + rulesetId + ":versions"  -> "ruleset:{common}:testRuleset:versions"
      //   "rulesetSet:" + "{common}"                                 -> "rulesetSet:{common}"
      const versionsSet = await redisClient.smembers(
        `ruleset:{common}:${rulesetId}:versions`,
      );
      expect(versionsSet).toContain("1");

      const rulesetSet = await redisClient.smembers("rulesetSet:{common}");

      expect(rulesetSet).toContain(`ruleset:${rulesetId}:1`);

      // And for entity references the key is:
      //   "entity:" + "{common}" + ":" + entityId + ":rulesets:" + "{common}"
      const ent1Rulesets = await redisClient.smembers(
        `entity:{common}:ent1:${rulesetContextId}:rulesets`,
      );
      expect(ent1Rulesets).toContain(`${rulesetId}:1`);
    });

    it("increments version if latest ruleset is not DRAFT", async () => {
      const rulesetId = "abc";
      const contextId = "TESTING_CONTEXT";

      // Pre-seed an ACTIVE ruleset (version 5) into the rulesets hash and related sets
      const existingRuleset = {
        rulesetId,
        contextId,
        version: 5,
        status: "ACTIVE",
        entityId: "entX" ,
      };
      await redisClient.hset(
        "rulesets:{common}",
        `ruleset:${rulesetId}:5`,
        JSON.stringify(existingRuleset),
      );
      await redisClient.hset(
        "rulesets:{common}",
        `ruleset:${rulesetId}:latestVersion`,
        5,
      );
      await redisClient.sadd(`ruleset:{common}:${rulesetId}:version`, 5);
      await redisClient.sadd("rulesetSet:{common}", `${rulesetId}:5`);

      // Ensure the entity exists
      await redisClient.hset(
        "entities:{common}",
        "entity:entX",
        JSON.stringify({}),
      );

      // Now call saveRuleset for the same rulesetId; expect a new version (6) to be created
      const newRuleset = {
        rulesetId,
        contextId,
        entityId: "entX" 
      };
      const result = await rulesetService.saveRuleset(
        newRuleset,
        loggingContext,
      );
      expect(result.message).toBe("Ruleset successfully created");
      expect(result.rulesetId).toBe("abc");

      const storedV6 = await redisClient.hget(
        "rulesets:{common}",
        `ruleset:${rulesetId}:6`,
      );
      expect(storedV6).not.toBeNull();

      const parsedV6 = JSON.parse(storedV6);
      expect(parsedV6.version).toBe(6);
      expect(parsedV6.status).toBe("DRAFT");

      const newLatest = await redisClient.hget(
        "rulesets:{common}",
        `ruleset:${rulesetId}:latestVersion`,
      );
      expect(newLatest).toBe("6");
    });
  });

  // ---------------------------------------------------------------------------
  // deleteRuleset tests
  // ---------------------------------------------------------------------------
  describe("deleteRuleset", () => {
    it("deletes a DRAFT ruleset successfully", async () => {
      const rulesetId = "delId";
      const contextId = "TESTING_CONTEXT";

      // Insert a draft ruleset into the rulesets hash
      const ruleset = {
        rulesetId,
        contextId,
        version: 1,
        status: "DRAFT",
        entityId: "delEnt1",
      };
      await redisClient.hset(
        "rulesets:{common}",
        `ruleset:${rulesetId}:1`,
        JSON.stringify(ruleset),
      );
      await redisClient.hset(
        "rulesets:{common}",
        `ruleset:${rulesetId}:latestVersion`,
        "1",
      );

      // For deletion, note that the DELETE Lua script operates on set keys without the "{common}" tag:
      //   It calls SREM on "ruleset:" .. rulesetId .. ":versions"
      //   and on "rulesetSet", and on "entity:" .. entityId .. ":rulesets"
      await redisClient.sadd(`ruleset:{common}:${rulesetId}:versions`, "1");
      await redisClient.sadd("rulesetSet:{common}", `ruleset:${rulesetId}:1`);

      // Also seed the entity reference using the key the DELETE script expects
      await redisClient.hset(
        "entities:{common}",
        "entity:delEnt1",
        JSON.stringify({}),
      );
      await redisClient.sadd("entity:{common}:delEnt1:rulesets", `ruleset:${rulesetId}:1`);

      // Now call deleteRuleset
      const result = await rulesetService.deleteRuleset(
        rulesetId,
        loggingContext,
      );
      expect(result).toStrictEqual({
        message: "Ruleset deleted successfully",
      });

      // Verify the ruleset is removed from the rulesets hash
      const afterStr = await redisClient.hget(
        "rulesets:{common}",
        `ruleset:${rulesetId}:1`,
      );
      expect(afterStr).toBeNull();

      // Verify that the ruleset versions set is now empty (using the key without {common})
      const versions = await redisClient.smembers(
        `ruleset:{common}:${rulesetId}:versions`,
      );
      expect(versions).toHaveLength(0);

      // Verify that the global ruleset set no longer contains the ruleset (using key "rulesetSet")
      const globalSet = await redisClient.smembers("rulesetSet:{common}");
      expect(globalSet).not.toContain(`${rulesetId}:1`);

      // The 'latestVersion' field in the hash should be removed
      const latest = await redisClient.hget(
        "rulesets:{common}",
        `ruleset:${rulesetId}:latestVersion`,
      );
      expect(latest).toBeNull();

      // Verify that the entity's rulesets set (key without {common}) no longer references this ruleset
      const entRulesets = await redisClient.smembers(
        "entity{common}:delEnt1:rulesets",
      );
      expect(entRulesets).not.toContain(`${rulesetId}:1`);
    });

    it("throws if the ruleset is ACTIVE", async () => {
      const rulesetId = "delActiveId";
      const contextId = "TESTING_CONTEXT";

      // Insert an ACTIVE ruleset into the rulesets hash
      const ruleset = {
        rulesetId,
        contextId,
        version: 2,
        status: "ACTIVE",
        entityId: null,
        startDateTime: "2024-01-01T00:00:00Z",
        endDateTime: "2024-12-31T23:59:59Z",
      };
      await redisClient.hset(
        "rulesets:{common}",
        `ruleset:${rulesetId}:2`,
        JSON.stringify(ruleset));

      await redisClient.hset(
        "rulesets:{common}",
        `ruleset:${rulesetId}:latestVersion`,
        2,
      );
      await redisClient.sadd(`ruleset:{common}:${rulesetId}:versions`, 2);
      await redisClient.sadd("rulesetSet:{common}", `ruleset:${rulesetId}:2`);

      await expect(
        rulesetService.deleteRuleset(rulesetId,loggingContext),
      ).rejects.toThrow("Only rulesets in DRAFT status can be deleted");
  });
});

  // ---------------------------------------------------------------------------
  // activateRuleset tests
  // ---------------------------------------------------------------------------
  describe("activateRuleset", () => {
    it("sets a DRAFT ruleset to ACTIVE and completes other ACTIVE versions", async () => {
      const rulesetId = "actId";
      const contextId = "TESTING_CONTEXT";
      // Pre-seed rulesets:
      // version 1: ACTIVE, version 2: DRAFT, version 3: COMPLETED
      const completedV1Ruleset = {
        rulesetId,
        version: 1,
        contextId,
        status: "COMPLETED",
        startDateTime: "2024-01-01T00:00:00Z",
        endDateTime: "2024-12-31T23:59:59Z",
      };
      const activeV2Ruleset = {
        rulesetId,
        version: 2,
        contextId,
        status: "ACTIVE",
        startDateTime: "2025-01-01T00:00:00Z",
        endDateTime: "2025-12-31T23:59:59Z",
      };
      const draftV3Ruleset= {
        rulesetId,
        version: 3,
        contextId,
        status: "DRAFT",
        startDateTime: "2025-01-01T00:00:00Z",
        endDateTime: "2025-12-31T23:59:59Z",
      };

      await redisClient.hset(
        "rulesets:{common}",
        `ruleset:${rulesetId}:1`,
        JSON.stringify(completedV1Ruleset),
      );
      await redisClient.hset(
        "rulesets:{common}",
        `ruleset:${rulesetId}:2`,
        JSON.stringify(activeV2Ruleset),
      );
      await redisClient.hset(
        "rulesets:{common}",
        `ruleset:${rulesetId}:3`,
        JSON.stringify(draftV3Ruleset),
      );
      await redisClient.hset(
        "rulesets:{common}",
        `ruleset:${rulesetId}:latestVersion`,
        3,
      );

      // For activateRuleset, note that the ACTIVATE Lua script uses the key:
      //   "ruleset:" .. tag .. rulesetId .. ":versions"
      // With tag = "{common}", that becomes "ruleset:{common}actId:versions" (no extra colon between {common} and actId)
      await redisClient.sadd(
        `ruleset:{common}:${rulesetId}:versions`,
        1,
        2,
        3,
      );
      await redisClient.sadd(
        "rulesetSet:{common}",
        `ruleset:${rulesetId}:1`,
        `ruleset:${rulesetId}:2`,
        `ruleset:${rulesetId}:3`,
      );

      // Activate version 3
      await rulesetService.activateRuleset(rulesetId, 3,loggingContext);

      // Verify that version 3 is now ACTIVE
      const activatedRulsetStr = await redisClient.hget(
        "rulesets:{common}",
        `ruleset:${rulesetId}:3`,
      );
      const activatedRulset = JSON.parse(activatedRulsetStr);
      expect(activatedRulset.status).toBe("ACTIVE");

      // Verify that version 2 (which was ACTIVE) is now marked as COMPLETED
      const completedRulesetStr = await redisClient.hget(
        "rulesets:{common}",
        `ruleset:${rulesetId}:2`,
      );
      const completedRuleset = JSON.parse(completedRulesetStr);
      expect(completedRuleset.status).toBe("COMPLETED");

      // Verify that version 3 remains COMPLETED
      const previouslyCompletedRulsetStr = await redisClient.hget(
        "rulesets:{common}",
        `ruleset:${rulesetId}:1`,
      );
      const previouslyCompletedRulset = JSON.parse(previouslyCompletedRulsetStr);
      expect(previouslyCompletedRulset.status).toBe("COMPLETED");
    });

    it("throws if the ruleset is not in DRAFT", async () => {
      const contextId = "TESTING_CONTEXT";
      const rulesetId = "actId";

      const activeCamp = {
        rulesetId,
        version: 5,
        contextId,
        status: "ACTIVE",
        startDateTime: "2025-01-01",
        endDateTime: "2025-12-31",
      };
      await redisClient.hset(
        "rulesets:{common}",
        `ruleset:${rulesetId}:5`,
        JSON.stringify(activeCamp),
      );
      // For this test, seed the ruleset versions set using the key as used in ACTIVATE:
      await redisClient.sadd("ruleset:{common}actId:versions", "5");

      await expect(
        rulesetService.activateRuleset(rulesetId, 5,loggingContext),
      ).rejects.toThrow("Only rulesets in DRAFT status can be activated");
    });
  });

  // Additional tests (for completeRuleset, copyRuleset, etc.) can be added here.
});
