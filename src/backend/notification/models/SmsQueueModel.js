import { DataTypes } from 'sequelize';
import { SmsQueueDb } from '../db/index.js';
import { smsQueueDbConfig } from '../config/database.js';


/**
 * Model for the smsQueueDbConfig.tableName table in the database.
 */
const SmsQueueModel = SmsQueueDb.define('SmsQueue', { // SmsQueue is the model name just for internal sequelize use
    id: {
      type: DataTypes.BIGINT.UNSIGNED,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false,
    },
    transaction_id: {
      type: DataTypes.BIGINT.UNSIGNED,
      allowNull: false,
    },
    application: {
      type: DataTypes.STRING(16),
      allowNull: false,
      defaultValue: 'Ruleforge',
    },
    source_msisdn: {
      type: DataTypes.STRING(32),
      allowNull: false,
      defaultValue: '123',
    },
    destination_msisdn: {
      type: DataTypes.STRING(32),
      allowNull: false,
    },
    dcs_encoding: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 3,
    },
    message: {
      type: DataTypes.STRING(16384), // Sequelize uses STRING for VARCHAR fields
      allowNull: false,
    },
    kvpinfo: {
      type: DataTypes.STRING(256),
      allowNull: false,
      defaultValue: '',
    },
    insertion_date: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    start_hour: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },
    end_hour: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 24,
    },
    ttl: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 240,
    },
  }, {
    tableName: smsQueueDbConfig.tableName,
    timestamps: false,      // Disables createdAt and updatedAt fields
    underscored: true,      // Uses snake_case for fields if needed
  });
  

export default SmsQueueModel;


/**
 * @typedef {Object} SmsQueueModel
 * @property {number} id - The unique identifier for the SMS queue entry (auto-incremented).
 * @property {number} transaction_id - The transaction ID associated with the SMS queue entry.
 * @property {string} [application='Ruleforge'] - The application name. Default is 'Ruleforge'.
 * @property {string} [source_msisdn='123'] - The source MSISDN. Default is '123'.
 * @property {string} destination_msisdn - The destination MSISDN.
 * @property {number} [dcs_encoding=3] - The DCS (Data Coding Scheme) encoding. Default is 3.
 * @property {string} message - The message content for the SMS.
 * @property {string} [kvpinfo=''] - Additional key-value pair information. Default is an empty string.
 * @property {Date} [insertion_date=NOW] - The date and time when the entry was inserted. Default is the current timestamp.
 * @property {number} [start_hour=0] - The start hour for processing. Default is 0.
 * @property {number} [end_hour=24] - The end hour for processing. Default is 24.
 * @property {number} [ttl=240] - Time-to-live for the entry in seconds. Default is 240.
 */