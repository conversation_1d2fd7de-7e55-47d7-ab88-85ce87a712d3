import SmsQueueService from "../services/smsServices.js";

import settings from "../config/settings.js";

class SmsController {
  constructor() {
    
  }



  /**
   * Function to send the bulk SMS to the list
   * @param {import('express').Request} req
   * @param {import('express').Response} res
   * @returns
   */
  sendBulkSms = async (req, res) => {

    try {
      // Get the list ID from the request
      const listId = req.params.id;

      // Get the data from the database
      const dataT = await this.service.getListById(
        listId,
        // true,
        // loggingContext,
      );

      // The msidin data
      const msidinData = dataT.elements;

      // Prepare the message queue entries
      const smsQueueEntries = prepareSmsQueueEntries(msidinData, dataT.text);

      // Get the sms queue settings
      const batchSize = settings.smsQueue.batchSize;
      const batchDelay = settings.smsQueue.batchDelay;

      // Prepare the batches
      const batches = prepareBatchesData(smsQueueEntries, batchSize);

      // Process the batches with delay
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        const resultT = await this.SmsQueueService.bulkCreateQueueEntries(
          batch
        );

        await new Promise((resolve) => setTimeout(resolve, batchDelay));
      }

      // All good so return the success response
      return res
        .status(200)
        .json({ message: "Bulk notification sent successfully" });
    } catch (error) {
      console.error( error);
      return res.status(500).json({ message: error.message });
    }
  };

  /**
   * Function to send the bulk SMS to the list
   * @param {import('express').Request} req
   * @param {import('express').Response} res
   * @returns
   */
  sendSingleSms = async (req, res) => {

    const smsData = req.body;

    try {
      const entry = await SmsQueueService.createSingleSmsEntry(smsData);

      return res.status(201).json({
        success: true,
        message: "SMS entry created successfully",
        // data: entry,
      }); 
    } catch (error) {
      console.error("[SMS Controller] Error creating single SMS entry:", error.stack || error);
      return res.status(400).json({
        success: false,
        message: error.message || "Failed to create single SMS entry",
      });
    }
  };
}

export default new SmsController();