export const smsQueueDbConfig = {
  host: process.env.SMS_QUEUE_DB_HOST ||  "127.0.0.1",
  port: process.env.SMS_QUEUE_DB_PORT || 3307,
  user: process.env.SMS_QUEUE_DB_USERNAME || "root",
  password: process.env.SMS_QUEUE_DB_PASSWORD || "ussdgw",
  database: process.env.SMS_QUEUE_DB_NAME || "ruleforge_cre_journal_db",
  tableName: process.env.SMS_QUEUE_TABLE_NAME || "smsq_queue",
  dialect: "mysql",
  logging: process.env.NODE_ENV === "development",
  pool: {
    max: 1,
    min: 0,
    acquire: 30000,
    idle: 10000,
  },
};
