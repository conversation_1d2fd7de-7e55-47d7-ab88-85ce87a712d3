let jwtPublicKey = process.env.JWT_PUBLIC_KEY || "testKey"; // process.env.JWT_PUBLIC_KEY
let batchSize = process.env.SMS_QUEUE_BATCH_SIZE || 100;
let batchDelay = process.env.SMS_QUEUE_BATCH_DELAY || 1000;

if (!jwtPublicKey) {
  console.error("JWT_PUBLIC_KEY is not set");
  process.exit(1);
}

const settings = {
  smsQueue: {
    /**
     * The maximum number of SMS queue entries to process in a single batch.
     */
    batchSize: batchSize,
    /**
     * The delay (in milliseconds) between processing each batch.
     */
    batchDelay: batchDelay,
  },

  // Decode the JWT_PUBLIC_KEY from base64 to utf8
  jwtPublicKey: Buffer.from(jwtPublicKey, "base64").toString("utf8"),
  
};

export default settings;
