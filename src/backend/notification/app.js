// Import database
import express from "express";
import { initDb } from "./db/initDB.js";
import { smsRouter } from "./routes/routes.js";
import './logger/index.js';
import SmsQueueModel from "./models/SmsQueueModel.js"; 
// Initialize the database
await initDb(); 

// after Sequelize has connected:
await SmsQueueModel.sync({ alter: true }); 


const app = express();
 
// Middlewares
app.use(express.json()); // Parse JSON requests
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded data
// app.use(configureCors());
// app.use(helmet()); // Set security headers

// Declare routes
app.use("/api/v1/sms", smsRouter);

// Health check endpoint
app.get("/api/v1/health", (req, res) => {
  res.status(200).json({ success: true, message: "API is running smoothly" });
});

// Global Error Handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ success: false, message: "Internal Server Error" });
});

// Start Server and Connect to Database
const PORT = process.env.PORT || 6000;
app.listen(PORT, () => {
  console.info(`Server is running on port ${PORT}`);
});
