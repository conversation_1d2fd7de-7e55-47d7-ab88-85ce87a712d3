import fs from "fs";
import util from "util";

class Logger {
    static LEVELS = {
        FATAL: 1,    // 0b00001
        ERROR: 2,    // 0b00010
        WARN: 4,     // 0b00100
        INFO: 8,     // 0b01000
        TRACE: 16    // 0b10000
    };

    /**
     * Initialize the logger
     */
    constructor(logFilePath = "/logs/notification_log.level") {
        this.logFilePath = logFilePath;
        this.logLevel = this.readLogLevel();
        this.logInstanceName = process.env.LOG_INSTANCE_NAME || "default";
        this.overrideConsole();
        this.watchLogLevelFile();
    }

    /**
     * Read the log level from the file which will be used to set the log level runtime
     * @returns {number}
     */
    readLogLevel() {
        try {
            const level = fs.readFileSync(this.logFilePath, "utf8").trim();
            return parseInt(level, 10) || 31; // Default to all logs
        } catch (err) {
            return 31; // Default log level if file not found
        }
    }
    getColorByLevel(levelLabel) {
        const colors = {
            FATAL: "\x1b[41m\x1b[37m", // Red background, white text
            ERROR: "\x1b[31m",         // Red
            WARN: "\x1b[33m",          // Yellow
            INFO: "\x1b[36m",          // Cyan
            TRACE: "\x1b[90m",         // Gray
        };
        const reset = "\x1b[0m";
        return (text) => `${colors[levelLabel] || ""}${text}${reset}`;
    }
    

    /**
     * Convert objects/arrays to string representation if they are not string.
     * @param {any[]} args 
     * @returns {string}
     */
    formatArgs(args) {
        try {
            return args.map(arg => {
                if (typeof arg === "object") {
                    return util.inspect(arg, {
                        depth: null,
                        colors: false,
                        compact: true,
                        breakLength: Infinity
                    });
                }
                return String(arg);
            }).join(" ");
        } catch (err) {
            return "Error formatting log message";
        }
    }

    

    /**
     * Watch the log level file for changes and update the log level.
     */
    watchLogLevelFile() {
        fs.watchFile(this.logFilePath, { interval: 5000 }, () => {
            this.logLevel = this.readLogLevel();
        });
    }

    /**
     * Check if the log level is enabled by comparing the bitwise log level
     * @param {number} level 
     * @returns 
     */
    isEnabled(level) {
        return (this.logLevel & level) !== 0;
    }

    /**
     * Function to encode a value to CSV format
     * 
     * @param {string} value 
     * @returns 
     */
    csvEncode(value) {

        value = String(value); // Ensure it's a string

        // Escape double quotes and wrap in quotes if needed
        if (value.includes(",") || value.includes("\n") || value.includes('"')) {
            value = `"${value.replace(/"/g, '""')}"`;
        }

        return value;
    }


    /**
     * Functionto log a message with the specific format
     * @param {number} level 
     * @param {"FATAL" | "ERROR" | "WARN" | "INFO" | "TRACE"} levelLabel 
     * @param {function} originalMethod 
     * @param  {...any} args 
     */
    async log(level, levelLabel, originalMethod, ...args) {

        try {

            const logString = this.csvEncode(this.formatArgs(args));

            if (this.isEnabled(level)) {


               const logEntry = `${new Date().toISOString()},${levelLabel},${this.logInstanceName},${logString}`;

               const colorize = this.getColorByLevel(levelLabel);
                originalMethod(colorize(logEntry));
            }
        } catch (err) {

            console._error("Error logging message", err.stack);
        }


    }

    /**
     * Override the console methods to use the logger
     */
    overrideConsole() {

        // Define the log methods
        const logMethods = {

            fatal: { label: "FATAL", level: Logger.LEVELS.FATAL, method: console.log },
            error: { label: "ERROR", level: Logger.LEVELS.ERROR, method: console.log },
            warn: { label: "WARN", level: Logger.LEVELS.WARN, method: console.log },
            log: { label: "INFO", level: Logger.LEVELS.INFO, method: console.log },
            info: { label: "INFO", level: Logger.LEVELS.INFO, method: console.log },
            debug: { label: "TRACE", level: Logger.LEVELS.TRACE, method: console.log },
            trace: { label: "TRACE", level: Logger.LEVELS.TRACE, method: console.log }
        };

        // Override the console methods with the logger
        for (const [method, { level, label, method: originalMethod }] of Object.entries(logMethods)) {
            console[`_${method}`] = originalMethod.bind(console);
            console[method] = this.log.bind(this, level, label, originalMethod);
        }
    }
}
 new Logger();

export default new Logger();