services:
    rulesforge_notification_service:
    build: .
    container_name: rulesforge_notification_service
    working_dir: /usr/src/app
    command: bash -c "sleep 1 && node app.js"
    volumes:
      - .:/usr/src/app
      # Map the log_level file
      # Set the application log level file host mounted path, this file will contain the log level number i.e 15 etc. It is the Bitmask mapping number from 1-31
      # Example: 1-FATAL, 2-ERROR, 3-FATAL+ERROR, 4-WARN, 5-FATAL+WARN, 6-ERROR+WARN, 7-FATAL+ERROR+WARN,
      # 8-INFO, 9-FATAL+INFO, 10-ERROR+INFO, 11-FATAL+ERROR+INFO, 12-WARN+INFO, 13-FATAL+WARN+INFO,
      # 14-ERROR+WARN+INFO, 15-FATAL+ERROR+WARN+INFO, 16-TRACE, 17-FATAL+TRACE, 18-ERROR+TRACE,
      # 19-FA<PERSON>L+ERROR+TRACE, 20-WA<PERSON><PERSON>+<PERSON>RACE, 21-<PERSON><PERSON><PERSON>+<PERSON>RN+TRACE, 22-<PERSON><PERSON><PERSON>+<PERSON>RN+TRACE,
      # 23-FATAL+ERROR+WARN+TRACE, 24-INFO+TRACE, 25-FATAL+INFO+TRACE, 26-ERROR+INFO+TRACE,
      # 27-FATAL+ERROR+INFO+TRACE, 28-WARN+INFO+TRACE, 29-FATAL+WARN+INFO+TRACE,
      # 30-ERROR+WARN+INFO+TRACE, 31-FATAL+ERROR+WARN+INFO+TRACE
      - ./logger/notification_log.level:/logs/notification_log.level
    environment:
      - PORT: 5000
      - JWT_PUBLIC_KEY=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUZrd0V3WUhLb1pJemowQ0FRWUlLb1pJemowREFRY0RRZ0FFaHRGQ0U3blo0RlhvZzMvN3lGRG5velRCSlhLdQpoNWpYWDI5T1NYOW0xbWxseWNMTlNzdE8vWWRDTk1oQmxmdXZnWjNuSWluUlNyWlBYQTRmQmZ1Wmd3PT0KLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg==
    networks:
      - app-network
    ports:
      - "5000:5000"
    restart: always

networks:
  app-network:
    driver: bridge
