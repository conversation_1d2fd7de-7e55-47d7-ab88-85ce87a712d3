import SmsControllers from "../controllers/controllers.js";
import express from "express";
import { validateSingleSms } from "../validations/index.js";
import { apiTokenAuthMiddleware } from "../middleware/authMiddleware.js";
const smsRouter = express.Router();

// Create a single SMS entry
smsRouter.post(
  "/create/single",
  validateSingleSms,
  SmsControllers.sendSingleSms
);
// smsRouter.post("/create/single", apiTokenAuthMiddleware, validateSingleSms, SmsControllers.sendSingleSms);

// Create a bulk SMS entries
smsRouter.post(
  "/create/bulk",
  apiTokenAuthMiddleware,
  SmsControllers.sendBulkSms
);

export { smsRouter };
