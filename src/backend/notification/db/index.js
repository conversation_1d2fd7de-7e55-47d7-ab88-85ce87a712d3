
import { Sequelize } from "sequelize";
import { smsQueueDbConfig } from "../config/database.js";


/**
 * Initialize the SMS queue database
 */
export const SmsQueueDb = new Sequelize(
  smsQueueDbConfig.database,
  smsQueueDbConfig.user,
  smsQueueDbConfig.password,
  {
    host: smsQueueDbConfig.host,
    dialect: smsQueueDbConfig.dialect,
    logging: smsQueueDbConfig.logging,
    port: smsQueueDbConfig.port,
    pool: smsQueueDbConfig.pool,
  }
);
