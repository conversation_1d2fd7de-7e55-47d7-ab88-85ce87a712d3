import { Sequelize } from "sequelize";
import { smsQueueDbConfig } from "../config/database.js";

/**
 * Ensure the SMS queue database exists
 */
export const ensureDatabase = async () => {
  const dbName = smsQueueDbConfig.database;

  console.info(`[DB] Checking if database '${dbName}' exists...`);

  const rootSequelize = new Sequelize(
    "",
    smsQueueDbConfig.user,
    smsQueueDbConfig.password,
    {
      host: smsQueueDbConfig.host,
      port: smsQueueDbConfig.port,
      dialect: "mariadb",
      logging: false,
    }
  );

  // For local development, uncomment the following code
  // try {
  //   // This will create the database if it doesn't exist (idempotent)
  //   await rootSequelize.query(`CREATE DATABASE IF NOT EXISTS \`${dbName}\`;`);
  //   console.info(`[DB] Database '${dbName}' is ready.`);
  // } catch (error) {
  //   console.error(`[DB] Failed to ensure database '${dbName}':`, error);
  //   throw error;
  // } finally {
  //   await rootSequelize.close(); // clean up connection
  // }
};

/**
 * Initialize the SMS queue database
 */
export const initDb = async () => {
  try {
    await ensureDatabase();
  } catch (error) {
    console.error("Error inside initDb", error);
    process.exit(1);
  }
};
