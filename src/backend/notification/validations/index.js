// middleware/validateSms.js
import <PERSON><PERSON> from "joi";

const smsSchema = Joi.object({
  msisdn: Joi.string().max(32).required(),
  message: Joi.string().max(16384).required(),
});
/**
 * Middleware to validate single SMS payload
 */
export const validateSingleSms = (req, res, next) => {
  const { error, value } = smsSchema.validate(req.body, { abortEarly: false });

  if (error) {
    return res.status(400).json({
      success: false,
      message: "Validation failed",
      details: error.details.map((err) => err.message),
    });
  }

  next();
};
