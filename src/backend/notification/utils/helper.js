import jwt from 'jsonwebtoken';
import settings from '../config/settings.js';

export const helperFunctions = {
  /**
   * Generates a URL-friendly slug from a given string using underscores.
   *
   * @param {string} name - The name to convert to a slug.
   * @returns {string} - The generated slug with underscores instead of hyphens.
   */
  generateUnderscoreSlug: function (name) {
    return name
      .trim() // Remove leading/trailing whitespace
      .replace(/[^\w\s-]/g, "") // Remove non-alphanumeric characters (except spaces, underscores, and hyphens)
      .replace(/[\s-]+/g, "_") // Replace spaces and hyphens with underscores
      .replace(/^_+|_+$/g, ""); // Remove leading/trailing underscores
  },

  /**
   * Function to create the middleware for Joi validation.
   * It returns middleware that validates using the passed Joi schema.
   */
  joiValidationMiddleware: (validationSchema) => {
    return (req, res, next) => {
      const { error } = validationSchema.validate(req.body, {
        abortEarly: false,
      }); // Validate the body with <PERSON><PERSON>
      if (error) {
        // Aggregate error details into a structured format
        const errors = error.details.map((err) => err.message);
        return res.status(400).json({ error: errors[0] });
      }
      next(); // Proceed to the next middleware if validation passes
    };
  },

  /**
 * Verifies a JWT token using Ed25519 public key.
 * @param {string} token - JWT token to verify.
 * @returns {{id: number, tenant_id: number, role_id: number, role_name: string} & Object.<string, any>} - Decoded token payload.
 */
   verifyJWT: (token) => {

    const publicKey = settings.jwtPublicKey;

    return jwt.verify(token, publicKey, { algorithms: ['ES256'] });
  },
  /**
 * Prepare the sms queue entries array
 * @param {String[]} msidins
 * @param {string} messageText
 * @returns {import("../models/smsQueue/SmsQueueModel.js").SmsQueueModel[]}
 */
  prepareSmsQueueEntries: (msidins, messageText) => {
  return msidins.map(
    /**
     * @param {string} msidin - The msidin to process.
     * @returns {import("../models/smsQueue/SmsQueueModel.js").SmsQueueModel} An object containing the phone number and the message.
     */
    (msidin) => ({
      destination_msisdn: msidin,
      message: messageText,
      // Generate unique id for the transaction by timestamp and msidin number
      transaction_id: helperFunctions.generateTid(msidin),
    }),
  );
},
/**
 * Generate a unique transaction ID based on the current timestamp and the last 4 digits of the MSISDN.
 *
 * @param {string|number} msisdn - The MSISDN (phone number) to use for generating the transaction ID.
 * @returns {number} The generated transaction ID.
 */
  generateTid: (msisdn) => {
  const timestamp = Date.now(); // Current timestamp in milliseconds
  const lastDigits = String(msisdn).slice(-4); // Extract the last 4 digits of the MSISDN
  return Number(`${timestamp}${lastDigits}`); // Concatenate and return as a number
}
};
