

import { helperFunctions } from "../utils/helper.js";
/**
 * Factory function that creates an Express middleware for role-based authentication.
 *
 * This middleware verifies the JWT token from the request headers and ensures the user has the required role/s
 * before granting access to the requested resource.
 *
 * @param {("user-admin" | "ruleset-admin" | "api")[] | null} requiredRoles - An array of roles that can access the route. Pass `null` to allow any authenticated user.
 * @returns {Function} Express middleware function that checks authentication and authorization.
 *
 * @example
 * // Protect a route, allowing only 'user-admin' or 'ruleset-admin'
 * app.get('/admin-dashboard', requireAuthWithRole(['user-admin', 'ruleset-admin']), (req, res) => {
 *     res.json({ message: 'Welcome Admin!' });
 * });
 *
 */
export const requireAuthWithRole = (requiredRoles) => {
  /**
   * Express middleware to verify authentication and authorization.
   *
   * @param {import('express').Request} req - Express request object.
   * @param {import('express').Response} res - Express response object.
   * @param {import('express').NextFunction} next - Express next function to proceed to the next middleware.
   * @throws {Object} 401 - Unauthorized error if the token is missing or invalid.
   * @throws {Object} 403 - Forbidden error if the user does not have any of the required roles.
   */
  return async (req, res, next) => {
    try {
      // Get token from headers
      const authHeader = req.headers.authorization;

      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return res
          .status(401)
          .json({ message: "Unauthorized: No token provided" });
      }

      // Extract token
      const token = authHeader.split(" ")[1];

      // Verify token
      let decoded = helperFunctions.verifyJWT(token);

      
      if (!decoded || !decoded.id) {
        return res.status(401).json({ message: `Unauthorized: Invalid token` });
      }

      // Extract user data from token
      req.user = {
        user_id: decoded.id,
        role_name: decoded.role_name,
      };

      req.userId = decoded.id;

      // Check if user has at least one of the required roles
      if (
        Array.isArray(requiredRoles) &&
        !requiredRoles.includes(decoded.role_name)
      ) {
        return res
          .status(403)
          .json({ message: `Forbidden: Insufficient permissions` });
      }

      next();
    } catch (error) {


      // Console log if the error is not the jwt error, expired error or not a jwt error
      if (
        error.name !== "JsonWebTokenError" &&
        error.name !== "TokenExpiredError"
      ) {
        console.log("warn", "Unauthorized: Invalid token", error);
      }

      return res.status(401).json({ message: "Unauthorized: Invalid token" });
    }
  };
};
