import { requireAuthWithRole } from "./authRoleMiddleware.js";


// Middleware to authenticate JWT token
export const jwtTokenAuthMiddleware = requireAuthWithRole(null);

// Middleware to authenticate API token
export const apiTokenAuthMiddleware = requireAuthWithRole(['api']);

// Middleware to authenticate non-API user
export const nonApiUserAuthMiddleware = requireAuthWithRole(['user-admin', 'ruleset-admin']);