import SmsQueueModel from "../models/SmsQueueModel.js";
import { helperFunctions } from "../utils/helper.js";

class SmsQueueService {
  /**
   * Creates a single SMS entry in the database
   * @param {Object[]} smsDataArray
   * @returns {Promise<Object[]>}
   */
  async createSingleSmsEntry(smsData) {
    let { msisdn, message } = smsData;

    let smsDataArray = helperFunctions.prepareSmsQueueEntries(
      [msisdn],
      message
    );
  
    try {
      // Create the single SMS entry
      const createdEntries = await SmsQueueModel.create(smsDataArray[0], {
        validate: true,
        ignoreDuplicates: false,
      });

      return createdEntries;
    } catch (error) {
      console.error("[SMS Service] Error creating single SMS entry:", error);
      throw new Error("Could not create SMS entry. " + error.message);
    }
  }

  /**
   * Creates bulk SMS entries in the database
   * @param {Object[]} smsDataArray
   * @returns {Promise<Object[]>}
   */
  async createBulkSmsEntries(smsDataArray) {
    if (!Array.isArray(smsDataArray) || smsDataArray.length === 0) {
      throw new Error("Input data must be a non-empty array.");
    }

    try {
      const createdEntries = await SmsQueueModel.bulkCreate(smsDataArray, {
        validate: true,
        ignoreDuplicates: false,
      });
      return createdEntries;
    } catch (error) {
      console.error("[SMS Service] Error creating bulk SMS entries:", error);
      throw new Error("Could not create bulk SMS entries. " + error.message);
    }
  }
}

export default new SmsQueueService();
