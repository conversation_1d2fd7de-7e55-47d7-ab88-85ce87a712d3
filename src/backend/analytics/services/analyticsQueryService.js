import { initSequelize } from '../db/sequelizeData.js';
import { logsFactoryFn } from '../logger/index.js';
import { Op } from 'sequelize';

const logs = logsFactoryFn('AnalyticsQueryService');

/**
 * Analytics Query Service - OPTIMIZED VERSION
 * Handles database operations for analytics data retrieval
 */
class AnalyticsQueryService {
    constructor() {
        this.sequelize = null;
        this.PersistentVariableHistory = null;
        this.isInitialized = false;
    }

    /**
     * Initialize database connection and models - SINGLETON PATTERN
     */
    async init() {
        if (this.isInitialized) {
            console.log(logs("init", "Analytics service already initialized"));
        }

        if (!this.isInitialized) {
            const { sequelize, PersistentVariableHistory } = await initSequelize();
            this.sequelize = sequelize;
            this.PersistentVariableHistory = PersistentVariableHistory;
            this.isInitialized = true;
            console.info(logs("init", "Analytics service initialized with connection pooling"));
        }
    }

    /**
 * Manually format Date object to ISO string format (YYYY-MM-DDTHH:mm:ss.sssZ)
 * @param {Date} dateObj - Date object to format
 * @returns {string} Manual ISO formatted string
 */
    _formatDateToISOString(dateObj) {
        const year = dateObj.getUTCFullYear();
        const month = String(dateObj.getUTCMonth() + 1).padStart(2, '0'); // Month is 0-indexed
        const day = String(dateObj.getUTCDate()).padStart(2, '0');
        const hours = String(dateObj.getUTCHours()).padStart(2, '0');
        const minutes = String(dateObj.getUTCMinutes()).padStart(2, '0');
        const seconds = String(dateObj.getUTCSeconds()).padStart(2, '0');
        const milliseconds = String(dateObj.getUTCMilliseconds()).padStart(3, '0');

        return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}Z`;
    }

    /**
     * Normalize timestamp to ISO format regardless of input type
     * Handles: ISO strings, database datetime strings, Unix timestamps, Date objects
     * @param {string|number|Date} timestamp - Input timestamp in any format
     * @returns {string} ISO formatted timestamp string
     */
    _normalizeTimestamp(timestamp) {
        try {
            // If already null or undefined, return null
            if (timestamp === null || timestamp === undefined) {
                return null;
            }

            // If it's already a valid ISO string, return as-is
            if (typeof timestamp === 'string' && timestamp.includes('T') && timestamp.includes('Z')) {
                // Validate it's actually a valid ISO string
                const testDate = new Date(timestamp);
                if (!isNaN(testDate.getTime())) {
                    return timestamp;
                }
            }

            // If it's a Unix timestamp (number)
            if (typeof timestamp === 'number') {
                // Handle both seconds and milliseconds
                const timestampMs = timestamp > 9999999999 ? timestamp : timestamp * 1000;
                const dateObj = new Date(timestampMs);
                if (!isNaN(dateObj.getTime())) {
                    return this._formatDateToISOString(dateObj);
                }
            }

            // If it's a database datetime string (e.g., "2025-07-07 11:04:12.380")
            if (typeof timestamp === 'string') {
                // Replace space with 'T' if it's a database format
                let normalizedString = timestamp;
                if (timestamp.includes(' ') && !timestamp.includes('T')) {
                    normalizedString = timestamp.replace(' ', 'T');
                    // Add 'Z' if missing timezone info
                    if (!normalizedString.includes('Z') && !normalizedString.includes('+') && !normalizedString.includes('-', 10)) {
                        normalizedString += 'Z';
                    }
                }

                const dateObj = new Date(normalizedString);
                if (!isNaN(dateObj.getTime())) {
                    return this._formatDateToISOString(dateObj);
                }
            }

            // If it's already a Date object
            if (timestamp instanceof Date) {
                if (!isNaN(timestamp.getTime())) {
                    return this._formatDateToISOString(timestamp);
                }
            }

            // If all else fails, try to create a Date object
            const fallbackDate = new Date(timestamp);
            if (!isNaN(fallbackDate.getTime())) {
                return this._formatDateToISOString(fallbackDate);
            }

            // If nothing works, return the original value as string
            console.warn(`Unable to normalize timestamp: ${timestamp}`);
            return String(timestamp);

        } catch (error) {
            console.error(`Error normalizing timestamp: ${error.message}`, timestamp);
            return String(timestamp);
        }
    }

    /**
     * Query analytics data with optimized single query approach
     */
    async queryAnalyticsData(filters = {}, pagination = {}) {
        try {

            console.info(logs("queryAnalyticsData", "Processing analytics query request"));

            // Extract and validate time bucket
            const timeBucket = filters.time_bucket;
            const isRawData = timeBucket === null || timeBucket === 0 || timeBucket === undefined;

            // Build WHERE clause (excluding time_bucket as it's not a database filter)
            const whereClause = this._buildWhereClause(filters);

            // Calculate pagination
            const page = parseInt(pagination.page) || 1;
            const limit = parseInt(pagination.limit) || 100;
            const offset = (page - 1) * limit;

            let dataResult, aggregationResult;

            if (isRawData) {
                // Path A: Raw Data - Use existing logic
                [dataResult, aggregationResult] = await Promise.all([
                    // Main data query with count
                    this.PersistentVariableHistory.findAndCountAll(
                        {
                            attributes: [
                                'transaction_timestamp',
                                'entity_id',
                                'context_id',
                                'rule_set_id',
                                'rule_set_version',
                                'collection_id',
                                'collection_key',
                                'collection_value',
                                'variable_id',
                                'variable_type',
                                'value_numeric',
                                'transaction_id',
                                'initiating_rule_id',
                                'rule_phase'
                            ],
                            where: whereClause,
                            limit,
                            offset,
                            order: [['transaction_timestamp', 'ASC']],
                            raw: true,
                            logging: false
                        }
                    ),
                    // Aggregation query for raw data (counts individual records)
                    this._getVariablesAggregationOptimized(whereClause)
                ]);

                const { count, rows } = dataResult;

                // Simplified data formatting for raw data
                const formattedData = rows.map(record => ({
                    timestamp: this._normalizeTimestamp(record.transaction_timestamp),
                    variable_id: record.variable_id,
                    value_numeric: record.value_numeric,
                }));

                // Calculate pagination metadata
                const totalPages = Math.ceil(count / limit);
                const paginationInfo = {
                    current_page: page,
                    total_pages: totalPages,
                    total_records: count,
                    records_per_page: limit,
                    has_next_page: page < totalPages,
                    has_previous_page: page > 1
                };

                console.info(logs("queryAnalyticsData", `Raw Data query completed: ${count} total records, ${formattedData.length} returned`));

                return {
                    records: formattedData,
                    pagination: paginationInfo,
                    aggregation: aggregationResult,
                    dateRange: this._calculateActualDateRange(filters)
                };

            } else {
                // Path B: Time-Bucketed Data

                [dataResult, aggregationResult] = await Promise.all([
                    this._executeTimeBucketedQuery(whereClause, timeBucket, limit, offset),
                    // Use time-bucketed aggregation (counts time buckets, not raw records)
                    this._getVariablesAggregationForTimeBucket(whereClause, timeBucket)
                ]);

                const { count, rows } = dataResult;
                const formattedData = this._formatTimeBucketedResults(rows);

                // Calculate pagination metadata for bucketed data
                const totalPages = Math.ceil(count / limit);
                const paginationInfo = {
                    current_page: page,
                    total_pages: totalPages,
                    total_records: count,
                    records_per_page: limit,
                    has_next_page: page < totalPages,
                    has_previous_page: page > 1
                };

                console.info(logs("queryAnalyticsData", `Bucketed query completed: ${count} total buckets, ${formattedData.length} returned`));

                return {
                    records: formattedData,
                    pagination: paginationInfo,
                    aggregation: aggregationResult,
                    dateRange: this._calculateActualDateRange(filters)
                };
            }

        } catch (error) {
            console.error(logs("queryAnalyticsData", `Error querying analytics data: ${error.message}`));
            throw error;
        }
    }


    /**
     * Execute time-bucketed aggregation query
     * @param {Object} whereClause - Sequelize WHERE clause
     * @param {number} timeBucket - Time bucket size in minutes
     * @param {number} limit - Pagination limit
     * @param {number} offset - Pagination offset
     * @returns {Promise} Query result with count and rows
     */
    async _executeTimeBucketedQuery(whereClause, timeBucket, limit, offset) {
        try {
            // Pre-calculate and cache commonly used values
            const sqlConditions = this._buildSqlWhereConditions(whereClause);
            const bucketSeconds = timeBucket * 60;
            const whereClauseStr = sqlConditions.length > 0 ? `WHERE ${sqlConditions.join(' AND ')}` : '';

            //  Single CTE-based query for both data and count - ColumnStore Compatible
            const optimizedQuery = `
                WITH bucketed_data AS (
                    SELECT 
                        variable_id,
                        FLOOR(UNIX_TIMESTAMP(transaction_timestamp) / ${bucketSeconds}) as time_bucket,
                        SUM(value_numeric) as aggregated_value,
                        COUNT(*) as record_count
                    FROM persistent_variable_history 
                    ${whereClauseStr}
                    GROUP BY variable_id, FLOOR(UNIX_TIMESTAMP(transaction_timestamp) / ${bucketSeconds})
                ),
                counted_data AS (
                    SELECT 
                        variable_id,
                        time_bucket,
                        time_bucket * ${bucketSeconds} as bucket_start_timestamp_unix,
                        aggregated_value,
                        record_count,
                        COUNT(*) OVER() as total_count,
                        ROW_NUMBER() OVER(ORDER BY time_bucket ASC) as row_num
                    FROM bucketed_data
                )
                SELECT 
                    variable_id,
                    bucket_start_timestamp_unix,
                    aggregated_value,
                    record_count,
                    total_count
                FROM counted_data
                WHERE row_num > ${offset} AND row_num <= ${offset + limit}
                ORDER BY bucket_start_timestamp_unix ASC
            `;

            console.info(logs("_executeTimeBucketedQuery", `Executing optimized bucketed query with ${timeBucket} minute buckets`));

            //  Single query execution instead of Promise.all
            const results = await this.sequelize.query(optimizedQuery, {
                type: this.sequelize.QueryTypes.SELECT,
                //  Query hints for ColumnStore
                logging: false,
                nest: false,
                raw: true
            });

            //  Extract count from first row (if exists)
            const totalCount = results.length > 0 ? parseInt(results[0].total_count) : 0;

            //  Remove total_count from result rows to clean data
            const cleanResults = results.map(row => ({
                variable_id: row.variable_id,
                bucket_start_timestamp_unix: row.bucket_start_timestamp_unix,
                aggregated_value: row.aggregated_value,
            }));

            return {
                count: totalCount,
                rows: cleanResults
            };

        } catch (error) {
            console.error(logs("_executeTimeBucketedQuery", `Error in optimized bucketed query: ${error.message}`));
            throw error;
        }
    }

    /**
     * Build SQL WHERE conditions from Sequelize WHERE clause - ColumnStore Compatible
     * @param {Object} whereClause - Sequelize WHERE clause
     * @returns {Array} Array of SQL condition strings
     */
    _buildSqlWhereConditions(whereClause) {
        const conditions = [];

        Object.keys(whereClause).forEach(key => {
            const value = whereClause[key];

            if (key === 'transaction_timestamp' && typeof value === 'object') {
                // Handle date range conditions for ColumnStore
                if (value[Op.gte]) {
                    // ColumnStore-compatible date comparison using YEAR, MONTH, DAY functions
                    const startDate = value[Op.gte];
                    conditions.push(`DATE(transaction_timestamp) >= DATE('${startDate.toISOString().split('T')[0]}')`);
                }
                if (value[Op.lte]) {
                    const endDate = value[Op.lte];
                    conditions.push(`DATE(transaction_timestamp) <= DATE('${endDate.toISOString().split('T')[0]}')`);
                }
                // Skip empty timestamp objects
            } else if (typeof value === 'object' && value !== null && value[Op.in]) {
                // Handle IN conditions
                const values = value[Op.in].map(v => `'${v}'`).join(',');
                conditions.push(`${key} IN (${values})`);
            } else if (typeof value === 'string' || typeof value === 'number') {
                // Handle simple equality conditions
                conditions.push(`${key} = '${value}'`);
            }
        });

        return conditions;
    }

    /**
     * Format time-bucketed results to match API response structure
     * @param {Array} rawResults - Raw SQL query results
     * @returns {Array} Formatted results matching current API structure
     */
    _formatTimeBucketedResults(rawResults) {
        return rawResults.map(record => ({
            timestamp: this._normalizeTimestamp(record.bucket_start_timestamp_unix * 1000),
            variable_id: record.variable_id,
            value_numeric: parseFloat(record.aggregated_value),
        }));
    }

    /**
     * Build WHERE clause based on provided filters
     * @param {Object} filters - Filter criteria
     * @returns {Object} Sequelize WHERE clause
     */
    _buildWhereClause(filters) {
        const whereClause = {};

        // Simple equality filters
        if (filters.entity_id) {
            whereClause.entity_id = filters.entity_id;
        }

        if (filters.context_id) {
            whereClause.context_id = filters.context_id;
        }

        if (filters.rule_set_id) {
            whereClause.rule_set_id = filters.rule_set_id;
        }

        if (filters.rule_set_version) {
            whereClause.rule_set_version = filters.rule_set_version;
        }

        if (filters.collection_id) {
            whereClause.collection_id = filters.collection_id;
        }

        if (filters.collection_key) {
            whereClause.collection_key = filters.collection_key;
        }

        if (filters.collection_value) {
            whereClause.collection_value = filters.collection_value;
        }

        // Array filters using IN operator
        if (filters.variable_ids && Array.isArray(filters.variable_ids) && filters.variable_ids.length > 0) {
            whereClause.variable_id = {
                [Op.in]: filters.variable_ids
            };
        }

        if (filters.initiating_rule_ids && Array.isArray(filters.initiating_rule_ids) && filters.initiating_rule_ids.length > 0) {
            whereClause.initiating_rule_id = {
                [Op.in]: filters.initiating_rule_ids
            };
        }

        // Date range filters

        // Smart date range filters with consistent logic
        const now = new Date();
        now.setUTCHours(23, 59, 59, 999);
        const sevenDaysAgo = new Date(now.getTime() - (6 * 24 * 60 * 60 * 1000));
        sevenDaysAgo.setUTCHours(0, 0, 0, 0);

        if (filters.start_date && filters.end_date) {
            // Case 1: Both start and end date provided
            const startDate = new Date(filters.start_date);
            startDate.setUTCHours(0, 0, 0, 0);

            const endDate = new Date(filters.end_date);
            endDate.setUTCHours(23, 59, 59, 999);
            whereClause.transaction_timestamp = {
                [Op.gte]: startDate,
                [Op.lte]: endDate
            };
        } else if (filters.start_date && !filters.end_date) {
            // Case 2: Only start date provided → from start date to current date
            const startDate = new Date(filters.start_date);
            startDate.setUTCHours(0, 0, 0, 0);
            whereClause.transaction_timestamp = {
                [Op.gte]: startDate,
                [Op.lte]: now
            };
        } else if (!filters.start_date && filters.end_date) {
            // Case 3: Only end date provided → 7 days before end date to end date
            const endDate = new Date(filters.end_date);
            endDate.setUTCHours(23, 59, 59, 999);
            const startDate = new Date(endDate.getTime() - (6 * 24 * 60 * 60 * 1000));
            startDate.setUTCHours(0, 0, 0, 0);
            whereClause.transaction_timestamp = {
                [Op.gte]: startDate,
                [Op.lte]: endDate
            };
        } else if (!filters.start_date && !filters.end_date) {
            // Case 4: No dates provided → last 7 days from current date
            whereClause.transaction_timestamp = {
                [Op.gte]: sevenDaysAgo,
                [Op.lte]: now
            };
        }

        console.info(logs("_buildWhereClause", "Built WHERE clause for analytics query"));

        return whereClause;
    }
    /**
     * Simple function to log raw SQL queries
     * @param {Object} queryOptions - Sequelize query options
     * @returns {Object} Modified query options with logging
     */
    _addQueryLogging(queryOptions) {
        return {
            ...queryOptions,
            logging: (sql) => {
                console.info(logs("SQL_QUERY Raw ##", `🔍 ${sql}`));
            }
        };
    }
    /**
     * Get variables aggregation for time-bucketed data
     * This function counts time buckets instead of raw records, providing accurate aggregation for bucketed data
     * @param {Object} whereClause - Sequelize WHERE clause
     * @param {number} timeBucket - Time bucket size in minutes
     * @returns {Promise} Aggregation result with bucket counts
     */
    async _getVariablesAggregationForTimeBucket(whereClause, timeBucket) {
        try {
            // Pre-calculate bucket seconds
            const bucketSeconds = timeBucket * 60;
            const sqlConditions = this._buildSqlWhereConditions(whereClause);
            const whereClauseStr = sqlConditions.length > 0 ? `WHERE ${sqlConditions.join(' AND ')}` : '';

            // CTE-based query to first create time buckets, then aggregate by variable
            const bucketedAggregationQuery = `
                WITH bucketed_data AS (
                    SELECT 
                        variable_id,
                        variable_type,
                        FLOOR(UNIX_TIMESTAMP(transaction_timestamp) / ${bucketSeconds}) as time_bucket,
                        SUM(value_numeric) as bucket_value
                    FROM persistent_variable_history 
                    ${whereClauseStr}
                    GROUP BY variable_id, variable_type, FLOOR(UNIX_TIMESTAMP(transaction_timestamp) / ${bucketSeconds})
                ),
                variable_aggregation AS (
                    SELECT 
                        variable_id,
                        variable_type,
                        SUM(bucket_value) as total_value,
                        COUNT(*) as bucket_count
                    FROM bucketed_data
                    GROUP BY variable_id, variable_type
                )
                SELECT 
                    variable_id,
                    total_value,
                    bucket_count as record_count
                FROM variable_aggregation
                ORDER BY variable_id
            `;

            console.info(logs("_getVariablesAggregationForTimeBucket", `Executing time-bucketed aggregation with ${timeBucket} minute buckets`));

            const aggregationResults = await this.sequelize.query(bucketedAggregationQuery, {
                type: this.sequelize.QueryTypes.SELECT,
                logging: false,
                nest: false,
                raw: true
            });

            return {
                variables: aggregationResults.map(item => ({
                    variable_id: item.variable_id,
                    totalValue: parseFloat(item.total_value),
                    record_count: parseInt(item.record_count) // This now represents bucket count, not raw record count
                }))
            };

        } catch (error) {
            console.error(logs("_getVariablesAggregationForTimeBucket", `Error in time-bucketed variables aggregation: ${error.message}`));
            throw error;
        }
    }

    /**
     *  Single aggregation query instead of reusing main query logic
     */
    async _getVariablesAggregationOptimized(whereClause) {
        try {
            const aggregationResults = await this.PersistentVariableHistory.findAll({
                attributes: [
                    'variable_id',
                    'variable_type',
                    [this.sequelize.fn('SUM', this.sequelize.col('value_numeric')), 'total_value'],
                    [this.sequelize.fn('COUNT', this.sequelize.col('variable_id')), 'record_count'],
                ],
                where: whereClause,
                group: ['variable_id', 'variable_type'],
                raw: true,
                logging: false // ✅ Reduce logging overhead
            });

            return {
                variables: aggregationResults.map(item => ({
                    variable_id: item.variable_id,
                    totalValue: item.total_value,
                    record_count: item.record_count
                }))
            };

        } catch (error) {
            console.error(logs("_getVariablesAggregationOptimized", `Error in variables aggregation: ${error.message}`));
            throw error;
        }
    }

    /**
     * Calculate the actual date range used (based on actual filters applied)
     * @param {Object} filters - Filter criteria
     * @returns {Object} Actual date range used
     */
    _calculateActualDateRange(filters) {
        const now = new Date();
        now.setUTCHours(23, 59, 59, 999);
        if (filters.start_date && filters.end_date) {
            // Case 1: Both start and end date provided
            const startDate = new Date(filters.start_date);
            startDate.setUTCHours(0, 0, 0, 0);
            const endDate = new Date(filters.end_date);
            endDate.setUTCHours(23, 59, 59, 999);
            return {
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString()
            };
        } else if (filters.start_date && !filters.end_date) {
            // Case 2: Only start date provided → from start date to current date
            const startDate = new Date(filters.start_date);
            startDate.setUTCHours(0, 0, 0, 0);
            return {
                startDate: startDate.toISOString(),
                endDate: now.toISOString()
            };
        } else if (!filters.start_date && filters.end_date) {
            // Case 3: Only end date provided → 7 days before end date to end date
            const endDate = new Date(filters.end_date);
            endDate.setUTCHours(23, 59, 59, 999);
            const startDate = new Date(endDate.getTime() - (6 * 24 * 60 * 60 * 1000));
            startDate.setUTCHours(0, 0, 0, 0);
            return {
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString()
            };
        } else {
            // Case 4: No date filters provided → last 7 days from current date
            const sevenDaysAgo = new Date(now.getTime() - (6 * 24 * 60 * 60 * 1000));
            sevenDaysAgo.setUTCHours(0, 0, 0, 0);
            return {

                startDate: sevenDaysAgo.toISOString(),
                endDate: now.toISOString()
            };
        }
    }

}


export default AnalyticsQueryService; 