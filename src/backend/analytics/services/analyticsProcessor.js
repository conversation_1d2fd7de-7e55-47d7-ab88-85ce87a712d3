import TransactionListCollector from './transactionListCollector.js';
import RedisClient from '../db/redisClient.js';
import { initSequelize } from '../db/sequelizeData.js';
import { logsFactoryFn } from '../logger/index.js';

const logs = logsFactoryFn("AnalyticsProcessor");

// Analytics processor configuration
const config = {
  listKey: process.env.REDIS_QUEUE_LIST_KEY || "analytics_queue:{common}",
  pollIntervalMs: process.env.POLL_INTERVAL_MINS ? process.env.POLL_INTERVAL_MINS * 60000 : 60000,
  batchSize: process.env.BATCH_SIZE || 10
};

class AnalyticsProcessor {
  constructor() {
    this.collector = null;
    this.running = false;
    this.sequelize = null;
    this.model = null;
  }

  /**
   * Starts the analytics processing service
   */
  async start() {
    if (this.running) {
        console.info(logs("start", "Analytics processor already running"));
      return;
    }

    try {
      console.info(logs("start", "Starting analytics processor..."));

      // Initialize Sequelize and get model
      const { sequelize, PersistentVariableHistory } = await initSequelize();
      this.sequelize = sequelize;
      this.model = PersistentVariableHistory;

      // Get Redis connection
      const redis = RedisClient.getConnection();
      if (!redis) {
        throw new Error('Redis connection not available');
      }
   

      // Create and start the transaction collector with proper model
      this.collector = new TransactionListCollector(redis, this.model, config);
      this.collector.start();

      this.running = true;
      console.info(logs("start", `Analytics processor started with config: ${JSON.stringify(config)}`));

    } catch (error) {
      console.error(logs("start", 'Failed to start analytics processor', error));
      throw error;
    }
  }

  /**
   * Stops the analytics processing service
   */
  stop() {
    if (!this.running) {
      console.warn(logs("stop", 'Analytics processor not running'));
      return;
    }

    console.info(logs("stop", 'Stopping analytics processor...'));
    
    if (this.collector) {
      this.collector.stop();
      this.collector = null;
    }

    // Close Sequelize connection
    if (this.sequelize) {
      this.sequelize.close();
    }

    this.running = false;
    console.info(logs("stop", 'Analytics processor stopped'));
  }

  /**
   * Gets the current status of the processor
   */
  getStatus() {
    return {
      running: this.running,
      collectorActive: this.collector ? true : false
    };
  }
}

export default AnalyticsProcessor;