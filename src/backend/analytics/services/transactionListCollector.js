import { logsFactoryFn } from '../logger/index.js';

const logs = logsFactoryFn("TransactionListCollector");

class TransactionListCollector {
  /**
   * @param {import('ioredis').Redis} redis - Redis instance
   * @param {import('sequelize').Model} model - Sequelize model
   * @param {Object} config
   * @param {string} config.listKey - Redis list key name
   * @param {number} config.pollIntervalMs - Polling interval in ms
   * @param {number} config.batchSize - Number of transactions per batch
   */
  constructor(redis, model, { listKey, pollIntervalMs = 60000, batchSize = 10 }) {
    this.redis = redis;
    this.model = model;
    this.listKey = listKey;
    this.pollIntervalMs = pollIntervalMs;
    this.batchSize = batchSize;
    this.running = false;
  }

  /**
   * Starts the transaction list collector
   */
  start() {
    if (this.running) return;
    this.running = true;
    console.info(logs("start", 'TransactionListCollector started'));
    this._pollLoop();
  }

  /**
   * Stops the transaction list collector
   */
  stop() {
    this.running = false;
    console.info(logs("stop", 'TransactionListCollector stopped'));
  }

  /**
   * Polls the transaction list and processes batches of transactions
   * 
   */
  async _pollLoop() {
    while (this.running) {
      await this._processBatch();
      await new Promise(resolve => setTimeout(resolve, this.pollIntervalMs));
    }
  }

  /**
   * Processes a batch of transactions
   */
  async _processBatch() {
    try {
      console.info(logs("processBatch", 'Processing batch'));
      const listLength = await this.redis.llen(this.listKey);
      console.info(logs("processBatch", `List length: ${listLength}`));
      if (listLength === 0) return;
      const loopCount = Math.ceil(listLength / this.batchSize);
      // loop loopCount times
      for (let i = 0; i < loopCount; i++) {
        const itemsLeft = listLength - this.batchSize * (i);
        console.info(logs("processBatch", `Items left: ${itemsLeft}`));
        const batchSize = Math.min(this.batchSize, itemsLeft);
        console.info(logs("processBatch", `Batch size: ${batchSize}`));
        const endNegativePos = -1;
        const startNegativePos = -batchSize;
        const rawBatch = await this.redis.lrange(this.listKey, startNegativePos, endNegativePos);

        // Process the batch
        if (!rawBatch.length) return;

        const allRows = [];
        for (const item of rawBatch) {
          const tx = JSON.parse(item);
          const rows = this._flattenTransaction(tx);
          allRows.push(...rows);
        }

        await this.model.sequelize.transaction(async (t) => {
          const chunkSize = 500; // Adjust chunk size as needed
          for (let i = 0; i < allRows.length; i += chunkSize) {
            const chunk = allRows.slice(i, i + chunkSize);
            await this.model.bulkCreate(chunk, {
              transaction: t,
              // Explicitly specify fields to avoid 'id' column error in ColumnStore
              fields: [
                'transaction_timestamp',
                'entity_id',
                'context_id',
                'rule_set_id',
                'rule_set_version',
                'collection_id',
                'collection_key',
                'collection_value',
                'variable_id',
                'variable_type',
                'value_numeric',
                'transaction_id',
                'initiating_rule_id',
                'rule_phase'
              ]
            });
          }
        });

        console.info(logs("processBatch", `Rows data are processed`));

        // Trim the list from redis

        // await this.redis.ltrim(this.listKey, startNegativePos, endNegativePos);
        await this.redis.ltrim(this.listKey, 0, -(batchSize + 1));
        const newListLength = await this.redis.llen(this.listKey);
        console.info(logs("processBatch", `Trimmed ${batchSize} items from list. New length: ${newListLength}`));

      }

    } catch (error) {
      console.error(logs("processBatch", `Error processing batch: ${error}`));
    }
  }

  /**
   * Flattens a transaction into a list of rows
   * @param {Object} tx - The transaction object
   * @returns {Array} The list of rows
   */
  _flattenTransaction(tx) {
    const {
      timestamp,
      entity_id,
      context_id,
      transaction_id,
      ruleset_id,
      ruleset_ver,
      collections,
      rule_phase,
    } = tx;

    const rows = [];
    for (const collection of collections) {
      const { id: collection_id, key, value, persistence } = collection;
      for (const variable of persistence) {
        // Handle the new nested data structure
        for (const dataEntry of variable.data) {
          const variable_type = this._inferType(dataEntry.value);

          // Convert the iso timestamp to mariadb timestamp
          const timestamp = dataEntry.timestamp.replace('Z', '').replace('T', ' ');

          rows.push({
            transaction_timestamp: timestamp,
            entity_id,
            context_id,
            rule_set_id: ruleset_id,
            rule_set_version: parseInt(ruleset_ver),
            collection_id,
            collection_key: key,
            collection_value: value,
            variable_id: variable.id,
            variable_type,
            value_numeric: variable_type === 'number' ? parseFloat(dataEntry.value) : null,
            transaction_id,
            initiating_rule_id: dataEntry.ruleId,
            rule_phase,
          });
        }
      }
    }
    return rows;
  }

  /**
   * Infers the type of a value
   * @param {any} value - The value to infer the type of
   * @returns {string} The type of the value
   */
  _inferType(value) {
    if (typeof value === 'number') return 'number';
    if (typeof value === 'boolean') return 'boolean';
    if (value instanceof Date) return 'date';
    return 'string';
  }
}

export default TransactionListCollector;