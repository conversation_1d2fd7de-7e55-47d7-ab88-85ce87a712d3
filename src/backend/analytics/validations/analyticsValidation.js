import Joi from 'joi';

/**
 * Validation schema for analytics query API
 * entity_id, context_id, and rule_set_id are mandatory for proper data filtering
 */
export const analyticsQuerySchema = Joi.object({
    filters: Joi.object({
        entity_id: Joi.string().uuid().required(),
        context_id: Joi.string().max(50).required(),
        rule_set_id: Joi.string().max(50).required(),
        rule_set_version: Joi.number().integer().min(1).optional().allow(null),
        collection_id: Joi.string().max(50).optional().allow(null),
        collection_key: Joi.string().max(255).optional().allow(null),
        collection_value: Joi.string().max(255).optional().allow(null),
        variable_ids: Joi.array().items(Joi.string().max(50)).optional().allow(null),
        initiating_rule_ids: Joi.array().items(Joi.string().max(50)).optional().allow(null),
        start_date: Joi.string().isoDate().optional().allow(null),
        end_date: Joi.string().isoDate().optional().allow(null),
        time_bucket: Joi.number().integer().min(0).optional().allow(null)
    }).required(),
    pagination: Joi.object({
        page: Joi.number().integer().min(1).default(1).optional(),
        limit: Joi.number().integer().min(1).max(1000).default(100).optional()
    }).optional()
}).custom((value, helpers) => {
    // Custom validation to ensure start_date is equal to or before end_date if both are provided
    if (value.filters?.start_date && value.filters?.end_date) {
        const startDate = new Date(value.filters.start_date);
        const endDate = new Date(value.filters.end_date);

        if (startDate > endDate) {
            return helpers.error('any.custom', {
                message: 'start_date must be before or equal to end_date'
            });
        }
    }

    return value;
}); 