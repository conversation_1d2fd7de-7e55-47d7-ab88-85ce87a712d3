#!/bin/bash

echo "💡 [Init Script] Ensuring root@'%' has access..."

# Wait for MariaD<PERSON> to be ready
until mariadb -uroot -p'MyS3cur3P@ssw0rd123!' -e "SELECT 1;" > /dev/null 2>&1; do
  echo "⏳ Waiting for MariaDB..."
  sleep 2
done

# Run the grant commands
mariadb -uroot -p'MyS3cur3P@ssw0rd123!' <<-EOSQL
  GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' IDENTIFIED BY 'MyS3cur3P@ssw0rd123!' WITH GRANT OPTION;
  GRANT ALL PRIVILEGES ON *.* TO 'root'@'_gateway' IDENTIFIED BY 'MyS3cur3P@ssw0rd123!' WITH GRANT OPTION;
  FLUSH PRIVILEGES;
EOSQL

echo "✅ [Init Script] root@'%' and root@'_gateway' granted access."