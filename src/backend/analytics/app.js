import express from "express";
import "./logger/index.js";
import RedisClient from "./db/redisClient.js";
import AnalyticsProcessor from "./services/analyticsProcessor.js";
import { logsFactoryFn } from './logger/index.js';
import { registerAllRoutes } from './routes/index.js';
import { closeConnection } from './db/sequelizeData.js';

const REDIS_HOSTS =
  process.env.REDIS_HOSTS || '[{"host":"ruleforge_cre_db","port":6379}]';

await RedisClient.configure(REDIS_HOSTS)


const logs = logsFactoryFn('app');

const app = express();

// Export app for use in routes
export default app;

// Middlewares
app.use(express.json()); // Parse JSON requests
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded data

// Initialize Analytics Processor
const analyticsProcessor = new AnalyticsProcessor();

// Register API Routes
registerAllRoutes();
console.info(logs("routes", "API routes registered successfully"));

// Start Analytics Processing Service
try {
  await analyticsProcessor.start();
  console.info(logs("start", "Analytics processor started successfully"));
} catch (error) {
  console.error(logs("start", `Failed to start analytics processor: ${error}`));
  process.exit(1);
}

// Global Error Handler
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ success: false, message: "Internal Server Error" });
});

// Start Server and Connect to Database
const PORT = process.env.PORT
const server = app.listen(PORT, () => {
  console.info(logs("listen", `Server is running on port ${PORT}`));
});

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  console.info(logs("gracefulShutdown", "Graceful shutdown initiated"));

  // Stop analytics processor first
  try {
    analyticsProcessor.stop();
    console.info(logs("gracefulShutdown", "Analytics processor stopped"));
  } catch (error) {
    console.error(logs("gracefulShutdown", `Error stopping analytics processor: ${error}`));
  }
  try {
    await closeConnection();
    console.info(logs("gracefulShutdown", "Database connection closed"));
  } catch (error) {
    console.error(logs("gracefulShutdown", `Error closing database connection: ${error}`));
  }


  // Close server
  server.close(() => {
    console.info(logs("gracefulShutdown", "HTTP server closed"));
  });

  process.exit(0);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));