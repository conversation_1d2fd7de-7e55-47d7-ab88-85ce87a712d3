import { queryAnalytics } from "../controllers/analyticsController.js";
import routePath from "./index.js";
import app from "../app.js";
import { jwtTokenAuthMiddleware } from "../middleware/authMiddleware.js";

/**
 * Register analytics routes.
 * Defines API endpoints for analytics data retrieval
 */
function registerAnalyticsRoutes() {
  // Analytics query endpoint

  app.post(routePath("analytics/query"),
    jwtTokenAuthMiddleware,
    queryAnalytics
  );
}

export default registerAnalyticsRoutes; 