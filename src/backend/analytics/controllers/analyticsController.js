import { analyticsQuerySchema } from '../validations/analyticsValidation.js';
import { validate<PERSON><PERSON> } from '../utils/validationsUtils.js';
import { sendSuccessResponse, sendErrorResponse } from '../utils/responseHandler.js';
import { logsFactoryFn } from '../logger/index.js';

const logs = logsFactoryFn('AnalyticsController');
let analyticsServiceInstance = null;
async function getAnalyticsServiceSingleton() {
    if (!analyticsServiceInstance) {
        const { default: AnalyticsQueryService } = await import('../services/analyticsQueryService.js');
        analyticsServiceInstance = new AnalyticsQueryService();
        await analyticsServiceInstance.init();
    }
    return analyticsServiceInstance;
}
/**
 * Analytics Query Controller
 * Handles HTTP requests for analytics data retrieval
 */

/**
 * Query analytics data based on filters and pagination
 * 
 * @async
 * @param {Object} req - Express request object
 * @param {Object} req.body - Request body containing filters and pagination
 * @param {Object} res - Express response object
 * @returns {Promise<void>} Sends response to client
 */
export const queryAnalytics = async (req, res) => {
    try {
        console.info(logs("queryAnalytics", "Processing analytics query request"));

        // Validate request body
        const requestData = {
            filters: req.body.filters || {},
            pagination: req.body.pagination || { page: 1, limit: process.env.RECORD_LIMIT }
        };

        // Apply default values for pagination
        requestData.pagination.page = requestData.pagination.page || 1;
        requestData.pagination.limit = requestData.pagination.limit || process.env.RECORD_LIMIT;

        // Validate using Joi schema
        validateJoi(analyticsQuerySchema, requestData);

        console.info(logs("queryAnalytics", "Request validated successfully"));
        // Create service instance and query data
        const analyticsService = await getAnalyticsServiceSingleton();
        const result = await analyticsService.queryAnalyticsData(
            requestData.filters,
            requestData.pagination
        );

        console.info(logs("queryAnalytics", `Query completed successfully. Records: ${result.records.length}, Total: ${result.pagination.total_records}`));

        // Send success response with the specified format
        sendSuccessResponse(
            res,
            {
                records: result.records,
                pagination: result.pagination,
                aggregation: result.records.length > 0 ? result.aggregation : { variables: [] },
                dateRange: result.dateRange
            },
            "Analytics data retrieved successfully"
        );

    } catch (error) {
        console.error(logs("queryAnalytics", `Error processing analytics query: ${error.message}`));

        // Handle different types of errors
        if (error.name === 'SequelizeDatabaseError') {
            return sendErrorResponse(
                res,
                "Database query failed",
                {
                    code: "DATABASE_ERROR",
                    details: "Error executing database query"
                },
                500
            );
        }

        if (error.name === 'SequelizeConnectionError') {
            return sendErrorResponse(
                res,
                "Database connection failed",
                {
                    code: "CONNECTION_ERROR",
                    details: "Unable to connect to database"
                },
                503
            );
        }

        // Handle validation errors and other errors
        sendErrorResponse(res, error, null, error.status || 500);
    }
}; 