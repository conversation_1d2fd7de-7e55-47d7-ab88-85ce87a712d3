import { logsFactoryFn } from '../logger/index.js';

const logs = logsFactoryFn("settings");

// If the JWT_PUBLIC_KEY is not set, throw an error
if (!process.env.JWT_PUBLIC_KEY) {
    console.error(logs("initializeJwtPublicKey", "JWT_PUBLIC_KEY is not set"));
    process.exit(1);
}

const settings = {

    // Decode the JWT_PUBLIC_KEY from base64 to utf8
    jwtPublicKey: Buffer.from(process.env.JWT_PUBLIC_KEY, 'base64').toString('utf8'),

}

export default settings;