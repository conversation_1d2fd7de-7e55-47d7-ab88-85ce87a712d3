import { JoiError } from "./validationsUtils.js";

/**
 * Handles API success responses.
 * @param {Response} res - Express response object
 * @param {Object|null} data - Response data (optional)
 * @param {string} message - Success message
 * @param {number} statusCode - HTTP status code (default 200)
 */
export const sendSuccessResponse = (res, data = null, message = 'Success', statusCode = 200) => {
    const response = {
        success: true,
        message,
    };

    if (data !== null) {
        response.data = data;
    }

    return res.status(statusCode).json(response);
};

/**
 * Sends a standardized error response to the client.
 *
 * @param {Object} res - The Express response object.
 * @param {string|JoiError|Error} [message='An error occurred'] - The error message Error or a JoiError object.
 * @param {Object|null} [errors=null] - Additional error details to include in the response.
 * @param {number} [statusCode=400] - The HTTP status code for the response.
 * @returns {Object} The response object with error details.
 */
export const sendErrorResponse = (res, message, errors = null, statusCode = 400) => {
    const response = {
        message: message || 'An error occurred',
    };

    // If the message is a JoiError object, extract the errors and set to the errors property
    if (message instanceof JoiError) {
        response.error = {
            code: "VALIDATION_ERROR",
            details: "Request validation failed",
            validation_errors: Object.entries(message.errors).map(([field, msg]) => ({
                field,
                message: msg
            }))
        };
        // Get first error message from the object
        response.message = Object.values(message.errors)[0];

        // If the message is an instance of Error, set the message property to the error message
    } else if (message instanceof Error) {
        response.message = message.message;
        response.error = {
            code: "INTERNAL_ERROR",
            details: message.message
        };
    }

    // If additional errors are provided, add them to the response
    if (errors) {
        response.error = errors;
    }

    return res.status(statusCode).json(response);
}; 