import mysql from "mysql2/promise";
import { logsFactoryFn } from '../logger/index.js';

const logs = logsFactoryFn('dbConnPool');

const DB_HOST = process.env.ANALYTICS_DB_HOST || "localhost";
const DB_PORT = process.env.ANALYTICS_DB_PORT || 3307;
const DB_USERNAME = process.env.ANALYTICS_DB_USERNAME || "root";
const DB_PASSWORD = process.env.ANALYTICS_DB_PASSWORD || "ussdgw";
const DB_NAME = process.env.ANALYTICS_DB_NAME || "ruleforge_cre_analytics_db";

/**
 * Ensures that a database exists; if it doesn't, creates it.
 *
 * @param {string} dbName - The name of the database to ensure.
 */
export async function ensureDatabase(dbName = DB_NAME) {
  let connection;
  try {
    // Connect without specifying a database.
    connection = await mysql.createConnection({
      host: DB_HOST,
      port: DB_PORT,
      user: DB_USERNAME,
      password: DB_PASSWORD
    });
    // Create the database if it doesn't exist.
    await connection.query(`CREATE DATABASE IF NOT EXISTS \`${dbName}\``);
   
  } catch (error) {
    console.error(logs("ensureDatabase", `Error ensuring database '${dbName}': ${error}`));
    throw error;
  } finally {
    if (connection) await connection.end();
  }
}

/**
 * Returns a connection pool for the given database after ensuring it exists.
 *
 * @param {string} dbName - The name of the database.
 * @returns {Promise<Pool>} - The mysql2 connection pool.
 */
export async function getDatabasePool(dbName) {
 
  await ensureDatabase(dbName);
  return mysql.createPool({
    host: DB_HOST,
    port: DB_PORT,
    user: DB_USERNAME,
    password: DB_PASSWORD,
    database: dbName,
    waitForConnections: true,
    connectionLimit: 10, // Maximum number of connections in the pool
    queueLimit: 0, // No limit for queued requests
    dateStrings: true // Disable the automatic conversion of the timestamp because we are using the custom timestamp as per MariaDb for Date(3) example: 2025-03-15 16:20:15.025
  });
} 

/**
 * Returns connection pools for both the journal and analytics databases.
 *
 * @returns {Promise<{journalPool: Pool, analyticsPool: Pool}>}
 */
export async function getConnectionPools() {
    try {
  const analyticsPool = await getDatabasePool(DB_NAME);
  console.info(logs("getConnectionPools", "db connected"));
  return { analyticsPool };
    } catch (error) {
        console.error(logs("getConnectionPools", `Error ensuring database ': ${error}`));
        throw error;
    }
}