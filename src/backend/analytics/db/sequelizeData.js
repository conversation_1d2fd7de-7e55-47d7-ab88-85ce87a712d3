import { Sequelize } from 'sequelize';
import { ensureDatabase } from './dbConnPool.js';
import PersistentVariableHistoryModel from '../models/PersistentVariableHistory.js';
import { logsFactoryFn } from '../logger/index.js';

const logs = logsFactoryFn('sequelizeData');

const DB_HOST = process.env.ANALYTICS_DB_HOST || "localhost";
const DB_PORT = process.env.ANALYTICS_DB_PORT || 3307;
const DB_USERNAME = process.env.ANALYTICS_DB_USERNAME || "root";
const DB_PASSWORD = process.env.ANALYTICS_DB_PASSWORD || "ussdgw";
const DB_NAME = process.env.ANALYTICS_DB_NAME || "ruleforge_cre_analytics_db";

let sequelize;
let PersistentVariableHistory;
let isInitialized = false;

export async function initSequelize() {
  try {
    if (isInitialized && sequelize && PersistentVariableHistory) {
      console.info(logs("initSequelize", 'database already initialized'));
      return { sequelize, PersistentVariableHistory };
    }
    sequelize = new Sequelize(DB_NAME, DB_USERNAME, DB_PASSWORD, {
      host: DB_HOST,
      port: DB_PORT,
      dialect: 'mysql',
      pool: {
        max: 2,           // Reduce max connections
        min: 1,           // Keep minimum connection
        acquire: 60000,   // 60 seconds to get connection
      },
      dialectOptions: {
        connectTimeout: 60000,    // 60 seconds
        dateStrings: true,
      },
      retry: {
        match: [/ETIMEDOUT/, /EHOSTUNREACH/, /ECONNRESET/, /ECONNREFUSED/],
        max: 3
      },
      logging: false
    });

    // Ensure database exists
    await ensureDatabase();

    console.info(logs("initSequelize", 'Database connection established successfully'));

    // Initialize models
    PersistentVariableHistory = PersistentVariableHistoryModel(sequelize);

    // ✅ Create table using raw query instead of sync()
    await createTableIfNotExists();
    isInitialized = true;
    console.info(logs("initSequelize", 'database singleton initialized and cached'));

    return { sequelize, PersistentVariableHistory };
  } catch (error) {
    console.error(logs("initSequelize", `Unable to connect to database: ${error}`));
    throw error;
  }
}

// ✅ Function to create table using raw SQL
async function createTableIfNotExists() {
  try {
    console.log('🔧 [Table Creation] Checking if table exists...');

    // Check if table exists
    const tableExistsQuery = `
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = '${DB_NAME}' 
      AND table_name = 'persistent_variable_history'
    `;

    const [results] = await sequelize.query(tableExistsQuery);
    const tableExists = results[0].count > 0;

    if (tableExists) {
      console.log('ℹ️ [Table Creation] Table persistent_variable_history already exists');
      return;
    }

    console.log('🔧 [Table Creation] Creating table persistent_variable_history...');

    // ✅ Raw SQL to create the table without id column
    const createTableSQL = `

      CREATE TABLE persistent_variable_history (
        transaction_timestamp DATETIME(3) NOT NULL,
        entity_id VARCHAR(36) NOT NULL,
        context_id VARCHAR(50),
        rule_set_id VARCHAR(50) NOT NULL,
        rule_set_version INT NOT NULL,
        collection_id VARCHAR(50) NOT NULL,
        collection_key VARCHAR(255) NOT NULL,
        collection_value VARCHAR(255) NOT NULL,
        variable_id VARCHAR(50) NOT NULL,
        variable_type VARCHAR(20) NOT NULL,
        value_numeric DOUBLE,
        transaction_id VARCHAR(50),
        initiating_rule_id VARCHAR(50),
        rule_phase VARCHAR(20)
      )
      ENGINE = ColumnStore;
    `;

    // console.log('🔍 [Table Creation] Executing SQL:', createTableSQL);

    await sequelize.query(createTableSQL);

    console.log('✅ [Table Creation] Table persistent_variable_history created successfully');

  } catch (error) {
    console.error('❌ [Table Creation] Error creating table:', error);
    throw error;
  }
}
export async function closeConnection() {
  if (sequelize) {
    await sequelize.close();
    sequelize = null;
    PersistentVariableHistory = null;
    isInitialized = false;
    console.info(logs("closeConnection", "Database connection closed"));
  }
}
export { sequelize, PersistentVariableHistory };