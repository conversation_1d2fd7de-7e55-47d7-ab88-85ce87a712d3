services:
  mariadb_columnstore:
    image: mariadb/columnstore:latest
    container_name: mariadb_columnstore
    ports:
      - "3310:3306"
    environment:
      MARIADB_ROOT_PASSWORD: MyS3cur3P@ssw0rd123!
      MARIADB_DATABASE: ruleforge_cre_analytics_db
      COLUMNSTORE_INSTALL_TYPE: single
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./docker/initdb/init-user.sh:/docker-entrypoint-initdb.d/init-user.sh
    networks:
      - app-network
    restart: unless-stopped
    


  rulesforge_analytics_service:
    build: .
    container_name: rulesforge_analytics_service
    working_dir: /usr/src/app
    command: bash -c "sleep 1 && node app.js"
    volumes:
      - .:/usr/src/app
      - ./logger/analytics_log.level:/logs/analytics_log.level
    environment:
      - PORT=5002
      - REDIS_HOSTS=[{"host":"localhost","port":6379}]
      - ANALYTICS_DB_HOST=mariadb_columnstore
      - ANALYTICS_DB_USERNAME=root
      - ANALYTICS_DB_PASSWORD=MyS3cur3P@ssw0rd123!
      - ANALYTICS_DB_PORT=3306
      - ANALYTICS_DB_NAME=ruleforge_cre_analytics_db
      - BATCH_SIZE=500
      - POLL_INTERVAL_MINS=1
      - REDIS_QUEUE_LIST_KEY=analytics_queue:{common}
      - RECORD_LIMIT=20
    networks:
      - app-network
    ports:
      - "5002:5002"
    depends_on:
      - mariadb_columnstore
    restart: always

networks:
  app-network:
    driver: bridge

volumes:
  mariadb_data: