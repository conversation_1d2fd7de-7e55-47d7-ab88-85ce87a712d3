import { DataTypes } from 'sequelize';

export default (sequelize) => {
  return sequelize.define('PersistentVariableHistory', {

    transaction_timestamp: {
      type: 'DATETIME(3)',
      allowNull: false,
    },
    entity_id: {
      type: DataTypes.STRING(36),
      allowNull: false,
    },
    context_id: { type: DataTypes.STRING(50), allowNull: true },
    rule_set_id: {
      type: DataTypes.STRING(50),
      allowNull: false,
    },
    rule_set_version: { type: DataTypes.INTEGER, allowNull: false },
    collection_id: {
      type: DataTypes.STRING(50),
      allowNull: false,
    },
    collection_key: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    collection_value: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    variable_id: {
      type: DataTypes.STRING(50),
      allowNull: false,
    },
    variable_type: { type: DataTypes.STRING(20), allowNull: false },
    value_numeric: { type: DataTypes.DECIMAL(20, 5), allowNull: true },
    transaction_id: { type: DataTypes.STRING(50), allowNull: true },
    initiating_rule_id: { type: DataTypes.STRING(50), allowNull: true },
    rule_phase: { type: DataTypes.STRING(20), allowNull: true }
  }, {
    tableName: 'persistent_variable_history',
    timestamps: false,
    primaryKey: false,
    noPrimaryKey: true,
    createdAt: false,
    updatedAt: false,
    engine: 'Columnstore',
    raw: false,

  });
};
