---
title: "Property Modification Guide"
classification: Internal
created: 2025-03-13
updated: 2025-04-15
authors:
  - "[[<PERSON>]]"
version: 1.1.0
next-review: 2025-06-13
category: "[[Specifications]]"
tags:
  - specification
  - guide
  - integration
topics:
  - "[[Property Modification Framework]]"
  - "[[Transaction Evaluation]]"
  - "[[Integration]]"
  - "[[RuleForge]]"
---

# Property Modification Guide

## Document Specification

### Purpose
This document provides comprehensive guidance on using the RuleForge Property Modification Framework (PMF) to apply rule set-driven property modifications to transactions. It serves as the technical reference for entity developers implementing transaction evaluation and property modification.

### Scope
This guide covers the complete transaction evaluation process, from building transaction contexts through applying modified properties and reporting outcomes. It includes context construction, API interactions, response handling, and best practices for property modification.

This document does not cover:
- Entity registration (covered in [[Entity Registration Guide]])
- Rule set creation and management (covered in separate documentation)
- Internal implementation details of the RuleForge system

### Target Audience
- Entity Developers
- System Integrators
- Technical Architects
- QA Engineers

## Table of Contents

- [[#1. Property Modification Overview]]
- [[#2. Two-Phase Rule Execution Model]]
- [[#3. Building Transaction Contexts]]
- [[#4. Transaction Evaluation (Phase 1)]]
- [[#5. Applying Property Modifications]]
- [[#6. Reporting Transaction Outcomes (Phase 2)]]
- [[#7. Error Handling]]
- [[#8. Implementation Best Practices]]
- [[#9. Integration Examples]]
- [[#10. Common Pitfalls and Solutions]]

## 1. Property Modification Overview

### 1.1 What is Property Modification?
Property modification is the core capability of RuleForge, allowing rule set rules to dynamically modify transaction properties in real-time. After an entity is registered, it can send transaction contexts to RuleForge for evaluation. RuleForge applies active rule set rules to the context and returns modified property values that the entity can then apply to its transactions.

### 1.2 Modification Lifecycle
```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ Build       │     │ Send to     │     │ Apply       │     │ Report      │
│ Transaction │────►│ RuleForge   │────►│ Modified    │────►│ Transaction │
│ Context     │     │ for Eval    │     │ Properties  │     │ Outcome     │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
```

### 1.3 Key Concepts
1. **Transaction Context**: The data representing a transaction, including its properties
2. **Property Modification**: Changes to property values determined by rule set rules
3. **Modification Constraints**: Business rules that limit valid modifications
4. **Transaction Outcome**: The result of applying modifications to a transaction
5. **Evaluation Rules**: Rules that execute during transaction evaluation to modify properties
6. **Outcome Rules**: Rules that execute when processing transaction outcomes

## 2. Two-Phase Rule Execution Model

RuleForge implements a two-phase rule execution model that aligns with the transaction lifecycle:

### 2.1 Phase 1: Evaluation Phase (Synchronous)
- **Purpose**: Evaluate the transaction and determine property modifications before transaction completion
- **Trigger**: The entity calls the `/evaluate` endpoint with a transaction context
- **Process**: RuleForge executes rules from the `evaluationRules` collection of applicable rule sets
- **Response**: Returns a list of property modifications to apply to the transaction
- **Implementation**: Must be synchronous, as property modifications need to be applied before transaction processing continues
- **Typical Use Cases**: Pricing adjustments, discounts, eligibility determination
- **Response Time**: Optimized for low-latency (typically <20ms)

### 2.2 Phase 2: Outcome Phase (Asynchronous)
- **Purpose**: Process transaction outcome data after transaction completion
- **Trigger**: The entity calls the `/outcomes` endpoint with the transaction outcome data
- **Process**: RuleForge executes rules from the `outcomeRules` collection of applicable rule sets
- **Response**: 202 Accepted (success acknowledgment without waiting for rule execution)
- **Implementation**: Should be implemented asynchronously by the entity to avoid impacting transaction latency
- **Typical Use Cases**: Loyalty point accrual, customer status updates, follow-up actions
- **Performance Benefits**: Asynchronous implementation reduces latency impact on the entity

### 2.3 Implementation Considerations
- The evaluation phase directly impacts customer-facing transaction processing and must be synchronous
- The outcome phase should be implemented asynchronously (e.g., using message queues or background tasks) to minimize integration costs and latency impacts
- Separate rules are executed in each phase - evaluation rules during evaluation, outcome rules during outcome reporting

```
┌─────────────────────────────────────────┐     ┌────────────────────────────────────┐
│           Evaluation Phase              │     │          Outcome Phase             │
│                                         │     │                                    │
│  ┌────────────┐     ┌────────────────┐  │     │ ┌────────────┐    ┌────────────┐  │
│  │            │     │                │  │     │ │            │    │            │  │
│  │  Entity    │────►│  RuleForge     │  │     │ │  Entity    │───►│ RuleForge  │  │
│  │ Transaction│◄────│  evaluationRules│  │     │ │ Background│◄───│ outcomeRules│  │
│  │ Processing │     │  (Sync)        │  │     │ │ Process    │    │ (Async)    │  │
│  │            │     │                │  │     │ │            │    │            │  │
│  └────────────┘     └────────────────┘  │     │ └────────────┘    └────────────┘  │
│                                         │     │                                    │
└─────────────────────────────────────────┘     └────────────────────────────────────┘
```

## 3. Building Transaction Contexts

### 3.1 Transaction Context Structure

A transaction context represents all relevant data for a specific transaction instance:

```json
{
  "entityId": "550e8400-e29b-41d4-a716-************",
  "contextId": "CONTEXT_ID",
  "transactionId": "UNIQUE_TRANSACTION_ID",
  "timestamp": "2025-03-13T14:30:00Z",
  "transactionData": {
    "property1": "value1",
    "property2": 50,
    "property3": true
  }
}
```

Key elements:
- **entityId**: Your registered entity identifier (UUID format)
- **contextId**: The registered transaction context type (UPPER_SNAKE_CASE)
- **transactionId**: A unique identifier for this specific transaction
- **timestamp**: When the transaction occurred or evaluation was requested
- **transactionData**: All properties relevant to the transaction

### 3.2 Context Construction Guidelines

For effective transaction contexts:

1. **Include Targeting Data**: Include properties that help rule sets target the right transactions
2. **Use Consistent Types**: Ensure property types match those defined during registration
3. **Include Immutable Properties**: Include both mutable and immutable properties for targeting
4. **Use Unique Transaction IDs**: Generate a unique ID for each transaction for traceability
5. **Set Accurate Timestamps**: Use the actual transaction time, not the evaluation request time
6. **Include Contextual Information**: Add relevant context like device info, location, etc.

### 3.3 Property Value Types

Property values must conform to the types defined during registration:

| Type | JSON Representation | Example |
|------|---------------------|---------|
| string | String literal | `"Premium"` |
| number | Number literal | `42` or `10.5` |
| boolean | Boolean literal | `true` or `false` |
| date | ISO 8601 string | `"2025-03-13T14:30:00Z"` |
| enum | String matching defined values | `"GOLD"` |
| object | JSON object | `{"city": "San Francisco", "country": "US"}` |
| array | JSON array | `[1, 2, 3]` |

### 3.4 Standard Properties

In addition to the explicitly defined properties in the transaction context, several standard properties are automatically available for use in rules:

- **transactionId**: The unique identifier for the transaction
- **entityId**: The identifier of the entity sending the transaction
- **contextId**: The identifier for the transaction context
- **timestamp**: When the transaction occurred

These standard properties can be referenced in rules using the same syntax as entity-defined properties.

### 3.5 Enriching Transaction Contexts

Consider selectively enriching contexts with relevant data based on your specific rule set requirements:

1. **User Segment Information**: Include when segment-specific rule sets are needed
2. **Location Data**: Add when geographical targeting is a key use case
3. **Business Time Context**: Include business-specific time information when relevant (e.g., "businessHours": true, "holidayPeriod": "CHRISTMAS")
4. **Device Information**: Add when device-specific experiences are needed

**Note on Historical Data**: Rather than including extensive historical data in each transaction context, RuleForge maintains its own historical tracking for rule sets. Only include historical context that is:
- Not already tracked by RuleForge
- Directly relevant to current rule set requirements
- Optimized for transmission (aggregated summaries rather than raw history)

Balance enrichment with performance considerations - only include data that enables your specific targeting needs.

#### 3.5.1 Example Enriched Context for E-commerce

```json
{
  "entityId": "550e8400-e29b-41d4-a716-************",
  "contextId": "PURCHASE",
  "transactionId": "TXN-123456",
  "timestamp": "2025-03-13T14:30:00Z",
  "transactionData": {
    "productPrice": 99.99,
    "productCategory": "ELECTRONICS",
    "discountPercentage": 0,
    "shippingCost": 5.99,
    "customerSegment": "LOYAL",
    "customerLifetimeValue": 1250.75,
    "purchaseCount": 12,
    "locationCity": "San Francisco",
    "locationCountry": "US",
    "deviceType": "MOBILE"
  }
}
```

**Note on Time Dimensions**: RuleForge automatically derives time-based dimensions (day of week, time of day, month, etc.) from the transaction timestamp. Entities should not include these derived values in their transaction contexts.

## 4. Transaction Evaluation (Phase 1)

### 4.1 Evaluation API

To evaluate a transaction context against active rule sets:

```
POST /api/v1/evaluate
Content-Type: application/json
Authorization: Bearer <your_api_token>

{
  "entityId": "550e8400-e29b-41d4-a716-************",
  "contextId": "CONTEXT_ID",
  "transactionId": "UNIQUE_TRANSACTION_ID",
  "timestamp": "2025-03-13T14:30:00Z",
  "transactionData": {
    ...
  }
}
```

### 4.2 Response Format

A successful evaluation response includes modified properties:

```json
{
  "transactionId": "UNIQUE_TRANSACTION_ID",
  "modifiedProperties": {
    "propertyName1": {
      "original": 50,
      "modified": 45,
      "ruleSetId": "RULE_SET_ID",
      "ruleId": "RULE_ID"
    },
    "propertyName2": {
      "original": "STANDARD",
      "modified": "PREMIUM",
      "ruleSetId": "RULE_SET_ID",
      "ruleId": "RULE_ID"
    }
  }
}
```

The `modifiedProperties` object contains:
- Property names as keys
- For each modified property:
  - `original`: The original value from the transaction context
  - `modified`: The new value determined by rule set rules
  - `ruleSetId`: The rule set that triggered the modification
  - `ruleId`: The specific rule that caused the modification

### 4.3 Performance Considerations

For optimal performance:

1. **Set Appropriate Timeouts**: Typically 500ms is recommended
2. **Implement Connection Pooling**: Reuse HTTP connections
3. **Batch Related Transactions**: When possible, process related transactions together
4. **Optimize Context Size**: Balance completeness with efficiency:
   - Always include all registered properties that rule sets might use for targeting
   - Include all mutable properties that could be modified by rule sets
   - For enrichment data, work with rule set managers to identify which properties are actually used in active rule sets
   - Consider implementing a configuration mechanism allowing selective inclusion of properties based on active rule set needs
   - Periodically review property usage analytics to identify unused properties
   - For very large contexts, consider implementing field filtering based on rule set requirements
5. **Local Caching**: Consider caching evaluation results for similar transactions

## 5. Applying Property Modifications

### 5.1 Validation Process

Before applying modifications:

1. Validate that the modification response corresponds to the sent transaction
2. Ensure the modified property values conform to expected types
3. Apply any additional business validations beyond RuleForge constraints
4. Log validation failures for analysis

### 5.2 Application Process

To apply property modifications:

1. Extract each modified property from the response
2. Convert property values to appropriate native types if needed
3. Apply the modifications to your transaction objects
4. Store both original and modified values in transaction records
5. **Update Derived Values**: Recalculate any values that depend on the modified properties:
   - If a discount percentage is modified, recalculate the final price
   - If a bundle's validity period is modified, update the expiry date
   - If multiple pricing components are modified, recalculate the total amount
   - If a quantity is modified, update the subtotal and any volume-based calculations
   - For interdependent properties, ensure all calculations reflect the modified values

### 5.3 Handling Multiple Modifications

When multiple properties are modified:

1. Apply modifications in a consistent order
2. Consider dependencies between properties
3. Recalculate any derived values after all modifications
4. Validate the final transaction state after all modifications

### 5.4 Example Application Logic

```javascript
function applyModifications(transaction, modificationResponse) {
  // Extract modifications
  const modifications = modificationResponse.modifiedProperties || {};
  
  // Track applied modifications for reporting
  const applied = [];
  const rejected = [];
  
  // Apply each modification
  for (const [propertyName, modification] of Object.entries(modifications)) {
    try {
      // Validate modification against business rules
      if (isValidModification(transaction, propertyName, modification.modified)) {
        // Store original value for reporting
        const originalValue = transaction[propertyName];
        
        // Apply the modification
        transaction[propertyName] = modification.modified;
        
        // Log successful application
        logModification(transaction.transactionId, propertyName, 
                        originalValue, modification.modified, 
                        modification.ruleSetId, modification.ruleId);
        
        applied.push(propertyName);
      } else {
        // Log rejected modification
        logRejectedModification(transaction.transactionId, propertyName, 
                               modification.original, modification.modified,
                               "Business rule violation");
        
        rejected.push(propertyName);
      }
    } catch (error) {
      // Log application error
      logModificationError(transaction.transactionId, propertyName, error);
      rejected.push(propertyName);
    }
  }
  
  // Update any derived values
  recalculateDerivedValues(transaction);
  
  return { applied, rejected };
}
```

## 6. Reporting Transaction Outcomes (Phase 2)

### 6.1 Asynchronous Implementation Approach

The outcome notification process should be implemented asynchronously to minimize the impact on transaction latency:

1. **Complete the transaction processing** using the modified properties
2. **Enqueue the outcome notification** for asynchronous processing:
   - Store outcome data in a durable queue or database
   - Process outcome notifications using a background worker or service
   - Implement retry logic for delivery failures
3. **Send the outcome notification** to RuleForge when resources are available

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ Complete    │     │ Enqueue     │     │ Process     │     │ Send to     │
│ Transaction │────►│ Outcome     │────►│ Queue       │────►│ RuleForge   │
│ Processing  │     │ Data        │     │ Async       │     │ /outcomes   │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
```

Benefits of asynchronous implementation:
- Minimizes impact on customer-facing transaction latency
- Improves system resilience to RuleForge unavailability
- Allows for efficient batching of outcome notifications
- Ensures transaction processing is not blocked by outcome reporting

### 6.2 Outcome Notification API

After completing a transaction with applied modifications:

```
POST /api/v1/outcomes
Content-Type: application/json
Authorization: Bearer <your_api_token>

{
  "entityId": "550e8400-e29b-41d4-a716-************",
  "contextId": "CONTEXT_ID",
  "transactionId": "UNIQUE_TRANSACTION_ID",
  "timestamp": "2025-03-13T14:35:00Z",
  "status": "COMPLETED",
  "transactionData": {
    "property1": "modified_value1",
    "property2": 45
  },
  "modificationStatus": {
    "applied": ["property2"],
    "rejected": []
  },
  "additionalData": {
    "conversionResult": true,
    "processingTime": 250
  }
}
```

Key elements:
- **status**: The final transaction status (COMPLETED, FAILED, ABANDONED)
- **transactionData**: The final property values after modifications
- **modificationStatus**: Which modifications were applied or rejected
- **additionalData**: Optional additional analytics data

### 6.3 Standard Properties in Outcome Phase

During the outcome phase, additional standard properties are available in rules:

- All standard properties from the evaluation phase
- **status**: Transaction status (COMPLETED, FAILED, ABANDONED)
- **modificationsApplied**: Array of property identifiers that were successfully modified
- **modificationsRejected**: Array of property identifiers that couldn't be modified

These properties can be referenced in outcome rules using the format `{propertyId}`. For example, a rule condition might check `{status} == "COMPLETED"`.

### 6.4 Outcome Notification Importance

Outcome notifications provide critical feedback for:

1. **Rule Set Effectiveness**: Measuring which rule sets drive conversions
2. **Modification Impact**: Understanding the effect of property modifications
3. **Persistent Variable Updates**: Updating counters and accumulators
4. **Analytics & Reporting**: Providing data for dashboards and reports

### 6.5 Queuing Outcome Notifications

For reliability, consider:

1. Storing outcome notifications in a durable queue
2. Implementing retry logic for failed notification delivery
3. Setting up a background process to handle notification delivery
4. Monitoring notification delivery success rates

## 7. Error Handling

### 7.1 Common Error Scenarios

| Scenario | Detection | Handling Strategy |
|----------|-----------|-------------------|
| Evaluation Timeout | Response time exceeds timeout | Use original property values, log incident |
| Service Unavailable | 503 status code | Use original values, queue for retry later |
| Authentication Error | 401/403 status codes | Log security incident, use original values |
| Validation Error | 400 status code | Log validation details, use original values |
| Internal Server Error | 500 status code | Log error, use original values |

### 7.2 Resilience Patterns

Implement these patterns for robust integration:

1. **Circuit Breaker**: Temporarily stop calling RuleForge after multiple failures
2. **Fallback Strategy**: Define clear behavior when modifications are unavailable
3. **Retry with Backoff**: Use exponential backoff for transient failures
4. **Health Checks**: Periodically verify RuleForge availability
5. **Degraded Mode**: Continue core operations when RuleForge is unavailable

### 7.3 Monitoring and Alerting

Monitor these key metrics:

1. **Evaluation Success Rate**: Percentage of successful evaluations
2. **Average Response Time**: Time taken for evaluation responses
3. **Modification Rejection Rate**: Percentage of modifications rejected
4. **Circuit Breaker Status**: Open/closed status of circuit breaker
5. **Outcome Notification Success**: Delivery success rate for notifications

## 8. Implementation Best Practices

### 8.1 Transaction Context Design

1. **Be Consistent**: Use consistent property names and types across contexts
2. **Be Comprehensive**: Include all properties that might affect rule set decisions
3. **Be Efficient**: Only include properties that are needed for rule set targeting or modification
4. **Handle High Volume**: Design for performance in high-volume transaction scenarios

### 8.2 Property Modification Application

1. **Validate Everything**: Never trust external services without validation
2. **Log Extensively**: Log all modifications for traceability
3. **Clear Fallbacks**: Have clear fallback behavior for failure scenarios
4. **Atomic Changes**: Apply modifications as a single atomic change where possible
5. **Preserve Original Values**: Store both original and modified values

### 8.3 Outcome Reporting

1. **Implement Asynchronously**: Process outcome notifications in the background
2. **Ensure Eventual Delivery**: Use durable storage and retry mechanisms
3. **Include All Outcomes**: Report all transactions, not just those with modifications
4. **Provide Detailed Status**: Clearly indicate which modifications were applied vs. rejected
5. **Monitor Delivery**: Track notification success rates and failures

### 8.4 Security Considerations

1. **Secure Tokens**: Properly secure and rotate API tokens
2. **Validate Responses**: Verify responses come from legitimate sources
3. **Sanitize Inputs**: Clean any user-provided data before including in contexts
4. **Limit Sensitive Data**: Never include sensitive user data in transaction contexts
5. **Audit Modifications**: Regularly audit property modifications for unexpected patterns

## 9. Integration Examples

### 9.1 E-commerce Example

```javascript
// Building transaction context
const transactionContext = {
  entityId: "550e8400-e29b-41d4-a716-************",
  contextId: "PURCHASE",
  transactionId: `TXN-${generateUniqueId()}`,
  timestamp: new Date().toISOString(),
  transactionData: {
    productPrice: product.price,
    productCategory: product.category,
    discountPercentage: 0,
    shippingCost: calculateShipping(cart),
    customerSegment: customer.segment,
    purchaseCount: customer.purchaseCount,
    deviceType: session.deviceType
  }
};

// Evaluating transaction (synchronous)
const evaluationResponse = await evaluateTransaction(transactionContext);

// Applying modifications
const { applied, rejected } = applyModifications(transaction, evaluationResponse);

// Processing transaction
const transactionResult = processTransaction(transaction);

// Enqueue outcome for asynchronous processing
await enqueueOutcomeNotification({
  entityId: "550e8400-e29b-41d4-a716-************",
  contextId: "PURCHASE",
  transactionId: transactionContext.transactionId,
  timestamp: new Date().toISOString(),
  status: transactionResult.success ? "COMPLETED" : "FAILED",
  transactionData: transaction,
  modificationStatus: {
    applied: applied,
    rejected: rejected
  },
  additionalData: {
    conversionResult: transactionResult.success,
    revenue: transactionResult.revenue
  }
});

// In a background worker process:
async function processOutcomeQueue() {
  const pendingOutcomes = await fetchPendingOutcomeNotifications(10);
  
  for (const outcome of pendingOutcomes) {
    try {
      await reportOutcome(outcome);
      await markOutcomeAsProcessed(outcome.id);
    } catch (error) {
      if (isRetryableError(error)) {
        await scheduleOutcomeRetry(outcome.id, calculateBackoff(outcome.attempts));
      } else {
        await markOutcomeAsFailed(outcome.id, error.message);
      }
    }
  }
}
```

### 9.2 Telecom Example

```javascript
// Building transaction context
const transactionContext = {
  entityId: "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
  contextId: "BUNDLE_SALE",
  transactionId: `BSALE-${generateUniqueId()}`,
  timestamp: new Date().toISOString(),
  transactionData: {
    bundleId: selectedBundle.id,
    bundleName: selectedBundle.name,
    bundleType: selectedBundle.type,
    agentPurchasePrice: selectedBundle.agentPrice,
    bundleRetailPrice: selectedBundle.retailPrice,
    bundleValidityDays: selectedBundle.validityDays,
    bundleDataAmount: selectedBundle.dataAmount,
    agentId: agent.id,
    agentTier: agent.tier,
    agentLocation: agent.location,
    subscriberId: subscriber.msisdn,
    subscriberSegment: subscriber.segment
  }
};

// Evaluating transaction (synchronous)
const evaluationResponse = await evaluateTransaction(transactionContext);

// Applying modifications
const { applied, rejected } = applyModifications(bundleSale, evaluationResponse);

// Processing transaction
const saleResult = processBundleSale(bundleSale);

// Enqueue outcome for asynchronous processing
await enqueueOutcomeNotification({
  entityId: "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
  contextId: "BUNDLE_SALE",
  transactionId: transactionContext.transactionId,
  timestamp: new Date().toISOString(),
  status: saleResult.success ? "COMPLETED" : "FAILED",
  transactionData: bundleSale,
  modificationStatus: {
    applied: applied,
    rejected: rejected
  },
  additionalData: {
    activationSuccess: saleResult.activationSuccess,
    agentCommission: saleResult.commission
  }
});

// Background processing handled by separate worker service
```

## 10. Common Pitfalls and Solutions

| Pitfall | Symptoms | Solution |
|---------|----------|----------|
| Synchronous Outcome Processing | High transaction latency | Implement asynchronous outcome reporting |
| Inconsistent Types | Type errors in evaluation or application | Ensure property types match registration |
| Missing Properties | Rule sets not triggering as expected | Include all properties defined during registration |
| Transaction ID Reuse | Duplicate transaction records | Generate unique IDs for each transaction |
| Timeout Too Short | High rate of timeout errors | Increase timeout value up to 500ms |
| Slow Evaluation | Transaction delays | Optimize context size and use connection pooling |
| Modification Loops | Infinite modifications | Apply modifications as a single atomic operation |
| Missing Outcome Reporting | Incomplete analytics | Ensure outcome reporting for all transactions |

## Related Documents
- [[RuleForge Framework Overview]]
- [[Entity Registration Guide]]
- [[Entity Developer Integration Guide]]
- [[Transaction Request Schema]]
- [[Transaction Response Schema]]
- [[Transaction Outcome Schema]]
- [[RuleForge API Reference]]
- [[Transaction Evaluation API]]
- [[Standard Transaction Properties Schema]]

## Approvals
| Role/Department | Name | Date | Signature |
|-----------------|------|------|-----------|
| Technical Architect |      |      |           |
| API Team Lead  |      |      |           |
| Product Manager |      |      |           |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.1.0   | [[2025-04-15]] | [[Wayne Smith]] | Updated terminology to consistently use "rule set" instead of "campaign"; added dedicated section on two-phase rule execution model; emphasized asynchronous implementation for outcome reporting; added standard properties information; updated API references to match current endpoints; reformatted document to align with Root Document Template v1.1.0; added metadata including category, tags, and topics. |
| 1.0.0   | [[2025-03-13]] | [[Wayne Smith]] | Initial version of the guide. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->
