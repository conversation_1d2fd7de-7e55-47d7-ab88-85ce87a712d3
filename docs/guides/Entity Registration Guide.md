---
title: "Entity Registration Guide"
classification: Internal
created: 2025-03-13
updated: 2025-04-17
authors:
  - "[[<PERSON>]]"
version: 1.1.2
next-review: 2025-06-13
category: "[[Specifications]]"
tags:
  - specification
  - guide
  - integration
topics:
  - "[[Property Modification Framework]]"
  - "[[Entity Management]]"
  - "[[Integration]]"
  - "[[RuleForge]]"
---

# Entity Registration Guide

## Document Specification

### Purpose
This document provides comprehensive guidance on registering entities with the RuleForge framework, including best practices for defining transaction contexts and properties. It serves as the technical reference for entity developers implementing the first phase of RuleForge integration.

### Scope
This guide covers the complete entity registration process, from initial analysis through successful registration confirmation. It includes property type definitions, constraint specifications, transaction context modeling, and registration validation.

This document does not cover:
- Transaction evaluation processes (covered in [[Property Modification Guide]])
- Rule set creation and management (covered in separate documentation)
- Internal implementation details of the RuleForge system

### Target Audience
- Entity Developers
- System Integrators
- Technical Architects
- QA Engineers

## Table of Contents

- [[#1. Entity Registration Overview]]
- [[#2. Preparing for Registration]]
- [[#3. Property Definition Guidelines]]
- [[#4. Registration Process]]
- [[#5. Advanced Registration Topics]]
- [[#6. Best Practices]]
- [[#7. Troubleshooting]]
- [[#8. Example Registrations]]

## 1. Entity Registration Overview

### 1.1 What is Entity Registration?
Entity registration is the foundational step for integrating with RuleForge. During registration, an entity defines its identity, transaction contexts, and properties that will be available for rule set-driven modifications. This process establishes the schema that RuleForge will use to validate transaction contexts and property modifications throughout the entity's lifecycle.

Upon successful registration, the system assigns a UUID entity ID (e.g., 550e8400-e29b-41d4-a716-************) that must be used in all subsequent API calls. This entity ID is different from the human-readable entity name that you provide during registration.

### 1.2 Registration Lifecycle
```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ Define      │     │ Register    │     │ Validate    │     │ Confirm     │
│ Properties  │────►│ Entity      │────►│ Registration│────►│ Integration │
│ & Contexts  │     │ with RF     │     │ Details     │     │ Success     │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
```

### 1.3 Key Components in Registration
1. **Entity Metadata** - Basic information about the entity
2. **Transaction Contexts** - Different types of transactions the entity processes
3. **Properties** - Data elements within each transaction context
4. **Mutability Flags** - Indicators of which properties can be modified by rule sets
5. **Constraints** - Rules that govern valid property modifications

## 2. Preparing for Registration

### 2.1 Analyzing Your Entity System

Before registering with RuleForge, analyze your entity system to identify:

1. **Transaction Types**: Different types of transactions that could benefit from rule set-driven modifications
2. **Transaction Contexts**: The data surrounding each transaction type
3. **Modifiable Properties**: Properties that business users should be able to modify through rule sets
4. **Business Constraints**: Rules that define valid modifications for each property

#### 2.1.1 Example Analysis for E-commerce Entity

| Transaction Type | Context ID | Properties | Modifiable? | Constraints |
|------------------|------------|------------|-------------|-------------|
| Product Purchase | PURCHASE   | productPrice | Yes | Min: cost price, Max: list price |
| Product Purchase | PURCHASE   | discountAmount | Yes | Min: 0, Max: 50% of price |
| Product Purchase | PURCHASE   | shippingCost | Yes | Min: 0 |
| Product Purchase | PURCHASE   | customerId | No | N/A |
| Cart Abandonment | ABANDONMENT | reminderDelay | Yes | Min: 1 hour, Max: 48 hours |
| Cart Abandonment | ABANDONMENT | offerType | Yes | Valid values: DISCOUNT, FREE_SHIPPING, BONUS_ITEM |

Note: For constraints that reference other values like "cost price" or "list price," the entity must include these values in the transaction context sent to RuleForge.

### 2.2 Identifying Transaction Contexts

For each major transaction type in your system, define a transaction context that includes:

1. **Context Identifier**: A unique identifier for the context (e.g., `PURCHASE`, `SUBSCRIPTION`, `BUNDLE_SALE`)
2. **Context Name**: A human-readable name for the context
3. **Context Description**: A clear description of when this context applies
4. **Property Set**: The complete set of properties available in this context

Consider the following when defining transaction contexts:
- Keep contexts focused on specific transaction types
- Include all properties that might be useful for rule targeting
- Consider future use cases beyond immediate requirements
- Align context names with existing business terminology

## 3. Property Definition Guidelines

### 3.1 Property Types

RuleForge supports the following property types:

| Type | Description | Example Values | Modifiable? |
|------|-------------|---------------|-------------|
| string | Text values | "Premium", "John Doe" | Yes |
| number | Numeric values (decimal or integer) | 10.5, 42 | Yes |
| boolean | True/false values | true, false | Yes |
| date | ISO 8601 date/time values | "2025-03-13T14:30:00Z" | Yes |
| enum | Values from a predefined set | "HIGH", "MEDIUM", "LOW" | Yes |
| array | Collection of values | [1, 2, 3], ["a", "b"] | No - Arrays themselves can't be modified |
| object | Nested property structure | {"name": "John", "age": 30} | No - Objects themselves can't be modified |

### 3.2 Mutability Considerations

When deciding which properties should be mutable (modifiable by rule sets):

1. **Business Control**: Properties that business teams need to control through rule sets
2. **Safety**: Avoid making properties mutable if modifications could compromise system integrity
3. **Performance**: Consider performance impact of modifications on high-volume properties
4. **Compliance**: Ensure modifications won't violate regulatory requirements
5. **Customer Experience**: Consider how modifications affect the end-user experience

#### 3.2.1 Recommended Mutable Properties

The following property types are typically good candidates for rule set modifications:

- Pricing values (prices, discounts, fees)
- Time values (validity periods, expiration dates)
- Quantity values (data amounts, product quantities)
- Option selections (service tiers, feature flags)
- Text messages (notifications, descriptions)

#### 3.2.2 Properties to Keep Immutable

The following property types should typically remain immutable:

- Customer identifiers
- Transaction identifiers
- Timestamps of events that have occurred
- Audit information
- Security-related fields
- Technical configuration parameters

### 3.3 Property Constraints

Define constraints to ensure rule set-driven modifications remain within acceptable bounds:

| Constraint Type | Description                                      | Example                                   |
| --------------- | ------------------------------------------------ | ----------------------------------------- |
| min             | Minimum allowed value for numeric properties     | min: 0                                    |
| max             | Maximum allowed value for numeric properties     | max: 100                                  |
| pattern         | Regular expression pattern for string properties | pattern: "^\[A-Z0-9]{6}$"                 |
| validValues     | Set of allowed values for enum properties        | validValues: ["GOLD", "SILVER", "BRONZE"] |
| minLength       | Minimum string length                            | minLength: 3                              |
| maxLength       | Maximum string length                            | maxLength: 50                             |
| function        | Dynamic constraint based on other properties     | function: "return value >= (price * 0.5)" |

**Note on Function Constraints:**
In addition to static values, RuleForge supports dynamic constraints through JavaScript functions that can reference other properties in the transaction context. These functions allow for constraints that adapt based on the specific transaction data. For full details on function constraint syntax, execution context, and examples, please refer to the [[RuleForge API Reference]] document.

**Note on Regular Expression Patterns:**
Pattern constraints use JavaScript's implementation of regular expressions (ECMAScript). For details on supported syntax and features, refer to the [[RuleForge API Reference]] document.

### 3.4 Constraint Violation Handling

When RuleForge applies rule set rules that modify property values, it enforces all defined constraints. The behavior when a modification violates a constraint depends on the constraint type:

| Constraint Type | Violation Handling | Example |
|-----------------|-------------------|---------|
| min | Clamp to minimum value | If rule sets bonus=5 but min=10, final value will be 10 |
| max | Clamp to maximum value | If rule sets discount=15 but max=10, final value will be 10 |
| pattern | Reject modification | If rule sets code="AB-1" but pattern requires digits only, modification is rejected |
| validValues | Reject modification | If rule sets tier="PLATINUM" but valid values are ["GOLD","SILVER"], modification is rejected |
| minLength/maxLength | Reject modification | If string length constraints are violated, modification is rejected |
| function | Reject modification | If custom function constraint returns false, modification is rejected |

For numeric constraints (min/max), RuleForge will automatically adjust values to comply with the constraints rather than rejecting the modification entirely. For all other constraint types where automatic adjustment isn't possible, the modification will be rejected and the original property value will be preserved.

When a modification is rejected due to constraint violation, RuleForge logs the violation and continues processing other properties. The transaction will still be processed with the original (unmodified) value for any rejected properties.

## 4. Registration Process

### 4.1 Creating the Registration Payload

To register your entity with RuleForge, create a JSON payload following the Entity Registration JSON Structure:

```json
{
  "name": "Your Entity Name",
  "description": "Description of your entity system",
  "transactionContexts": [
    {
      "contextId": "CONTEXT_ID",
      "name": "Context Name",
      "description": "Description of the context",
      "properties": [
        {
          "propertyId": "propertyName",
          "name": "Property Display Name",
          "description": "Description of property",
          "type": "string",
          "mutable": true,
          "constraints": {
            "pattern": "^[A-Za-z0-9]+$"
          }
        },
        {
          "propertyId": "numericProperty",
          "name": "Numeric Property",
          "description": "Numeric property example",
          "type": "number",
          "mutable": true,
          "constraints": {
            "min": 0,
            "max": 100
          }
        }
      ]
    }
  ]
}
```

Note the order of fields in property definitions:
1. `propertyId`: Identifier of the property
2. `name`: Human-readable name
3. `description`: Description of purpose
4. `type`: Data type
5. `mutable`: Mutability flag
6. Other fields (constraints, values) depending on type

### 4.2 Submitting the Registration

Submit your registration payload to the Entity Integration API:

```
POST /entities
Content-Type: application/json
Authorization: Bearer <your_api_token>

{
  "name": "Your Entity Name",
  ...
}
```

### 4.3 Handling Registration Responses

#### 4.3.1 Successful Registration

A successful registration will return a 201 Created response with a system-assigned UUID for your entity:

```json
{
  "entityId": "550e8400-e29b-41d4-a716-************",
  "status": "success",
  "message": "Entity successfully registered",
  "registrationDateTime": "2025-03-13T14:30:00Z"
}
```

Note that while you provide a human-readable name in your registration payload, the system assigns a UUID as the entity's unique identifier. This entityId should be used in all subsequent API calls and will be used to track your entity across the RuleForge platform.

#### 4.3.2 Validation Errors

If registration validation fails, you'll receive a 400 Bad Request response with details:

```json
{
  "status": "error",
  "message": "Registration validation failed",
  "errors": [
    {
      "field": "transactionContexts[0].properties[1].constraints.min",
      "message": "Min value must be less than max value"
    }
  ]
}
```

## 5. Advanced Registration Topics

### 5.1 Entity Updates

To update an existing entity registration:

```
PUT /entities/{entityId}
Content-Type: application/json
Authorization: Bearer <your_api_token>

{
  "name": "Your Entity Name",
  ...
}
```

Keep in mind the following compatibility rules for updates:

1. **Adding Properties**: You can add new properties to existing contexts
2. **Mutability Changes**: You can change immutable properties to mutable, but cannot change mutable properties to immutable (as this would break existing rule sets)
3. **Constraint Changes**: You can relax constraints but not tighten them
4. **Data Type Changes**: You cannot change the data type of existing properties

### 5.2 Reserved for Future Implementation

**Note**: Nested object properties are not currently supported in the current implementation. This section is reserved for future implementation.

The ability to define nested object properties will be added in a future release of the RuleForge system. When implemented, this feature will allow entities to define hierarchical property structures.

Any attempt to register an entity with nested object properties will result in a validation error in the current API version.

## 6. Best Practices

### 6.1 Naming Conventions

Follow these naming conventions for consistent and clear registration:

1. **Entity Names**: Use clear, descriptive names (e.g., "SmartShop Bundle Delivery Service", "Payment Gateway")
2. **Context IDs**: Use UPPER_SNAKE_CASE (e.g., `PURCHASE`, `BUNDLE_SALE`)
3. **Property IDs**: Use camelCase (e.g., `productPrice`, `customerSegment`)
4. **Enum Values**: Use UPPER_SNAKE_CASE (e.g., `GOLD_TIER`, `PREMIUM_PACKAGE`)

Note that entity IDs are system-assigned UUIDs and not chosen by the entity developer.

### 6.2 Property Design

For effective property design:

1. **Be Specific**: Use precise property names that clearly indicate purpose
2. **Be Consistent**: Use consistent naming patterns across properties
3. **Use Constraints**: Always define constraints for mutable properties
4. **Document Well**: Provide clear descriptions for all properties
5. **Think Future**: Consider future use cases when defining properties
6. **Avoid Redundancy**: Don't create multiple properties for the same data

### 6.3 Testing Registration

Before registering in production:

1. Use a test environment for initial registration
2. Validate all property constraints with sample data
3. Create test rule sets targeting registered properties
4. Verify registration with RuleForge's validation endpoints
5. Test edge cases for all constraints

## 7. Troubleshooting

### 7.1 Common Registration Issues

| Issue | Possible Causes | Resolution |
|-------|----------------|------------|
| Invalid property type | Unsupported data type specified | Use one of the supported types |
| Constraint validation failure | Contradictory constraints | Ensure constraints are logically consistent |
| Duplicate property | Property name already exists | Use unique property names within a context |
| Missing required field | Required attribute not provided | Ensure all required fields are present |
| Authorization failure | Invalid or expired API token | Obtain a new API token |

### 7.2 Getting Support

If you encounter issues with entity registration:

1. Check the [Known Issues](https://support.ruleforge.com/known-issues) page
2. Contact your RuleForge integration specialist
3. Email <EMAIL> with your entity ID and error details
4. Open a support ticket in the RuleForge portal

## 8. Example Registrations

### 8.1 E-commerce Example

```json
{
  "name": "E-commerce Platform",
  "description": "Online shopping platform",
  "transactionContexts": [
    {
      "contextId": "PURCHASE",
      "name": "Product Purchase",
      "description": "Customer purchasing products",
      "properties": [
        {
          "propertyId": "customerId",
          "name": "Customer ID",
          "description": "Unique identifier for the customer",
          "type": "string",
          "mutable": false
        },
        {
          "propertyId": "productPrice",
          "name": "Product Price",
          "description": "Price of the product",
          "type": "number",
          "mutable": true,
          "constraints": {
            "min": 0
          }
        },
        {
          "propertyId": "discountPercentage",
          "name": "Discount Percentage",
          "description": "Discount percentage",
          "type": "number",
          "mutable": true,
          "constraints": {
            "min": 0,
            "max": 50
          }
        },
        {
          "propertyId": "customerSegment",
          "name": "Customer Segment",
          "description": "Type of customer",
          "type": "enum",
          "mutable": false,
          "values": ["NEW", "RETURNING", "LOYAL", "VIP"]
        }
      ]
    },
    {
      "contextId": "CART_ABANDONMENT",
      "name": "Cart Abandonment",
      "description": "Customer abandoned cart without completing purchase",
      "properties": [
        {
          "propertyId": "customerId",
          "name": "Customer ID",
          "description": "Unique identifier for the customer",
          "type": "string",
          "mutable": false
        },
        {
          "propertyId": "cartTotal",
          "name": "Cart Total",
          "description": "Total value of abandoned cart",
          "type": "number",
          "mutable": false
        },
        {
          "propertyId": "reminderDelay",
          "name": "Reminder Delay",
          "description": "Delay before sending reminder (hours)",
          "type": "number",
          "mutable": true,
          "constraints": {
            "min": 1,
            "max": 48
          }
        },
        {
          "propertyId": "offerType",
          "name": "Offer Type",
          "description": "Type of offer to send",
          "type": "enum",
          "mutable": true,
          "values": ["DISCOUNT", "FREE_SHIPPING", "BONUS_ITEM"],
          "constraints": {
            "validValues": ["DISCOUNT", "FREE_SHIPPING"]
          }
        }
      ]
    }
  ]
}
```

### 8.2 Telecom Example

```json
{
  "name": "Telecom Operator",
  "description": "Mobile telecommunication services provider",
  "transactionContexts": [
    {
      "contextId": "BUNDLE_SALE",
      "name": "Bundle Sale",
      "description": "Agent selling service bundle to subscriber",
      "properties": [
        {
          "propertyId": "agentMsisdn",
          "name": "Agent MSISDN",
          "description": "The mobile number of the agent performing the transaction",
          "type": "string",
          "mutable": false
        },
        {
          "propertyId": "agentCgi",
          "name": "Agent CGI",
          "description": "The Cell Global Identity representing the agent's location",
          "type": "string",
          "mutable": false
        },
        {
          "propertyId": "bundleRetailPrice",
          "name": "Bundle Retail Price",
          "description": "The retail price of the bundle being sold",
          "type": "number",
          "mutable": false
        },
        {
          "propertyId": "agentPurchasePrice",
          "name": "Agent Purchase Price",
          "description": "Wholesale price charged to agent",
          "type": "number",
          "mutable": true,
          "constraints": {
            "min": 0
          }
        },
        {
          "propertyId": "bundleValidityDays",
          "name": "Bundle Validity Days",
          "description": "Validity period in days",
          "type": "number",
          "mutable": true,
          "constraints": {
            "min": 1,
            "max": 90
          }
        },
        {
          "propertyId": "bundleId",
          "name": "Bundle ID",
          "description": "Identifier for the bundle being sold",
          "type": "string",
          "mutable": false
        },
        {
          "propertyId": "subscriberMsisdn",
          "name": "Subscriber MSISDN",
          "description": "The mobile number of the subscriber receiving the bundle",
          "type": "string",
          "mutable": false
        }
      ]
    }
  ]
}
```

## Related Documents
- [[RuleForge Interface Overview]]
- [[Property Modification Guide]]
- [[Entity Integration Schema]]
- [[RuleForge API Reference]]
- [[Transaction Evaluation API]]
- [[Rule Set Schema]]
- [[RuleForge Naming Convention Guide]]

## Approvals
| Role/Department | Name | Date | Signature |
|-----------------|------|------|-----------|
| Technical Architect |      |      |           |
| API Team Lead  |      |      |           |
| Product Manager |      |      |           |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.1.2   | [[2025-04-17]] | [[Wayne Smith]] | Comprehensive update to align with current implementation: removed nested object properties and array properties not currently supported; clarified entity ID handling as system-assigned UUIDs; updated API endpoint paths to match interface overview (/entities instead of /api/v1/entities); updated example registrations to match production implementation; improved field ordering documentation; updated related documents list. |
| 1.1.1   | [[2025-04-10]] | [[Wayne Smith]] | Fixed naming inconsistency: changed `contextDescription` to `description` in transaction contexts to align with RuleForge Naming Convention Guide. |
| 1.1.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated terminology to consistently use "rule set" instead of "campaign"; aligned with Entity Integration API v2.2.0; updated Entity Registration endpoint from POST `/entities/registration` to `/entities`; updated property field naming to use `propertyId`; reformatted document to align with Root Document Template v1.1.0; added metadata including category, tags, and topics. |
| 1.0.0   | [[2025-03-13]] | [[Wayne Smith]] | Initial version of the guide. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->