---
title: "RuleForge Naming Convention Guide"
classification: Confidential
created: 2025-04-06
updated: 2025-04-08
authors:
  - "[[<PERSON>]]"
version: 1.1.0
next-review: 2025-10-06
category: "[[Specifications]]"
tags:
  - specification
  - reference
  - coding-standards
topics:
  - "[[Property Modification Framework]]"
  - "[[Development Standards]]"
  - "[[RuleForge]]"
  - "[[Integration]]"
---

# RuleForge Naming Convention Guide

## Document Specification

### Purpose
This document defines the standard naming conventions for all components within the RuleForge Property Modification Framework (PMF). It provides authoritative guidance for identifier naming, case conventions, and field standards to ensure consistency across all system components, APIs, and documentation.

### Scope
This document covers:
- Identifier field naming standards across all component types
- Case conventions for different component types and fields
- Formats and constraints for identifier fields
- Naming patterns for human-readable descriptive fields
- Collection and array naming conventions
- Examples of correct usage for each convention

This document does not cover:
- Implementation details for enforcing naming conventions
- Validation rules for names beyond their format
- Specifics of data validation not related to naming

### Target Audience
- Developers implementing RuleForge components
- System architects designing RuleForge integrations
- Documentation specialists creating RuleForge documentation
- QA engineers validating adherence to standards
- Partners integrating with the RuleForge system

## Table of Contents

- [[#1. Core Naming Principles]]
- [[#2. Identifier Field Standards]]
- [[#3. Case Conventions]]
- [[#4. Component-Specific Naming Standards]]
- [[#5. Collection and Array Naming]]
- [[#6. Human-Readable Name and Description Fields]]
- [[#7. Cross-Reference of Identifier Relationships]]
- [[#8. Examples]]

## 1. Core Naming Principles

The RuleForge system follows these core naming principles:

1. **Consistency**: Similar components use similar naming patterns
2. **Clarity**: Names clearly communicate purpose and content
3. **Specificity**: Names are specific enough to avoid ambiguity
4. **Alignment**: Schema definitions align with API usage
5. **Convention**: Industry standard conventions are followed where appropriate

## 2. Identifier Field Standards

### 2.1 Standardized Identifier Field Pattern

All identifiers in the RuleForge system follow the consistent pattern of `{type}Id`:

| Component Type | Identifier Field | Format | Example |
|----------------|-----------------|--------|---------|
| Entity | `entityId` | UUID format | `550e8400-e29b-41d4-a716-************` |
| Rule Set | `ruleSetId` | String, max 50 chars, UPPER_SNAKE_CASE | `PRICING_RULES_2025` |
| Rule | `ruleId` | String, max 50 chars, UPPER_SNAKE_CASE | `HIGH_VALUE_PRICING` |
| Context | `contextId` | String, max 50 chars, UPPER_SNAKE_CASE | `PURCHASE` |
| Variable | `variableId` | String, max 50 chars, camelCase | `discountAmount` |
| Property | `propertyId` | String, max 50 chars, camelCase | `productPrice` |
| Collection | `collectionId` | String, max 50 chars, camelCase | `customers` |
| Condition Type | `conditionTypeId` | String, max 50 chars, UPPER_SNAKE_CASE | `COMPARISON` |
| Function | `functionId` | String, max 50 chars, camelCase | `dayOfWeek` |
| Assignment Type | `assignmentTypeId` | String, max 50 chars, UPPER_SNAKE_CASE | `SET` |
| Parameter | `parameterId` | String, max 50 chars, camelCase | `leftOperand` |
| Transaction | `transactionId` | String, max 50 chars, alphanumeric | `TXN123456789` |

### 2.2 Field Length Constraints

All identifier fields have specific length constraints to ensure consistency and efficient storage:

- All identifiers: Maximum 50 characters
- Human-readable name fields: Maximum 100 characters
- Description fields: Maximum 250 characters (500 for Rule Sets and Rules)

### 2.3 Identifier Uniqueness Requirements

To prevent ambiguity in references:

1. Rule set IDs must be unique within the system
2. Rule IDs must be unique within a rule set
3. Context IDs must be unique within an entity
4. Property IDs must be unique within a context
5. Variable IDs must be unique within a rule set
6. Variable IDs must not conflict with property IDs of the associated context

## 3. Case Conventions

### 3.1 Case Convention Definitions

The RuleForge system uses the following case conventions:

- **UPPER_SNAKE_CASE**: All letters uppercase, words separated by underscores
  - Example: `PRICING_RULES_2025`, `PURCHASE`, `COMPARISON`
  
- **camelCase**: First letter lowercase, subsequent words capitalized, no separators
  - Example: `productPrice`, `discountAmount`, `dayOfWeek`

- **kebab-case**: All letters lowercase, words separated by hyphens
  - Example: `rule-sets`, `api-keys` (used for API route paths)

### 3.2 Case Convention by Component and Context

| Context | Component | Case Convention | Examples |
|---------|-----------|-----------------|----------|
| **Schema** | Entity ID | UUID format | `550e8400-e29b-41d4-a716-************` |
| | Rule Set ID | UPPER_SNAKE_CASE | `PRICING_RULES_2025` |
| | Rule ID | UPPER_SNAKE_CASE | `HIGH_VALUE_PRICING` |
| | Context ID | UPPER_SNAKE_CASE | `PURCHASE` |
| | Variable ID | camelCase | `discountAmount` |
| | Property ID | camelCase | `productPrice` |
| | Condition Type ID | UPPER_SNAKE_CASE | `COMPARISON` |
| | Function ID | camelCase | `dayOfWeek` |
| | Assignment Type ID | UPPER_SNAKE_CASE | `SET` |
| | Enum Values | UPPER_SNAKE_CASE | `COMPLETED`, `SATURDAY` |
| | JSON Fields | camelCase | `entityName`, `startDateTime` |
| **API** | Route Paths | kebab-case | `/rule-sets`, `/api-keys` |
| | Path Parameters | camelCase | `ruleSetId`, `entityId` |
| | Query Parameters | camelCase | `entityId`, `pageSize` |
| | JSON Fields | camelCase | `modifiedProperties` |

## 4. Component-Specific Naming Standards

### 4.1 Entity Identifiers

- **Field**: `entityId`
- **Format**: UUID (server-generated)
- **Example**: `550e8400-e29b-41d4-a716-************`
- **Notes**: 
  - Entity IDs are generated by the server upon entity registration
  - They follow the standard UUID format
  - Entity IDs are not user-editable
  - In API requests, the entityId must be included to specify which entity is being referenced when using organization-level API keys

### 4.2 Rule Set Identifiers

- **Field**: `ruleSetId`
- **Format**: String, max 50 characters, UPPER_SNAKE_CASE
- **Example**: `PRICING_RULES_2025`, `LOYALTY_PROGRAM_VIP`
- **Notes**:
  - Rule Set IDs should be semantically meaningful
  - They should indicate the purpose or domain of the rules
  - They may include a time period if version-specific
  - Rule Set IDs should be unique across the system

### 4.3 Rule Identifiers

- **Field**: `ruleId`
- **Format**: String, max 50 characters, UPPER_SNAKE_CASE
- **Example**: `HIGH_VALUE_PRICING`, `WEEKEND_DISCOUNT`
- **Notes**:
  - Rule IDs should clearly indicate the rule's purpose
  - They should be unique within a rule set
  - They should be concise but descriptive
  - They are referenced in transaction responses to identify which rule modified a property

### 4.4 Context Identifiers

- **Field**: `contextId`
- **Format**: String, max 50 characters, UPPER_SNAKE_CASE
- **Example**: `PURCHASE`, `CART_ABANDONMENT`
- **Notes**:
  - Context IDs should represent a specific transaction scenario
  - They should be unique within an entity
  - They should clearly communicate the transaction type
  - They are used in transaction requests to identify the transaction context for evaluation

### 4.5 Variable Identifiers

- **Field**: `variableId`
- **Format**: String, max 50 characters, camelCase
- **Example**: `discountAmount`, `totalPurchases`
- **Notes**:
  - Variable IDs should indicate what the variable represents
  - They should be unique within their scope (local or persistent)
  - They should follow common programming variable naming conventions
  - They are used in variable assignments to specify which variable to modify
  - Variable IDs must not conflict with property IDs

### 4.6 Property Identifiers

- **Field**: `propertyId`
- **Format**: String, max 50 characters, camelCase
- **Example**: `productPrice`, `customerType`
- **Notes**:
  - Property IDs should clearly indicate what data they represent
  - They should follow common programming property naming conventions
  - They should be unique within their transaction context
  - They are referenced in curly braces in conditions (e.g., `{productPrice}`)

### 4.7 Collection Identifiers

- **Field**: `collectionId`
- **Format**: String, max 50 characters, camelCase
- **Example**: `customers`, `products`
- **Notes**:
  - Collection IDs should be plural nouns
  - They should clearly indicate what type of entities the collection contains
  - They are used to group persistent variables

### 4.8 Condition Type Identifiers

- **Field**: `conditionTypeId`
- **Format**: String, max 50 characters, UPPER_SNAKE_CASE
- **Example**: `COMPARISON`, `LOGICAL`
- **Notes**:
  - Condition type IDs should reflect the operation they perform
  - They should be succinct and clear
  - They represent operations/evaluations (not transformations)

### 4.9 Function Identifiers

- **Field**: `functionId`
- **Format**: String, max 50 characters, camelCase
- **Example**: `dayOfWeek`, `formatDate`, `calculateDiscount`
- **Notes**:
  - Function IDs should indicate what the function does
  - They should follow common programming function naming conventions
  - They represent transformations/computations

### 4.10 Assignment Type Identifiers

- **Field**: `assignmentTypeId`
- **Format**: String, max 50 characters, UPPER_SNAKE_CASE
- **Example**: `SET`, `ADD`, `DECREASE_BY_PERCENTAGE`
- **Notes**:
  - Assignment type IDs should reflect the operation they perform
  - They should be succinct and clear
  - They represent operations/actions (not transformations)

### 4.11 Parameter Identifiers

- **Field**: `parameterId`
- **Format**: String, max 50 characters, camelCase
- **Example**: `leftOperand`, `rightOperand`, `date`, `pattern`
- **Notes**:
  - Parameter IDs should clearly describe the parameter's purpose
  - They should follow standard parameter naming conventions
  - For fixed-purpose parameters (like comparison operands), use descriptive names
  - For generic parameters, use names that reflect their purpose and type

### 4.12 Enum Values

- **Format**: UPPER_SNAKE_CASE
- **Example**: `COMPLETED`, `NEW_CUSTOMER`, `SATURDAY`
- **Notes**:
  - Enum values should be descriptive
  - They should be concise
  - They should clearly represent the specific option
  - For status fields, common values include: `ACTIVE`, `DRAFT`, `PAUSED`, `ARCHIVED`

## 5. Collection and Array Naming

### 5.1 Collection Naming Guidelines

- Collections should have plural names: `customers`, `products`, `orders`
- Individual items should have singular names: `customer`, `product`, `order`

### 5.2 Array Field Naming

All array fields should use consistent plural naming:

| Array Field | Items Type | Example |
|-------------|------------|---------|
| `rules` | Rule objects | List of rules in a rule set |
| `properties` | Property definitions | List of properties in a context |
| `variableAssignments` | Assignment operations | List of assignments in a rule |
| `collectionMappings` | Collection mapping objects | List of collection mappings |
| `persistentVariables` | Variable definitions | List of persistent variables |
| `localVariables` | Variable definitions | List of local variables |
| `parameters` | Parameter definitions | List of parameters for a function |
| `conditions` | Condition objects | List of conditions in a logical condition |
| `operators` | Operator strings | List of allowed operators |

## 6. Human-Readable Name and Description Fields

### 6.1 Name Field Standards

All components use a consistent `name` field for human-readable identification:

| Component Type | Identifier Field | Name Field | Example |
|----------------|-----------------|------------|---------|
| Entity | `entityId` | `name` | "E-commerce Platform" |
| Rule Set | `ruleSetId` | `name` | "Dynamic Pricing Rules 2025" |
| Rule | `ruleId` | `name` | "High Value Customer Pricing" |
| Context | `contextId` | `name` | "Product Purchase" |
| Variable | `variableId` | `name` | "Discount Amount" |
| Property | `propertyId` | `name` | "Product Price" |
| Collection | `collectionId` | `name` | "Customers" |
| Condition Type | `conditionTypeId` | `name` | "Comparison" |
| Function | `functionId` | `name` | "Day of Week" |
| Assignment Type | `assignmentTypeId` | `name` | "Set Value" |
| Parameter | `parameterId` | `name` | "Left Operand" |

### 6.2 Description Field Standards

Description fields provide additional context beyond the name, with a consistent format:

| Component Type | Description Field | Example |
|----------------|------------------|---------|
| Entity | `description` | "Online shopping platform" |
| Rule Set | `description` | "Adaptive pricing rules based on demand and customer segments" |
| Rule | `description` | "Apply special pricing for high value customers on purchases over $100" |
| Context | `description` | "Customer purchasing products" |
| Variable | `description` | "Calculated discount amount for the current transaction" |
| Property | `description` | "Price of the product before any discounts" |
| Condition Type | `description` | "Compares two values using a specified operator" |
| Function | `description` | "Returns the day of the week for a given date" |
| Assignment Type | `description` | "Sets the variable to the specified value" |
| Parameter | `description` | "The left-hand side of the comparison" |

> **Note**: Description field names should be consistently named `description` rather than using prefixes like `entityDescription`. This maintains consistent field naming throughout the schema.

## 7. Cross-Reference of Identifier Relationships

The following table shows how identifiers relate across different schemas:

| Primary Identifier | Referenced In | As Field | Purpose |
|-------------------|---------------|----------|---------|
| `entityId` | Rule Set Schema | `entityId` | Identifies entity the rule set applies to |
| `entityId` | Transaction Request Schema | `entityId` | Identifies transaction source |
| `contextId` | Rule Set Schema | `contextId` | Identifies transaction context the rule set applies to |
| `contextId` | Transaction Request Schema | `contextId` | Identifies transaction type |
| `name` | All Components | `name` | Provides human-readable identification |
| `ruleSetId` | Transaction Response Schema | `ruleSetId` | Identifies source of property modifications |
| `ruleId` | Transaction Response Schema | `ruleId` | Identifies specific rule that modified property |
| `variableId` | Variable Assignment | `variableId` | Identifies target for assignment operation |
| `propertyId` | Condition Parameter | `{propertyId}` | References property value in conditions |
| `functionId` | Function Call | `functionId` | Identifies function to call in a function reference |
| `conditionTypeId` | Condition Object | `conditionTypeId` | Identifies type of condition for evaluation |
| `assignmentTypeId` | Assignment Object | `assignmentTypeId` | Identifies type of assignment operation |

## 8. Examples

### 8.1 Complete Entity Registration Example

```json
{
  "name": "E-commerce Platform",
  "description": "Online shopping platform",
  "transactionContexts": [
    {
      "contextId": "PURCHASE",
      "name": "Product Purchase",
      "description": "Customer purchasing products",
      "properties": [
        {
          "propertyId": "customerId",
          "name": "Customer ID",
          "description": "Unique identifier for the customer",
          "type": "string",
          "mutable": false
        },
        {
          "propertyId": "productPrice",
          "name": "Product Price",
          "description": "Price of the product",
          "type": "number",
          "mutable": true,
          "constraints": {
            "min": 0
          }
        }
      ]
    }
  ]
}
```
 
### 8.2 Rule Set Example

```json
{
  "schemaVersion": "5.0.0",
  "ruleSetId": "PRICING_RULES_2025",
  "name": "Dynamic Pricing Rules 2025",
  "description": "Adaptive pricing rules based on demand and customer segments",
  "entityId": "550e8400-e29b-41d4-a716-************",
  "contextId": "PURCHASE", 
  "version": 1,
  "status": "ACTIVE",
  "category": "PRICING",
  "startDateTime": "2025-06-01T00:00:00Z",
  "endDateTime": "2025-12-31T23:59:59Z",
  "lastModifiedDateTime": "2025-04-05T14:30:00Z",
  "collectionMappings": [
    {
      "collectionId": "customers",
      "name": "Customers",
      "imported": false,
      "keyMapping": {
        "propertyId": "customerId"
      }
    }
  ],
  "persistentVariables": [
    {
      "variableId": "totalPurchases",
      "name": "Total Purchases",
      "description": "The total amount of purchases made by a customer",
      "type": "number",
      "defaultValue": 0,
      "collectionId": "customers"
    }
  ],
  "localVariables": [
    {
      "variableId": "discountAmount",
      "name": "Discount Amount",
      "description": "Calculated discount amount for the current transaction",
      "type": "number",
      "defaultValue": 0
    }
  ],
  "rules": [
    {
      "ruleId": "HIGH_VALUE_PRICING",
      "name": "High Value Customer Pricing",
      "description": "Apply special pricing for high value customers on purchases over $100",
      "priority": 1,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": ">",
              "parameters": {
                "leftOperand": "{productPrice}",
                "rightOperand": 100
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{customerSegment}",
                "rightOperand": "HIGH_VALUE"
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "discountAmount",
          "assignmentTypeId": "SET",
          "value": 10
        },
        {
          "variableId": "productPrice",
          "assignmentTypeId": "DECREASE_BY_PERCENTAGE",
          "value": 10
        }
      ]
    }
  ]
}
```

### 8.3 Condition Type Example

```json
{
  "conditionTypeId": "COMPARISON",
  "name": "Comparison",
  "description": "Compares two values using a specified operator",
  "applicableTypes": ["string", "number", "boolean", "date", "enum"],
  "parameters": [
    {
      "parameterId": "leftOperand",
      "name": "Left Operand",
      "description": "The left-hand side of the comparison",
      "type": "any",
      "required": true
    },
    {
      "parameterId": "rightOperand",
      "name": "Right Operand",
      "description": "The right-hand side of the comparison",
      "type": "any",
      "required": true
    }
  ],
  "operators": ["==", "!=", "<", "<=", ">", ">=", "IN", "NOT_IN", "CONTAINS", "STARTS_WITH", "ENDS_WITH"]
}
```

### 8.4 Function Example

```json
{
  "functionId": "dayOfWeek",
  "name": "Day of Week",
  "description": "Returns the day of the week for a given date",
  "parameters": [
    {
      "parameterId": "date",
      "name": "Date",
      "description": "The date to get the day of week for",
      "type": "date"
    }
  ],
  "returnType": "string",
  "returnValues": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"],
  "examples": [
    {
      "reference": {
        "functionId": "dayOfWeek",
        "args": ["2025-03-28T00:00:00Z"]
      },
      "result": "FRIDAY"
    }
  ]
}
```

### 8.5 Variable Assignment Type Example

```json
{
  "assignmentTypeId": "DECREASE_BY_PERCENTAGE",
  "name": "Decrease by Percentage",
  "description": "Decreases the variable by the specified percentage",
  "applicableTypes": ["number"]
}
```

### 8.6 Transaction Request Example

```json
{
  "entityId": "550e8400-e29b-41d4-a716-************",
  "contextId": "PURCHASE",
  "transactionId": "TXN123456789",
  "timestamp": "2025-03-15T14:30:00Z",
  "transactionData": {
    "customerId": "CUST123456",
    "productPrice": 99.99,
    "discountPercentage": 0,
    "customerSegment": "HIGH_VALUE",
    "paymentMethod": "CREDIT_CARD"
  }
}
```

### 8.7 Transaction Response Example

```json
{
  "transactionId": "TXN123456789",
  "modifiedProperties": {
    "productPrice": {
      "original": 99.99,
      "modified": 89.99,
      "ruleSetId": "PRICING_RULES_2025",
      "ruleId": "HIGH_VALUE_PRICING"
    },
    "discountPercentage": {
      "original": 0,
      "modified": 10,
      "ruleSetId": "PRICING_RULES_2025",
      "ruleId": "HIGH_VALUE_PRICING"
    }
  }
}
```

### 8.8 API Endpoints Example

```
GET /rule-sets?entityId=550e8400-e29b-41d4-a716-************&contextId=PURCHASE
POST /rule-sets
GET /rule-sets/{ruleSetId}
PUT /rule-sets/{ruleSetId}
DELETE /rule-sets/{ruleSetId}
PUT /rule-sets/{ruleSetId}/status
```

## Related Documents

- [[Rule Set Schema]]
- [[Condition Type Schema]]
- [[Function Schema]]
- [[Variable Assignment Schema]]
- [[Entity Integration Schema]]
- [[Transaction Request Schema]]
- [[Transaction Response Schema]]
- [[Transaction Outcome Schema]]
- [[RuleForge Management API]]
- [[Entity Integration API]]
- [[Transaction Evaluation API]]
- [[RuleForge Interface Overview]]

## Approvals

| Role/Department       | Name | Date | Signature |
| --------------------- | ---- | ---- | --------- |
| Lead Architect        |      |      |           |
| API Team Lead         |      |      |           |
| Documentation Manager |      |      |           |
| Integration Lead      |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                 |
|---------|----------------|-----------------|--------------------------------------------------------|
| 1.1.0   | [[2025-04-08]] | [[Wayne Smith]] | Fixed property/variable identifier inconsistency in variable assignments example; updated metadata to reflect current date |
| 1.0.0   | [[2025-04-06]] | [[Wayne Smith]] | Initial version of the RuleForge Naming Convention Guide |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->