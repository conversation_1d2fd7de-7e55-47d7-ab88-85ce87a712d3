# RuleForge Developer Reference Guide
**Version 1.0 | September 2025**

## Important Notice About This Guide

**This guide uses JavaScript/Node.js for example code, but RuleForge works with ANY programming language.**

The code examples are for illustration only. You should:
- Understand the API endpoints and data structures
- Implement the pattern in YOUR language
- Use YOUR existing frameworks and libraries
- Follow YOUR coding standards

Do NOT copy-paste these examples. They show the concept - your implementation will be different.

## What is RuleForge?

RuleForge is a Property Modification Framework that dynamically modifies transaction values based on business rules. Instead of hardcoding discounts and bonuses in your code, RuleForge applies them dynamically.

### The Simple Concept

**Without RuleForge:**
```
Customer buys bundle → Database says $50 → Charge $50
```

**With RuleForge:**
```
Customer buys bundle → Database says $50 → Ask RuleForge → RuleForge says $45 → Charge $45
```

### How the System Works

1. **Business Configuration (Not Your Job)**
   - Administrators register your system (entity) in RuleForge GUI
   - They define what properties you have
   - Campaign managers create rule sets in the GUI
   - These rule sets modify your properties

2. **Your Integration (Your Job)**
   - Send transaction properties to RuleForge
   - Receive modified values back
   - Apply modifications to your transaction
   - Report the outcome

You never see or write the rules. You just send data and apply the results.

## System Architecture

### Entities (Configured in GUI Only)

An entity is your system's definition in RuleForge. Administrators configure this through the GUI.

**Example Entity Structure:**
```
Entity: Your Telecom System
├── Context: BUNDLE_PURCHASE
│   ├── bundlePrice (mutable) - Can be modified by campaigns
│   ├── dataAmount (mutable) - Can be modified by campaigns  
│   ├── validityDays (mutable) - Can be modified by campaigns
│   ├── customerId (immutable) - For targeting rules
│   ├── customerSegment (immutable) - For targeting rules
│   └── channel (immutable) - For checking transaction against campaign criteria
│
└── Context: AIRTIME_PURCHASE
    ├── airtimeAmount (mutable) - Can be modified
    ├── bonusPercentage (mutable) - Can be modified
    ├── customerId (immutable) - For targeting
    └── channel (immutable) - For targeting
```

**Key Terms:**
- **Mutable Properties**: Values RuleForge can change (prices, bonuses, etc.)
- **Immutable Properties**: Used only for targeting (customer ID, location, etc.)
- **Context**: Type of transaction (BUNDLE_PURCHASE, AIRTIME_PURCHASE)

### Rule Sets (Created in GUI Only)

Campaign managers create rule sets through the GUI. You don't need to understand the rules.

**Example Rule (You don't implement this):**
```
IF customerSegment = "PREPAID" AND channel = "USSD"
THEN SET bundlePrice = bundlePrice * 0.9
```

## Prerequisites

Before integrating:

1. **Entity Registration** ✓ 
   - Your admin has registered your system in RuleForge GUI
   - Transaction contexts are defined
   - Properties are marked as mutable or immutable

2. **API Credentials** ✓
   - You have your API key
   - You have your entity ID

3. **Understanding** ✓
   - You know which properties to send
   - You understand which can be modified
   - You have fallback logic planned

## API Integration

### Core Flow

```
1. Get base values from your database
2. Call RuleForge /evaluate with all properties
3. Receive modifications (or empty if no campaigns or transaction does not qualify for modification)
4. Apply modifications (or use original values)
5. Process transaction
6. Report outcome to RuleForge
```

### Authentication

All requests require:
```
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

### Base URL
```
https://api.ruleforge.com/v1
```

## Implementation Examples

**Remember: These examples use JavaScript but your implementation can use ANY language**

### Basic RuleForge Client

```javascript
// Example in JavaScript - implement in YOUR language
const axios = require('axios');

class RuleForgeClient {
    constructor(apiKey, entityId) {
        this.apiKey = apiKey;
        this.entityId = entityId;
        this.baseUrl = 'https://api.ruleforge.com/v1';
    }
    
    async evaluate(contextId, properties) {
        const transactionId = 'TXN_' + Date.now();
        
        try {
            const response = await axios.post(
                `${this.baseUrl}/evaluate`,
                {
                    entityId: this.entityId,
                    contextId: contextId,
                    transactionId: transactionId,
                    timestamp: new Date().toISOString(),
                    transactionData: properties
                },
                {
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: 500  // Don't wait longer than 500ms
                }
            );
            
            return {
                transactionId,
                modifications: response.data.modifiedProperties || {}
            };
        } catch (error) {
            // CRITICAL: If RuleForge is down, return empty modifications
            // Your system must continue working
            console.error('RuleForge unavailable, using fallback');
            return { transactionId, modifications: {} };
        }
    }
    
    async reportOutcome(transactionId, contextId, status, finalValues) {
        // Report what happened - this is fire-and-forget
        try {
            await axios.post(
                `${this.baseUrl}/outcomes`,
                {
                    entityId: this.entityId,
                    contextId: contextId,
                    transactionId: transactionId,
                    timestamp: new Date().toISOString(),
                    status: status,  // 'COMPLETED' or 'FAILED'
                    transactionData: finalValues,
                    modificationStatus: {
                        modificationsApplied: Object.keys(finalValues),
                        modificationsRejected: []
                    }
                },
                {
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json'
                    },
                    timeout: 1000
                }
            );
        } catch (error) {
            // Don't let outcome reporting failure affect transaction
            console.error('Outcome reporting failed - transaction continues');
        }
    }
}
```

### Bundle Purchase Integration

**This shows the PATTERN - implement it in your language with your property names**

```javascript
async function processBundlePurchase(customerId, bundleId) {
    // Initialize client (do this once, not per transaction)
    const ruleforge = new RuleForgeClient('YOUR_API_KEY', 'YOUR_ENTITY_ID');
    
    // 1. GET YOUR DATA (your existing code)
    const bundle = await getFromDatabase(bundleId);
    const customer = await getCustomer(customerId);
    
    // 2. BUILD PROPERTIES OBJECT
    // Include ALL properties - both mutable and immutable
    const properties = {
        // Mutable (can be changed by campaigns)
        bundlePrice: bundle.price,        // e.g., 50.00
        dataAmount: bundle.dataMb,        // e.g., 10240 (10GB)
        validityDays: bundle.validity,    // e.g., 30
        
        // Immutable (for targeting rules)
        customerId: customerId,
        customerSegment: customer.segment,  // e.g., "PREPAID"
        bundleId: bundleId,
        channel: 'USSD'
    };
    
    // 3. ASK RULEFORGE
    const { transactionId, modifications } = await ruleforge.evaluate(
        'BUNDLE_PURCHASE',
        properties
    );
    
    // 4. APPLY MODIFICATIONS (with fallback)
    // CRITICAL: Always fallback to original if no modification
    const finalPrice = modifications.bundlePrice?.modified || bundle.price;
    const finalData = modifications.dataAmount?.modified || bundle.dataMb;
    const finalValidity = modifications.validityDays?.modified || bundle.validity;
    
    // Log what changed (optional but recommended)
    if (modifications.bundlePrice) {
        console.log(`Price changed: ${bundle.price} → ${finalPrice}`);
    }
    
    // 5. PROCESS WITH FINAL VALUES
    // This is your existing code, just using modified values
    const result = await chargeCustomer(customerId, finalPrice);
    await provisionBundle(customerId, finalData, finalValidity);
    
    // 6. REPORT OUTCOME
    await ruleforge.reportOutcome(
        transactionId,
        'BUNDLE_PURCHASE',
        result.success ? 'COMPLETED' : 'FAILED',
        {
            bundlePrice: finalPrice,
            dataAmount: finalData,
            validityDays: finalValidity,
            customerId: customerId
        }
    );
    
    return result;
}
```

### Airtime Purchase Integration

```javascript
async function processAirtimePurchase(customerId, amount) {
    const ruleforge = new RuleForgeClient('YOUR_API_KEY', 'YOUR_ENTITY_ID');
    
    // 1. GET YOUR DATA
    const customer = await getCustomer(customerId);
    
    // 2. BUILD PROPERTIES
    const properties = {
        // Mutable
        airtimeAmount: amount,
        bonusPercentage: 0,  // Default: no bonus
        
        // Immutable
        customerId: customerId,
        customerSegment: customer.segment,
        channel: 'USSD'
    };
    
    // 3. EVALUATE
    const { transactionId, modifications } = await ruleforge.evaluate(
        'AIRTIME_PURCHASE',
        properties
    );
    
    // 4. APPLY
    let finalAmount = amount;
    const bonusPercent = modifications.bonusPercentage?.modified || 0;
    
    if (bonusPercent > 0) {
        const bonusAmount = (amount * bonusPercent) / 100;
        finalAmount = amount + bonusAmount;
        console.log(`Bonus applied: ${bonusPercent}% = +${bonusAmount}`);
    }
    
    // 5. PROCESS
    const result = await creditAirtime(customerId, finalAmount);
    
    // 6. REPORT
    await ruleforge.reportOutcome(
        transactionId,
        'AIRTIME_PURCHASE',
        result.success ? 'COMPLETED' : 'FAILED',
        {
            airtimeAmount: finalAmount,
            bonusPercentage: bonusPercent,
            customerId: customerId
        }
    );
    
    return result;
}
```

## API Endpoints

### POST /evaluate

**Purpose:** Get property modifications for a transaction

**Request Structure:**
```json
{
    "entityId": "550e8400-e29b-41d4-a716-446655440000",
    "contextId": "BUNDLE_PURCHASE",
    "transactionId": "TXN_123456",
    "timestamp": "2025-09-24T10:30:00Z",
    "transactionData": {
        "bundlePrice": 50.00,
        "dataAmount": 10240,
        "validityDays": 30,
        "customerId": "CUST123",
        "customerSegment": "PREPAID",
        "channel": "USSD"
    }
}
```

**Response Structure:**
```json
{
    "transactionId": "TXN_123456",
    "modifiedProperties": {
        "bundlePrice": {
            "original": 50.00,
            "modified": 45.00,
            "ruleSetId": "CAMPAIGN_123",
            "ruleId": "DISCOUNT_RULE"
        }
    }
}
```

**Empty Response (no campaigns):**
```json
{
    "transactionId": "TXN_123456",
    "modifiedProperties": {}
}
```

### POST /outcomes

**Purpose:** Report transaction completion

**Request Structure:**
```json
{
    "entityId": "550e8400-e29b-41d4-a716-446655440000",
    "contextId": "BUNDLE_PURCHASE",
    "transactionId": "TXN_123456",
    "timestamp": "2025-09-24T10:30:05Z",
    "status": "COMPLETED",
    "transactionData": {
        "bundlePrice": 45.00,
        "dataAmount": 10240,
        "validityDays": 30,
        "customerId": "CUST123"
    },
    "modificationStatus": {
        "modificationsApplied": ["bundlePrice"],
        "modificationsRejected": []
    }
}
```

## Critical Implementation Rules

### 1. Always Send All Properties

```javascript
// ❌ WRONG - Missing properties
const properties = {
    bundlePrice: 50.00
};

// ✅ CORRECT - All properties included
const properties = {
    bundlePrice: 50.00,        // Mutable
    dataAmount: 10240,         // Mutable
    validityDays: 30,          // Mutable
    customerId: 'CUST123',     // Immutable
    customerSegment: 'PREPAID', // Immutable
    channel: 'USSD'            // Immutable
};
```

### 2. Always Use Fallback

```javascript
// ❌ WRONG - Will crash if no modifications
const finalPrice = modifications.bundlePrice.modified;

// ✅ CORRECT - Falls back to original
const finalPrice = modifications.bundlePrice?.modified || originalPrice;
```

### 3. Never Wait Too Long

```javascript
// ❌ WRONG - 30 second timeout
timeout: 30000

// ✅ CORRECT - 500ms timeout
timeout: 500
```

### 4. Validate Modifications

```javascript
// Simple validation before applying
if (modifiedPrice < 0 || modifiedPrice > originalPrice * 2) {
    // Suspicious modification, use original
    modifiedPrice = originalPrice;
}
```

## Error Handling

### HTTP Status Codes

| Code | Meaning | Action |
|------|---------|--------|
| 200 | Success | Use modifications |
| 400 | Bad request | Check your request format |
| 401 | Unauthorized | Check API key |
| 422 | Invalid data | Check property names and types |
| 429 | Rate limited | Implement retry with backoff |
| 500 | Server error | Use fallback values |

### Common Issues

| Problem | Solution |
|---------|----------|
| No modifications returned | Normal - no campaigns active |
| Connection timeout | Check network, use fallback |
| Wrong property modified | Ensure property names match exactly |
| Modifications rejected | Check with admin about constraints |

## Testing Your Integration

### Phase 1: Basic Testing (No Campaigns)
1. Send transactions to RuleForge
2. Verify you get empty modifications
3. Ensure transactions complete with original values

### Phase 2: Failure Testing
1. Use wrong API key - should fallback
2. Disconnect network - should fallback
3. Send invalid data - should handle error

### Phase 3: Campaign Testing
1. Ask campaign manager to create test campaign
2. Send test transactions
3. Verify modifications apply correctly

## Implementation Checklist

- [ ] Understand which properties are mutable vs immutable
- [ ] Have API key and entity ID
- [ ] Implement /evaluate call with ALL properties
- [ ] Implement fallback for when RuleForge unavailable
- [ ] Set timeout to 500ms
- [ ] Implement /outcomes reporting
- [ ] Test without campaigns
- [ ] Test with RuleForge unavailable
- [ ] Coordinate campaign testing

## Remember

1. **You don't write rules** - They're created in the GUI
2. **You must handle failures** - RuleForge being down cannot break your system
3. **Send all properties** - Both mutable and immutable
4. **Use your own code** - These examples are just to show the concept

## Support

- **Technical Issues:** <EMAIL>
- **Campaign Questions:** Contact your Campaign Manager
- **Entity Setup:** Contact your RuleForge Administrator

---
**© 2025 Concurrent Systems. All Rights Reserved.**