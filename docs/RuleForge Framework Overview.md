---
title: RuleForge Framework Overview
classification: Internal
created: 2025-03-13
updated: 2025-04-15
authors:
  - "[[<PERSON>]]"
version: 1.1.0
next-review: 2025-06-13
category: "[[Specifications]]"
tags:
  - specification
  - overview
  - reference
topics:
  - "[[Property Modification Framework]]"
  - "[[System Architecture]]"
  - "[[Integration]]"
  - "[[RuleForge]]"
---

# RuleForge Framework Overview

## Document Specification

### Purpose
This document provides a comprehensive overview of the RuleForge framework, its components, and how they work together. It serves as the primary entry point for understanding the RuleForge ecosystem and its integration patterns.

### Scope
This overview covers the core concepts, architecture, key components, and integration patterns of RuleForge. It explains how entities integrate with RuleForge, how rule sets modify properties, and the relationships between the various framework components.

This document does not cover:
- Detailed API specifications (covered in separate API documentation)
- Internal implementation details of individual components
- User interface design of the Rules Management GUI

### Target Audience
- Entity Developers
- System Architects
- Product Managers
- Rule Set Managers
- System Integrators
- Technical Decision Makers

## Table of Contents

- [[#1. Introduction to RuleForge]]
- [[#2. System Architecture]]
- [[#3. Two-Phase Rule Execution Model]]
- [[#4. Integration Patterns]]
- [[#5. Framework Documentation Structure]]
- [[#6. Getting Started]]
- [[#7. Technical Constraints]]

## 1. Introduction to RuleForge

### 1.1 What is RuleForge?
RuleForge is a real-time, high-performance rules engine that enables transaction property modifications across various integrated entities. It allows business users to create targeted rule sets that dynamically modify transaction properties based on configurable conditions, without requiring additional development work on entities once their initial RuleForge integration is complete.

### 1.2 Core Value Proposition
- **Dynamic Property Modifications**: Modify transaction properties in real-time based on rule set rules
- **Centralized Rule Management**: Create and manage rules across multiple entities from a single interface
- **No-Code Rule Configuration**: Design complex rules without writing code
- **High Performance**: Process transaction evaluations in milliseconds
- **Flexible Integration**: Standard patterns for integrating any entity type
- **Real-Time Analytics**: Track rule set performance and impact across entities

### 1.3 Key Use Cases
1. **Personalized Pricing**: Adjust prices based on customer segments, time of day, location, etc.
2. **Bundle Configuration**: Modify bundle eligibility or discount levels based on customer attributes and transaction context
3. **Targeted Promotions**: Apply special offers to specific customer segments
4. **Loyalty Incentives**: Provide enhanced benefits to high-value customers
5. **Contextual Modifications**: Adapt transaction properties based on real-time context

## 2. System Architecture

### 2.1 High-Level Architecture
```
┌────────────────────┐     ┌──────────────────────┐     ┌────────────────────┐
│                    │     │                      │     │                    │
│  Integrated        │     │     RuleForge        │     │    Rule Set        │
│  Entities          │◄────┤                      │◄────┤    Management      │
│  (SmartShop, etc.) │     │                      │     │    Interface       │
│                    │     │                      │     │                    │
└─────────┬──────────┘     └──────────┬───────────┘     └────────────────────┘
          │                           │
          │                           │
          ▼                           ▼
┌────────────────────┐     ┌──────────────────────┐
│                    │     │                      │
│  Transaction       │     │     Analytics &      │
│  Processing        │     │     Reporting        │
│                    │     │                      │
└────────────────────┘     └──────────────────────┘
```

### 2.2 Core Components

#### 2.2.1 RuleForge Evaluation Engine
High-performance processing core that evaluates transaction contexts against rules in real-time. Optimized for low-latency, high-throughput transaction processing with minimal overhead. Supports two-phase rule execution with separate evaluation and outcome phases.

#### 2.2.2 Entity Registry
Manages entity registration, property definitions, and transaction context schemas. Maintains all entity-related metadata including property constraints, mutability flags, and integration settings.

#### 2.2.3 Rule Registry
Store for individual rule definitions including conditions, actions, and property modifications. Provides versioning and reusability of rules across multiple rule sets.

#### 2.2.4 Rule Set Registry
Manages rule set configurations, including scheduling, activation status, priority, and rule assignments. Controls the lifecycle of rule sets from creation through testing, activation, and retirement. Each rule set is associated with a specific entity and transaction context.

#### 2.2.5 Rule Set Management GUI
Interface for business users to create, test, deploy, and monitor rule sets without requiring technical knowledge or coding skills.

#### 2.2.6 List Manager
Service for creating, importing, and managing lists of values (e.g., MSISDNs, product codes, location codes) that can be referenced by rule conditions for membership testing. Designed to efficiently handle very large datasets.

#### 2.2.7 Target Communication System
Manages bidirectional communications between rule sets and end users. Handles outbound notifications (SMS, email, push) triggered by rule sets as well as inbound communications such as opt-in/opt-out requests, responses to prompts, and user-initiated interactions. Designed to support multiple channels and communication patterns while maintaining compliance with messaging regulations.

#### 2.2.8 Analytics Service
Captures and analyzes transaction data, rule set performance, and modification impacts to provide insights on effectiveness and ROI.

### 2.3 System Component Diagram
```
┌─────────────────────────────────────────────────────────────────────────────┐
│                          RuleForge (Current Monolith)                       │
│                                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌───────────────────┐   │
│  │             │  │             │  │             │  │                   │   │
│  │   Entity    │  │    Rule     │  │  Rule Set   │  │    Evaluation     │   │
│  │  Registry   │  │  Registry   │  │  Registry   │  │      Engine       │   │
│  │             │  │             │  │             │  │                   │   │
│  └─────────────┘  └─────────────┘  └─────────────┘  └───────────────────┘   │
│                                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────────────────────┐  │
│  │             │  │             │  │                                     │  │
│  │    List     │  │   Target    │  │             Analytics               │  │
│  │  Manager    │  │Communication│  │             Service                 │  │
│  │             │  │   System    │  │                                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────────────────────┘  │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
          ▲                                              │
          │                                              │
          │                                              ▼
┌─────────────────────┐                       ┌─────────────────────────┐
│                     │                       │                         │
│    Rule Set         │                       │      Integrated         │
│    Management       │                       │      Entities           │
│    GUI              │                       │                         │
│                     │                       │                         │
└─────────────────────┘                       └─────────────────────────┘
```

### 2.4 Future Microservices Architecture
While currently implemented as a monolith, RuleForge's component-based design facilitates future evolution toward a microservices architecture. Each core component is designed with clear boundaries and responsibilities to enable independent deployment, scaling, and maintenance. The separation between the Rule Registry and Rule Set Registry specifically enables more efficient scaling of the rule management capabilities.

## 3. Two-Phase Rule Execution Model

The RuleForge system implements a two-phase rule execution model that aligns with the transaction lifecycle:

### 3.1 Evaluation Phase
- Triggered by the `/evaluate` endpoint
- Executes rules from the `evaluationRules` collection
- Focus on property modification before transaction completion
- Typical use cases: pricing adjustments, discounts, eligibility determination
- Real-time execution with low latency

### 3.2 Outcome Phase
- Triggered by the `/outcomes` endpoint
- Executes rules from the `outcomeRules` collection
- Focus on post-transaction processing
- Typical use cases: loyalty point accrual, customer status updates, follow-up actions
- Asynchronous execution that doesn't block transaction completion

This separation provides clear boundaries between modification logic (applied during evaluation) and outcome processing logic (applied after knowing the transaction result).

## 4. Integration Patterns

### 4.1 Integration Flow

#### 4.1.1 Entity Registration Process
```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ Define      │     │ Register    │     │ Validate    │     │ Confirm     │
│ Properties  │────►│ Entity      │────►│ Registration│────►│ Integration │
│ & Contexts  │     │ with RF     │     │ Details     │     │ Success     │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
```

#### 4.1.2 Transaction Evaluation Process
```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ Build       │     │ Send to     │     │ Apply       │     │ Report      │
│ Transaction │────►│ RuleForge   │────►│ Modified    │────►│ Transaction │
│ Context     │     │ for Eval    │     │ Properties  │     │ Outcome     │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
```

#### 4.1.3 Rule Set Creation Process
```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ Define      │     │ Configure   │     │ Test        │     │ Activate    │
│ Rule Set    │────►│ Rules &     │────►│ Rule Set    │────►│ Rule Set    │
│ Objectives  │     │ Conditions  │     │ Rules       │     │ for Users   │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
```

### 4.2 Entity Registration Pattern
Entities must register with RuleForge before they can leverage property modifications. Registration defines the entity, its transaction contexts, and properties available for modification.

**Key Steps:**
1. Analyze entity system to identify transaction contexts
2. Classify properties as mutable or immutable
3. Define constraints for mutable properties
4. Register entity with RuleForge

### 4.3 Transaction Evaluation Pattern
Entities send transaction contexts to RuleForge for evaluation against applicable rule set rules, then apply any resulting property modifications.

**Key Steps:**
1. Build transaction context with relevant properties
2. Send context to RuleForge for evaluation
3. Process response and apply modified properties
4. Report transaction outcome back to RuleForge

### 4.4 Standard Properties Pattern
All transactions have access to a set of standard properties regardless of entity configuration. These properties can be referenced in rules to make decisions or perform modifications.

**Key Standard Properties:**
1. Transaction identification: `transactionId`, `entityId`, `contextId`
2. Timestamps: `timestamp`
3. Outcome-specific (for outcome rules): `status`, `modificationsApplied`, `modificationsRejected`

### 4.5 List Management Pattern
Lists provide an efficient way to check if a value belongs to a predefined set (e.g., eligible MSISDNs, product codes, regions). RuleForge provides dedicated APIs for creating and managing lists that can be referenced in rule conditions.

**Key List Operations:**
1. Create lists through the List Management API
2. Reference lists in rule conditions using the `IN` and `NOT_IN` operators
3. Update lists as needed to reflect changes in business requirements

### 4.6 Error Handling Pattern
Entities must implement proper error handling to ensure system stability even when RuleForge is unavailable.

**Key Strategies:**
1. Implement timeouts for all RuleForge calls
2. Define fallback behavior (e.g., use original property values)
3. Implement circuit breaker pattern for repeated failures
4. Queue outcome notifications for later delivery if needed

## 5. Framework Documentation Structure

The RuleForge framework is documented through a series of interconnected guides that provide progressive detail:

### 5.1 Core Framework Documentation
1. **RuleForge Framework Overview** (this document)
   - Comprehensive overview of the entire framework
   - Explains how all pieces fit together
   - Entry point for all developers

2. **Entity Registration Guide**
   - How entities register with RuleForge
   - Property definition and constraints
   - Complete technical reference for registration

3. **Property Modification Guide**
   - How the property modification framework works
   - Transaction context and evaluation
   - Handling modifications and reporting outcomes

### 5.2 Integration Documentation
4. **Entity Developer Integration Guide**
   - Step-by-step guide for entity developers
   - Integration patterns and best practices
   - Error handling and resilience strategies
   - Performance optimization guidance

### 5.3 Entity-Specific Documentation
5. **Entity-Specific Integration Specifications**
   - Entity-specific implementation details
   - Property specifications for each entity
   - Entity-specific business rules and constraints
   - Integration with other entity components

### 5.4 API References
6. **RuleForge Management API**
   - Rule set management endpoints
   - System configuration retrieval
   - Authentication and authorization

7. **Entity Integration API**
   - Entity registration endpoints
   - Integration validation
   - Entity management

8. **Transaction Evaluation API**
   - Transaction evaluation endpoints
   - Transaction outcome reporting
   - API key management

9. **List Management API**
   - List creation and management
   - File upload for bulk lists
   - Element management

## 6. Getting Started

### 6.1 For Entity Developers
1. Start with this overview to understand the RuleForge framework
2. Read the Entity Registration Guide to learn how to register your entity
3. Study the Property Modification Guide to understand how property modifications work
4. Follow the Entity Developer Integration Guide for step-by-step integration instructions
5. Implement your entity-specific integration based on these guidelines

### 6.2 For Rule Set Managers
1. Start with this overview to understand the RuleForge framework
2. Familiarize yourself with available entities and their mutable properties
3. Learn how to create and test rule sets in the Rule Set Management System
4. Monitor rule set performance through the Analytics Platform

## 7. Technical Constraints

### 7.1 Performance Requirements
- Transaction evaluation must complete within 20ms
- Entity registration can take up to 2 seconds
- Each production instance of RuleForge must handle at least 1000 transactions per second under normal operating conditions

### 7.2 Availability Requirements
- RuleForge targets 99.99% availability
- Entities must implement fallback strategies for RuleForge unavailability
- Maximum recommended timeout for transaction evaluation: 500ms

### 7.3 Security Requirements
- All API calls must use secure authentication
- API keys are associated with organizations, not individual entities
- Sensitive data must not be included in transaction contexts
- Entity credentials must be properly secured

## Related Documents
- [[Entity Registration Guide]]
- [[Property Modification Guide]]
- [[Entity Developer Integration Guide]]
- [[RuleForge API Reference]]
- [[Rule Set Management Guide]]
- [[Analytics Platform Documentation]]
- [[Entity Integration API]]
- [[Transaction Evaluation API]]
- [[List Management API]]
- [[Standard Transaction Properties Schema]]

## Approvals
| Role/Department | Name | Date | Signature |
|-----------------|------|------|-----------|
| Technical Architect |      |      |           |
| API Team Lead  |      |      |           |
| Product Manager |      |      |           |

## Changelog

| Version | Date       | Author          | Changes                                   |
| ------- | ---------- | --------------- | ----------------------------------------- |
| 1.1.0   | [[2025-04-15]] | [[Wayne Smith]] | Updated terminology to consistently use "rule set" instead of "campaign"; added two-phase rule execution model section; added standard properties pattern; added list management pattern; added references to new API docs and schemas; aligned with current component naming; updated formatting to match Root Document Template v1.1.0. |
| 1.0.1   | [[2025-03-20]] | [[Wayne Smith]] | Clarified integration requirements, refined use cases, and updated performance specifications. |
| 1.0.0   | [[2025-03-13]] | [[Wayne Smith]] | Initial version of the overview document. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->
