{"rulesetId": "stock-transfer-ruleset-001", "version": "1.0.0", "name": "Stock Transfer Rules", "description": "Automated stock transfers based on sales performance", "status": "active", "transactionParameters": [{"propertyId": "salesAmount", "type": "number", "mutable": false, "description": "Total sales amount for the transaction"}, {"propertyId": "sellerMsisdn", "type": "string", "mutable": false, "description": "MSISDN of the seller"}, {"propertyId": "productType", "type": "string", "mutable": false, "description": "Type of product sold"}, {"propertyId": "regionCode", "type": "string", "mutable": false, "description": "Region code where sale occurred"}], "localVariables": [{"variableId": "commissionRate", "type": "number", "defaultValue": 0.05, "description": "Commission rate for sales"}, {"variableId": "bonusThreshold", "type": "number", "defaultValue": 1000, "description": "Sales threshold for bonus eligibility"}], "persistentVariables": [{"variableId": "totalSalesThisMonth", "type": "number", "defaultValue": 0, "description": "Cumulative sales for current month"}, {"variableId": "transferCount", "type": "number", "defaultValue": 0, "description": "Number of transfers executed"}], "evaluationRules": [{"ruleId": "commission-transfer-rule", "priority": 1, "name": "Sales Commission Transfer", "description": "Transfer commission to seller based on sales amount", "condition": {"conditionTypeId": "COMPARISON", "operator": ">", "parameters": {"leftOperand": "{salesAmount}", "rightOperand": 0}}, "variableAssignments": [{"variableId": "commissionAmount", "assignmentTypeId": "SET", "value": {"functionId": "multiply", "args": ["{salesAmount}", "{commissionRate}"]}}, {"variableId": "totalSalesThisMonth", "assignmentTypeId": "ADD", "value": "{salesAmount}"}, {"variableId": "transferCount", "assignmentTypeId": "ADD", "value": 1}], "apiCalls": [{"name": "commissionTransfer", "type": "stockTransfer", "apiId": "crediverse-stock-transfer", "rulesetId": "stock-transfer-ruleset-001", "accountRef": "primary", "parameters": [{"name": "fromA<PERSON>unt", "value": "company-main-account"}, {"name": "toAccount", "value": "{sellerMsisdn}"}, {"name": "amount", "value": "{commissionAmount}"}, {"name": "stockType", "value": "airtime"}]}], "actions": [{"type": "SMS", "parameters": {"recipient": "{sellerMsisdn}", "message": "Commission of {commissionAmount} transferred for sales of {salesAmount}. Total monthly sales: {totalSalesThisMonth}"}}]}, {"ruleId": "bonus-transfer-rule", "priority": 2, "name": "High Sales Bonus Transfer", "description": "Additional bonus for high-value sales", "condition": {"conditionTypeId": "LOGICAL", "operator": "AND", "parameters": {"conditions": [{"conditionTypeId": "COMPARISON", "operator": ">=", "parameters": {"leftOperand": "{salesAmount}", "rightOperand": "{bonusThreshold}"}}, {"conditionTypeId": "COMPARISON", "operator": "IN", "parameters": {"leftOperand": "{productType}", "rightOperand": ["premium", "enterprise", "bulk"]}}]}}, "variableAssignments": [{"variableId": "bonusAmount", "assignmentTypeId": "SET", "value": {"functionId": "multiply", "args": ["{salesAmount}", "0.02"]}}], "apiCalls": [{"name": "bonusTransfer", "type": "stockTransfer", "apiId": "crediverse-stock-transfer", "rulesetId": "stock-transfer-ruleset-001", "accountRef": "bonus", "parameters": [{"name": "fromA<PERSON>unt", "value": "company-bonus-account"}, {"name": "toAccount", "value": "{sellerMsisdn}"}, {"name": "amount", "value": "{bonusAmount}"}, {"name": "stockType", "value": "data"}]}], "actions": [{"type": "SMS", "parameters": {"recipient": "{sellerMsisdn}", "message": "Congratulations! Bonus of {bonusAmount} data transferred for premium sale of {salesAmount}!"}}]}, {"ruleId": "regional-incentive-rule", "priority": 3, "name": "Regional Sales Incentive", "description": "Special incentive for sales in specific regions", "condition": {"conditionTypeId": "LOGICAL", "operator": "AND", "parameters": {"conditions": [{"conditionTypeId": "COMPARISON", "operator": "IN", "parameters": {"leftOperand": "{regionCode}", "rightOperand": {"listId": "high-priority-regions"}}}, {"conditionTypeId": "COMPARISON", "operator": ">", "parameters": {"leftOperand": "{salesAmount}", "rightOperand": 500}}]}}, "variableAssignments": [{"variableId": "incentiveAmount", "assignmentTypeId": "SET", "value": 50}], "apiCalls": [{"name": "regionalIncentive", "type": "stockTransfer", "apiId": "crediverse-stock-transfer", "rulesetId": "stock-transfer-ruleset-001", "accountRef": "primary", "parameters": [{"name": "fromA<PERSON>unt", "value": "regional-incentive-account"}, {"name": "toAccount", "value": "{sellerMsisdn}"}, {"name": "amount", "value": "{incentiveAmount}"}, {"name": "stockType", "value": "airtime"}]}], "actions": [{"type": "SMS", "parameters": {"recipient": "{sellerMsisdn}", "message": "Regional incentive of {incentiveAmount} airtime awarded for sale in {regionCode}!"}}]}], "metadata": {"createdBy": "system-admin", "createdAt": "2025-01-07T10:00:00Z", "lastModified": "2025-01-07T10:00:00Z", "tags": ["stock-transfer", "commission", "sales", "automation"], "category": "financial-operations"}}