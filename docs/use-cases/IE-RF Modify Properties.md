---
title: "IE-RF Modify Properties"
classification: Internal
created: 2025-03-12
updated: 2025-03-12
authors:
  - "[[<PERSON>]]"
version: 1.0.0
next-review: 2025-06-12
tags:
  - use-case
  - ruleforge
  - property-modification
  - integration
---

# IE-RF Modify Properties

## Document Specification

### Purpose
This document describes the "IE-RF Modify Properties" use case, detailing the standardized interaction between Integrated Entities (IEs) and the RuleForge Campaign Rules Engine for property modification during transaction processing.

### Scope
This use case covers the transaction evaluation request, property modification determination, and outcome notification processes. It does not cover the entity and property registration process, which is covered in use case IE-RF-REG-001 (Integrated Entity Registration).

### Target Audience
- Product Managers
- Developers implementing entity integrations
- Campaign Managers
- QA Engineers
- Mobile Operators and other system integrators

## Use Case Details

### Overview
| Item                       | Description |
|----------------------------|-------------|
| Use Case ID                | RF-PMF-001 |
| Use Case Name              | IE-RF Modify Properties |
| Created By                 | <PERSON> |
| Last Updated               | 2025-03-12 |
| Version                    | 1.0.0 |

### Specifics
| Item                       | Description |
|----------------------------|-------------|
| Goal in Context            | An Integrated Entity exchanges transaction data with RuleForge to receive and apply standardized property modifications according to configured campaign rules |
| Scope                      | RuleForge Property Modification Framework |
| Level                      | System-level |
| Primary Actor              | Integrated Entity (IE) |
| Secondary Actors           | RuleForge Campaign Rules Engine, Campaign Manager |
| Stakeholders and Interests | - Integrated Entity: Wants consistent property modification without custom actions<br>- Campaign Manager: Wants simplified campaign creation without technical expressions<br>- Developer: Wants standardized integration patterns<br>- Operator: Wants faster campaign deployment and better analytics |
| Preconditions              | - Integrated Entity is registered in RuleForge (IE-RF-REG-001)<br>- Entity properties are registered with mutability flags and constraints<br>- Campaigns are configured in RuleForge with property modification rules |
| Minimal Guarantee          | - Transaction proceeds with original property values if RuleForge is unavailable<br>- All modifications are logged for audit purposes |
| Success Guarantee          | - Transaction properties are modified according to applicable campaign rules<br>- All modifications are within defined constraints<br>- Entity applies modifications correctly<br>- Transaction outcome is recorded for analytics |
| Triggers                   | - Entity initiates a transaction requiring property evaluation |
| Frequency of Use           | Very High - Multiple times per second across all integrated entities |

## Main Success Scenario
1. Integrated Entity initiates a transaction that requires property evaluation
2. Integrated Entity constructs a Transaction Context containing current property values following the standardized JSON structure
3. Integrated Entity calls the Transaction Evaluation API endpoint `/api/v1/evaluate` with the context
4. RuleForge validates the request structure and entity permissions
5. RuleForge evaluates the Transaction Context against applicable campaign rules
6. RuleForge determines property modifications based on matching rules
7. RuleForge validates all modifications against the registered property constraints
8. RuleForge returns a standardized response with modified properties in a "modified_properties" object containing both original and new values
9. Integrated Entity extracts the modified properties from the response
10. Integrated Entity applies the modifications according to its business logic
11. Integrated Entity completes the transaction using the modified property values
12. Integrated Entity constructs a Transaction Outcome Notification with complete transaction data
13. Integrated Entity sends the outcome notification to RuleForge
14. RuleForge processes the notification for analytics and persistent variable updates
15. RuleForge acknowledges the outcome notification
16. Transaction processing is completed with all modifications and analytics properly recorded

## Extension Points

### Transaction Context Building - After Step 2
- **Purpose**: Enables different approaches to context building
- **Implemented Extensions**: Standard JSON structure for transaction context
- **Planned Extensions**:
  - Context enrichment from external sources
  - Contextual variable inclusion
  - Historical context incorporation

### Modification Determination - After Step 6
- **Purpose**: Allows different strategies for determining property modifications
- **Implemented Extensions**: Campaign rule-based modification
- **Planned Extensions**:
  - Machine learning-driven modification suggestions
  - A/B testing automation
  - Personalization algorithms

### Modification Validation - After Step 7
- **Purpose**: Provides mechanisms to validate modification compliance
- **Implemented Extensions**: Property constraint validation
- **Planned Extensions**:
  - Business rule validation
  - Cross-property validation
  - Regulatory compliance checks

### Modification Application - After Step 10
- **Purpose**: Provides flexibility in how modifications are applied
- **Implemented Extensions**: Direct property value application
- **Planned Extensions**:
  - Staged application
  - Conditional application
  - User confirmation workflows

### Outcome Recording - After Step 14
- **Purpose**: Enables various approaches to recording transaction outcomes
- **Implemented Extensions**: Basic outcome recording
- **Planned Extensions**:
  - Advanced analytics processing
  - Real-time dashboarding
  - Modification effectiveness reporting

## Extensions / Alternate Flows

### E1: Request Validation Error - Extension from Step 4
1. RuleForge detects invalid request format or missing required fields
2. RuleForge returns a validation error response with specific details
3. Integrated Entity logs the error and may retry with corrected format
4. If retry is not possible, transaction proceeds with original property values

### E2: Property Constraint Violation - Extension from Step 7
1. RuleForge detects that a modification would violate property constraints
2. RuleForge handles the violation based on constraint type:
   a. If property has min/max constraint, value is adjusted to boundary
   b. If property has pattern constraint, modification is rejected
   c. If property has enumeration constraint, nearest valid value is used
3. RuleForge logs constraint adjustments in the response
4. Flow continues with Step 8 using adjusted modifications

### E3: RuleForge Unavailable - Extension from Step 3
1. Integrated Entity cannot reach RuleForge or receives a timeout
2. Entity implements fallback strategy according to configuration:
   a. Proceed with original property values
   b. Use cached default modifications if available
3. Entity logs the failed RuleForge interaction for reporting
4. Transaction proceeds with unmodified properties
5. Entity may still send outcome notification once RuleForge is available again

### E4: No Applicable Rules - Extension from Step 5
1. RuleForge evaluates Transaction Context and finds no matching rules
2. RuleForge returns empty "modified_properties" object
3. Integrated Entity proceeds with original property values
4. Flow continues at Step 11 with unmodified properties

### E5: Partial Modification Application - Extension from Step 10
1. Integrated Entity's business logic determines some modifications cannot be applied
2. Entity applies only feasible modifications and tracks rejected ones
3. Entity includes application details in outcome notification
4. Flow continues at Step 11 with partially modified properties

### E6: Transaction Failure - Extension from Step 11
1. Transaction processing fails after modifications are applied
2. Integrated Entity includes failure details in outcome notification
3. RuleForge records failure statistics but does not update persistent variables
4. Flow continues at Step 12 with failure status in the notification

### E7: Notification Failure - Extension from Step 13
1. Outcome notification to RuleForge fails due to communication issue
2. Integrated Entity queues the notification for later retry
3. Entity continues with its operations
4. Notification is reattempted during system maintenance window
5. If notification permanently fails, discrepancy is flagged for reconciliation

### E8: Multiple Property Modifications - Extension from Step 6
1. Multiple campaign rules match the transaction context, affecting the same property
2. RuleForge applies prioritization based on rule priority settings
3. Only the highest priority rule's modification is applied to each property
4. RuleForge includes applied rule information in the response
5. Flow continues with prioritized modifications

## Special Requirements
- Transaction Evaluation API must respond in under 15ms per call
- Property modification framework must support all data types in Entity Registration specification
- System must handle at least 1000 transactions per second at peak load
- All property modifications must be traceable to specific campaigns and rules
- Modification response structure must be backward compatible with existing action response structures

## Technology and Data Variations List
- REST API for transaction evaluation
- JSON format for all requests and responses
- Support for all data types defined in Entity Registration specification
- Various constraint types (min/max, pattern, enumeration)
- Multiple timestamp formats and timezone handling
- Various entity systems with different technical capabilities

## User Experience Considerations
- Campaign managers need clear visibility of which properties are available for modification
- Property constraints must be visually indicated in the campaign configuration UI
- Preview functionality should show expected modifications for sample transactions
- Error messages must be clear and actionable
- Integration documentation must be comprehensive and include examples
- Testing tools must allow simulation of transactions with modification preview

## Component Mapping
| Step | Component(s) Involved | Notes |
|------|----------------------|-------|
| 1-2 | IE Transaction System | Entity prepares transaction context |
| 3-8 | IE System, RuleForge Evaluation API | Real-time synchronous evaluation call |
| 9-11 | IE Transaction System | Entity applies modifications |
| 12-15 | IE System, RuleForge Notification API | May be synchronous or asynchronous |
| 16 | IE Transaction System | Transaction completion |

## API Specifications

### 1. Transaction Context Evaluation API

**Endpoint:** `POST /api/v1/evaluate`

**Request:**
```json
{
  "entityId": "ENTITY_ID",
  "transactionContextId": "CONTEXT_ID",
  "transactionId": "TRANSACTION123",
  "timestamp": "2025-03-12T14:30:00Z",
  "transactionData": {
    "propertyName1": "value1",
    "propertyName2": 50,
    "propertyName3": true
  }
}
```

**Response:**
```json
{
  "transactionId": "TRANSACTION123",
  "modified_properties": {
    "propertyName2": {
      "original": 50,
      "modified": 45,
      "campaignId": "CAMPAIGN_ID",
      "ruleId": "RULE_ID"
    }
  }
}
```

### 2. Transaction Outcome Notification API

**Endpoint:** `POST /api/v1/outcomes`

**Request:**
```json
{
  "entityId": "ENTITY_ID",
  "transactionContextId": "CONTEXT_ID",
  "transactionId": "TRANSACTION123",
  "timestamp": "2025-03-12T14:30:05Z",
  "status": "COMPLETED",
  "transactionData": {
    "propertyName1": "value1",
    "propertyName2": 45,
    "propertyName3": true
  },
  "modificationStatus": {
    "applied": ["propertyName2"],
    "rejected": []
  },
  "additionalData": {
    "metric1": "value1",
    "metric2": 123
  }
}
```

**Response:**
```json
{
  "transactionId": "TRANSACTION123",
  "recorded": true
}
```

## Related Information
- Priority: High
- Performance Target: Under 15ms for evaluation calls
- Superordinate Use Case: Entity Integration Framework
- Subordinate Use Cases: 
  - RF-CAMP-001: Build Campaign Using Property Modifications
  - RF-SS-001: Evaluate SmartShop Transaction Using Property Modifications
- Channel to Primary Actor: REST API
- Open Issues: 
  - How should multi-level property paths be handled for nested objects?
  - What auditing level is required for property modifications?
  - How should modification conflicts be visualized to campaign managers?

## Implementation Slices

| Slice ID | Description | Status | PR/Commit Reference |
|----------|-------------|--------|---------------------|
| RF-PMF-001.0 | Basic Transaction Evaluation | Planned | - |
| RF-PMF-001.1 | Transaction Outcome Recording | Planned | - |
| RF-PMF-001.2 | Property Constraint Handling | Planned | - |
| RF-PMF-001.3 | Error Handling and Recovery | Planned | - |
| RF-PMF-001.4 | Campaign Manager UI for Property Modifications | Planned | - |
| RF-PMF-001.5 | Batch Processing Support | Planned | - |
| RF-PMF-001.6 | Performance Optimization for High Volume | Planned | - |
| RF-PMF-001.7 | SmartShop Integration with PMF | Planned | - |
| RF-PMF-001.8 | Analytics Dashboard for Modification Effectiveness | Planned | - |

## Test Cases

| Test Case ID | Description | Expected Result |
|--------------|-------------|-----------------|
| RF-PMF-001.0.T1 | Evaluate transaction with matching campaign rule | Property modifications returned with original and modified values |
| RF-PMF-001.0.T2 | Evaluate transaction with no matching rules | Empty modified_properties object returned |
| RF-PMF-001.0.T3 | Evaluate transaction attempting to modify immutable property | No modification returned for immutable property |
| RF-PMF-001.1.T1 | Submit outcome notification with complete transaction data | Outcome recorded successfully |
| RF-PMF-001.2.T1 | Campaign rule attempts modification beyond constraint | Value adjusted to constraint boundary |
| RF-PMF-001.2.T2 | Multiple rules attempt to modify same property | Highest priority rule's modification applied |
| RF-PMF-001.3.T1 | Simulate RuleForge service timeout | Entity proceeds with original values |
| RF-PMF-001.3.T2 | Submit malformed evaluation request | Clear validation error returned |
| RF-PMF-001.5.T1 | Process batch of 100 evaluation requests | All processed correctly with appropriate modifications |
| RF-PMF-001.6.T1 | Process 1000 evaluation requests per second | All requests complete under 15ms with correct modifications |
| RF-PMF-001.7.T1 | Integrate SmartShop bundle sale with PMF | Bundle properties correctly modified |
| RF-PMF-001.8.T1 | Generate modification effectiveness report | Report shows conversion rates for modified vs. unmodified transactions |

## Related Documents
- [[IE-RF-REG-001: Integrated Entity Registration]]
- [[Entity Integration Schema]]
- [[Rule Set Schema]]
- [[Transaction Request Schema]]
- [[Transaction Response Schema]]
- [[RuleForge Interface Overview]]
- [[RuleForge Property Modification Framework Proposal]]

## Approvals
| Role/Department | Name | Date | Signature |
|-----------------|------|------|-----------|
| Product Manager |      |      |           |
| Lead Developer  |      |      |           |
| QA Lead         |      |      |           |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0.0   | 2025-03-12 | [[Wayne Smith]] | Initial version of the use case. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Use Case Template v2.0.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->