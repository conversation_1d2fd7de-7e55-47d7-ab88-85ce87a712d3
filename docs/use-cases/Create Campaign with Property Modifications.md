---
title: "Create Campaign with Property Modifications"
classification: Internal
created: 2025-03-12
updated: 2025-03-12
authors:
  - "[[<PERSON>]]"
version: 1.0.0
next-review: 2025-06-12
tags:
  - use-case
  - ruleforge
  - campaign-management
  - property-modification
---

# Create Campaign with Property Modifications

## Document Specification

### Purpose
This document describes the "Create Campaign with Property Modifications" use case, detailing how Campaign Managers use the RuleForge GUI to create campaigns that modify entity properties through the Property Modification Framework.

### Scope
This use case covers the campaign creation process, including selecting entities, defining conditions, configuring property modifications, and activating campaigns. It builds upon the entity registration process (IE-RF-REG-001) and enables the property modification functionality (RF-PMF-001).

### Target Audience
- Campaign Managers
- Marketing Specialists
- Product Managers
- QA Engineers
- System Administrators

## Use Case Details

### Overview
| Item                       | Description |
|----------------------------|-------------|
| Use Case ID                | RF-CAMP-001 |
| Use Case Name              | Create Campaign with Property Modifications |
| Created By                 | <PERSON> |
| Last Updated               | 2025-03-12 |
| Version                    | 1.0.0 |

### Specifics
| Item                       | Description |
|----------------------------|-------------|
| Goal in Context            | A Campaign Manager creates a campaign in RuleForge that modifies entity properties according to defined rules and conditions |
| Scope                      | RuleForge Campaign Management System |
| Level                      | User-goal |
| Primary Actor              | Campaign Manager |
| Secondary Actors           | RuleForge Campaign Management System, Integrated Entity |
| Stakeholders and Interests | - Campaign Manager: Wants intuitive interface to create effective campaigns without technical knowledge<br>- Marketing Team: Needs campaigns that drive business objectives<br>- Operations: Needs visibility into campaign configurations<br>- End Customers: Will experience the effects of property modifications |
| Preconditions              | - Campaign Manager has logged into RuleForge<br>- At least one entity has been registered with mutable properties (IE-RF-REG-001)<br>- Campaign Manager has necessary permissions |
| Minimal Guarantee          | - Campaign creation attempts are logged for audit purposes<br>- No invalid campaigns can be activated |
| Success Guarantee          | - Campaign is created with property modification rules<br>- Campaign is properly scheduled and activated<br>- Campaign modifications are applied to entity properties when conditions are met |
| Triggers                   | Campaign Manager initiates campaign creation process |
| Frequency of Use           | Medium - Daily to weekly depending on marketing calendar |

## Main Success Scenario
1. Campaign Manager navigates to Campaign Management section in RuleForge GUI
2. Campaign Manager selects "Create New Campaign" option
3. Campaign Manager enters campaign metadata (name, description, start/end dates)
4. Campaign Manager selects target entity from available registered entities
5. Campaign Manager selects entity's transaction context to target
6. System displays list of available properties, clearly indicating which are mutable
7. Campaign Manager defines campaign conditions (e.g., customer segment, location, time of day)
8. Campaign Manager selects a mutable property to modify
9. System displays appropriate modification options based on property type
10. Campaign Manager selects modification type (percentage adjustment, fixed value, etc.)
11. Campaign Manager configures modification parameters (e.g., 10% discount)
12. System validates modification against property constraints
13. Campaign Manager adds additional property modifications if needed
14. Campaign Manager reviews complete campaign configuration
15. Campaign Manager saves the campaign as draft
16. Campaign Manager tests the campaign using simulation tool
17. Campaign Manager activates the campaign
18. System confirms campaign activation
19. Campaign is now ready to modify entity properties when conditions are met

## Extension Points

### Campaign Targeting - After Step 7
- **Purpose**: Enables different approaches to defining campaign target audience
- **Implemented Extensions**: Basic condition builder
- **Planned Extensions**:
  - Advanced segment builder
  - Lookalike audience targeting
  - A/B test group assignment
  - Customer journey stage targeting

### Property Modification Configuration - After Step 11
- **Purpose**: Provides different ways to configure property modifications
- **Implemented Extensions**: Basic modification types (percentage, fixed value)
- **Planned Extensions**:
  - Tiered modification rules
  - Conditional modifications
  - Competitive response rules
  - Personalized modification mapping

### Campaign Testing - After Step 16
- **Purpose**: Enables different approaches to testing campaigns before activation
- **Implemented Extensions**: Basic simulation tool
- **Planned Extensions**:
  - Historical data replay
  - Impact analysis
  - Conflict detection
  - Multi-scenario testing

### Campaign Activation - After Step 17
- **Purpose**: Supports different activation strategies
- **Implemented Extensions**: Immediate activation
- **Planned Extensions**:
  - Gradual rollout
  - Scheduled activation
  - Approval workflow
  - Emergency kill switch

## Extensions / Alternate Flows

### E1: Invalid Campaign Metadata - Extension from Step 3
1. System detects invalid campaign metadata (missing required fields, invalid dates, etc.)
2. System displays validation errors
3. Campaign Manager corrects the errors
4. Flow continues from Step 3

### E2: No Mutable Properties Available - Extension from Step 6
1. System determines selected entity has no mutable properties
2. System displays notification that no properties can be modified
3. Campaign Manager can either:
   a. Select a different entity (return to Step 4)
   b. Contact system administrator to request property mutability changes
   c. Cancel campaign creation

### E3: Invalid Condition Configuration - Extension from Step 7
1. System detects invalid or contradictory condition configuration
2. System displays specific validation errors
3. Campaign Manager corrects the conditions
4. Flow continues from Step 7

### E4: Property Constraint Violation - Extension from Step 12
1. System detects that configured modification would violate property constraints
2. System displays constraint violation warning with details
3. System suggests valid alternative configurations
4. Campaign Manager adjusts modification parameters
5. Flow continues from Step 10

### E5: Campaign Simulation Failure - Extension from Step 16
1. Campaign simulation detects potential issues:
   a. Low match rate (few transactions would match conditions)
   b. Performance concerns
   c. Conflicts with other active campaigns
2. System displays simulation results with warnings
3. Campaign Manager can either:
   a. Adjust campaign configuration (return to appropriate step)
   b. Acknowledge warnings and proceed
   c. Save as draft for further refinement
4. Flow continues based on Campaign Manager's choice

### E6: Duplicate Campaign - Extension from Step 15
1. System detects high similarity with existing campaign
2. System displays duplicate campaign warning
3. Campaign Manager can either:
   a. Continue with current campaign
   b. Modify to differentiate from existing campaign
   c. Cancel and edit existing campaign instead
4. Flow continues based on Campaign Manager's choice

### E7: Campaign Activation Conflict - Extension from Step 17
1. System detects conflict with other active campaigns
2. System displays conflict details:
   a. Campaigns modifying same properties
   b. Contradictory modifications
   c. Resource contention issues
3. Campaign Manager can either:
   a. Continue activation (system will use priority rules)
   b. Modify campaign to resolve conflicts
   c. Deactivate conflicting campaigns
4. Flow continues based on Campaign Manager's choice

### E8: Save as Template - Extension from Step 15
1. Campaign Manager chooses to save campaign as reusable template
2. Campaign Manager provides template name and category
3. System saves configuration as template
4. Campaign Manager can either:
   a. Continue with current campaign activation
   b. Exit creation process
5. Flow continues based on Campaign Manager's choice

### E9: Clone Existing Campaign - Extension from Step 2
1. Campaign Manager selects "Clone Existing Campaign" instead of creating new
2. Campaign Manager selects source campaign to clone
3. System copies all configuration from source campaign
4. Campaign Manager adjusts parameters as needed
5. Flow continues from Step 3

## Special Requirements
- Campaign creation interface must be intuitive for non-technical users
- Property constraint visualization must clearly show valid modification ranges
- Campaign condition builder must support complex logical expressions
- Campaign simulation must process at least 10,000 sample transactions
- Campaign activation must take effect within 5 minutes
- Campaign management must support at least 1,000 concurrent active campaigns

## Technology and Data Variations List
- Web-based GUI for campaign management
- REST API for programmatic campaign creation
- Different property types (numeric, string, boolean, date, enum)
- Various modification types (percentage, fixed value, replacement, conditional)
- Multiple condition operators (equals, greater than, contains, etc.)
- Various activation mechanisms (immediate, scheduled, triggered)

## User Experience Considerations
- Property selection must clearly distinguish between mutable and immutable properties
- Modification options must be tailored to property data types
- Constraint visualization should use sliders or other intuitive controls
- Condition builder should use natural language phrasing where possible
- Campaign simulation results should be visually represented
- Error messages must be clear and actionable
- Campaign activation should include confirmation steps
- Campaign dashboard should show key metrics and status

## Component Mapping
| Step | Component(s) Involved | Notes |
|------|----------------------|-------|
| 1-5 | Campaign Management UI | Campaign setup flow |
| 6-7 | Campaign Management UI, Entity Registry | Property and condition configuration |
| 8-13 | Property Modification Builder UI | Modification configuration |
| 14-15 | Campaign Review UI | Review and save functionality |
| 16 | Campaign Simulation Engine | Test campaign before activation |
| 17-19 | Campaign Activation Service | Activate and deploy campaign |

## Campaign Property Modification Options

### Numeric Property Modifications
- **Percentage Adjustment**: Increase/decrease by percentage
  - Example: Reduce `agentPurchasePrice` by 10%
- **Fixed Value Adjustment**: Add/subtract a fixed value
  - Example: Add 500MB to `bundleDataAmount`
- **Set Value**: Replace with specific value
  - Example: Set `bundleValidityDays` to 45

### String Property Modifications
- **Replace**: Set to specific string
  - Example: Set `offerMessage` to "Special weekend offer!"
- **Prefix/Suffix**: Add text to beginning/end
  - Example: Add "BONUS: " prefix to `bundleName`
- **Format Template**: Use template with placeholders
  - Example: Set message to "Save {discount}% today only!"

### Boolean Property Modifications
- **Force True/False**: Set to specific boolean value
  - Example: Set `eligibleForBonus` to true
- **Toggle**: Flip current value
  - Example: Toggle `specialOfferEligible`

### Date Property Modifications
- **Add Time Period**: Add days/weeks/months
  - Example: Add 7 days to `expiryDate`
- **Set to Date**: Replace with specific date
  - Example: Set `promotionEndDate` to end of month

### Enum Property Modifications
- **Replace**: Set to specific enum value
  - Example: Set `customerSegment` to "PREMIUM"

## Related Information
- Priority: High
- Performance Target: Campaign creation completed in under 2 minutes
- Superordinate Use Case: Campaign Management Framework
- Subordinate Use Cases: 
  - RF-CAMP-002: Monitor Campaign Performance
  - RF-CAMP-003: Modify Existing Campaign
- Channel to Primary Actor: Web Interface
- Open Issues: 
  - How should campaign priority conflicts be visualized?
  - What level of approval workflow is needed for high-impact campaigns?
  - How detailed should campaign simulation feedback be?

## Implementation Slices

| Slice ID | Description | Status | PR/Commit Reference |
|----------|-------------|--------|---------------------|
| RF-CAMP-001.0 | Basic Campaign Creation | Planned | - |
| RF-CAMP-001.1 | Property Modification Builder | Planned | - |
| RF-CAMP-001.2 | Campaign Condition Builder | Planned | - |
| RF-CAMP-001.3 | Campaign Simulation | Planned | - |
| RF-CAMP-001.4 | Campaign Activation | Planned | - |
| RF-CAMP-001.5 | Campaign Templates | Planned | - |
| RF-CAMP-001.6 | Campaign Conflict Detection | Planned | - |
| RF-CAMP-001.7 | Advanced Targeting Options | Planned | - |
| RF-CAMP-001.8 | Campaign Performance Preview | Planned | - |

## Test Cases

| Test Case ID | Description | Expected Result |
|--------------|-------------|-----------------|
| RF-CAMP-001.0.T1 | Create basic campaign with single property modification | Campaign created successfully |
| RF-CAMP-001.0.T2 | Attempt to create campaign with invalid metadata | Clear validation errors shown |
| RF-CAMP-001.1.T1 | Configure percentage discount on numeric property | Modification saved with correct parameters |
| RF-CAMP-001.1.T2 | Attempt modification beyond property constraints | Constraint violation warning shown |
| RF-CAMP-001.2.T1 | Create campaign with complex conditional logic | Conditions saved correctly |
| RF-CAMP-001.2.T2 | Test condition preview with sample data | Accurate match/non-match results shown |
| RF-CAMP-001.3.T1 | Run simulation on campaign | Simulation results show expected modifications |
| RF-CAMP-001.4.T1 | Activate campaign | Campaign becomes active within 5 minutes |
| RF-CAMP-001.5.T1 | Save and reuse campaign template | Template can be used to create new campaign |
| RF-CAMP-001.6.T1 | Detect conflict with existing campaign | Conflict warning displayed |
| RF-CAMP-001.7.T1 | Create campaign with advanced segment targeting | Campaign correctly targets specified segment |
| RF-CAMP-001.8.T1 | View projected performance metrics | Accurate projection based on historical data |

## Related Documents
- [[IE-RF-REG-001: IE-RF Register Entity]]
- [[RF-PMF-001: IE-RF Modify Properties]]
- [[Rule Set Schema]]
- [[Entity Integration Schema]]
- [[RuleForge Campaign Manager User Guide]]
- [[Property Modification Framework Proposal]]

## Approvals
| Role/Department | Name | Date | Signature |
|-----------------|------|------|-----------|
| Product Manager |      |      |           |
| Marketing Lead  |      |      |           |
| QA Lead         |      |      |           |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0.0   | 2025-03-12 | [[Wayne Smith]] | Initial version of the use case. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Use Case Template v2.0.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->