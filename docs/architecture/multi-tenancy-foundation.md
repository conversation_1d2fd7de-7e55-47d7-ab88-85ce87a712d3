# Multi-Tenancy Foundation Architecture Documentation

# Overview

This system follows a multi-tenant, role-based access control (RBAC) model,
designed to support multiple tenants (also called organisations) within a
single shared platform.

Each tenant (team/organisation) represents an isolated workspace where users
collaborate on their own data and resources. Within a tenant, users are
assigned roles such as Owner, Admin, or Member, which determine their level
of access and responsibilities.

In addition to tenant-scoped roles, the system also defines global roles at
the platform level. System Admins and System Users operate across all
tenants, providing oversight, support, and platform-wide configuration.

This layered structure ensures:

- **Data isolation**: users in one tenant cannot access another tenant's data
  unless granted explicit cross-tenant privileges.
- **Granular permissions**: role-based access defines what actions a user can
  perform, both globally and within a tenant.
- **Scalability**: the same model supports a single-tenant setup as well as
  large-scale, multi-organisation deployments.
- **Clarity**: roles are consistently defined at both global and tenant levels,
  preventing overlap or ambiguity in responsibilities.

In short, the model combines multi-tenancy for organisational separation with
RBAC for controlled access, providing a secure and flexible foundation for
growth.

# Users and roles overview

The system distinguishes between two levels of users - those with global
(system-wide) authority and those with tenant (team/organisation) authority.
Roles define what each type of user can see and do.

## Global roles (system-level)

These roles operate across all tenants and are not limited to a single tenant
or organisation.

- System Admin
    - Full visibility and control across the entire platform.
    - Manages tenants, users, global settings, and billing.
    - Equivalent to a super admin.

- System User
    - Has cross-tenant access for support, monitoring, or specific system operations.
    - Permissions are limited compared to System Admins and typically read-only or
      operational in scope.

## Tenant roles (Team/organisation-level)

These roles apply within the context of a specific tenant. A user can belong to multiple tenants with different roles in each.

- Owner
    - Highest authority within a tenant.
    - Manages tenant settings, billing, and user assignments.
    - Can transfer ownership.
- Admin
    - Manages tenant resources and users.
    - Cannot transfer ownership or override billing (unless explicitly granted).
- Member
    - Standard tenant user.
    - Has access only to the resources and functions granted within the tenant.

## Scope summary

- Global roles (System Admin, System User): span all tenants.
- Tenant roles (Owner, Admin, Member): scoped to a single tenant, ensuring
  separation of responsibilities and data.

## Permission matrix

*NOTE:* This is an example draft permission matrix to communicate the
concepts.

| Action                              | System Admin | System User | Tenant Owner | Tenant Admin | Tenant Member |
| ----------------------------------- | ------------ | ----------- | ------------ | ------------ | ------------- |
| Modify system settings              | Y            | -           | -            | -            | -             |
| Manage system admins and users      | Y            | -           | -            | -            | -             |
| Manage non-owning tenants           | Y            | -           | -            | -            | -             |
| View all tenants                    | Y            | Y           | -            | -            | -             |
| View own tenants                    | Y            | Y           | Y            | -            | -             |
| Manage tenant ownership and billing | Y            | Y           | Y            | -            | -             |
| Manage tenant users/roles           | Y            | Y           | Y            | Y            | -             |
| View tenant resources               | Y            | Y           | Y            | Y            | Y             |
| Operate tenant resources            | Y            | Y           | Y            | Y            | Y             |


# Implementation

## Overview

The following microservices currently exist:

- analytics
- notification
- rules-engine
- user-management

`user-management` contains the data models which describe the user and tenant
grouping information.

The other microservices associate their data primarily with the tenant ID
and determine access-rights based on user roles and tenant group membership.

User identification is determined by the JWT token received via the API.

## User management data models

### Constants

These **User Roles** are defined as constants:

- System Admin
- System User
- User

These **Tenant Membership Roles** are defined as constants:

- Owner
- Admin
- Member

These **Access Levels** are defined as constants:

- System Admin
- System Superuser
- Tenant Owner
- Tenant Admin
- Tenant Member

### Models

*NOTE*: These model descriptions are not exhaustive of the complete entity
and only describe the relevant fields.

**User**

| Field | Description |
| ----- | ------- |
| id    | Unique id |
| role  | A value of **User Roles** |

**Tenant**

| Field | Description |
| ----- | ------- |
| id    | Unique id |

**TenantMembership**

| Field | Description |
| ----- | ------- |
| user_id | User.id |
| tenant_id | Tenant.id |
| role  | A value of **Tenant Membership Roles** |


### Relevant APIs

All API calls honour the access levels as per defined within the permissions
matrix. These are specific to the multi-tenancy approach and do not include
additional or unrelated APIs.

**Tenant**

| API Call | Description |
| -------- | ----------- |
| create_tenant | Create a tenant |
| get_tenants | Retrieve tenants, with filters |
| get_tenant | Retrieve a single tenant |
| update_tenant | Update a tenant's information |
| delete_tenant | Delete a tenant |

**Tenant Memberships**

| API Call | Description |
| -------- | ----------- |
| get_tenant_memberships | Retrieve tenant memberships, with filters |
| get_tenant_memberships_for_user | Retrieve tenant memberships for a user |
| get_user_tenant_membership | Retrieve the tenant membership for a user and a tenant |
| set_tenant_membership | Set the tenant membership for a user within a tenant |
| delete_tenant_membership | Delete a tenant membership for a user from a tenant |

**Access Checks**

| API Call | Description |
| -------- | ----------- |
| get_tenant_access_level_for_user | Determine a user's access level for a specific tenant |

## Other services

The following patterns are implemented in the other services:

- All other services associate information with users or tenants.
- On API requests, the user and tenant memberships are identified and access
  checks are performed in order to determine if the operation may proceed.
- Operations proceed based on the access permissions which have been decided
  upon.

## Data scoping

*NOTE*: It is assumed that all information is held within a relational
database.

Data isolation is performed within the API calls themselves. This is done
very easily using standard filter functions (think SQL `where` clauses).
Relational sets are reduced using `where` clauses built up from tenant
memberships.

For clarity:

- System admins and system users have no `where` clause applied.
- Standard users have `where` clauses applied along with tenant membership
  information taken into account.

Due to this approach, the same API set may be used by admins and users.

