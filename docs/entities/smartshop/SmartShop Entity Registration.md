---
title: SmartShop Entity Registration
classification: Internal
created: 2025-04-17
updated: 2025-04-17
authors:
  - "[[<PERSON>]]"
version: 1.0.0
next-review: 2025-10-17
category: "[[Entities]]"
tags:
  - entity
  - registration
  - integration
topics:
  - "[[SmartShop]]"
  - "[[Entity Management]]"
  - "[[RuleForge]]"
  - "[[System Integration]]"
---

# SmartShop Entity Registration

## Document Specification

### Purpose

To document the formal registration of the SmartShop Bundle Delivery Service entity with the RuleForge platform, defining its properties, transaction contexts, and integration details.

### Scope

This document covers the entity properties, transaction contexts, and implementation details required for integrating SmartShop with the RuleForge engine. It provides the foundational entity structure that can be leveraged by multiple campaigns.

### Target Audience

- RuleForge implementation team
- Entity integration developers
- Campaign implementation teams
- System architects

## Table of Contents

- [[#Entity Overview]]
- [[#Integration Details]]
- [[#Registration JSON]]
- [[#Transaction Contexts]]
- [[#Key Properties]]
- [[#Implementation Notes]]

## Entity Overview

The SmartShop Bundle Delivery Service is a platform that enables agents to sell customizable service bundles to subscribers. It provides transaction contexts definitions for bundle sales through the agents. This registration focuses on the bundle sales context which is most commonly used for marketing campaigns.

## Integration Details

**Entity Name:** SmartShop Bundle Delivery Service  
**Entity ID:** 550e8400-e29b-41d4-a716-446655440000  
**Integration Date:** 2025-03-15  
**Entity Owner:** Mobile Commerce Team  
**Primary Contact:**
**System Integration:** Direct API integration via REST endpoints  

## Registration JSON

```json
{
  "name": "SmartShop Bundle Delivery Service",
  "description": "Customizable bundles delivery service with agent network",
  "transactionContexts": [
    {
      "contextId": "BUNDLE_SELL",
      "name": "Bundle Sell",
      "description": "Represents an agent selling a bundle to a subscriber",
      "properties": [
        {
          "propertyId": "agentMsisdn",
          "name": "Agent MSISDN",
          "description": "The mobile number of the agent performing the transaction",
          "type": "string",
          "mutable": false
        },
        {
          "propertyId": "agentCgi",
          "name": "Agent CGI",
          "description": "The Cell Global Identity representing the agent's location",
          "type": "string",
          "mutable": false
        },
        {
          "propertyId": "bundleRetailPrice",
          "name": "Bundle Retail Price",
          "description": "The retail price of the bundle being sold",
          "type": "number",
          "mutable": false
        },
        {
          "propertyId": "agentPurchasePrice",
          "name": "Agent Purchase Price",
          "description": "The purchase price of the bundle, debited from the agent's wallet",
          "type": "number",
          "mutable": true,
          "constraints": {
            "min": 0
          }
        },
        {
          "propertyId": "bundleId",
          "name": "Bundle ID",
          "description": "Identifier for the bundle being sold (format: SS-PP, where SS is service sequence number and PP is package sequence number)",
          "type": "string",
          "mutable": false
        },
        {
          "propertyId": "subscriberMsisdn",
          "name": "Subscriber MSISDN",
          "description": "The mobile number of the subscriber receiving the bundle",
          "type": "string",
          "mutable": false
        }
      ]
    }
  ]
}
```

## Transaction Contexts

### BUNDLE_SELL Context

The BUNDLE_SELL context represents a transaction where an agent sells a service bundle to a subscriber. It captures details about:

- Agent identification and location
- Bundle pricing and identification
- Subscriber information
- Transaction process flow

This context is primarily used for:
- Sales incentive campaigns
- Regional promotions
- Agent performance tracking
- Dynamic pricing adjustments

### Context Workflow

1. Agent initiates bundle sale from their device
2. Transaction evaluation request is sent to RuleForge
3. RuleForge processes applicable rules and returns property modifications
4. Bundle is provisioned to the subscriber
5. Transaction outcome notification is sent to RuleForge
6. RuleForge processes outcome rules for tracking and analytics

### Typical Transaction Volume

- Average daily transactions: ~50,000
- Peak transactions per second: ~20
- Busiest periods: 9:00-11:00 and 16:00-18:00 local time
- Regional distribution: 65% in urban centers, 35% in rural areas

## Key Properties

### agentMsisdn

- **Description**: The mobile number of the agent performing the transaction
- **Format**: International format with country code (e.g., "22890123456")
- **Usage**: Primary identifier for agents, commonly used as collection key for agent-specific persistent variables
- **Mutability**: Immutable

### agentCgi

- **Description**: The Cell Global Identity representing the agent's location
- **Format**: String in the format "MCC-MNC-LAC-CI" (e.g., "62320-10458")
- **Usage**: Used for location-based targeting and regional campaign enforcement
- **Mutability**: Immutable

### bundleRetailPrice

- **Description**: The retail price of the bundle being sold
- **Format**: Numeric value in local currency (FCFA)
- **Usage**: Used for qualification criteria in campaigns and for analytics
- **Mutability**: Immutable

### agentPurchasePrice

- **Description**: The purchase price of the bundle, debited from the agent's wallet
- **Format**: Numeric value in local currency (FCFA)
- **Usage**: Target property for discounts and price modifications
- **Mutability**: Mutable (can be modified by rule sets)
- **Constraints**: Must be greater than or equal to 0

### bundleId

- **Description**: Identifier for the bundle being sold
- **Format**: String in the format "SS-PP" where SS is service sequence number and PP is package sequence number
- **Usage**: Used to identify specific bundles for targeting and analytics
- **Mutability**: Immutable

### subscriberMsisdn

- **Description**: The mobile number of the subscriber receiving the bundle
- **Format**: International format with country code (e.g., "22891234567")
- **Usage**: Used for subscriber segmentation and campaign targeting
- **Mutability**: Immutable

## Implementation Notes

### API Integration

The SmartShop entity integrates with RuleForge through the following endpoints:

1. **Transaction Evaluation API**
   - Endpoint: `POST /evaluate`
   - Required parameters:
     - `entityId`: "550e8400-e29b-41d4-a716-446655440000"
     - `contextId`: "BUNDLE_SELL" 
     - `transactionId`: Unique identifier for each transaction

2. **Transaction Outcome API**
   - Endpoint: `POST /outcomes`
   - Required parameters:
     - `entityId`: "550e8400-e29b-41d4-a716-446655440000"
     - `contextId`: "BUNDLE_SELL"
     - `transactionId`: Matching ID from evaluation request

### Performance Considerations

- Transaction evaluation phase should complete in under 25ms
- Transaction outcome phase should complete in under 10ms
- High availability is critical as bundle sales are a core business function
- Retry logic is implemented on the SmartShop side with exponential backoff

### Common Use Cases

1. **Price modifications**: Campaigns often modify the `agentPurchasePrice` property to offer discounts
2. **Regional targeting**: The `agentCgi` property is commonly used for location-based campaigns
3. **Agent incentives**: The `agentMsisdn` is used as collection key for tracking agent performance
4. **Bundle qualification**: The `bundleRetailPrice` is used to determine if bundles qualify for promotions

## Related Documents

- [[RuleForge Interface Overview]]
- [[Transaction Evaluation API]]
- [[Entity Registration Guide]]
- [[Campaign One Entity Specification Review]]

## Approvals

| Role | Name | Date | Signature |
|-----------------|------|------|-----------|
| Entity Owner | Mobile Commerce Team Lead |  |  |
| Integration Lead |  |  |  |
| RuleForge Architect |  |  |  |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0.0   | [[2025-04-17]] | [[Wayne Smith]] | Created standalone entity registration document for SmartShop, extracted from Campaign One documentation. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->