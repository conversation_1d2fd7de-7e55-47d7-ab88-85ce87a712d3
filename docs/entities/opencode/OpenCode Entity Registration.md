---
title: OpenCde Entity Registration
classification: Internal
created: 2025-09-30
authors:
  - "[[<PERSON><PERSON>]]"
version: 1.0.0
next-review: 2025-10-09
category: "[[Entities]]"
tags:
  - entity
  - registration
  - integration
topics:
  - "[[OpenCode]]"
  - "[[Entity Management]]"
  - "[[RuleForge]]"
  - "[[System Integration]]"
---

# OpenCode Entity Registration

## Registration JSON
```json
{
  "name": "OpenCode Subscriber Self Bundle Purchase Service",
  "description": "Subscriber self-service platform for bundle purchases",
  "transactionContexts": [
    {
      "contextId": "SELF_BUNDLE_PURCHASE",
      "name": "Self Bundle Purchase",
      "description": "Represents a subscriber self purchasing a bundle",
      "properties": [
        {
          "propertyId": "bundlePrice",
          "name": "Bundle Price",
          "description": "The amount subscriber is charged for the bundle upon purchase",
          "type": "number",
          "mutable": true,
          "constraints": {
            "min": 0
          }
        }
      ]
    }
  ]
}
```