---
title: Campaign K0 Rule Set Definition
classification: Internal
created: 2025-10-01
updated: 2025-10-01
authors:
  - "[[<PERSON>]]"
version: 1.0.0
next-review: 2025-10-03
category: "[[Campaigns]]"
tags:
  - specification
  - campaign
  - rule-set
topics:
  - "[[RuleForge]]"
  - "[[Rule Logic]]"
---
# Campaign K0 Rule Set Definition

This document provides the rule set definition for the Campaign K0 - Self Bundle Purchase  Discount. The definition follows Schema v7.0.0 format and implements a simple conditional discount based on bundle price threshold. It leverages the OpenCode Subscriber Self Bundle Purchase Service entity.

## Document Specification

### Purpose

To define the complete rule set that implements the K0 campaign business logic within the RuleForge Property Modification Framework, building on the OpenCode Subscriber Self Bundle Purchase Service entity.

### Scope

This document covers the rule set to implement the conditional 10% discount mechanism for the campaign based on the bundle price. ```

### Target Audience

- OpenCode team
- QA and testing team

## Entity Reference

This rule set operates on the OpenCode Subscriber Self Bundle Purchase Service entity with the following characteristics:

### Entity Properties Used

- **Mutable Properties:** bundlePrice
- **Targeting Properties:** bundlePrice (used in condition)
- **Collection Key:** None (no persistent state required)

### Property Constraints

The bundlePrice property has the following constraints defined in the entity registration:

- **Type:** number
- **Mutable:** true
- **Minimum:** 0
- **Required:** true

## Rule Set JSON

```json
{
  "schemaVersion": "7.0.0",
  "rulesetId": "SELF_BUNDLE_DISCOUNT",
  "name": "10% Discount for all bundles with purchase price greater than 100FCFA",
  "description": "Applies a 10% discount on bundle for bundles having purchase price equal or exceeding to 100 FCFA,
  "entityId": "YOUR_ENTITY_ID",
  "contextId": "SELF_BUNDLE_PURCHASE",
  "version": 1,
  "status": "ACTIVE",
  "category": "PRICING",
  "startDateTime": "2025-09-30T00:00:00Z",
  "endDateTime": "2025-12-31T23:59:59Z",
  "lastModifiedDateTime": "2025-09-30T00:00:00Z",
  "collectionMappings": [],
  "persistentVariables": [],
  "localVariables": [],
  "evaluationRules": [
    {
      "ruleId": "APPLY_10_PERCENT_DISCOUNT_OVER_100",
      "name": "Apply 10% Discount for Bundles Over 100",
      "description": "Applies 10% discount to bundle price when bundlePrice is greater than or equal to 100",
      "priority": 1,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": ">=",
        "parameters": {
          "leftOperand": "{bundlePrice}",
          "rightOperand": 100
        }
      },
      "variableAssignments": [
        {
          "variableId": "bundlePrice",
          "assignmentTypeId": "DECREASE_BY_PERCENTAGE",
          "value": 10
        }
      ],
      "webhookCalls": []
    }
  ],
  "outcomeRules": []
}
```

## Key Rules

### Evaluation Rules

#### 1. APPLY_10_PERCENT_DISCOUNT_OVER_100

**Purpose**: Applies a 10% discount to the bundle price when the price is greater than or equal to 100.

**Priority**: 1 (highest priority, as it's the only evaluation rule)

**Condition**:

- **Type**: COMPARISON
- **Operator**: >= (greater than or equal to)
- **Left Operand**: `{bundlePrice}` (current transaction's bundle price)
- **Right Operand**: 100 (threshold value)
- **Logic**: The condition evaluates to TRUE when bundlePrice >= 100

**Variable Assignments**:

1. Modify bundlePrice property
    - **Assignment Type**: DECREASE_BY_PERCENTAGE
    - **Value**: 10
    - **Effect**: Reduces the bundlePrice by 10%

**Behavior Examples**:

|Input bundlePrice|Condition Result|Discount Applied|Output bundlePrice|
|---|---|---|---|
|50|FALSE (50 < 100)|NO|50|
|99.99|FALSE (99.99 < 100)|NO|99.99|
|100|TRUE (100 >= 100)|YES|90|
|150|TRUE (150 >= 100)|YES|135|
|1000|TRUE (1000 >= 100)|YES|900|

## Implementation Notes

### Campaign Characteristics

**Campaign K0** is designed as a simple, straightforward discount campaign with the following characteristics:

1. **Single Condition**: Only one business rule - price threshold check
2. **No Persistent State**: No variables stored between transactions
3. **No Collection Management**: No per-subscriber or per-agent tracking
4. **Evaluation Phase Only**: All logic in synchronous evaluation phase
5. **No Outcome Rules**: No asynchronous processing required

### Rule Execution Flow

#### Evaluation Phase (Synchronous)

1. Transaction evaluation request received with bundlePrice value
2. Rule engine evaluates condition: `bundlePrice >= 100`
3. If TRUE:
    - Apply DECREASE_BY_PERCENTAGE assignment (10%)
    - Return modified bundlePrice in response
4. If FALSE:
    - Skip variable assignment
    - Return transaction without modifications

#### Outcome Phase (Asynchronous)

Not used in this campaign - no outcome rules defined.

### Business Logic

#### Discount Calculation

The 10% discount is calculated using the DECREASE_BY_PERCENTAGE assignment:

```
Modified Price = Original Price - (Original Price × 0.10)
Modified Price = Original Price × 0.90
```

**Examples**:

- bundlePrice = 100 → 100 × 0.90 = 90 (discount: 10)
- bundlePrice = 150 → 150 × 0.90 = 135 (discount: 15)
- bundlePrice = 200 → 200 × 0.90 = 180 (discount: 20)

#### Threshold Behavior

The condition uses the `>=` operator, which means:

- **Inclusive**: bundlePrice = 100 **qualifies** for discount
- **Boundary**: bundlePrice = 99.99 does **not** qualify for discount

This inclusive behaviour encourages subscribers to purchase bundles at exactly 100 or above.


## Changelog

| Version | Date       | Author            | Changes                                                                                                                                                                           |
| ------- | ---------- | ----------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1.0.0   | 2025-09-30 | [Kennedy Karanda] | Initial version of Campaign K0 rule set definition; implements conditional 10% discount for bundlePrice >= 100; single evaluation rule with no persistent state or outcome rules. |

---

© 2025 Concurrent Systems. All Rights Reserved.