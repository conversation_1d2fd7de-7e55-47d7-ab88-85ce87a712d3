---
title: Campaign Framework Summary
classification: Internal
created: 2025-04-10
updated: 2025-04-10
authors:
  - "[[Campaign Team]]"
version: 1.0.0
next-review: 2025-10-10
category: "[[Guides]]"
tags:
  - guide
  - summary
  - framework
topics:
  - "[[Campaign Management]]"
  - "[[RuleForge]]"
  - "[[Marketing]]"
  - "[[Documentation]]"
---

# Campaign Framework Summary

## Introduction

This document provides a concise overview of the Campaign Development Framework created based on the analysis of "Campaign One." It summarizes the standardized approach for designing, building, documenting, and implementing marketing campaigns using the RuleForge platform with pre-integrated entities, with specific focus on the two-phase rule execution model.

## Framework Purpose

The Campaign Development Framework aims to:

1. Standardize the campaign development process
2. Ensure comprehensive documentation
3. Leverage pre-integrated entities efficiently
4. Optimize technical implementation
5. Support efficient two-phase rule execution
6. Facilitate testing and validation
7. Enable performance monitoring and optimization

## Pre-Integrated Entity Approach

A key aspect of this framework is recognizing that campaigns are built on top of pre-integrated entities:

1. **Entities Are Pre-Integrated**: Entities like SmartShop are already integrated with RuleForge before campaign development begins
2. **Campaigns Reference Entities**: Campaigns leverage existing entity specifications rather than defining them
3. **Entity Documentation Is External**: Entity integration details are documented separately from campaign documentation
4. **Campaign Rules Use Entity Properties**: Campaigns create rules that interact with existing entity properties

This approach offers several benefits:
- Eliminates duplication of entity specifications
- Ensures consistency across campaigns using the same entity
- Allows campaigns to focus on business logic rather than integration details
- Supports multiple campaigns running on the same entity

## Campaign Documentation Structure

Every campaign requires a comprehensive set of documentation that covers both business and technical aspects:

### Core Documentation Set

1. **Campaign Description Document**
   - Provides business overview, objectives, and key mechanics
   - Sets success metrics and implementation considerations
   - References the pre-integrated entity the campaign will use
   - Acts as the foundational document for all stakeholders

2. **Entity Specification Review Document**
   - Reviews the pre-integrated entity's properties and contexts
   - Identifies which entity properties will be used by the campaign
   - Analyzes constraints and limitations
   - Does NOT redefine the entity but analyzes how to use it

3. **Rule Set Definition Document**
   - Contains the complete rule set JSON definition
   - Implements the two-phase execution model with separate evaluation and outcome rules
   - Documents persistent variables, collection mappings, and rule logic
   - References entity properties without redefining them

4. **Implementation Guide**
   - Outlines implementation priorities and key components
   - Defines performance requirements and integration points
   - Provides a comprehensive launch checklist
   - Focuses on campaign-specific implementation, not entity integration

5. **Transaction Examples Document**
   - Illustrates transaction flows with concrete examples
   - Shows the two-phase execution with evaluation and outcome processing
   - Provides examples for various scenarios, including edge cases
   - Uses actual entity property names and formats

### Template Usage

For each new campaign, start by creating copies of the five core templates:

1. Use the **Campaign Description Template** first to establish business requirements
2. Complete the **Entity Specification Review Template** to analyze the entity you'll be working with
3. Develop the **Rule Set Definition Template** to implement the campaign logic
4. Create the **Implementation Guide Template** to plan the development work
5. Finally, document the **Transaction Examples Template** to illustrate the functionality

Each template follows the standard Concurrent Systems document format with proper metadata, structured sections, and versioning.

## Two-Phase Rule Execution Model

A key aspect of the framework is the implementation of RuleForge's two-phase rule execution model:

### Evaluation Phase (Synchronous)

- Executes during transaction initiation in real-time
- Focuses on immediate property modifications (e.g., discounts, prices)
- Must be optimized for performance (typically under 18ms)
- Primarily uses the `evaluationRules` collection in the rule set

### Outcome Phase (Asynchronous)

- Executes after transaction completion
- Focuses on counting, tracking, and status updates
- Handles complex business logic where performance is less critical
- Updates persistent variables based on transaction outcomes
- Primarily uses the `outcomeRules` collection in the rule set

### Benefits of Two-Phase Processing

1. **Transaction Integrity**: Only count/track successful transactions
2. **Performance Optimization**: Keep real-time processing lightweight
3. **Complex Logic Handling**: Run detailed logic after transaction completion
4. **Clear Separation of Concerns**: Modification vs. tracking
5. **Improved User Experience**: Fast response times for end users

## Campaign Development Process

The campaign development follows a structured lifecycle:

1. **Conceptualization**: Define objectives and mechanics
2. **Entity Specification Review**: Analyze available entity properties and contexts
3. **Rule Set Definition**: Design campaign-specific rules using entity properties
4. **Implementation**: Develop rules for the pre-integrated entity
5. **Testing**: Verify functionality and performance
6. **Deployment**: Launch the campaign
7. **Monitoring**: Track performance and optimize
8. **Analysis**: Evaluate results and document learnings

Each phase has specific deliverables and documentation requirements as detailed in the Campaign Development Process Guide.

## Best Practices

### Documentation Best Practices

1. **Use Templates**: Start with approved templates for all documents
2. **Maintain Consistent Terminology**: Use the same terms across all documents
3. **Reference Entity Documentation**: Link to external entity documentation rather than duplicating it
4. **Include Diagrams**: Add visual representations of complex processes
5. **Document Changes**: Keep changelogs updated
6. **Cross-Reference**: Link related documents using wiki links

### Technical Best Practices

1. **Respect Entity Constraints**: Adhere to property constraints defined in the entity
2. **Optimize Evaluation Phase**: Keep real-time processing lightweight
3. **Use Existing Properties**: Leverage pre-defined entity properties rather than creating new ones
4. **Implement Proper Validation**: Use constraints to validate modifications
5. **Use Collection Mappings**: Implement proper state persistence
6. **Test Edge Cases**: Particularly date/time boundaries and status transitions

### Implementation Best Practices

1. **Follow the Two-Phase Model**: Properly separate evaluation and outcome logic
2. **Test Both Phases**: Verify both phases work correctly
3. **Monitor Performance**: Especially for the evaluation phase
4. **Implement Proper Error Handling**: Plan for failures
5. **Document Example Transactions**: Provide comprehensive examples
6. **Consult Entity Owners**: Engage with entity owners for guidance on best practices

## Campaign One as Reference Implementation

"Campaign One" (the Lomé Agent Bundle Sales Incentive campaign) serves as the reference implementation for this framework:

1. It demonstrates proper separation of evaluation and outcome rules
2. It shows effective use of persistent variables and collection mappings with the SmartShop entity
3. It implements complex business logic like daily counter resets and target achievement
4. It illustrates location-based targeting and qualification criteria
5. It demonstrates applying discounts in the evaluation phase
6. It shows counting and tracking in the outcome phase

When developing new campaigns, refer to Campaign One documentation for practical examples of the framework in action.

## Related Documents

- [[Campaign Development Process Guide]]
- [[Campaign Description Template]]
- [[Entity Specification Review Template]]
- [[Rule Set Definition Template]]
- [[Implementation Guide Template]]
- [[Transaction Examples Template]]
- [[Campaign One Description]]
- [[Campaign One Entity Specification Review]]
- [[Campaign One Rule Set Definition]]
- [[Campaign One Implementation Guide]]
- [[Campaign One Transaction Examples]]

## Approvals

| Role | Name | Date | Signature |
|-----------------|------|------|-----------|
| Campaign Director |  |  |  |
| Technical Lead |  |  |  |
| Marketing Lead |  |  |  |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0.0   | [[2025-04-10]] | [[Campaign Team]] | Initial version of the Campaign Framework Summary. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->