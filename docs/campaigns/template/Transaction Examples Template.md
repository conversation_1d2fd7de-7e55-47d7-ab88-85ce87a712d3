---
title: Transaction Examples Template
classification: Internal
created: 2025-04-10
updated: 2025-04-10
authors:
  - "[[Campaign Team]]"
version: 1.0.0
next-review: 2025-10-10
category: "[[Templates]]"
tags:
  - template
  - campaign
  - examples
topics:
  - "[[Campaign Management]]"
  - "[[Transaction Processing]]"
  - "[[RuleForge]]"
---

# [Campaign Name] Transaction Examples

This document provides example transactions for the [Campaign Name] campaign, including transaction requests, responses, and outcome notifications that demonstrate the two-phase rule execution model as applied to the pre-integrated [Entity Name] entity.

## Document Specification

### Purpose

To illustrate the complete transaction flow for [Campaign Name] with concrete examples that demonstrate how transaction data from the [Entity Name] entity is processed in both the evaluation and outcome phases.

### Scope

This document covers standard transaction request formats, expected responses when [campaign-specific] rules are applied, and example outcome notifications for various scenarios in the campaign. It does NOT define the entity structure but shows how campaign rules interact with it.

### Target Audience

- RuleForge implementation team
- Campaign administrators and testers
- Business stakeholders

## Table of Contents

- [[#Entity Reference]]
- [[#Two-Phase Execution Flow]]
- [[#Standard Transaction Examples]]
- [[#Scenario Examples]]
- [[#Implementation Notes]]

## Entity Reference

These examples use the pre-integrated [Entity Name] entity with the following characteristics:

**Entity ID:** [UUID of the entity]  
**Context ID:** [CONTEXT_ID used by this campaign]

For full details on the entity properties and structure, refer to the [[Entity Specification Review]] document.

## Two-Phase Execution Flow

The [Campaign Name] campaign uses RuleForge's two-phase rule execution model:

1. **Evaluation Phase** (synchronous):
   - Transaction is evaluated in real-time
   - [Describe what happens in this phase]
   - [Describe what the end user experiences]

2. **Outcome Phase** (asynchronous):
   - Transaction outcome is reported after completion
   - [Describe what happens in this phase]
   - [Describe what gets updated/tracked]

This separation ensures [describe key benefit(s) of the separation].

## Standard Transaction Examples

### Example 1: [Describe scenario - e.g., Initial Transaction]

#### Transaction Request (Evaluation Phase)

```json
{
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "transactionId": "[TRANSACTION_ID_1]",
  "timestamp": "[Timestamp in ISO format]",
  "transactionData": {
    "[propertyId1]": "[value1]",
    "[propertyId2]": "[value2]",
    "[propertyId3]": "[value3]",
    "[propertyId4]": "[value4]",
    "[propertyId5]": "[value5]",
    "[propertyId6]": "[value6]"
  }
}
```

#### Transaction Response (no modifications)

```json
{
  "transactionId": "[TRANSACTION_ID_1]",
  "modifiedProperties": {}
}
```

#### Outcome Notification

```json
{
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "transactionId": "[TRANSACTION_ID_1]",
  "timestamp": "[Timestamp in ISO format]",
  "status": "COMPLETED",
  "transactionData": {
    "[propertyId1]": "[value1]",
    "[propertyId2]": "[value2]",
    "[propertyId3]": "[value3]",
    "[propertyId4]": "[value4]",
    "[propertyId5]": "[value5]",
    "[propertyId6]": "[value6]"
  },
  "modificationStatus": {
    "applied": [],
    "rejected": []
  },
  "additionalData": {
    "[additionalDataField1]": "[value1]",
    "[additionalDataField2]": "[value2]"
  }
}
```

> After this outcome notification, the campaign rules will [describe what happens in the system].

### Example 2: [Describe scenario - e.g., Achievement Transaction]

#### Transaction Request (Evaluation Phase)

```json
{
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "transactionId": "[TRANSACTION_ID_2]",
  "timestamp": "[Timestamp in ISO format]",
  "transactionData": {
    "[propertyId1]": "[value1]",
    "[propertyId2]": "[value2]",
    "[propertyId3]": "[value3]",
    "[propertyId4]": "[value4]",
    "[propertyId5]": "[value5]",
    "[propertyId6]": "[value6]"
  }
}
```

#### Transaction Response [describe if modifications occur or not]

```json
{
  "transactionId": "[TRANSACTION_ID_2]",
  "modifiedProperties": {}
}
```

#### Outcome Notification

```json
{
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "transactionId": "[TRANSACTION_ID_2]",
  "timestamp": "[Timestamp in ISO format]",
  "status": "COMPLETED",
  "transactionData": {
    "[propertyId1]": "[value1]",
    "[propertyId2]": "[value2]",
    "[propertyId3]": "[value3]",
    "[propertyId4]": "[value4]",
    "[propertyId5]": "[value5]",
    "[propertyId6]": "[value6]"
  },
  "modificationStatus": {
    "applied": [],
    "rejected": []
  },
  "additionalData": {
    "[additionalDataField1]": "[value1]",
    "[additionalDataField2]": "[value2]"
  }
}
```

> After this outcome notification, the campaign rules will:
> 1. [Action 1]
> 2. [Action 2]
> 3. [Action 3]
> 4. [Describe resulting state change]

### Example 3: [Describe scenario - e.g., Reward Transaction]

#### Transaction Request (Evaluation Phase)

```json
{
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "transactionId": "[TRANSACTION_ID_3]",
  "timestamp": "[Timestamp in ISO format]",
  "transactionData": {
    "[propertyId1]": "[value1]",
    "[propertyId2]": "[value2]",
    "[propertyId3]": "[value3]",
    "[propertyId4]": "[value4]",
    "[propertyId5]": "[value5]",
    "[propertyId6]": "[value6]"
  }
}
```

#### Transaction Response (with modifications)

```json
{
  "transactionId": "[TRANSACTION_ID_3]",
  "modifiedProperties": {
    "[modifiedPropertyId]": {
      "original": "[original value]",
      "modified": "[modified value]",
      "ruleSetId": "[RULE_SET_ID]",
      "ruleId": "[RULE_ID]"
    }
  }
}
```

#### Outcome Notification

```json
{
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "transactionId": "[TRANSACTION_ID_3]",
  "timestamp": "[Timestamp in ISO format]",
  "status": "COMPLETED",
  "transactionData": {
    "[propertyId1]": "[value1]",
    "[propertyId2]": "[value2]",
    "[propertyId3]": "[value3]",
    "[propertyId4]": "[value4]",
    "[propertyId5]": "[modified value]",
    "[propertyId6]": "[value6]"
  },
  "modificationStatus": {
    "applied": ["[modifiedPropertyId]"],
    "rejected": []
  },
  "additionalData": {
    "[additionalDataField1]": "[value1]",
    "[additionalDataField2]": "[value2]"
  }
}
```

> After this outcome notification, the campaign rules will:
> 1. [Action 1]
> 2. [Action 2]
> 3. [Describe resulting state change]

## Scenario Examples

### Scenario 1: [Special Scenario 1]

#### Transaction Request (Evaluation Phase)

```json
{
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "transactionId": "[TRANSACTION_ID_SCENARIO_1]",
  "timestamp": "[Timestamp in ISO format]",
  "transactionData": {
    "[propertyId1]": "[value1]",
    "[propertyId2]": "[value2]",
    "[propertyId3]": "[value3]",
    "[propertyId4]": "[value4]",
    "[propertyId5]": "[value5]",
    "[propertyId6]": "[value6]"
  }
}
```

#### Transaction Response [describe response]

```json
{
  "transactionId": "[TRANSACTION_ID_SCENARIO_1]",
  "modifiedProperties": {}
}
```

#### Outcome Notification

```json
{
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "transactionId": "[TRANSACTION_ID_SCENARIO_1]",
  "timestamp": "[Timestamp in ISO format]",
  "status": "COMPLETED",
  "transactionData": {
    "[propertyId1]": "[value1]",
    "[propertyId2]": "[value2]",
    "[propertyId3]": "[value3]",
    "[propertyId4]": "[value4]",
    "[propertyId5]": "[value5]",
    "[propertyId6]": "[value6]"
  },
  "modificationStatus": {
    "applied": [],
    "rejected": []
  },
  "additionalData": {
    "[additionalDataField1]": "[value1]",
    "[additionalDataField2]": "[value2]"
  }
}
```

> When processing this outcome notification, the campaign rules will:
> 1. [Action 1]
> 2. [Action 2]
> 3. [Action 3]
> 4. [Action 4]
> 5. [Describe resulting state change]

### Scenario 2: [Special Scenario 2]

#### Transaction Request (Evaluation Phase)

```json
{
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "transactionId": "[TRANSACTION_ID_SCENARIO_2]",
  "timestamp": "[Timestamp in ISO format]",
  "transactionData": {
    "[propertyId1]": "[value1]",
    "[propertyId2]": "[value2-special]", // Note special value
    "[propertyId3]": "[value3]",
    "[propertyId4]": "[value4]",
    "[propertyId5]": "[value5]",
    "[propertyId6]": "[value6]"
  }
}
```

#### Transaction Response [describe response]

```json
{
  "transactionId": "[TRANSACTION_ID_SCENARIO_2]",
  "modifiedProperties": {}
}
```

#### Outcome Notification

```json
{
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "transactionId": "[TRANSACTION_ID_SCENARIO_2]",
  "timestamp": "[Timestamp in ISO format]",
  "status": "COMPLETED",
  "transactionData": {
    "[propertyId1]": "[value1]",
    "[propertyId2]": "[value2-special]",
    "[propertyId3]": "[value3]",
    "[propertyId4]": "[value4]",
    "[propertyId5]": "[value5]",
    "[propertyId6]": "[value6]"
  },
  "modificationStatus": {
    "applied": [],
    "rejected": []
  },
  "additionalData": {
    "[additionalDataField1]": "[value1]",
    "[additionalDataField2]": "[value2]"
  }
}
```

> [Describe system behavior for this special scenario]

### Scenario 3: [Failure Scenario]

#### Transaction Request (Evaluation Phase)

```json
{
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "transactionId": "[TRANSACTION_ID_SCENARIO_3]",
  "timestamp": "[Timestamp in ISO format]",
  "transactionData": {
    "[propertyId1]": "[value1]",
    "[propertyId2]": "[value2]",
    "[propertyId3]": "[value3]",
    "[propertyId4]": "[value4]",
    "[propertyId5]": "[value5]",
    "[propertyId6]": "[value6]"
  }
}
```

#### Transaction Response [describe response, e.g., with modifications]

```json
{
  "transactionId": "[TRANSACTION_ID_SCENARIO_3]",
  "modifiedProperties": {
    "[modifiedPropertyId]": {
      "original": "[original value]",
      "modified": "[modified value]",
      "ruleSetId": "[RULE_SET_ID]",
      "ruleId": "[RULE_ID]"
    }
  }
}
```

#### Outcome Notification (with failure)

```json
{
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "transactionId": "[TRANSACTION_ID_SCENARIO_3]",
  "timestamp": "[Timestamp in ISO format]",
  "status": "FAILED",
  "transactionData": {
    "[propertyId1]": "[value1]",
    "[propertyId2]": "[value2]",
    "[propertyId3]": "[value3]",
    "[propertyId4]": "[value4]",
    "[propertyId5]": "[modified value]",
    "[propertyId6]": "[value6]"
  },
  "modificationStatus": {
    "applied": ["[modifiedPropertyId]"],
    "rejected": []
  },
  "additionalData": {
    "failureReason": "[FAILURE_REASON]",
    "[additionalDataField1]": "[value1]",
    "[additionalDataField2]": "[value2]"
  }
}
```

> [Describe system behavior for failure scenario]

## Implementation Notes

1. **Two-Phase Processing**:
   - [Implementation note about two-phase processing]
   - [Additional detail]
   - [Additional detail]

2. **Transaction Integrity**:
   - [Implementation note about transaction integrity]
   - [Additional detail]
   - [Additional detail]

3. **State Management**:
   - [Implementation note about state management]
   - [Additional detail]
   - [Additional detail]

4. **Field Descriptions**:
   - `status`: [Describe status field options and usage]
   - `modificationStatus`: [Describe modification status field usage]
   - `additionalData`: [Describe additional data field usage]

5. **Entity Property Usage**:
   - **[propertyId1]**: [Explain how this property is used in transactions]
   - **[propertyId2]**: [Explain how this property is used in transactions]
   - **[propertyId3]**: [Explain how this property is used in transactions]

## Related Documents

- [[Campaign Description]]
- [[Entity Specification Review]]
- [[Campaign Rule Set Definition]]
- [[Campaign Implementation Guide]]
- [[Entity Integration Documentation]] (external reference to existing entity documentation)

## Approvals

| Role | Name | Date | Signature |
|-----------------|------|------|-----------|
| Product Owner |  |  |  |
| Technical Lead |  |  |  |
| QA Lead |  |  |  |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0.0   | [[YYYY-MM-DD]] | [[Author Name]] | Initial version of the transaction examples. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->