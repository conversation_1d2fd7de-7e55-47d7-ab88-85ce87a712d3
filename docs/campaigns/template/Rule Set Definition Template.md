---
title: Rule Set Definition Template
classification: Internal
created: 2025-04-10
updated: 2025-04-10
authors:
  - "[[Campaign Team]]"
version: 1.0.0
next-review: 2025-10-10
category: "[[Templates]]"
tags:
  - template
  - campaign
  - rule-set
topics:
  - "[[Campaign Management]]"
  - "[[Rule Logic]]"
  - "[[RuleForge]]"
---

# [Campaign Name] Rule Set Definition

This document provides the rule set definition for the [Campaign Name] campaign. The definition follows Schema v6.0.0 format and implements the two-phase rule execution model to properly separate property modification from outcome processing. It leverages the pre-integrated [Entity Name] entity.

## Document Specification

### Purpose

To define the complete set of rules that implement the [Campaign Name] business logic within the RuleForge Property Modification Framework, building on the existing [Entity Name] entity.

### Scope

This document covers the rule set configuration, persistent variables, local variables, and all rules required to implement the [describe key campaign mechanics] for the campaign, using the two-phase rule execution model. It does NOT redefine the entity structure but rather defines how the campaign will use it.

### Target Audience

- RuleForge implementation team
- Campaign administrators
- QA and testing team

## Table of Contents

- [[#Entity Reference]]
- [[#Rule Set JSON]]
- [[#Key Rules]]
- [[#Implementation Notes]]

## Entity Reference

This rule set operates on the pre-integrated [Entity Name] entity with the following characteristics:

**Entity ID:** [UUID of the entity]  
**Context ID:** [CONTEXT_ID used by this campaign]

For full details on the entity properties and structure, refer to the [[Entity Specification Review]] document. This rule set leverages the following key aspects of the entity:

- **Mutable Properties:** [List key mutable properties used by this campaign]
- **Targeting Properties:** [List key properties used for rule targeting]
- **Collection Key:** [Identify the property used as collection key]

## Rule Set JSON

```json
{
  "schemaVersion": "6.0.0",
  "ruleSetId": "[RULE_SET_ID]",
  "name": "[Campaign Name]",
  "description": "[Brief description of the campaign]",
  "entityId": "[Entity UUID]",
  "contextId": "[CONTEXT_ID]",
  "version": 1,
  "status": "ACTIVE",
  "category": "[CATEGORY]",
  "startDateTime": "[Start date in ISO format]",
  "endDateTime": "[End date in ISO format]",
  "lastModifiedDateTime": "[Last modified date in ISO format]",
  "collectionMappings": [
    {
      "collectionId": "[collectionId]",
      "name": "[Collection Name]",
      "imported": [boolean],
      "keyMapping": {
        "propertyId": "[propertyId]"
      }
    }
  ],
  "persistentVariables": [
    {
      "variableId": "[persistentVar1]",
      "name": "[Persistent Variable 1 Name]",
      "description": "[Description of persistent variable 1]",
      "type": "[data type]",
      "defaultValue": "[default value]",
      "collectionId": "[collectionId]"
    },
    {
      "variableId": "[persistentVar2]",
      "name": "[Persistent Variable 2 Name]",
      "description": "[Description of persistent variable 2]",
      "type": "[data type]",
      "defaultValue": "[default value]",
      "collectionId": "[collectionId]"
    }
    // Add additional persistent variables as needed
  ],
  "localVariables": [
    {
      "variableId": "[localVar1]",
      "name": "[Local Variable 1 Name]",
      "description": "[Description of local variable 1]",
      "type": "[data type]",
      "defaultValue": "[default value]"
    },
    {
      "variableId": "[localVar2]",
      "name": "[Local Variable 2 Name]",
      "description": "[Description of local variable 2]",
      "type": "[data type]",
      "defaultValue": "[default value]"
    }
    // Add additional local variables as needed
  ],
  "evaluationRules": [
    {
      "ruleId": "[EVALUATION_RULE_1]",
      "name": "[Evaluation Rule 1 Name]",
      "description": "[Description of evaluation rule 1]",
      "priority": 1,
      "condition": {
        // Define condition object based on your logic
      },
      "variableAssignments": [
        // Define variable assignments based on your logic
      ]
    }
    // Add additional evaluation rules as needed
  ],
  "outcomeRules": [
    {
      "ruleId": "[OUTCOME_RULE_1]",
      "name": "[Outcome Rule 1 Name]",
      "description": "[Description of outcome rule 1]",
      "priority": 1,
      "condition": {
        // Define condition object based on your logic
      },
      "variableAssignments": [
        // Define variable assignments based on your logic
      ]
    }
    // Add additional outcome rules as needed
  ]
}
```

## Key Rules

### Evaluation Rules

1. **[EVALUATION_RULE_1]**: [Describe the purpose and function of this rule]
2. **[EVALUATION_RULE_2]**: [Describe the purpose and function of this rule]
3. **[EVALUATION_RULE_3]**: [Describe the purpose and function of this rule]
4. **[EVALUATION_RULE_4]**: [Describe the purpose and function of this rule]

### Outcome Rules

1. **[OUTCOME_RULE_1]**: [Describe the purpose and function of this rule]
2. **[OUTCOME_RULE_2]**: [Describe the purpose and function of this rule]
3. **[OUTCOME_RULE_3]**: [Describe the purpose and function of this rule]
4. **[OUTCOME_RULE_4]**: [Describe the purpose and function of this rule]
5. **[OUTCOME_RULE_5]**: [Describe the purpose and function of this rule]
6. **[OUTCOME_RULE_6]**: [Describe the purpose and function of this rule]

## Persistent Variables

### Collection-Based Variables

These variables are stored per [collection key] (e.g., per customer, per agent, per store):

1. **[persistentVar1]**
   - **Purpose**: [Describe what this variable tracks and why]
   - **Lifecycle**: [Describe when/how the variable is reset or updated]
   - **Usage**: [Describe which rules use this variable and how]

2. **[persistentVar2]**
   - **Purpose**: [Describe what this variable tracks and why]
   - **Lifecycle**: [Describe when/how the variable is reset or updated]
   - **Usage**: [Describe which rules use this variable and how]

### Global Variables

These variables apply across all transactions (not tied to a specific collection):

1. **[persistentVar3]**
   - **Purpose**: [Describe what this variable tracks and why]
   - **Lifecycle**: [Describe when/how the variable is reset or updated]
   - **Usage**: [Describe which rules use this variable and how]

## Implementation Notes

### Two-Phase Rule Execution

This implementation leverages the RuleForge two-phase rule execution model, optimized for performance:

1. **Evaluation Phase** (evaluationRules):
   - Streamlined for synchronous, real-time execution
   - Only includes essential rules for [describe core evaluation phase functionality]
   - No [describe skipped checks] in the evaluation phase, as these are only needed for [describe why]
   - Only evaluates [describe key evaluation criteria]
   - Prioritizes fast response time for real-time transactions
   - Modifies the following entity properties: [list mutable properties modified]

2. **Outcome Phase** (outcomeRules):
   - Designed for asynchronous processing
   - Includes all logic for [describe core outcome phase functionality]
   - Handles the more complex business rules for [describe complex logic]
   - Manages [describe state management functionality]
   - Focuses on ensuring data integrity rather than real-time performance
   - Updates the following persistent variables: [list variables updated]

### Key Benefits of This Approach

1. **Optimized Performance**: [Describe performance benefit]
2. **[Benefit 2]**: [Describe benefit 2]
3. **Transaction Integrity**: [Describe integrity benefit]
4. **[Benefit 4]**: [Describe benefit 4]
5. **Clean Separation**: [Describe separation benefit]

### Implementation Considerations

- [Check/functionality 1] checks are only in the [phase] phase because [rationale]
- [Check/functionality 2] checks are in both phases: in evaluation to [purpose] and in outcome for [purpose]
- The [functionality 3] logic is in the [phase] phase to ensure [rationale]
- [State/status 1] is determined in the [phase] phase but applied in the [phase] phase
- Rules in both phases use the same persistent variables to maintain consistent state
- [Describe key business rule implementation detail]

### Entity Property Usage

| Property ID | Usage in Evaluation Phase | Usage in Outcome Phase |
|-------------|---------------------------|------------------------|
| [propertyId1] | [How used in evaluation] | [How used in outcome] |
| [propertyId2] | [How used in evaluation] | [How used in outcome] |
| [propertyId3] | [How used in evaluation] | [How used in outcome] |
| [propertyId4] | [How used in evaluation] | [How used in outcome] |

## Related Documents

- [[Campaign Description]]
- [[Entity Specification Review]]
- [[Campaign Transaction Examples]]
- [[Campaign Implementation Guide]]

## Approvals

| Role | Name | Date | Signature |
|-----------------|------|------|-----------|
| Product Owner |  |  |  |
| Technical Lead |  |  |  |
| QA Lead |  |  |  |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0.0   | [[YYYY-MM-DD]] | [[Author Name]] | Initial version of the rule set definition. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->