---
title: Campaign One Rule Set Definition
classification: Internal
created: 2025-04-07
updated: 2025-05-12
authors:
- "[[<PERSON>]]"
- Bushra Gul
version: 3.0.0
next-review: 2025-08-31
category: "[[Campaigns]]"
tags:
  - specification
  - campaign
  - rule-set
topics:
  - "[[Campaign One]]"
  - "[[SmartShop]]"
  - "[[RuleForge]]"
  - "[[Rule Logic]]"
---

# Campaign One Rule Set Definition

This document provides the rule set definition for the Lomé Agent Bundle Sales Incentive campaign. The definition follows Schema v6.0.0 format and implements the two-phase rule execution model to properly separate property modification from outcome processing. It leverages the pre-integrated SmartShop entity.

## Document Specification

### Purpose

To define the complete set of rules that implement the Lomé Agent Bundle Sales Incentive campaign business logic within the RuleForge Property Modification Framework, building on the existing SmartShop entity.

### Scope

This document covers the rule set configuration, persistent variables, local variables, and all rules required to implement the daily sales target and discount mechanics for the campaign, using the two-phase rule execution model. It does NOT redefine the entity structure but rather defines how the campaign will use it.

### Target Audience

- RuleForge implementation team
- Campaign administrators
- QA and testing team

## Table of Contents

- [[#Entity Reference]]
- [[#Rule Set JSON]]
- [[#Key Rules]]
- [[#Implementation Notes]]

## Entity Reference

This rule set operates on the pre-integrated SmartShop entity with the following characteristics:

**Entity ID:** 550e8400-e29b-41d4-a716-446655440000  
**Context ID:** BUNDLE_SELL

For full details on the entity properties and structure, refer to the [[SmartShop Entity Registration]] and [[Campaign One Entity Specification Review]] documents. This rule set leverages the following key aspects of the entity:

- **Mutable Properties:** agentPurchasePrice
- **Targeting Properties:** agentMsisdn, agentCgi, bundleRetailPrice
- **Collection Key:** agentMsisdn

## Rule Set JSON

```json
{
  "schemaVersion": "7.0.0",
  "ruleSetId": "LOME_BUNDLE_SALES_JUN2025",
  "name": "Lomé Agent Bundle Sales Incentive - June 2025",
  "description": "Daily sales target incentive program for Lomé region agents with bonus discount rewards",
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "version": 1,
  "status": "ACTIVE",
  "category": "PRICING",
  "startDateTime": "2025-06-01T00:00:00Z",
  "endDateTime": "2025-06-30T23:59:59Z",
  "lastModifiedDateTime": "2025-05-12T12:00:00Z",
  "collectionMappings": [
    {
      "collectionId": "agentDailySales",
      "name": "Agent Daily Sales",
      "keyMapping": {
        "propertyId": "agentMsisdn"
      }
    }
  ],
  "persistentVariables": [
    {
      "variableId": "dailyBundleCount",
      "name": "Daily Bundle Sales Count",
      "description": "Number of qualifying bundles successfully sold by agent on current transaction date",
      "type": "number",
      "defaultValue": 0,
      "collectionId": "agentDailySales"
    },
    {
      "variableId": "lastTransactionDay",
      "name": "Last Transaction Day",
      "description": "Day of the agent's last transaction in YYYY-MM-DD format",
      "type": "string",
      "defaultValue": "1970-01-01T00:00:00Z",
      "collectionId": "agentDailySales"
    },
    {
      "variableId": "targetAchievedDay",
      "name": "Target Achieved Day",
      "description": "Stores the day (YYYY-MM-DD) when an agent last reached their daily sales target of 30 bundles. When this matches the current transaction day, the 2% discount is applied.",
      "type": "date",
      "defaultValue": "1970-01-01T00:00:00Z",
      "collectionId": "agentDailySales"
    },
    {
      "variableId": "campaignCosts",
      "name": "Campaign Costs",
      "description": "Tracks the total cost of additional 2% discounts granted by the campaign, calculated as the sum of all discount amounts applied when agents reach their daily targets.",
      "type": "number",
      "defaultValue": 0,
      "collectionId": "agentDailySales"
    }
  ],
  "localVariables": [
    {
      "variableId": "calculatedDiscount",
      "name": "Calculated Discount",
      "description": "The calculated 2% discount amount for the current transaction",
      "type": "number",
      "defaultValue": 0
    },
    {
      "variableId": "currentDay",
      "name": "Current Day",
      "description": "The current transaction day in YYYY-MM-DD format",
      "type": "string",
      "defaultValue": "1970-01-01T00:00:00Z"
    },
    {
      "variableId": "isLomeArea",
      "name": "Is Lomé Area",
      "description": "Flag indicating if the agent is in the Lomé area",
      "type": "boolean",
      "defaultValue": false
    },
    {
      "variableId": "currentDayOfWeek",
      "name": "Current Day of Week",
      "description": "The day of week for the current transaction",
      "type": "string",
      "defaultValue": "NONE"
    },
    {
      "variableId": "isWeekday",
      "name": "Is Weekday",
      "description": "Flag indicating if the current transaction is on a weekday",
      "type": "boolean",
      "defaultValue": false
    }
  ],
  "evaluationRules": [
    {
      "ruleId": "EXTRACT_CURRENT_DAY",
      "name": "Extract Current Day",
      "description": "Extracts the current day from the timestamp",
      "priority": 1,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "!=",
        "parameters": {
          "leftOperand": "{timestamp}",
          "rightOperand": "1970-01-01T00:00:00Z"
        }
      },
      "variableAssignments": [
        {
          "variableId": "currentDay",
          "assignmentTypeId": "SET",
          "value": { "functionId": "formatDate", "args": ["{timestamp}", "yyyy-MM-dd"] }
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "CHECK_REWARD_VALIDITY",
      "name": "Check Reward Validity",
      "description": "Clears the reward date if the current day is different",
      "priority": 2,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": "!=",
              "parameters": {
                "leftOperand": "{targetAchievedDay}",
                "rightOperand": "1970-01-01T00:00:00Z"
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "!=",
              "parameters": {
                "leftOperand": "{currentDay}",
                "rightOperand": "{targetAchievedDay}"
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "targetAchievedDay",
          "assignmentTypeId": "SET",
          "value": "1970-01-01T00:00:00Z"
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "CHECK_LOME_AREA",
      "name": "Check Lomé Area",
      "description": "Determines if the agent is in the Lomé area",
      "priority": 3,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "!=",
        "parameters": {
          "leftOperand": "{agentCgi}",
          "rightOperand": "000-000-000"
        }
      },
      "variableAssignments": [
        {
          "variableId": "isLomeArea",
          "assignmentTypeId": "SET",
          "value": false
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "SET_LOME_AREA_FLAG",
      "name": "Set Lomé Area Flag",
      "description": "Sets the Lomé area flag to true if the agent CGI is in the Lomé area list",
      "priority": 4,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "IN",
        "parameters": {
          "leftOperand": "{agentCgi}",
          "rightOperand": { "listId": "LOME_AREA_CGI_LIST" }
        }
      },
      "variableAssignments": [
        {
          "variableId": "isLomeArea",
          "assignmentTypeId": "SET",
          "value": true
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "APPLY_BONUS_DISCOUNT",
      "name": "Apply Bonus Discount",
      "description": "Applies additional 2% discount when target is reached and the agent is in the Lomé area",
      "priority": 5,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{targetAchievedDay}",
                "rightOperand": "{currentDay}"
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{isLomeArea}",
                "rightOperand": true
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "calculatedDiscount",
          "assignmentTypeId": "SET",
          "value": "{agentPurchasePrice}"
        },
        {
          "variableId": "calculatedDiscount",
          "assignmentTypeId": "MULTIPLY",
          "value": 0.02
        },
        {
          "variableId": "agentPurchasePrice",
          "assignmentTypeId": "DECREASE_BY_PERCENTAGE",
          "value": 2
        }
      ],
      "webhookCalls": []
    }
  ],
  "outcomeRules": [
    {
      "ruleId": "EXTRACT_CURRENT_DAY_OUTCOME",
      "name": "Extract Current Day for Outcome",
      "description": "Extracts the current day from the timestamp for outcome processing",
      "priority": 1,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "!=",
        "parameters": {
          "leftOperand": "{timestamp}",
          "rightOperand": "1970-01-01T00:00:00Z"
        }
      },
      "variableAssignments": [
        {
          "variableId": "currentDay",
          "assignmentTypeId": "SET",
          "value": { "functionId": "formatDate", "args": ["{timestamp}", "yyyy-MM-dd"] }
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "RESET_DAILY_COUNTER",
      "name": "Reset Daily Counter",
      "description": "Resets an agent's daily bundle count when they process their first transaction of a new day",
      "priority": 2,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": "!=",
              "parameters": {
                "leftOperand": "{lastTransactionDay}",
                "rightOperand": "1970-01-01T00:00:00Z"
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "!=",
              "parameters": {
                "leftOperand": "{currentDay}",
                "rightOperand": "{lastTransactionDay}"
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "dailyBundleCount",
          "assignmentTypeId": "SET",
          "value": 0
        },
        {
          "variableId": "lastTransactionDay",
          "assignmentTypeId": "SET",
          "value": "{currentDay}"
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "INITIALIZE_TRANSACTION_DAY",
      "name": "Initialize Transaction Day",
      "description": "Sets the current day as the last transaction day if no previous transactions",
      "priority": 3,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "==",
        "parameters": {
          "leftOperand": "{lastTransactionDay}",
          "rightOperand": "1970-01-01T00:00:00Z"
        }
      },
      "variableAssignments": [
        {
          "variableId": "lastTransactionDay",
          "assignmentTypeId": "SET",
          "value": "{currentDay}"
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "SET_WEEKDAY_FLAG_OUTCOME",
      "name": "Set Weekday Flag for Outcome",
      "description": "Sets the current day of week and initializes the weekday flag to false for outcome processing",
      "priority": 4,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "!=",
        "parameters": {
          "leftOperand": "{timestamp}",
          "rightOperand": "1970-01-01T00:00:00Z"
        }
      },
      "variableAssignments": [
        {
          "variableId": "currentDayOfWeek",
          "assignmentTypeId": "SET",
          "value": { "functionId": "dayOfWeek", "args": ["{timestamp}"] }
        },
        {
          "variableId": "isWeekday",
          "assignmentTypeId": "SET",
          "value": false
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "CHECK_IS_WEEKDAY_OUTCOME",
      "name": "Check If Weekday for Outcome",
      "description": "Sets the weekday flag to true if the current day is a weekday for outcome processing",
      "priority": 5,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "IN",
        "parameters": {
          "leftOperand": "{currentDayOfWeek}",
          "rightOperand": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"]
        }
      },
      "variableAssignments": [
        {
          "variableId": "isWeekday",
          "assignmentTypeId": "SET",
          "value": true
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "CHECK_LOME_AREA_OUTCOME",
      "name": "Check Lomé Area for Outcome",
      "description": "Determines if the agent is in the Lomé area for outcome processing",
      "priority": 6,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "!=",
        "parameters": {
          "leftOperand": "{agentCgi}",
          "rightOperand": "1970-01-01T00:00:00Z"
        }
      },
      "variableAssignments": [
        {
          "variableId": "isLomeArea",
          "assignmentTypeId": "SET",
          "value": false
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "SET_LOME_AREA_FLAG_OUTCOME",
      "name": "Set Lomé Area Flag for Outcome",
      "description": "Sets the Lomé area flag to true if the agent CGI is in the Lomé area list for outcome processing",
      "priority": 7,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "IN",
        "parameters": {
          "leftOperand": "{agentCgi}",
          "rightOperand": { "listId": "LOME_AREA_CGI_LIST" }
        }
      },
      "variableAssignments": [
        {
          "variableId": "isLomeArea",
          "assignmentTypeId": "SET",
          "value": true
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "TRACK_DISCOUNT_COSTS",
      "name": "Track Discount Costs",
      "description": "Tracks the cost of discounts applied for reporting",
      "priority": 8,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{status}",
                "rightOperand": "COMPLETED"
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "IN",
              "parameters": {
                "leftOperand": "agentPurchasePrice",
                "rightOperand": "{modificationStatus.applied}"
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "calculatedDiscount",
          "assignmentTypeId": "SET",
          "value": "{bundleRetailPrice}"
        },
        {
          "variableId": "calculatedDiscount",
          "assignmentTypeId": "MULTIPLY",
          "value": 0.02
        },
        {
          "variableId": "campaignCosts",
          "assignmentTypeId": "ADD",
          "value": "{calculatedDiscount}"
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "INCREMENT_BUNDLE_COUNT",
      "name": "Increment Bundle Count",
      "description": "Increments daily bundle count for qualifying successfully completed sales",
      "priority": 9,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{status}",
                "rightOperand": "COMPLETED"
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": ">=",
              "parameters": {
                "leftOperand": "{bundleRetailPrice}",
                "rightOperand": 500
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{isLomeArea}",
                "rightOperand": true
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{isWeekday}",
                "rightOperand": true
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "dailyBundleCount",
          "assignmentTypeId": "ADD",
          "value": 1
        }
      ],
      "webhookCalls": []
    },
    {
      "ruleId": "CHECK_TARGET_REACHED",
      "name": "Check Target Reached",
      "description": "Checks if daily target is reached and sets rewards flag",
      "priority": 10,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": ">=",
              "parameters": {
                "leftOperand": "{dailyBundleCount}",
                "rightOperand": 30
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{targetAchievedDay}",
                "rightOperand": "1970-01-01T00:00:00Z"
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "targetAchievedDay",
          "assignmentTypeId": "SET",
          "value": "{currentDay}"
        }
      ],
      "webhookCalls": []
    }
  ]
}
```

## Key Rules

### Evaluation Rules

1. **EXTRACT_CURRENT_DAY**: Extracts the current day in YYYY-MM-DD format from the timestamp
2. **CHECK_REWARD_VALIDITY**: Clears the reward date when the day changes
3. **CHECK_LOME_AREA** and **SET_LOME_AREA_FLAG**: Check if the agent is in the Lomé region
4. **APPLY_BONUS_DISCOUNT**: Applies the 2% discount to transactions if target has been achieved that day and the agent is in the Lomé area

### Outcome Rules

1. **EXTRACT_CURRENT_DAY_OUTCOME**: Extracts the day in YYYY-MM-DD format for outcome processing
2. **RESET_DAILY_COUNTER**: Resets an agent's daily bundle count when they process their first transaction of a new day
3. **INITIALIZE_TRANSACTION_DAY**: Sets the current day for first-time transactions
4. **SET_WEEKDAY_FLAG_OUTCOME** and **CHECK_IS_WEEKDAY_OUTCOME**: Check weekday status for outcome processing
5. **CHECK_LOME_AREA_OUTCOME** and **SET_LOME_AREA_FLAG_OUTCOME**: Check location for outcome processing
6. **TRACK_DISCOUNT_COSTS**: Records discount costs for reporting and analytics
7. **INCREMENT_BUNDLE_COUNT**: Increments the daily bundle count for completed qualifying transactions
8. **CHECK_TARGET_REACHED**: Sets the target achieved flag when the agent reaches 30 bundles

## Persistent Variables

### Collection-Based Variables

These variables are stored per agent (using agentMsisdn as the collection key):

1. **dailyBundleCount**
   - **Purpose**: Tracks the number of qualifying bundles sold by each agent on the current day
   - **Lifecycle**: Reset at the start of each agent's first transaction of a new day
   - **Usage**: Used by INCREMENT_BUNDLE_COUNT and CHECK_TARGET_REACHED rules

2. **lastTransactionDay**
   - **Purpose**: Stores the date of the agent's last transaction for day change detection
   - **Lifecycle**: Updated with each transaction to the current day
   - **Usage**: Used by RESET_DAILY_COUNTER to detect day changes

3. **targetAchievedDay**
   - **Purpose**: Records the day when an agent has reached their sales target
   - **Lifecycle**: Set when target is reached, cleared when day changes
   - **Usage**: Used by APPLY_BONUS_DISCOUNT to determine discount eligibility

4. **campaignCosts**
   - **Purpose**: Tracks the total cost of discounts granted to the agent
   - **Lifecycle**: Accumulates throughout the campaign
   - **Usage**: Used by TRACK_DISCOUNT_COSTS for analytics and reporting

## Implementation Notes

### Two-Phase Rule Execution

This implementation leverages the RuleForge two-phase rule execution model, optimized for performance:

1. **Evaluation Phase** (evaluationRules):
   - Streamlined for synchronous, real-time execution
   - Only includes essential rules for determining discount eligibility
   - No weekday checks in the evaluation phase, as these are only needed for counting bundles
   - Only evaluates location and achievement status for applying discounts
   - Prioritizes fast response time for real-time transactions

2. **Outcome Phase** (outcomeRules):
   - Designed for asynchronous processing
   - Includes all logic for qualifying bundle sales (weekday, location, minimum price)
   - Handles the more complex business rules for counting bundles and tracking achievements
   - Manages counter reset logic and target achievement status
   - Focuses on ensuring data integrity rather than real-time performance

### Key Benefits of This Approach

1. **Optimized Performance**: Evaluation phase contains only essential rules for fast synchronous processing
2. **Location Enforcement**: Ensures discounts only apply within the Lomé area, even after target achievement
3. **Transaction Integrity**: Bundle counts are only incremented for successfully completed transactions
4. **Accurate Targeting**: Weekday and location requirements are enforced for bundle counting
5. **Clean Separation**: Clear distinction between real-time discount application and asynchronous tracking

### Implementation Considerations

- Weekday checks are only in the outcome phase (where bundles are counted), not in the evaluation phase
- Location checks are in both phases: in evaluation to control discount eligibility and in outcome for counting bundles
- The daily counter reset logic is in the outcome rules to ensure counts are consistent
- Target achievement status is determined in the outcome phase but applied in the evaluation phase
- Rules in both phases use the same persistent variables to maintain consistent state
- Agents will receive the discount starting from their 31st qualifying bundle of the day (after target achievement)

### Entity Property Usage

| Property ID | Usage in Evaluation Phase | Usage in Outcome Phase |
|-------------|---------------------------|------------------------|
| agentMsisdn | Collection key for persistence | Collection key for persistence |
| agentCgi | Location-based targeting | Location validation for counting |
| agentPurchasePrice | Modified with 2% discount | Not used directly |
| bundleRetailPrice | Not used | Qualification check (≥500 FCFA) |

## Related Documents

- [[Campaign One Description]]
- [[SmartShop Entity Registration]]
- [[Campaign One Entity Specification Review]]
- [[Campaign One Implementation Guide]]
- [[Campaign One Transaction Examples]]

## Approvals

| Role | Name | Date | Signature |
|-----------------|------|------|-----------|
| Product Owner | Wayne Smith |  |  |
| Technical Lead | Nikolay |  |  |
| QA Lead |  |  |  |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 3.0.0   | [[2025-04-17]] | [[Wayne Smith]] | Updated document to follow Rule Set Definition Template; reorganized content for clarity; updated campaign dates to April 28 - May 2, 2025; updated Entity Reference section with links to separated entity documentation; added detailed Persistent Variables section; enhanced Implementation Notes section. |
| 2.0.0   | [[2025-04-08]] | [[Wayne Smith]] | Restructured rule set to implement two-phase execution model with separate evaluationRules and outcomeRules; moved bundle counting to outcome phase to address Nikolay's concerns about counting only completed transactions; optimized evaluation phase by removing weekday checks. |
| 1.0.0   | [[2025-04-07]] | [[Wayne Smith]] | Initial version of the rule set definition. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->
