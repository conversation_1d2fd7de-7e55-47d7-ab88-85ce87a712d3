---
title: Campaign One Description
classification: Internal
created: 2025-04-17
updated: 2025-04-17
authors:
  - "[[<PERSON>]]"
version: 3.0.0
next-review: 2025-08-31
category: "[[Campaigns]]"
tags:
  - overview
  - description
  - business-requirement
topics:
  - "[[Campaign One]]"
  - "[[SmartShop]]"
  - "[[RuleForge]]"
  - "[[Sales Incentive]]"
---

# Campaign One Description

## Document Specification

### Purpose

To provide a business overview of the Lomé Agent Bundle Sales Incentive campaign, explaining its goals, mechanics, and success metrics, along with key technical implementation considerations for integration with SmartShop.

### Scope

This document covers the business requirements, objectives, and mechanics of Campaign One, providing context for the technical implementation documents. It focuses on the campaign itself, not the underlying entity details which are documented elsewhere.

### Target Audience

- Business stakeholders
- Implementation team
- Marketing and communications teams
- Agent support teams

## Table of Contents

- [[#Entity Reference]]
- [[#Business Overview]]
- [[#Key Mechanics]]
- [[#Business Objectives]]
- [[#Success Metrics]]
- [[#Technical Implementation Overview]]
- [[#Two-Phase Execution Model]]
- [[#Notes for Implementation]]

## Entity Reference

This campaign will be implemented using the pre-integrated SmartShop entity with transaction context BUNDLE_SELL. The entity is already registered with RuleForge and has established transaction flows.

**Entity Owner:** Mobile Commerce Team  
**Entity Documentation:** [[SmartShop Entity Registration]]

## Business Overview

The Campaign-1, also referred to as "Lomé Agent Bundle Sales Incentive" campaign runs from April 28 to May 2, 2025, and is applicable to all retailer agents in the Lomé region of Togo. The campaign implements a daily sales target mechanism that rewards agents with additional discounts upon reaching their goals.

## Key Mechanics

- **Target:** Each agent has a daily sales target to sell 30 bundles
- **Qualification:** Only bundles valued at 500 FCFA or more count toward the target
- **Reward:** Upon reaching the daily target, agents receive an additional 2% discount on all bundle sales for the remainder of the day
- **Timing:** The incentive is only applicable on weekdays (Monday to Friday)
- **Location:** Limited to agents operating in the Lomé area
- **Reward Application:** The discount applies from the 31st bundle onward (after target achievement)
- **Completion Requirements:** Only successfully completed transactions count toward the daily target

## Business Objectives

1. Stimulate higher daily sales volumes from agents
2. Increase agent activity and motivation through achievable daily targets
3. Provide immediate rewards to high-performing agents
4. Focus promotional activity on the Lomé region where competition is highest
5. Deliver trackable ROI through the discount mechanism

## Success Metrics

- Number of agents reaching daily targets
- Increase in total bundle sales during campaign period
- Overall discount costs vs. increased sales volume
- Regional performance improvement in Lomé area
- Agent satisfaction and engagement
- Percentage of agents who continue high sales volumes after reaching targets

## Technical Implementation Overview

This campaign leverages RuleForge's two-phase rule execution model with the SmartShop entity, requiring implementation of several key capabilities:

1. **Collection-based persistent variables** - To track agent sales across transactions
2. **Time-bound persistent variables** - For maintaining state with daily counter resets
3. **Date-based targeting** - To restrict the campaign to weekdays
4. **Location-based targeting** - To focus on Lomé region
5. **Property modification** - To apply dynamic discounts
6. **Achievement tracking** - To set and monitor targets
7. **Two-phase rule execution** - To separate real-time discount application from bundle counting

### Key Entity Properties Used

This campaign will leverage the following properties from the SmartShop entity:

| Property ID | Purpose in Campaign |
|-------------|---------------------|
| agentMsisdn | Used as the collection key for agent data persistence |
| agentCgi | Used for location-based targeting (Lomé region) |
| agentPurchasePrice | The mutable property that receives the 2% discount |
| bundleRetailPrice | Used to check for qualifying purchases (≥500 FCFA) |

## Two-Phase Execution Model

The campaign implementation relies on RuleForge's two-phase rule execution model:

### Evaluation Phase (Synchronous)

- Occurs during transaction initiation
- Checks if the agent has already achieved their daily target
- Verifies that the agent is currently in the Lomé area
- Applies the 2% discount to qualifying transactions
- Must be optimized for speed (under 18ms per transaction)

### Outcome Phase (Asynchronous)

- Occurs after transaction completion
- Verifies that the transaction was successfully completed
- Validates that the transaction occurred on a weekday in the Lomé area
- Checks if the bundle price qualifies (≥500 FCFA)
- Increments the agent's daily bundle count if all criteria are met
- Sets the target achieved flag when the 30-bundle threshold is reached
- Tracks campaign costs for analytics and reporting

### Business Benefits of Two-Phase Processing

1. **Transaction Integrity** - Only successful transactions count toward targets
2. **Immediate Rewards** - Agents receive discounts in real-time after achievement
3. **Accurate Tracking** - Failed transactions don't artificially inflate achievement
4. **System Performance** - Fast evaluation phase ensures smooth agent experience
5. **Complete Analytics** - Outcome phase provides comprehensive campaign metrics

## Notes for Implementation

- The SMS notification feature should be handled via a separate notification service integration
- CGI location verification requires a list of Lomé area cell towers (referenced as "LOME_AREA_CGI_LIST")
- Daily counters are reset for each agent when they make their first transaction of a new day, effectively implementing a per-agent daily counter without requiring a scheduled midnight process
- The discount calculation must be applied as a percentage reduction to preserve margin ratios
- The standard timestamp field is used for all date-related operations:
  - Extracting the current day for daily counter resets
  - Determining if the transaction occurs on a weekday
  - Tracking when an agent achieves their daily target
- Transaction status from outcome notifications determines which transactions count toward targets
- Agents should be notified via SMS when they reach their daily target

## Related Documents

- [[SmartShop Entity Registration]]
- [[Campaign One Entity Specification Review]]
- [[Campaign One Rule Set Definition]]
- [[Campaign One Implementation Guide]]
- [[Campaign One Transaction Examples]]

## Approvals

| Role | Name | Date | Signature |
|-----------------|------|------|-----------|
| Product Owner | Wayne Smith |  |  |
| Technical Lead | Nikolay |  |  |
| Marketing Lead |  |  |  |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 3.0.0   | [[2025-04-17]] | [[Wayne Smith]] | Updated document to follow Campaign Description Template; reorganized content to align with template structure; updated campaign dates to April 28 - May 2, 2025; added Entity Reference section with link to separated entity documentation; updated Related Documents section. |
| 2.0.0   | [[2025-04-08]] | [[Wayne Smith]] | Added section on two-phase execution model; updated key mechanics to clarify when discount applies; added transaction completion requirement; expanded technical implementation overview; updated metadata to follow company standards. |
| 1.0.0   | [[2025-04-07]] | [[Wayne Smith]] | Initial version of the campaign description. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->