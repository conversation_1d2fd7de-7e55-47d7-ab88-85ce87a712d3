---
title: Campaign One Transaction Examples
classification: Internal
created: 2025-04-17
updated: 2025-04-17
authors:
  - "[[<PERSON>]]"
version: 3.0.0
next-review: 2025-08-31
category: "[[Campaigns]]"
tags:
  - example
  - specification
  - integration
topics:
  - "[[Campaign One]]"
  - "[[SmartShop]]"
  - "[[RuleForge]]"
  - "[[Transaction Processing]]"
---

# Campaign One Transaction Examples

This document provides example transactions for the Lomé Agent Bundle Sales Incentive campaign, including transaction requests, responses, and outcome notifications that demonstrate the two-phase rule execution model as applied to the pre-integrated SmartShop entity.

## Document Specification

### Purpose

To illustrate the complete transaction flow for Campaign One with concrete examples that demonstrate how transaction data from the SmartShop entity is processed in both the evaluation and outcome phases.

### Scope

This document covers standard transaction request formats, expected responses when campaign-specific rules are applied, and example outcome notifications for various scenarios in the campaign. It does NOT define the entity structure but shows how campaign rules interact with it.

### Target Audience

- RuleForge implementation team
- Campaign administrators and testers
- Business stakeholders

## Table of Contents

- [[#Entity Reference]]
- [[#Two-Phase Execution Flow]]
- [[#Standard Transaction Examples]]
- [[#Scenario Examples]]
- [[#Implementation Notes]]

## Entity Reference

These examples use the pre-integrated SmartShop entity with the following characteristics:

**Entity ID:** 550e8400-e29b-41d4-a716-446655440000  
**Context ID:** BUNDLE_SELL

For full details on the entity properties and structure, refer to the [[SmartShop Entity Registration]] and [[Campaign One Entity Specification Review]] documents.

## Two-Phase Execution Flow

The Lomé Agent Bundle Sales Incentive campaign uses RuleForge's two-phase rule execution model:

1. **Evaluation Phase** (synchronous):
   - Transaction is evaluated in real-time
   - Discount is applied for eligible agents who have already reached their target
   - Agent receives immediate price modification

2. **Outcome Phase** (asynchronous):
   - Transaction outcome is reported after completion
   - Successful bundle sales are counted toward the agent's daily target
   - Target achievement status is updated for future transactions

This separation ensures transactions are only counted after confirmed completion while still providing real-time discounts.

## Standard Transaction Examples

### Example 1: Initial Bundle Sale (no target achievement yet)

#### Transaction Request (Evaluation Phase)

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "transactionId": "BDL000000001",
  "timestamp": "2025-04-28T09:15:00Z",
  "transactionData": {
    "agentMsisdn": "22890123456",
    "agentCgi": "62320-10458",
    "bundleRetailPrice": 800,
    "agentPurchasePrice": 760,
    "bundleId": "1-2",
    "subscriberMsisdn": "22891234567"
  }
}
```

#### Transaction Response (no modifications)

```json
{
  "transactionId": "BDL000000001",
  "modifiedProperties": {}
}
```

#### Outcome Notification

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "transactionId": "BDL000000001",
  "timestamp": "2025-04-28T09:16:00Z",
  "status": "COMPLETED",
  "transactionData": {
    "agentMsisdn": "22890123456",
    "agentCgi": "62320-10458",
    "bundleRetailPrice": 800,
    "agentPurchasePrice": 760,
    "bundleId": "1-2",
    "subscriberMsisdn": "22891234567"
  },
  "modificationStatus": {
    "applied": [],
    "rejected": []
  },
  "additionalData": {
    "processingTime": 150,
    "deviceType": "POS"
  }
}
```

> After this outcome notification, the campaign rules will increment the agent's daily bundle count from 0 to 1, since this was a qualifying bundle (≥500 FCFA) sold on a weekday in the Lomé area.

### Example 2: Target Achievement Transaction (30th bundle)

#### Transaction Request (Evaluation Phase)

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "transactionId": "BDL000000030",
  "timestamp": "2025-04-28T15:45:00Z",
  "transactionData": {
    "agentMsisdn": "22890123456",
    "agentCgi": "62320-10458",
    "bundleRetailPrice": 1200,
    "agentPurchasePrice": 1140,
    "bundleId": "2-3",
    "subscriberMsisdn": "22897654321"
  }
}
```

#### Transaction Response (no modifications yet)

```json
{
  "transactionId": "BDL000000030",
  "modifiedProperties": {}
}
```

#### Outcome Notification

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "transactionId": "BDL000000030",
  "timestamp": "2025-04-28T15:46:00Z",
  "status": "COMPLETED",
  "transactionData": {
    "agentMsisdn": "22890123456",
    "agentCgi": "62320-10458",
    "bundleRetailPrice": 1200,
    "agentPurchasePrice": 1140,
    "bundleId": "2-3",
    "subscriberMsisdn": "22897654321"
  },
  "modificationStatus": {
    "applied": [],
    "rejected": []
  },
  "additionalData": {
    "processingTime": 130,
    "deviceType": "POS"
  }
}
```

> After this outcome notification, the campaign rules will:
> 1. Increment the agent's daily bundle count to 30
> 2. Recognize the agent has reached their target
> 3. Set `targetAchievedDay` to the current date
> 4. The agent will now receive the 2% discount on subsequent transactions

### Example 3: Post-Target Transaction with Discount (31st bundle)

#### Transaction Request (Evaluation Phase)

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "transactionId": "BDL000000031",
  "timestamp": "2025-04-28T16:30:00Z",
  "transactionData": {
    "agentMsisdn": "22890123456",
    "agentCgi": "62320-10458",
    "bundleRetailPrice": 1000,
    "agentPurchasePrice": 950,
    "bundleId": "1-5",
    "subscriberMsisdn": "22899988777"
  }
}
```

#### Transaction Response (with discount)

```json
{
  "transactionId": "BDL000000031",
  "modifiedProperties": {
    "agentPurchasePrice": {
      "original": 950,
      "modified": 931,
      "ruleSetId": "LOME_BUNDLE_SALES_APR2025",
      "ruleId": "APPLY_BONUS_DISCOUNT"
    }
  }
}
```

#### Outcome Notification

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "transactionId": "BDL000000031",
  "timestamp": "2025-04-28T16:31:00Z",
  "status": "COMPLETED",
  "transactionData": {
    "agentMsisdn": "22890123456",
    "agentCgi": "62320-10458",
    "bundleRetailPrice": 1000,
    "agentPurchasePrice": 931,
    "bundleId": "1-5",
    "subscriberMsisdn": "22899988777"
  },
  "modificationStatus": {
    "applied": ["agentPurchasePrice"],
    "rejected": []
  },
  "additionalData": {
    "processingTime": 125,
    "deviceType": "POS"
  }
}
```

> After this outcome notification, the campaign rules will:
> 1. Increment the agent's daily bundle count to 31
> 2. Track the discount cost for analytics purposes

## Scenario Examples

### Scenario 1: Next Day Transaction (counter reset)

#### Transaction Request (Evaluation Phase)

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "transactionId": "BDL000001001",
  "timestamp": "2025-04-29T08:30:00Z",
  "transactionData": {
    "agentMsisdn": "22890123456",
    "agentCgi": "62320-10458",
    "bundleRetailPrice": 500,
    "agentPurchasePrice": 475,
    "bundleId": "1-2",
    "subscriberMsisdn": "22891234567"
  }
}
```

#### Transaction Response (no modifications)

```json
{
  "transactionId": "BDL000001001",
  "modifiedProperties": {}
}
```

#### Outcome Notification

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "transactionId": "BDL000001001",
  "timestamp": "2025-04-29T08:31:00Z",
  "status": "COMPLETED",
  "transactionData": {
    "agentMsisdn": "22890123456",
    "agentCgi": "62320-10458",
    "bundleRetailPrice": 500,
    "agentPurchasePrice": 475,
    "bundleId": "1-2",
    "subscriberMsisdn": "22891234567"
  },
  "modificationStatus": {
    "applied": [],
    "rejected": []
  },
  "additionalData": {
    "processingTime": 140,
    "deviceType": "POS"
  }
}
```

> When processing this outcome notification, the campaign rules will:
> 1. Detect this is a new day compared to the last transaction day
> 2. Reset the agent's `dailyBundleCount` to 0
> 3. Set `lastTransactionDay` to the new current day
> 4. Increment the daily bundle count to 1
> 5. The target achieved status from the previous day is already cleared during evaluation

### Scenario 2: Location Restriction (agent outside Lomé area)

#### Transaction Request (Evaluation Phase)

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "transactionId": "BDL000000032",
  "timestamp": "2025-04-28T17:00:00Z",
  "transactionData": {
    "agentMsisdn": "22890123456",
    "agentCgi": "62320-20000", // Not in Lomé area
    "bundleRetailPrice": 1000,
    "agentPurchasePrice": 950,
    "bundleId": "1-6",
    "subscriberMsisdn": "22899988777"
  }
}
```

#### Transaction Response (no discount applied)

```json
{
  "transactionId": "BDL000000032",
  "modifiedProperties": {}
}
```

#### Outcome Notification

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "transactionId": "BDL000000032",
  "timestamp": "2025-04-28T17:01:00Z",
  "status": "COMPLETED",
  "transactionData": {
    "agentMsisdn": "22890123456",
    "agentCgi": "62320-20000",
    "bundleRetailPrice": 1000,
    "agentPurchasePrice": 950,
    "bundleId": "1-6",
    "subscriberMsisdn": "22899988777"
  },
  "modificationStatus": {
    "applied": [],
    "rejected": []
  },
  "additionalData": {
    "processingTime": 135,
    "deviceType": "POS"
  }
}
```

> Although the agent has already reached their target (in Example 2), they do not receive the discount because they are not in the Lomé area. The bundle also won't be counted toward their daily target for the same reason.

### Scenario 3: Failed Transaction (no bundle counting)

#### Transaction Request (Evaluation Phase)

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "transactionId": "BDL000000033",
  "timestamp": "2025-04-28T17:15:00Z",
  "transactionData": {
    "agentMsisdn": "22890123456",
    "agentCgi": "62320-10458",
    "bundleRetailPrice": 600,
    "agentPurchasePrice": 570,
    "bundleId": "1-7",
    "subscriberMsisdn": "22899988777"
  }
}
```

#### Transaction Response (with discount)

```json
{
  "transactionId": "BDL000000033",
  "modifiedProperties": {
    "agentPurchasePrice": {
      "original": 570,
      "modified": 558.6,
      "ruleSetId": "LOME_BUNDLE_SALES_APR2025",
      "ruleId": "APPLY_BONUS_DISCOUNT"
    }
  }
}
```

#### Outcome Notification (with failure)

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "BUNDLE_SELL",
  "transactionId": "BDL000000033",
  "timestamp": "2025-04-28T17:16:00Z",
  "status": "FAILED",
  "transactionData": {
    "agentMsisdn": "22890123456",
    "agentCgi": "62320-10458",
    "bundleRetailPrice": 600,
    "agentPurchasePrice": 558.6,
    "bundleId": "1-7",
    "subscriberMsisdn": "22899988777"
  },
  "modificationStatus": {
    "applied": ["agentPurchasePrice"],
    "rejected": []
  },
  "additionalData": {
    "failureReason": "INSUFFICIENT_BALANCE",
    "processingTime": 145,
    "deviceType": "POS"
  }
}
```

> Because the transaction failed, the bundle is not counted toward the agent's daily target, and the discount cost is not tracked for analytics. This demonstrates the benefit of only counting bundles in the outcome phase after confirming successful completion.

## Implementation Notes

1. **Two-Phase Processing**:
   - The examples demonstrate how transactions flow through both evaluation and outcome phases
   - Discounts are applied immediately during evaluation based on prior achievement
   - Bundle counting and target achievement happen only after successful completion
   - This approach ensures transaction integrity while still providing real-time benefits

2. **Transaction Integrity**:
   - Failed transactions (Scenario 3) receive discounts during evaluation but aren't counted
   - Location restrictions are enforced in both phases
   - Only COMPLETED status transactions increment the bundle count
   - This ensures accurate achievement tracking and campaign metrics

3. **State Management**:
   - Daily counters reset at the start of each day (Scenario 1)
   - Target achievement status is maintained within a single day only
   - Collection-based persistence ensures agent-specific tracking across transactions
   - The lastTransactionDay variable enables per-agent daily counter resets

4. **Field Descriptions**:
   - `status`: Indicates whether the transaction completed successfully ("COMPLETED", "FAILED", "ABANDONED")
   - `modificationStatus`: Shows which property modifications were applied or rejected
   - `additionalData`: Provides extra contextual information, including failure reasons when applicable

5. **Entity Property Usage**:
   - **agentMsisdn**: Used as the collection key for persistent variables
   - **agentCgi**: Used for location validation in Lomé area
   - **bundleRetailPrice**: Used to check the 500 FCFA minimum qualification
   - **agentPurchasePrice**: The property that receives the 2% discount modification
   - **bundleId**: Used for transaction identification
   - **subscriberMsisdn**: Used for subscriber identification but not directly for campaign logic

## Related Documents

- [[Campaign One Description]]
- [[SmartShop Entity Registration]]
- [[Campaign One Entity Specification Review]]
- [[Campaign One Rule Set Definition]]
- [[Campaign One Implementation Guide]]

## Approvals

| Role | Name | Date | Signature |
|-----------------|------|------|-----------|
| Product Owner | Wayne Smith |  |  |
| Technical Lead | Nikolay |  |  |
| QA Lead |  |  |  |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 3.0.0   | [[2025-04-17]] | [[Wayne Smith]] | Updated document to follow Transaction Examples Template; reorganized content for clarity; updated campaign dates to April 2025; updated Entity Reference section with links to separated entity documentation; enhanced Implementation Notes section with detailed descriptions of entity property usage. |
| 2.0.0   | [[2025-04-08]] | [[Wayne Smith]] | Completely restructured examples to demonstrate the two-phase rule execution model; added examples showing outcome notifications and how target achievement is processed; added example for failed transactions to show how bundle counting only happens for successful completions. |
| 1.0.0   | [[2025-04-07]] | [[Wayne Smith]] | Initial version of the transaction examples. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->