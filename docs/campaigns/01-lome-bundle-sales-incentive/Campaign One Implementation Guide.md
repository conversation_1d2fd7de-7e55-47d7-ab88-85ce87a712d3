---
title: Campaign One Implementation Guide
classification: Internal
created: 2025-04-17
updated: 2025-04-17
authors:
  - "[[<PERSON>]]"
version: 3.0.0
next-review: 2025-08-31
category: "[[Campaigns]]"
tags:
  - guide
  - implementation
  - integration
topics:
  - "[[Campaign One]]"
  - "[[SmartShop]]"
  - "[[RuleForge]]"
  - "[[Transaction Processing]]"
  - "[[System Integration]]"
---

# Campaign One Implementation Guide

This guide outlines the implementation approach for the Lomé Agent Bundle Sales Incentive campaign, highlighting key components that need to be built or modified to achieve the April 25, 2025 launch deadline. It leverages the pre-integrated SmartShop entity.

## Document Specification

### Purpose

To provide a prioritized roadmap for implementing Campaign One in the RuleForge platform, ensuring all required components are developed to support the two-phase rule execution model while leveraging the existing SmartShop entity.

### Scope

This document covers implementation priorities, key components, testing scenarios, and launch preparations required to successfully deploy the Lomé Agent Bundle Sales Incentive campaign. It assumes the SmartShop entity is already integrated with RuleForge.

### Target Audience

- RuleForge development team
- Campaign implementation team
- Project managers
- QA and testing teams

## Table of Contents

- [[#Entity Reference]]
- [[#Implementation Priority]]
- [[#Key Components]]
- [[#Testing Scenarios]]
- [[#Performance Requirements]]
- [[#Integration Points]]
- [[#Entity Property Usage]]
- [[#Launch Checklist]]

## Entity Reference

This implementation will leverage the pre-integrated SmartShop entity:

**Entity ID:** 550e8400-e29b-41d4-a716-446655440000  
**Context ID:** BUNDLE_SELL

For full details on the entity properties and structure, refer to the [[SmartShop Entity Registration]] and [[Campaign One Entity Specification Review]] documents. This implementation specifically depends on the following entity characteristics:

- **Transaction Context:** BUNDLE_SELL - Represents an agent selling a bundle to a subscriber
- **Key Mutable Properties:** agentPurchasePrice
- **Key Collection Property:** agentMsisdn

## Implementation Priority

Development teams should focus on implementing components in the following order:

1. **Two-Phase Rule Execution Framework**
   - Implement separate evaluationRules and outcomeRules collections
   - Configure rules engine to process each collection at the appropriate phase
   - Develop clear transaction flow from evaluation to outcome processing

2. **Persistent Variable Framework**
   - Implement collection-based storage for agent data using agentMsisdn as key
   - Configure Redis for reliable persistence of agent counters and flags
   - Set up appropriate TTL management for campaign-duration data

3. **Evaluation Phase Rules**
   - Optimize for synchronous processing and fast response times
   - Implement location-based targeting checks
   - Build the core discount application logic

4. **Outcome Phase Rules**
   - Design for asynchronous processing
   - Implement transaction status validation
   - Create the bundle counting and target achievement tracking
   - Develop daily counter reset logic based on date comparison

5. **Collection Mapping Implementation**
   - Configure the agentDailySales collection with agentMsisdn as key
   - Ensure proper data partitioning for high-volume agent operations
   - Set up caching for frequently accessed agent data

6. **Testing Framework**
   - Develop test cases for all key scenarios
   - Create test data for agents in and outside Lomé area
   - Build test harnesses for simulating transactions across day boundaries

7. **Analytics and Reporting**
   - Configure dashboards for tracking campaign metrics
   - Implement cost tracking for discount application
   - Set up agent achievement reports

8. **SMS Notification Integration**
   - Define the integration point for SMS notifications
   - Create a callback mechanism for goal achievement events

## Key Components

### 1. Two-Phase Rule Implementation

The campaign implementation relies on RuleForge's two-phase rule execution model:

#### 1.1 Evaluation Phase

- **Purpose**: Real-time transaction evaluation and price modification
- **Characteristics**: Optimized for speed and minimal processing
- **Key Functions**:
  - Check if agent has already achieved target
  - Validate agent location for discount eligibility
  - Apply 2% discount when conditions are met

#### 1.2 Outcome Phase

- **Purpose**: Post-transaction processing and state updates
- **Characteristics**: Comprehensive processing with less time sensitivity
- **Key Functions**:
  - Validate transaction completion status
  - Check weekday, location, and price qualifications
  - Count successful qualifying transactions
  - Mark target achievement when threshold reached
  - Track campaign costs and analytics

### 2. Persistent Variables

The rule set relies on these persistent variables that must be properly stored and retrieved:

- `dailyBundleCount` - Tracks qualifying bundle sales per agent per day
- `lastTransactionDay` - Used for counter reset logic at the start of each day
- `targetAchievedDay` - Tracks which agents have earned rewards on which days
- `campaignCosts` - Tracks total costs of the additional 2% discount granted by the campaign

### 3. Collection Mapping

The collection mapping uses `agentMsisdn` as the key to track agent-specific data:

```json
{
  "collectionId": "agentDailySales",
  "name": "Agent Daily Sales",
  "imported": true,
  "keyMapping": {
    "propertyId": "agentMsisdn"
  }
}
```

This enables persistence of variables per agent across multiple transactions.

### 4. Critical Rules

#### 4.1 Evaluation Phase Rules

1. **EXTRACT_CURRENT_DAY** - Extracts the YYYY-MM-DD formatted day from timestamp
2. **CHECK_REWARD_VALIDITY** - Clears reward flag if the day has changed
3. **CHECK_LOME_AREA** and **SET_LOME_AREA_FLAG** - Validate agent's location
4. **APPLY_BONUS_DISCOUNT** - Applies the 2% discount when conditions are met

#### 4.2 Outcome Phase Rules

1. **EXTRACT_CURRENT_DAY_OUTCOME** - Extracts day for outcome processing
2. **RESET_DAILY_COUNTER** - Resets counters at the start of each agent's first transaction of the day
3. **SET_WEEKDAY_FLAG_OUTCOME** and **CHECK_IS_WEEKDAY_OUTCOME** - Check if the transaction date is a weekday
4. **INCREMENT_BUNDLE_COUNT** - Tracks qualifying bundle sales for completed transactions
5. **CHECK_TARGET_REACHED** - Determines when an agent reaches their target
6. **TRACK_DISCOUNT_COSTS** - Records campaign costs for analytics

### 5. Required Functions

These functions are needed to implement the campaign logic:

1. **formatDate** - Extracts and formats date components from a timestamp
2. **dayOfWeek** - Returns the day name from a timestamp

## Testing Scenarios

Implement tests for these key scenarios:

1. **Two-Phase Transaction Flow** - Test both evaluation and outcome phases for a complete transaction
2. **Day Boundary** - Test counter resets at midnight
3. **Target Achievement** - Test that the 30th completed qualifying bundle sets `targetAchievedDay`
4. **Discount Application** - Test that the 31st bundle transaction receives the 2% discount
5. **Failed Transactions** - Test that failed transactions don't count toward the target
6. **Location Enforcement** - Test that agents outside Lomé area don't qualify and can't get discounts
7. **Day Filtering** - Test that weekend transactions don't count
8. **Bundle Price Filtering** - Test that bundles under 500 FCFA don't count

## Performance Requirements

### Evaluation Phase (Synchronous)

- Transaction evaluation must complete in under 18ms
- System must support at least 500 transactions per second
- Redis operations should use connection pooling for efficiency
- Rule evaluation should always use short-circuit logic for logical operators
- Minimize the number of rules in this phase for optimal performance

### Outcome Phase (Asynchronous)

- Outcome processing can take up to 250ms
- System should handle outcome bursts of up to 1000 notifications per minute
- Implement queuing for high-volume periods
- Use batch processing for analytics updates when possible

## Integration Points

### 1. Transaction Evaluation API

Each transaction is first processed through the evaluation endpoint:

```
POST /evaluate
```

This synchronously applies any applicable discounts based on the agent's current target achievement status.

### 2. Transaction Outcome API

After completion, transaction outcomes are reported through:

```
POST /outcomes
```

This asynchronously processes the outcome, counts qualifying bundles, and updates target achievement status.

### 3. SMS Notification Service

When an agent reaches their daily target, an SMS notification should be sent. This requires integration with the existing SMS gateway using:

```
POST /sms/send
```

## Entity Property Usage

| Property ID | Used For | Modified By Campaign | Notes |
|-------------|----------|----------------------|-------|
| agentMsisdn | Collection key, agent identification | No | Used as the key for agent-specific persistent variables |
| agentCgi | Location targeting | No | Checked against LOME_AREA_CGI_LIST for location validation |
| bundleRetailPrice | Qualification criteria | No | Used to check if bundle price ≥ 500 FCFA |
| agentPurchasePrice | Price modification | Yes | Reduced by 2% when agent has achieved daily target |
| bundleId | Transaction identification | No | May be useful for debugging and analytics |
| subscriberMsisdn | Subscriber identification | No | Not directly used in campaign logic but part of entity |

## Launch Checklist

Before the April 25, 2025 launch, verify:

1. **Rule Set Configuration**
   - [ ] Evaluation rules correctly deployed
   - [ ] Outcome rules correctly deployed
   - [ ] Rule priority order validated
   - [ ] Two-phase execution validated

2. **Persistent Variables**
   - [ ] Variables correctly defined with appropriate scopes
   - [ ] Collection mapping properly configured
   - [ ] Redis persistence confirmed working
   - [ ] Counter reset logic validated

3. **Collection Mappings**
   - [ ] agentDailySales collection created
   - [ ] Key mapping to agentMsisdn confirmed
   - [ ] Data partitioning optimized for performance
   - [ ] Cache settings configured appropriately

4. **Performance Validation**
   - [ ] Load testing for evaluation phase (500 TPS)
   - [ ] Load testing for outcome phase (1000 notifications/minute)
   - [ ] Response time validation for evaluation (< 18ms)
   - [ ] End-to-end flow timing validated

5. **Business Validation**
   - [ ] Discount calculation accuracy verified
   - [ ] Bundle counting logic validated
   - [ ] Target achievement confirmation tested
   - [ ] Agent-specific data partitioning confirmed

6. **Analytics & Reporting**
   - [ ] Campaign dashboard created
   - [ ] Cost tracking confirmed accurate
   - [ ] Agent achievement reports functional
   - [ ] Regional performance tracking enabled

7. **Rollback Plan**
   - [ ] Rollback procedure documented
   - [ ] Backup of base configuration created
   - [ ] Rollback test performed

## Related Documents

- [[Campaign One Description]]
- [[SmartShop Entity Registration]]
- [[Campaign One Entity Specification Review]]
- [[Campaign One Rule Set Definition]]
- [[Campaign One Transaction Examples]]

## Approvals

| Role | Name | Date | Signature |
|-----------------|------|------|-----------|
| Product Owner | Wayne Smith |  |  |
| Technical Lead | Nikolay |  |  |
| QA Lead |  |  |  |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 3.0.0   | [[2025-04-17]] | [[Wayne Smith]] | Updated document to follow Implementation Guide Template; reorganized content for clarity; updated campaign dates to April 25, 2025 launch; updated Entity Reference section with links to separated entity documentation; added detailed Entity Property Usage section; enhanced Launch Checklist. |
| 2.0.0   | [[2025-04-08]] | [[Wayne Smith]] | Major revision to align with two-phase rule execution model; added detailed sections on evaluation and outcome phases; updated performance requirements with phase-specific metrics; expanded testing scenarios to cover both phases; added integration points for Transaction Evaluation and Outcome APIs. |
| 1.0.0   | [[2025-04-07]] | [[Wayne Smith]] | Initial version of the implementation guide. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->