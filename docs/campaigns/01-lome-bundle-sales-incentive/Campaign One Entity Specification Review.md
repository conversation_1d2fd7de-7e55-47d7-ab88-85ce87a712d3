---
title: Campaign One Entity Specification Review
classification: Internal
created: 2025-04-17
updated: 2025-04-17
authors:
  - "[[<PERSON>]]"
version: 1.0.0
next-review: 2025-08-31
category: "[[Campaigns]]"
tags:
  - specification
  - campaign
  - integration
topics:
  - "[[Campaign One]]"
  - "[[Entity Management]]"
  - "[[System Integration]]"
  - "[[RuleForge]]"
---

# Campaign One Entity Specification Review

This document reviews the existing entity specification for SmartShop that will be leveraged for the Campaign One (Lomé Agent Bundle Sales Incentive) campaign. It analyzes the available properties and transaction contexts to ensure they meet campaign requirements.

## Document Specification

### Purpose

To review and document the existing SmartShop entity specification and identify how its properties and transaction contexts will be used to implement Campaign One's rule set with the two-phase execution model.

### Scope

This document covers the analysis of entity properties, transaction contexts, and integration details needed to support both evaluation and outcome phases of Campaign One's functionality. It does NOT redefine the entity but rather focuses on how the campaign will leverage its existing structure.

### Target Audience

- RuleForge implementation team
- Campaign administrators and testers
- Marketing and business stakeholders

## Table of Contents

- [[#Entity Information]]
- [[#Transaction Context Analysis]]
- [[#Key Properties Analysis]]
- [[#Two-Phase Processing Considerations]]
- [[#Integration Requirements]]
- [[#Implementation Notes]]

## Entity Information

**Entity Name:** SmartShop Bundle Delivery Service  
**Entity ID:** 550e8400-e29b-41d4-a716-446655440000  
**Integration Date:** 2025-03-15  
**Entity Owner:** Mobile Commerce Team  
**Integration Documentation:** [[SmartShop Entity Registration]]

### Entity Description

The SmartShop Bundle Delivery Service is a platform that enables agents to sell customizable service bundles to subscribers. It provides transaction contexts for bundle sales with properties for agent identification, location tracking, bundle details, and pricing information.

### Available Transaction Contexts

| Context ID | Name | Description | Campaign Relevance |
|------------|------|-------------|-------------------|
| BUNDLE_SELL | Bundle Sell | Represents an agent selling a bundle to a subscriber | Primary context used for Campaign One |

## Transaction Context Analysis

This campaign will primarily use the **BUNDLE_SELL** transaction context. The following analysis focuses on this context.

### Context Workflow

The typical transaction flow for the BUNDLE_SELL context involves:

1. Agent initiates bundle sale from their device (mobile app or POS terminal)
2. Transaction evaluation request is sent to RuleForge including agent details, location, and bundle information
3. RuleForge processes applicable rules and returns any price modifications
4. The agent completes the sale with the potentially modified price
5. Transaction outcome notification is sent to RuleForge
6. RuleForge processes outcome rules for tracking and analytics

The typical bundle transaction volume is approximately 410,000 daily transactions, with peaks of around 6 transactions per second during busy periods (9:00-11:00 and 16:00-18:00 local time).

### Context Compatibility

The BUNDLE_SELL context aligns well with Campaign One requirements:

1. It contains all necessary properties for:
   - Agent identification (agentMsisdn)
   - Location targeting (agentCgi)
   - Price modification (agentPurchasePrice)
   - Bundle qualification (bundleRetailPrice)

2. The transaction frequency supports the campaign's daily target mechanism, with typical agent activity allowing for achievement of the 30-bundle target within a working day.

3. The only potential limitation is that agent location (CGI) may change throughout the day as agents move, requiring consistent checking in both evaluation and outcome phases.

## Key Properties Analysis

### Evaluation Phase Properties

These properties are critical for the synchronous evaluation phase:

| Property ID | Name | Type | Mutable | Campaign Usage |
|-------------|------|------|---------|----------------|
| agentMsisdn | Agent MSISDN | string | No | Used as collection key for agent data persistence |
| agentCgi | Agent CGI | string | No | Used for location-based targeting (Lomé region) |
| agentPurchasePrice | Agent Purchase Price | number | Yes | The target property that will receive the 2% discount |

### Outcome Phase Properties

These properties are important for the asynchronous outcome phase:

| Property ID | Name | Type | Mutable | Campaign Usage |
|-------------|------|------|---------|----------------|
| agentMsisdn | Agent MSISDN | string | No | Used to identify the agent for bundle counting |
| agentCgi | Agent CGI | string | No | Used to verify location for qualifying bundle counts |
| bundleRetailPrice | Bundle Retail Price | number | No | Used to check for qualifying purchases (≥500 FCFA) |
| bundleId | Bundle ID | string | No | Used for analytics and potential future targeting |

### Property Constraints Analysis

Reserved for future use.

### Collection Mapping Opportunities

Based on the available properties, the following collection mapping options are identified:

| Potential Collection ID | Key Property | Usage in Campaign |
|-------------------------|--------------|-------------------|
| agentDailySales | agentMsisdn | Store daily bundle count, target achievement status, and campaign costs per agent |

## Two-Phase Processing Considerations

The entity specification supports RuleForge's two-phase rule execution model with the following considerations:

### Evaluation Phase

- Available mutable properties: Only agentPurchasePrice can be modified for the campaign
- Performance considerations: Property modification must be fast (<18ms) to ensure smooth agent experience
- Property modification constraints: agentPurchasePrice must respect the minimum value constraint (0)
- Targeting capabilities: agentCgi provides location targeting for Lomé area verification

### Outcome Phase

- Transaction status information: Available in outcome notifications (COMPLETED, FAILED, ABANDONED)
- Properties useful for qualification: bundleRetailPrice for minimum value check, agentCgi for location
- Tracking opportunities: Can track daily bundle counts, target achievement, and discount costs
- Analytics potential: Can analyze regional performance, agent achievement rates, and ROI metrics

## Integration Requirements

### API Integration

The campaign will use the following existing API endpoints:

1. **Transaction Evaluation API**
   - Endpoint: `POST /evaluate`
   - Required parameters:
     - `entityId`: "550e8400-e29b-41d4-a716-446655440000"
     - `contextId`: "BUNDLE_SELL"
     - `transactionId`: Unique identifier for each transaction

2. **Transaction Outcome API**
   - Endpoint: `POST /outcomes`
   - Required parameters:
     - `entityId`: "550e8400-e29b-41d4-a716-446655440000"
     - `contextId`: "BUNDLE_SELL"
     - `transactionId`: Matching ID from evaluation request

### Additional Integration Points

1. **SMS Notification System**
   - Required to notify agents when they achieve their daily target
   - Integration endpoint: `POST /sms/send`
   - Will be triggered when targetAchievedDay is set to current day

2. **Analytics Dashboard**
   - Campaign performance data should be exposed via the standard analytics API
   - Key metrics: daily target achievements, discount costs, qualifying transactions

## Implementation Notes

- Standard transaction `timestamp` field will be used for:
  - Extracting the date component for daily counter resets
  - Determining if the transaction occurs on a weekday
  - Tracking when an agent achieves their daily target

- Transaction outcome status will be used to:
  - Filter for only successfully completed transactions (status = "COMPLETED")
  - Exclude failed or abandoned transactions from bundle counting
  - Ensure accurate target achievement tracking

- Location-based targeting requires:
  - Creation of LOME_AREA_CGI_LIST before implementation
  - Consistent verification in both evaluation and outcome phases
  - Monitoring for potential new cell towers added in the Lomé area during the campaign

### Potential Limitations and Mitigations

| Limitation | Impact | Mitigation Plan |
|------------|--------|-----------------|
| Agent location may change during the day | Location-based targeting accuracy | Check location in both phases to ensure consistent enforcement |
| No built-in counter reset at midnight | Daily target reset complexity | Implement agent-specific day change detection logic |

## Related Documents

- [[Campaign One Description]]
- [[Campaign One Rule Set Definition]]
- [[Campaign One Implementation Guide]]
- [[Campaign One Transaction Examples]]
- [[SmartShop Entity Registration]]

## Approvals

| Role | Name | Date | Signature |
|-----------------|------|------|-----------|
| Campaign Owner | Wayne Smith |  |  |
| Technical Lead | Nikolay |  |  |
| Entity Integration Lead |  |  |  |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0.0   | [[2025-04-17]] | [[Wayne Smith]] | Created Entity Specification Review following template, analyzing SmartShop entity for Campaign One implementation. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->