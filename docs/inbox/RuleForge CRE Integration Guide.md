---
title: RuleForge CRE Integration Guide
classification: Confidential
created: 2024-08-27
updated: 2024-08-28
tags:
  - ruleforge
  - integration
  - api
  - campaign-management
---

# RuleForge CRE Integration Guide

## Document Specification

### Purpose
This document provides a comprehensive overview of the RuleForge Campaign Rules Engine (CRE) system and serves as the primary reference for developers integrating with the platform. It aims to explain key concepts, outline the relationships between different components, and guide users through the integration process.

### Scope
This document covers:
- Introduction to RuleForge CRE and its capabilities
- Key concepts and system architecture
- Integration process overview
- API usage and JSON structures
- Best practices for integration
- Troubleshooting and support resources

It does not cover:
- Detailed API specifications (covered in separate API documentation)
- In-depth explanations of algorithms or internal system operations
- GUI usage instructions for non-technical users

### Target Audience
- Software developers integrating client systems with RuleForge CRE
- System architects designing solutions that incorporate RuleForge CRE
- Technical project managers overseeing RuleForge CRE implementations
- QA engineers testing RuleForge CRE integrations

## Introduction

RuleForge CRE is a sophisticated, standalone system designed for creating, managing, and executing dynamic marketing campaigns in real-time. It addresses the challenge of delivering personalized, context-aware marketing in the telecom industry by enabling non-technical users to implement complex campaign strategies through an intuitive GUI and integrating with existing systems via a flexible API.

## Key Concepts

### Campaigns
A campaign is the top-level container for a set of marketing rules. It includes:
- Basic information (campaignId, name, description, start/end dates)
- Persistent and local variable definitions
- Entities and their transaction contexts
- Rules associated with specific transaction contexts

### Entities
Entities represent different systems or channels that interact with RuleForge CRE. Each entity can have multiple transaction contexts. Examples might include a point-of-sale system, a website, or a mobile app.

### Transaction Contexts
A transaction context represents a specific type of interaction within an entity. It defines:
- The properties available for rule evaluation
- The actions that can be triggered

### Rules
Rules are the core of the RuleForge CRE system. Each rule consists of:
- Conditions: Criteria that must be met for the rule to trigger
- Actions: Operations to perform when the rule is triggered
- Variable Operations: Updates to persistent or local variables

### Variables
- Persistent Variables: Maintain state across multiple transactions, associated with a specific key
- Local Variables: Scoped to a single campaign execution

## System Architecture

RuleForge CRE follows a multi-tiered architecture:

1. Client Systems (Calling Entities) send transaction data to RuleForge CRE.
2. RuleForge CRE API handles incoming requests and manages system interactions.
3. Rule Evaluation Engine processes transactions against defined rules.
4. Dedicated GUI allows non-technical users to create and manage campaigns.

## Integration Process

### Entity Registration
Before interacting with RuleForge CRE, each client system must register as an entity:

1. Define your entity's transaction contexts, properties (including those marked as persistentKey), and available actions.
2. Use the Entity Registration API to register your entity.

For detailed instructions, refer to the [[Entity Integration Schema]] document.

### Campaign Creation
Campaigns are created and managed through the RuleForge CRE API:

1. Design your campaign structure, including persistent variables, local variables, entities, transaction contexts, and rules.
2. Use the Campaign Management API endpoints to create and manage campaigns.

For more information, see the [[Rule Set Schema]] document.

### Transaction Evaluation
When a transaction occurs in your system:

1. Prepare the transaction data according to the registered transaction context.
2. Send the data to the Rule Evaluation API endpoint.
3. Receive and process the returned actions.

## Using the API and JSON Structures

### API Overview
The RuleForge CRE API provides endpoints for:
- Entity registration and management
- Campaign creation and management
- Rule evaluation

All API interactions use JSON for request and response bodies. For full API details, consult the [[RuleForge Interface Overview]].

### JSON Structures
RuleForge CRE uses several JSON structures for different purposes:

1. Entity Registration JSON: Used when registering a new entity with the system.
   See [[Entity Integration Schema]] for details.

2. Campaign Definition JSON: Used when creating or updating campaigns.
   For a comprehensive guide, see the [[Rule Set Schema]].

3. Rule Evaluation Request/Response JSON: Used when sending transactions for evaluation and receiving results.
   See [[Rule Evaluation JSON Structure]] for details.

## Best Practices

- Design your campaigns with reusability in mind.
- Use meaningful names for variables, rules, and actions.
- Carefully consider which properties should be marked as persistentKey in entity registration.
- Test your rules thoroughly before deploying to production.
- Monitor campaign performance and adjust rules as needed.

For more best practices, see [[Best Practices for Campaign Design]].

## Troubleshooting and Support

- Check the [[RuleForge CRE FAQ]] for common issues and solutions.
- Review the [[RuleForge CRE Error Code Reference]] for specific error messages.
- For additional support, contact our support <NAME_EMAIL>.

## Related Documents
- [[RuleForge Interface Overview]]
- [[Entity Integration Schema]]
- [[Rule Set Schema]]
- [[Rule Evaluation JSON Structure]]
- [[Transaction Evaluation API Guide]]
- [[Campaign Management API Guide]]
- [[RuleForge CRE FAQ]]
- [[RuleForge CRE Error Code Reference]]
- [[Best Practices for Campaign Design]]

## Version Control
Document Version: 1.1.0
Last Updated: [[2024-08-28]]
Author: [[RuleForge DevGenius]]
Next Review Date: [[2025-02-28]]

<!--
### Changelog:
* 1.1.0 (2024-08-28): Updated to align with latest Entity Registration and Campaign Definition JSON structures.
* 1.0.1 (2024-08-27): Restructured document to align with updated template format.
* 1.0.0 (2024-08-27): Initial version of the RuleForge CRE Integration Guide.
-->

## Approvals
| Role/Department | Name | Date | Signature |
|-----------------|------|------|-----------|
| Lead Architect  |      |      |           |
| API Team Lead   |      |      |           |
| Documentation Manager |      |      |           |

---
© 2024 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Document Template v1.6. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->