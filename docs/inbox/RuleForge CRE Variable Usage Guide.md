---
title: RuleForge CRE Variable Usage Guide
classification: Confidential
created: 2024-08-27
updated: 2024-08-29
tags:
  - variables
  - persistent
  - local
  - guide
  - ruleforge
---

# RuleForge CRE Variable Usage Guide

## Document Specification

### Purpose

This document provides comprehensive guidance on using variables in the RuleForge Campaign Rules Engine (CRE). It covers both persistent and local variables, explaining their purposes, usage, and best practices.

### Scope

This guide covers:

- Overview of variable types in RuleForge CRE
- Defining and using persistent variables
- Working with local variables
- Variable scopes and lifetimes
- Best practices for variable usage
- Common use cases and examples

This guide does not cover:

- Detailed internal implementation of variable storage
- Performance optimization techniques for variable usage
- GUI-specific instructions for variable management

### Target Audience

- Campaign designers creating rules in RuleForge CRE
- Developers implementing RuleForge CRE integrations
- System architects designing campaign structures
- QA engineers testing campaign behavior

## 1. Introduction to Variables in RuleForge CRE

Variables in RuleForge CRE allow for dynamic data storage and manipulation within campaigns and rules. They are crucial for creating sophisticated, stateful campaigns that can adapt to changing conditions and user behaviors.

## 2. Types of Variables

### 2.1 Persistent Variables

- Maintain state across multiple transactions or campaign executions
- Stored in the database and persist between rule evaluations
- Used for long-term tracking of user behavior or campaign progress
- Associated with specific persistent data collections

### 2.2 Local Variables

- Temporary variables that exist only during a single rule evaluation
- Used for intermediate calculations or temporary data storage
- Do not persist between different rule evaluations

## 3. Entity Registration and Persistent Collections

### 3.1 Entity Registration Process

1. Entities register with the RuleForge CRE system via the RuleForge API.
2. The entity registration JSON includes definitions for `transactionContexts` and their `properties`, some of which may be marked as persistent collections.

### 3.2 GUI Integration

1. The RuleForge GUI extracts the entity registration JSON from the API.
2. Based on this information, the GUI determines which persistent variable collections are available for use in campaigns.

### 3.3 Campaign Creation in GUI

1. Users define and use custom persistent variables within the GUI.
2. These variables must be associated with the persistent collections defined in the entity registration.

### 3.4 Rules Engine Execution

1. When a campaign is created or updated, the persistent variable definitions are sent to the rules engine.
2. During transaction evaluation, the rules engine executes rules using these persistent variables, storing and retrieving data from the appropriate collections.

This workflow ensures that campaigns only use valid persistent collections and that the rules engine has the necessary information to manage persistent data effectively.

## 4. Understanding Persistent Variables and Collections

### 4.1 Persistent Variables Overview

Persistent variables are a powerful feature of RuleForge CRE that allow campaigns to maintain state over time. They enable the system to remember and update information across multiple interactions or transactions, making it possible to create more sophisticated and personalized campaign logic.

### 4.2 Benefits of Persistent Variables

- Long-term memory: Track user behavior and preferences over time
- Stateful campaigns: Create campaigns that evolve based on cumulative interactions
- Cross-channel consistency: Maintain consistent user experience across different touchpoints
- Advanced segmentation: Use historical data for more precise targeting
- Personalization: Tailor experiences based on individual user history

### 4.3 Persistent Collections

Persistent collections are defined within the entity registration as properties of transaction contexts. They represent different types of entities or objects that your campaigns interact with. Common examples include:

- `subscribers`: For storing customer-related data
- `agents`: For maintaining information about service representatives
- `shops`: For tracking store-specific data

### 4.4 How Collections Work

- Persistent collections are defined in the `properties` of each `transactionContext` in the entity registration.
- Each property with a `persistentCollection` value represents a potential key for persistent variables.
- When defining persistent variables in a campaign, you associate them with these collections.
- During rule evaluation, RuleForge CRE uses the appropriate identifier (e.g., subscriberId, agentId) to locate the correct set of persistent variables within a collection.
- This approach allows for efficient data management and retrieval, even with large numbers of entities.

## 5. Defining and Using Persistent Variables

### 5.1 Defining Persistent Variables

Persistent variables are defined at the campaign level:

```json
{
  "persistentVariableDefinitions": [
    {
      "variableId": "TOTAL_PURCHASES",
      "name": "Total Purchases",
      "description": "The total number of purchases made by a customer",
      "type": "integer",
      "defaultValue": 0,
      "persistentCollection": "subscribers"
    }
  ]
}
```

### 5.2 Persistent Collections for Variables

- Use the `persistentCollection` field to specify which data collection the variable is associated with
- Common collections: "subscribers", "agents", "shops"
- Ensure the `persistentCollection` matches a collection defined in the entity registration
- The Rules Engine uses this information to store and retrieve variable values for the correct entity

Note: To retrieve the list of available persistent collections for an entity, use the API endpoint:

```
GET /entities/{entityId}
```

This will return the entity registration, including the `transactionContexts` array where persistent collections are defined.

### 5.3 Accessing Persistent Variables

In rule conditions or actions:

```
{persistentVariables.TOTAL_PURCHASES}
```

### 5.4 Updating Persistent Variables

Use the `variableOperations` array in rule definitions to update persistent variables:

```json
{
  "rules": [
    {
      "ruleId": "UPDATE_TOTAL_PURCHASES",
      "name": "Update Total Purchases",
      "description": "Adds the current transaction amount to total purchases",
      "priority": 1,
      "conditions": [],
      "actions": [],
      "variableOperations": [
        {
          "variableId": "TOTAL_PURCHASES",
          "operation": "ADD",
          "value": "{transactionAmount}"
        }
      ]
    }
  ]
}
```

The `variableOperations` array allows you to perform multiple variable updates within a single rule. Each operation specifies:

- `variableId`: The identifier of the variable to update
- `operation`: The type of operation to perform (e.g., "SET", "ADD", "SUBTRACT", "MULTIPLY", "DIVIDE")
- `value`: The value to use in the operation, which can be a literal value or an expression using other variables or transaction properties

By using `variableOperations`, you ensure that all variable updates are performed atomically as part of the rule execution, maintaining data consistency and enabling complex update logic.

## 6. Working with Local Variables

### 6.1 Defining Local Variables

Local variables are defined at the campaign level:

```json
{
  "localVariableDefinitions": [
    {
      "variableId": "DISCOUNT_AMOUNT",
      "name": "Discount Amount",
      "description": "Calculated discount amount for the current transaction",
      "type": "amount",
      "defaultValue": 0
    }
  ]
}
```

### 6.2 Using Local Variables

In rule conditions or actions:

```
{localVariables.DISCOUNT_AMOUNT}
```

### 6.3 Updating Local Variables

Similar to persistent variables, but changes only last for the current rule evaluation:

```json
{
  "variableOperations": [
    {
      "variableId": "DISCOUNT_AMOUNT",
      "operation": "SET",
      "value": "{transactionAmount * 0.1}"
    }
  ]
}
```

## 7. Variable Lifetimes

### 7.1 Persistent Variable Lifetimes

- Tied to the entity in the specified `persistentCollection` (e.g., subscriber lifetime for "subscribers" collection)
- Exist as long as the associated entity exists in the system
- Values are maintained across multiple transactions and campaign executions

### 7.2 Local Variable Lifetime

- Exists only during a single transaction evaluation
- Reinitialized for each new evaluation
- Used for temporary calculations or intermediate results within a single rule execution

## 8. Best Practices for Variable Usage

### 8.1 Choosing Between Persistent and Local Variables

- Use persistent variables for data that needs to be remembered across transactions
- Use local variables for temporary calculations or intermediate results
- Consider the frequency of updates and reads when deciding between persistent and local variables

### 8.2 Naming Conventions

- Use clear, descriptive names
- Follow a consistent naming pattern (e.g., UPPER_SNAKE_CASE for variableId)
- Use lowercase for persistent collection names to match entity property names

### 8.3 Performance Considerations

- Minimize use of persistent variables in high-volume scenarios
- Use local variables for complex calculations to improve readability and performance
- Be mindful of the number of persistent variables per collection to avoid performance degradation

### 8.4 Data Type Consistency

- Ensure consistent data types when updating variables
- Use appropriate data types for better performance (e.g., integer vs. number)
- Consider the range of possible values when choosing between integer and number types

### 8.5 Error Handling

- Implement checks for null or undefined variables
- Provide default values where appropriate
- Consider edge cases, such as division by zero or overflow, when performing calculations

### 8.6 Understanding Entity Registration

- Familiarize yourself with the entity registration structure, particularly the `transactionContexts` and their `properties`.
- Ensure that persistent variables are only associated with collections that are properly defined in the entity registration.
- Regularly review entity registrations to understand available persistent collections and their purposes.

## 9. Common Use Cases and Examples

### 9.1 Tracking Customer Behavior

```json
{
  "persistentVariableDefinitions": [
    {
      "variableId": "LAST_PURCHASE_DATE",
      "name": "Last Purchase Date",
      "description": "The date of the customer's last purchase",
      "type": "date",
      "defaultValue": null,
      "persistentCollection": "subscribers"
    },
    {
      "variableId": "PURCHASE_FREQUENCY",
      "name": "Purchase Frequency",
      "description": "Number of purchases made by the customer",
      "type": "integer",
      "defaultValue": 0,
      "persistentCollection": "subscribers"
    }
  ]
}
```

### 9.2 Calculating Discounts

```json
{
  "persistentVariableDefinitions": [
    {
      "variableId": "TOTAL_PURCHASES",
      "name": "Total Purchases",
      "description": "The total amount of purchases made by a customer",
      "type": "integer",
      "defaultValue": 0,
      "persistentCollection": "subscribers"
    }
  ],
  "localVariableDefinitions": [
    {
      "variableId": "DISCOUNT_PERCENTAGE",
      "name": "Discount Percentage",
      "description": "Calculated discount percentage for the current transaction",
      "type": "float",
      "defaultValue": 0
    }
  ],
  "rules": [
    {
      "ruleId": "APPLY_DISCOUNT_RULE",
      "name": "Apply Discount Based on Total Purchases",
      "description": "Applies a discount if total purchases exceed 1000",
      "priority": 1,
      "conditions": [
        {
          "type": "COMPARISON",
          "parameters": {
            "leftOperand": "{persistentVariables.TOTAL_PURCHASES}",
            "operator": ">",
            "rightOperand": 1000
          }
        }
      ],
      "actions": [
        {
          "type": "APPLY_DISCOUNT",
          "parameters": {
            "percentage": "{localVariables.DISCOUNT_PERCENTAGE}"
          }
        }
      ],
      "variableOperations": [
        {
          "variableId": "DISCOUNT_PERCENTAGE",
          "operation": "SET",
          "value": 10
        },
        {
          "variableId": "TOTAL_PURCHASES",
          "operation": "ADD",
          "value": "{transactionAmount}"
        }
      ]
    }
  ]
}
```

This example demonstrates the use of both persistent (`TOTAL_PURCHASES`) and local (`DISCOUNT_PERCENTAGE`) variables, as well as updating both types of variables within the same rule.

### 9.3 Agent Performance Tracking

```json
{
  "persistentVariableDefinitions": [
    {
      "variableId": "CALLS_HANDLED",
      "name": "Calls Handled",
      "description": "Total number of calls handled by the agent",
      "type": "integer",
      "defaultValue": 0,
      "persistentCollection": "agents"
    },
    {
      "variableId": "AVERAGE_CALL_DURATION",
      "name": "Average Call Duration",
      "description": "Average duration of calls handled by the agent (in seconds)",
      "type": "integer",
      "defaultValue": 0,
      "persistentCollection": "agents"
    }
  ],
  "rules": [
    {
      "ruleId": "UPDATE_AGENT_METRICS",
      "name": "Update Agent Performance Metrics",
      "description": "Updates agent metrics after each call",
      "priority": 1,
      "conditions": [
        {
          "type": "EVENT",
          "parameters": {
            "eventType": "CALL_ENDED"
          }
        }
      ],
      "actions": [],
      "variableOperations": [
        {
          "variableId": "CALLS_HANDLED",
          "operation": "ADD",
          "value": 1
        },
        {
          "variableId": "AVERAGE_CALL_DURATION",
          "operation": "SET",
          "value": "{(AVERAGE_CALL_DURATION * CALLS_HANDLED + currentCallDuration) / (CALLS_HANDLED + 1)}"
        }
      ]
    }
  ]
}
```

## 10. Troubleshooting Variable Issues

### 10.1 Common Problems

- Unexpected variable values
- Variables not persisting as expected
- Type mismatch errors
- Performance issues with heavy use of persistent variables

### 10.2 Debugging Techniques

- Use logging to track variable values
- Implement variable inspection in the RuleForge CRE GUI
- Review variable operation history
- Analyze rule execution logs to understand the flow of variable updates

### 10.3 Best Practices for Troubleshooting

- Implement comprehensive logging for critical variable operations
- Use meaningful variable names to make debugging easier
- Consider implementing a "debug mode" in your campaigns that provides more detailed logging
- Regularly review and clean up unused persistent variables to maintain system performance

## Related Documents

- [[Entity Integration Schema]]
- [[Rule Set Schema]]
- [[Transaction Evaluation API Guide]]
- [[RuleForge Interface Overview]]

## Version Control

Document Version: 3.1.0
Last Updated: [[2024-08-29]]
Author: [[RuleForge DevGenius]]
Next Review Date: [[2025-02-28]]

<!--
### Changelog:
* 3.1.0 (2024-08-29): Added new section on Entity Registration and Persistent Collections workflow. Renumbered subsequent sections.
* 3.0.0 (2024-08-28): Major revision to update terminology from "key" to "persistentCollection" for persistent variables. Added detailed explanations of persistent variables, their benefits, and how collections work. Updated examples and expanded best practices.
* 2.0.0 (2024-08-28): Major revision to align with latest RuleForge CRE terminology and structure. Updated variable definitions, usage examples, and best practices.
* 1.0.0 (2024-08-27): Initial version of the RuleForge CRE Variable Usage Guide.
-->

## Approvals

| Role/Department       | Name | Date | Signature |
| --------------------- | ---- | ---- | --------- |
| Lead Architect        |      |      |           |
| API Team Lead         |      |      |           |
| Documentation Manager |      |      |           |

---

© 2024 Concurrent Systems. All Rights Reserve

