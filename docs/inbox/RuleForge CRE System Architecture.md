---
title: RuleForge CRE System Architecture
classification: Confidential
created: 2024-08-22
updated: 2024-08-28
tags:
  - architecture
  - ruleforge
  - campaign-rules-engine
  - system-design
  - redis
---

# RuleForge CRE System Architecture

## Document Specification

### Purpose
This document outlines the system architecture of the RuleForge Campaign Rules Engine (CRE), providing a comprehensive overview of its components, workflow, and key considerations for implementation.

### Scope
This document covers:
- System overview and core components
- Detailed descriptions of key system elements
- Integration and workflow processes
- Technical considerations and implementation strategies
- Key technology choices (Node.js, Redis, Vue.js)

It does not cover:
- Detailed API specifications (covered in separate API documentation)
- In-depth explanations of algorithms or internal system operations
- GUI-specific functionalities not directly related to the system architecture

### Target Audience
- Software developers implementing RuleForge CRE
- System architects designing solutions that incorporate RuleForge CRE
- Technical project managers overseeing RuleForge CRE implementations
- QA engineers planning testing strategies for RuleForge CRE

## 1. System Overview

RuleForge CRE is a standalone system designed for creating, managing, and executing dynamic marketing campaigns in real-time. It combines a user-friendly interface for rule creation with robust backend processing to enable complex, data-driven campaign strategies.

Key features include:
- Flexible campaign rule creation and management
- Real-time transaction evaluation against defined rules
- Support for multiple transaction contexts per calling entity
- Persistent variable management across transactions using Redis
- Clear separation between rule evaluation and action execution
- Vue.js-based GUI for intuitive campaign management

## 2. Core Components

### 2.1 Dedicated GUI Rule Builder
- Vue.js-based user-friendly interface for creating and editing rules
- Generates JSON representation of rules
- Supports multiple transaction contexts
- Provides real-time rule validation and visual representation of rule flow

### 2.2 JSON Rule Configuration
- Structured format for campaign definitions
- Includes campaign metadata, variable definitions, and rule specifications
- Facilitates easy creation, updating, and management of campaigns
- Allows for programmatic manipulation and versioning

### 2.3 JavaScript Rule Generation
- Converts JSON campaign definitions into executable JavaScript
- Implements rule logic respecting defined sequencing
- Allows for advanced customization through direct JavaScript editing

### 2.4 Rule Execution Environment
- Standalone service communicating via RESTful API
- Processes transaction information against active campaigns
- Manages global context properties
- Returns triggered actions to calling entities for execution
- Utilizes Redis for efficient persistent variable storage and retrieval

## 3. System Workflow

1. Entity Registration:
   - Calling entities register with RuleForge CRE via API
   - Define transaction contexts and available actions
   - Specify properties that can be used as persistent collections

2. Campaign Creation:
   - Users create campaigns using the Dedicated GUI
   - Define rules, variables, and actions
   - Associate persistent variables with collections defined in entity registration

3. Transaction Evaluation:
   - Calling entities send transaction data to RuleForge CRE
   - Rules Engine evaluates data against applicable campaigns
   - Determines triggered actions based on rule conditions

4. Action Execution:
   - Rules Engine returns triggered actions to calling entity
   - Calling entity executes actions within its own environment

## 4. Key Architectural Considerations

### 4.1 Persistent Collections and Variable Handling

- Persistent collections defined in entity registration
- Represent different types of entities (e.g., subscribers, agents, shops)
- Persistent variables in campaigns associated with these collections
- Redis used for high-performance storage and retrieval of persistent variables
- Local variables for temporary calculations within rule execution

### 4.2 Transaction Context and Persistent Collections

- Each calling entity defines its transaction contexts
- Properties within contexts can be designated as persistent collections
- Provides flexibility for entity-specific data structures and persistence needs

### 4.3 Global Context

- Managed by Rules Engine
- Provides universally available properties and functions
- Includes system-wide data like current date, utility functions, etc.

### 4.4 Action Implementation

- Calling entities define and implement their own actions
- Actions registered with Rules Engine during entity registration
- Rules Engine determines which actions to trigger
- Calling entities responsible for action execution

### 4.5 Extensibility

- System designed to accommodate new condition types, actions, and variable operations
- Flexible JSON and JavaScript structures allow for easy additions

### 4.6 Security

- Implements strict sandboxing for user-editable JavaScript
- Server-side execution of all rule evaluation
- Use of HTTPS for API communications

### 4.7 Performance Optimization

- Redis utilized for efficient persistent variable storage and retrieval
- Caching strategies leveraging Redis for high-volume transactions
- Designed for scalability as transaction volumes grow

## 5. Implementation Framework

- Node.js selected as the primary backend implementation framework
- Vue.js chosen for the frontend GUI development
- Redis employed for persistent variable storage and caching
- Leverages JavaScript ecosystem for consistent development across frontend and backend
- Provides excellent performance for high-throughput, low-latency requirements
- Rich package ecosystem for rapid development and robust solutions

## 6. Integration with Existing Systems

- API-based architecture for seamless integration
- Calling entities interact with Rules Engine via defined API endpoints
- Minimal modifications required for existing systems to integrate
- Clear separation of concerns between Rules Engine and calling entities

## Related Documents
- [[RuleForge Interface Overview]]
- [[Entity Integration Schema]]
- [[Rule Set Schema]]
- [[JavaScript Code Generation Specification]]
- [[Transaction Evaluation API Guide]]
- [[Calling Entity Registration Specification]]
- [[Function Schema]]
- [[Redis Implementation Guide for RuleForge CRE]]
- [[Vue.js GUI Architecture for RuleForge CRE]]

## Version Control
Document Version: 2.1.0
Last Updated: [[2024-08-28]]
Author: [[RuleForge DevGenius]]
Next Review Date: [[2025-02-28]]

<!--
### Changelog:
* 2.1.0 (2024-08-30): Updated to include information about Redis for persistent variable storage and Vue.js for GUI development. Added related documents for Redis and Vue.js implementations.
* 2.0.0 (2024-08-29): Major revision of the document structure and content. Updated to align with latest understanding of persistent collections, entity registration, and system workflow. Improved overall clarity and organization.
* 1.0.0 (2024-08-22): Initial version of the RuleForge CRE System Architecture document.
-->

## Approvals
| Role/Department | Name | Date | Signature |
|-----------------|------|------|-----------|
| Lead Architect  |      |      |           |
| CTO             |      |      |           |
| Head of Engineering |  |      |           |

---
© 2024 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Document Template v1.6. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->