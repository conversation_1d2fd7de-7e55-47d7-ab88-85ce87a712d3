# Persistent Variable Management Specification Using Redis

## 1. Introduction

### 1.1 Purpose
This document specifies the management process for persistent variables within the standalone Campaign Rules Engine, including their storage, retrieval, updating, and lifecycle management, using Redis as the primary data store.

### 1.2 Scope
This document covers:
- Definition and structure of persistent variables
- Scoping rules for persistent variables
- Storage and retrieval mechanisms using Redis
- Update processes during rule evaluation
- Consistency and concurrency considerations
- Performance optimization strategies
- Monitoring, security, and maintenance

### 1.3 Benefits for the Rules Engine
- Low-latency access to persistent variables, crucial for real-time rule evaluation
- Improved scalability to handle high-volume campaigns and transactions
- Simplified data model compared to relational databases, aligning with our variable structure
- Built-in support for data structures like hashes, ideal for our variable storage needs

## 2. Persistent Variable Definition

### 2.1 Structure
A persistent variable is defined with the following attributes:
- `name`: Unique identifier for the variable (string)
- `type`: Data type of the variable (e.g., number, string, boolean, date)
- `scope`: Visibility and persistence level (campaign, agent, subscriber, global)
- `initialValue`: Starting value for the variable
- `currentValue`: Current value of the variable (stored in Redis)
- `lastUpdated`: Timestamp of the last update (stored in Redis as part of the variable's metadata)

### 2.2 Scoping Rules
- Campaign Scope: Variable is accessible only within a specific campaign
- Agent Scope: Variable is tied to a specific agent across all campaigns
- Subscriber Scope: Variable is tied to a specific subscriber across all campaigns
- Global Scope: Variable is accessible across all campaigns and entities

## 3. Storage Mechanism

### 3.1 Redis Data Structures
Use Redis Hashes as the primary data structure for storing variables:

- Global Variables: `HSET global <name> <value>`
- Campaign Variables: `HSET campaign:<campaign_id> <name> <value>`
- Agent Variables: `HSET agent:<agent_id> <name> <value>`
- Subscriber Variables: `HSET subscriber:<subscriber_id> <name> <value>`

### 3.2 Metadata Storage
Store metadata (like `lastUpdated`) in the same hash:

```
HSET campaign:<campaign_id> <name>_value <value>
HSET campaign:<campaign_id> <name>_lastUpdated <timestamp>
```

### 3.3 Data Types
- Use native Redis types for simple values (strings, numbers)
- For complex types, serialize to JSON before storing

### 3.4 Data Serialization
- Use JSON for serializing complex data structures before storing in Redis
- For performance-critical operations, consider using a binary serialization format like MessagePack

### 3.5 Caching and Persistence
- In-Memory Storage: All variables are stored in Redis's in-memory data store
- Persistence: Enable Redis AOF (Append-Only File) or RDB snapshots for data durability
- TTL (Time-To-Live): Use Redis's TTL feature for variables that should expire after a certain period, as defined by the Rules Engine logic or GUI settings.

### 3.6 Cluster Configuration
- Implement a 3-node Redis cluster for high availability and load distribution
- Use Redis Sentinel for automatic failover and monitoring
- Distribute cluster nodes across cspr01, cspr04, and cspr05 to optimize resource utilization

## 4. Variable Lifecycle Management

### 4.1 Initialization
- Variables are defined in campaign JSON during campaign creation through the standalone GUI.
- Upon initialization, variables are stored in Redis with their `initialValue`
- If the variable already exists, update its value or reset it based on campaign requirements

### 4.2 Retrieval
- Retrieve variables from Redis at the start of rule evaluation as requested by the Rules Engine.
- The GUI may also request variable retrieval for display or editing purposes.

### 4.3 Updating
- Update variable values in-memory during rule evaluation within the Rules Engine.
- Persist updated values to Redis using transactions (MULTI/EXEC) or Lua scripts for atomic updates.
- The GUI may trigger updates through user actions, which are then processed by the Rules Engine before being persisted to Redis.

### 4.4 Deletion
- Delete variables from Redis when they are no longer needed (e.g., when a campaign is deleted through the GUI).
- Implement Redis key expiration (TTL) for automatic cleanup of time-limited variables as set by the Rules Engine or GUI.

## 5. Consistency and Concurrency

### 5.1 Transactions
- Use Redis's MULTI/EXEC commands for atomic operations on related variables
- Implement optimistic locking using the WATCH command for scenarios requiring consistency across multiple operations

### 5.2 Locking Strategies
- Use Redis's SETNX command for simple locking mechanisms
- Implement the RedLock algorithm for distributed locks in a multi-node Redis setup

### 5.3 Deadlock Prevention
- Design Redis operations to avoid circular dependencies
- Order operations carefully to minimize locking conflicts

### 5.4 Lua Scripting
- Utilize Redis Lua scripting for complex, multi-step operations that need to be atomic
- Store commonly used scripts in Redis using SCRIPT LOAD for efficient reuse

## 6. Performance Optimization

### 6.1 Key Design
- Structure Redis keys with prefixes for logical grouping
- Utilize Redis hashes for storing related attributes of a variable together

### 6.2 Connection Pooling
- Implement Redis connection pooling to efficiently manage connections

### 6.3 Batch Operations
- Use Redis pipelining to execute batch operations, reducing network roundtrips
- Group related commands in transactions (MULTI/EXEC) to optimize performance

### 6.4 Partitioning and Sharding
- For large datasets, implement sharding across multiple Redis instances
- Use consistent hashing to ensure even distribution of keys

### 6.5 Local Caching
- Implement an in-memory cache in the application layer to reduce Redis calls for frequently accessed variables
- Use a cache-aside pattern with short TTLs to balance performance and data freshness

### 6.6 Monitoring and Alerting
- Implement Redis-specific monitoring using tools like Redis Exporter and Prometheus
- Set up alerts for key performance metrics such as memory usage, operation latency, and connection count
- Integrate monitoring data with the Rules Engine's dashboard for real-time visibility

## 7. Scalability Considerations

### 7.1 Vertical Scaling
- Optimize Redis configuration based on hardware resources
- Monitor memory usage and scale up Redis server as demand grows

### 7.2 Replication
- Set up Redis replication for high availability to ensure continuous operation of the Rules Engine and GUI.
- Use a master-slave replication setup where the master handles writes and slaves handle read operations

### 7.3 Redis Cluster
- For large-scale deployments, use Redis Cluster to horizontally scale across multiple nodes

### 7.4 High Availability
- Implement Redis Sentinel for automatic failover and high availability to minimize disruption to the Rules Engine and GUI operations.
- Configure Sentinel to monitor Redis instances and promote a slave to master in case of a master failure

## 8. Monitoring and Maintenance

### 8.1 Performance Monitoring
Track key metrics including:
- Operation latency
- Memory usage
- Hit/miss ratio for cached variables
- Eviction rate
- Network bandwidth usage
- Integrate monitoring data with the Rules Engine's dashboard in the GUI for real-time visibility into Redis performance.

Use Redis's built-in monitoring tools (e.g., `INFO`, `MONITOR`) and set up alerts for anomalies

### 8.2 Backup and Recovery
- Regularly back up Redis data using RDB snapshots or AOF files
- Test and document recovery procedures

### 8.3 Data Archiving
- Implement data archiving strategies for older, less frequently accessed data as determined by usage patterns observed through the Rules Engine and GUI.
- Export archived data to a long-term storage solution like MariaDB or an OLAP system

## 9. Security Considerations

### 9.1 Access Control
- Configure Redis with authentication to ensure secure access from both the Rules Engine and GUI.
- Implement role-based access control (RBAC) using Redis ACLs, aligned with user roles defined in the Rules Engine.

### 9.2 Data Encryption
- Use SSL/TLS to encrypt connections between the application and Redis
- For highly sensitive data, encrypt values before storing them in Redis

## 10. Error Handling and Recovery

### 10.1 Connection Failures
- Implement robust error handling for Redis connection failures in both the Rules Engine and GUI.

### 10.2 Data Integrity Checks
- Implement periodic data integrity checks to verify consistency of persistent variables, with results reportable through the GUI.

### 10.3 Circuit Breaker
- Implement a circuit breaker pattern to handle temporary Redis unavailability gracefully
- Use a library like Hystrix or Opossum to manage the circuit breaker logic

## 11. Testing and Validation

### 11.1 Load Testing
- Conduct thorough load testing to ensure Redis can handle expected variable operations volume from both the Rules Engine and GUI interactions.
- Simulate concurrent access patterns to identify potential bottlenecks

### 11.2 Failover Testing
- Test Redis failover scenarios, including master node failures and network partitions

## 12. Documentation and Training

### 12.1 Query Guidelines
- Develop documentation on efficient key design and query patterns for Redis operations within the context of the standalone Rules Engine and GUI.
- Provide training to developers on Redis best practices in relation to the Rules Engine architecture.

## 13. Data Migration

### 13.1 Migration Strategy
- Develop a strategy for migrating existing persistent variables from MariaDB to Redis
- Consider using a dual-write approach during migration to ensure data consistency

### 13.2 Rollback Plan
- Create a rollback plan in case of unforeseen issues with the Redis implementation

## Conclusion

Using Redis as the primary data store for managing persistent variables in the Campaign Rules Engine offers significant performance benefits, particularly for fast, real-time data access. This approach leverages Redis's in-memory storage, persistence options, and advanced data structures to ensure high scalability, flexibility, and efficiency in managing campaign-related data, while minimizing impact on existing transaction latencies.