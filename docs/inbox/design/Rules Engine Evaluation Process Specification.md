# Rules Engine Evaluation Process Specification

## 1. Introduction

### 1.1 Purpose
This document specifies the process of evaluating rules within the Campaign Rules Engine, including the crucial steps of JavaScript code generation and caching.

### 1.2 Scope
This document covers:
- The flow of data through the evaluation process
- JavaScript code generation from campaign rules
- Caching of generated JavaScript code
- Execution of cached JavaScript code
- Handling of evaluation results and action triggers

## 2. Evaluation Process Overview

The evaluation process consists of the following main steps:
1. Receive evaluation request
2. Identify transaction context
3. Retrieve relevant campaign(s)
4. Generate and cache JavaScript code from campaign rules
5. Prepare evaluation context
6. Execute cached JavaScript code for the specific transaction context
7. Process evaluation results
8. Update Redis with modified persistent variables
9. Return triggered actions

## 3. Detailed Process Steps

### 3.1 Receive Evaluation Request

1. 1. The Rules Engine receives a POST request to the `/evaluate` endpoint.
2. The request includes:
   - `callingEntityId`: Identifier of the calling entity
   - `transactionContextId`: Identifier of the specific transaction context
   - `transactionContext`: Object containing transaction-specific data

### 3.2 Retrieve Relevant Campaign(s)

1. Query the campaign database for active campaigns associated with the calling entity and applicable to the specified transaction context.
2. Load the campaign definitions, including rules, for all relevant campaigns.
3. Fetch relevant persistent variables from Redis based on the campaign and transaction context.

### 3.3 Generate and Cache JavaScript Code

1. For each relevant campaign:
   a. Check if a cached JavaScript version of the campaign rules exists and is up-to-date.
   b. If a valid cached version exists, load it and skip to step 2.
   c. If no valid cache exists:
      i. Iterate through the rules in the campaign.
      ii. Transform each rule into JavaScript code:
         - Convert conditions into JavaScript conditional statements.
         - Transform actions into function calls or object creations.
         - Incorporate variable operations into the code.
      iii. Wrap the generated code in a function that takes the evaluation context as an argument.
      iv. Cache the generated JavaScript code along with a timestamp and campaign version identifier.

2. Prepare the cached JavaScript functions for execution.

Example of cached JavaScript structure:

```javascript
const cachedCampaigns = {
  'CAMPAIGN_ID_1': {
    version: 'v1.2',
    timestamp: 1639005600000, // Unix timestamp
    code: function evaluateCampaign(context) {
      let triggeredActions = [];
      
      switch(context.transactionContextId) {
        case 'CONTEXT_1':
          // Evaluation code for CONTEXT_1
          break;
        case 'CONTEXT_2':
          // Evaluation code for CONTEXT_2
          break;
        // More contexts...
      }
      
      return triggeredActions;
    }
  },
  // More cached campaigns...
};
```

### 3.4 Redis Integration

#### 3.4.1 Redis Connection Management
- Implement a connection pool for efficient Redis connections
- Use a Redis client library that supports clustering and automatic reconnection

#### 3.4.2 Redis Operations
- Use Redis pipelining for batch retrieval of persistent variables
- Implement Redis transactions (MULTI/EXEC) for atomic updates of related variables
- Utilize Redis Lua scripts for complex operations that need to be atomic

#### 3.4.3 Error Handling
- Implement retry mechanisms for Redis operations with exponential backoff
- Use circuit breaker pattern to handle temporary Redis unavailability
### 3.5 Prepare Evaluation Context

1. Create a context object that includes:
   - Transaction context from the request
   - Global context properties from the GlobalContext
   - Persistent variable values retrieved from Redis for the relevant scope(s)
   - Any necessary utility functions (e.g., date manipulation)

### 3.6 Execute Cached JavaScript Code

1. Set up a sandboxed environment for code execution to ensure security.
2. For each campaign's cached JavaScript function:
   a. Call the function with the prepared context, ensuring execution of the correct transaction context branch.
   b. Capture the returned triggered actions.
   c. Track any modifications to persistent variables.
   d. Store any modifications to persistent variables in a local memory structure for later persistence to Redis.

### 3.7 Process Evaluation Results

1. Aggregate triggered actions from all evaluated campaigns.
2. Resolve any conflicts or duplicates in triggered actions based on predefined rules.
3. Prepare updates for persistent variables based on tracked modifications.

### 3.8 Persist Variable Updates to Redis

1. After rule evaluation, process the locally stored variable modifications:
   a. Group updates by variable scope (global, campaign, agent, subscriber)
   b. Prepare Redis pipeline or transaction for each group of updates
   c. Execute updates using Redis pipelining or transactions for efficiency
2. Implement optimistic locking using Redis WATCH command for variables that require strict consistency
3. Use Redis Lua scripts for complex update operations that need to be atomic
4. In case of failure:
   a. Implement a retry mechanism with exponential backoff
   b. Log detailed error information
   c. If retry fails, flag the transaction for manual review and potential rollback

### 3.9 Return Triggered Actions

1. Compile the final list of triggered actions.
2. Construct the response object.
3. Send the response back to the calling entity.

## 4. JavaScript Code Generation and Caching Considerations

- Implement a robust caching mechanism for generated JavaScript as a core component of the system.
- Use a combination of campaign ID, version, and rules hash to create unique cache keys.
- Implement cache invalidation strategies:
  - Time-based expiration (e.g., refresh cache every 24 hours)
  - Version-based invalidation (invalidate cache when campaign version changes)
  - Manual invalidation option for immediate updates
- Store cached JavaScript in a high-performance, distributed cache system (e.g., Redis) for quick access across multiple nodes.
- Implement a background job to pre-generate and cache JavaScript for all active campaigns during low-traffic periods.
- Monitor cache hit rates and generation times to optimize caching strategies.
- Implement separate caching for different transaction contexts within a campaign to optimize performance.
- Consider partial cache invalidation for specific contexts when rules are updated.
- Use a templating system or AST (Abstract Syntax Tree) manipulation for reliable code generation.
- Implement safeguards against infinite loops or excessive resource usage in generated code.
- Ensure generated code adheres to strict ECMAScript standards for consistency.

## 5. Security Considerations

- Implement strict input validation for all data used in code generation and execution.
- Use a JavaScript sandboxing library or VM to isolate executed code from the main system.
- Set up resource limits (e.g., execution time, memory usage) for each code execution to prevent DoS attacks.
- Regularly audit and update the code generation templates to prevent potential code injection vulnerabilities.
- Ensure that the caching mechanism does not introduce new security vulnerabilities.

## 6. Performance Optimization

- Prioritize cache hits to minimize real-time code generation.
- Implement a warm-up phase that pre-loads frequently used cached JavaScript into memory.
- Use efficient serialization methods for storing and retrieving cached JavaScript.
- Implement a cache preloading mechanism that anticipates and pre-caches JavaScript for upcoming campaigns.
- Monitor and optimize cache storage usage to balance memory consumption with performance gains.
- Implement parallel processing for multiple campaign evaluations when possible.
- Use efficient data structures for context and result management.
- Optimize generated JavaScript for performance, minimizing object creations and function calls where possible.
- Use Redis pipelining for bulk operations to reduce network roundtrips
- Implement client-side caching for frequently accessed, rarely changing variables
- Utilize Redis server-side caching mechanisms like `SCAN` for efficient key iteration
- Consider using Redis modules like RedisJSON for complex JSON operations if needed

Monitoring Redis Performance:
- Implement Redis-specific monitoring using tools like Redis Exporter and Prometheus
- Set up alerts for key Redis performance metrics such as memory usage, operation latency, and hit rates
- Integrate Redis monitoring data with the Rules Engine's performance dashboard

## 7. Error Handling

- Implement comprehensive error catching and logging throughout the evaluation process.
- Provide detailed error information for debugging purposes, while ensuring no sensitive information is exposed.
- Define a strategy for handling partial failures (e.g., if one campaign evaluation fails but others succeed).
- Implement fallback mechanisms for cache failures, allowing for on-the-fly code generation if cached code is unavailable.

Redis-Specific Error Handling:
- Implement specific error handling for Redis connection issues, including automatic reconnection attempts
- Handle Redis cluster failures and rebalancing events gracefully
- Provide detailed logging for Redis-related errors to aid in troubleshooting
- Implement a fallback mechanism for critical operations in case of persistent Redis unavailability

## 8. Monitoring and Logging

- Log key metrics for each evaluation, including execution time, number of rules processed, and actions triggered.
- Log cache hit/miss rates for each evaluation request.
- Track JavaScript generation times and execution times separately to identify optimization opportunities.
- Implement performance monitoring to identify bottlenecks or inefficiencies in the evaluation process.
- Set up alerts for anomalies in evaluation patterns or error rates.
- Implement alerts for sudden drops in cache hit rates or increases in code generation time.
- Regularly audit cache usage and performance to ensure optimal configuration.

## 9. Cache Consistency and Synchronization

- Implement a distributed locking mechanism to prevent concurrent cache updates for the same campaign.
- Ensure cache consistency across multiple nodes in a distributed deployment.
- Develop a strategy for graceful cache warm-up after system restarts or new node additions.
- Implement a cache version control system to manage and roll back cached JavaScript versions if needed.

## 10. Scalability Considerations

- Design the caching system to scale horizontally to handle increasing load.
- Implement load balancing for cache access to distribute requests across multiple cache nodes.
- Consider using a hierarchical caching strategy for large-scale deployments (e.g., local node cache backed by a distributed cache).
- Plan for cache storage growth and implement strategies to manage cache size (e.g., LRU eviction policies).

## 11. Integration with Calling Entities

- Implement a registration process for calling entities to declare their transaction contexts and associated actions.
- Validate incoming evaluation requests against registered transaction contexts for the calling entity.
- Ensure that returned actions are applicable to the calling entity and transaction context.

## 12. GlobalContext Integration

- Ensure that all GlobalContext properties are available and correctly integrated into the evaluation context.
- Implement efficient updating mechanism for time-sensitive GlobalContext properties (e.g., current date/time).

## 13. API Alignment

- Ensure that the evaluation process aligns with the `/evaluate` endpoint specification in the API documentation.
- Validate that request and response structures support multiple transaction contexts as defined in the API specification.


## Version Control
Document Version: 1.0 Last Updated: [[2024-08-22]]