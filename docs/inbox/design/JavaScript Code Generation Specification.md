# JavaScript Code Generation Specification

## 1. Introduction

### 1.1 Purpose

This document specifies the process and strategy for converting campaign rules into executable JavaScript code within the Campaign Rules Engine.

### 1.2 Scope

This document covers:

- The overall strategy for code generation
- Conversion of different rule components (conditions, actions, variable operations)
- Code structure and organization
- Performance considerations
- Security measures
- Extensibility and maintenance

The code generation process accounts for multiple transaction contexts, ensuring that rules are evaluated within their specified contexts. This approach allows for efficient and targeted rule execution based on the type of transaction being processed.

## 2. Context Definition

In the Campaign Rules Engine, "context" refers to the complete set of data available for rule evaluation during a transaction. It includes several key components:

1. Transaction Context: 
   - Specific data related to the current transaction.
   - Provided by the calling entity (e.g., BILLING_SYSTEM).
   - Varies based on the transaction type (e.g., AIRTIME_PURCHASE, DATA_BUNDLE).
   - Example properties: transactionAmount, customerId, productCode.

2. Global Context: 
   - Data that is universally available across all campaigns and transaction types.
   - Managed by the Rules Engine.
   - Example properties: currentDate, isWeekend, currentTime.

3. Persistent Variables:
   - Variables that maintain state across multiple transactions.
   - Scoped to campaign, subscriber, or global level.
   - Example: customerLifetimeValue, loyaltyPoints.

4. Local Variables:
   - Temporary variables used during the execution of a single rule set.
   - Example: calculatedDiscount, eligibleForPromotion.

5. Metadata:
   - Information about the current execution context.
   - Includes transactionContextId, campaignId, etc.

The "context" object passed to the evaluation functions combines all these elements, providing a comprehensive data set for rule evaluation. For example:

```javascript
const context = {
  transactionContextId: "AIRTIME_PURCHASE",
  campaignId: "SUMMER_PROMO_2024",
  transactionContext: {
    transactionAmount: 100,
    customerId: "CUST123",
    productCode: "AIRTIME_100"
  },
  globalContext: {
    currentDate: "2024-06-15",
    isWeekend: true
  },
  persistentVariables: {
    customerLifetimeValue: 5000,
    loyaltyPoints: 250
  },
  localVariables: {}
};
```

This context object is passed to the rule evaluation functions, allowing them to access all relevant data for making decisions and taking actions.

## 3. Code Generation Strategy

### 3.1 Overall Approach
1. Parse the campaign JSON to extract rule definitions.
2. Group rules by transaction context for efficient evaluation.
3. Generate a JavaScript function for each campaign that includes context-specific evaluation logic.
4. Use a template-based approach for generating code structures.
5. Implement a visitor pattern to traverse the rule structure and generate corresponding code.
6. Utilize an Abstract Syntax Tree (AST) for complex expressions and transformations.
7. Generate context-specific evaluation functions for each transaction context.
8. . Implement flow control logic to handle rule execution flow based on success or failure conditions.

### 3.2 Generated Function Structure
```javascript
function evaluateCampaign(context) {
  const triggeredActions = [];
  
  switch(context.transactionContextId) {
    case 'CONTEXT_1':
      evaluateContext_CONTEXT_1(context, triggeredActions);
      break;
    case 'CONTEXT_2':
      evaluateContext_CONTEXT_2(context, triggeredActions);
      break;
    // ... other contexts
  }
  
  return triggeredActions;
}

/**
 * Evaluate rules for CONTEXT_1
 */
function evaluateContext_CONTEXT_1(context, triggeredActions) {
  const rules = [/* Array of rule objects */];
  let ruleIndex = 0;

  while (ruleIndex < rules.length) {
    const rule = rules[ruleIndex];
    const result = evaluateRule(rule, context, triggeredActions);

    if (result.break) {
      break;
    } else if (result.goto) {
      ruleIndex = rules.findIndex(r => r.sequence === result.goto);
    } else {
      ruleIndex++;
    }
  }
}

function evaluateRule(rule, context, triggeredActions) {
  if (evaluateConditions(rule.conditions, context)) {
    executeActions(rule.actions, context, triggeredActions);
    executeVariableOperations(rule.variableOperations, context);
    
    if (rule.flowControl.breakOnSuccess) {
      return { break: true };
    }
    if (rule.flowControl.gotoOnSuccess !== null) {
      return { goto: rule.flowControl.gotoOnSuccess };
    }
  } else {
    if (rule.flowControl.breakOnFailure) {
      return { break: true };
    }
    if (rule.flowControl.gotoOnFailure !== null) {
      return { goto: rule.flowControl.gotoOnFailure };
    }
  }
  return { next: true };
}
```
## 4. Rule Components Conversion

### 4.1 Conditions

#### 4.1.1 Simple Conditions
Convert simple conditions to if statements within the context-specific function:

```javascript
if (context.transactionAmount > 1000) {
  // Action code
}
```

#### 4.1.2 Compound Conditions
Use logical operators for compound conditions within the context-specific function:

```javascript
if (context.transactionAmount > 1000 && context.customerTier === 'GOLD') {
  // Action code
}
```

#### 4.1.3 Complex Expressions
Use the AST to parse and generate code for complex expressions within the context-specific function:

```javascript
if (evaluateExpression(context, 'transactionAmount * 0.1 > customerLifetimeValue')) {
  // Action code
}
```

### 4.2 Actions
Generate function calls for actions within the context-specific function:

```javascript
triggeredActions.push({
  type: 'APPLY_DISCOUNT',
  parameters: { discountPercentage: 10 }
});
```

### 4.3 Variable Operations
Generate code for variable operations within the context-specific function:

```javascript
context.persistentVariables.customerPoints += 100;
```

### 4.4 Flow Control
Generate code to handle flow control based on the rule's `flowControl` object:

```javascript
function generateFlowControl(rule) {
  let code = '';
  if (rule.flowControl) {
    code += `if (conditionsMet) {\n`;
    if (rule.flowControl.breakOnSuccess) {
      code += `  return { break: true };\n`;
    } else if (rule.flowControl.gotoOnSuccess !== null) {
      code += `  return { goto: ${rule.flowControl.gotoOnSuccess} };\n`;
    }
    code += `} else {\n`;
    if (rule.flowControl.breakOnFailure) {
      code += `  return { break: true };\n`;
    } else if (rule.flowControl.gotoOnFailure !== null) {
      code += `  return { goto: ${rule.flowControl.gotoOnFailure} };\n`;
    }
    code += `}\n`;
  }
  return code;
}
```


## 5. Code Structure and Organization

### 5.1 Function Naming
Use a consistent naming convention for generated functions:
```javascript
function evaluateCampaign_[CampaignID](context) {
  // Campaign evaluation code
}

function evaluateContext_[ContextID]_[CampaignID](context, triggeredActions) {
  // Context-specific evaluation code
}
```

### 5.2 Code Comments
Generate comments for readability:
```javascript
/**
 * Rule: {ruleName}
 * Context: {transactionContextId}
 * {comment} (if provided)
 */
// Rule implementation
if (context.transactionAmount > 1000) {
  // Action: Apply 10% discount
  triggeredActions.push({ type: 'APPLY_DISCOUNT', parameters: { discountPercentage: 10 } });
}
```

### 5.3 Error Handling
Implement try-catch blocks for error handling within each context-specific function:
```javascript
try {
  // Rule evaluation code
} catch (error) {
  console.error(`Error evaluating rule ${ruleId} in context ${contextId}:`, error);
}
```

### 5.4 Context-Specific Functions
Generate separate functions for each transaction context, including flow control logic:

```javascript
function generateContextFunction(contextId, rules) {
  let code = `function evaluateContext_${contextId}(context, triggeredActions) {\n`;
  code += `  const rules = ${JSON.stringify(rules)};\n`;
  code += `  let ruleIndex = 0;\n\n`;
  code += `  while (ruleIndex < rules.length) {\n`;
  code += `    const rule = rules[ruleIndex];\n`;
  code += `    const result = evaluateRule(rule, context, triggeredActions);\n\n`;
  code += `    if (result.break) {\n`;
  code += `      break;\n`;
  code += `    } else if (result.goto) {\n`;
  code += `      ruleIndex = rules.findIndex(r => r.sequence === result.goto);\n`;
  code += `    } else {\n`;
  code += `      ruleIndex++;\n`;
  code += `    }\n`;
  code += `  }\n`;
  code += `}\n`;
  return code;
}
```

## 6. Comment Generation Process

Add a new section to describe the comment generation process:

The code generation process should include steps to generate meaningful comments based on the rule definition:

1. For each rule, generate a comment block that includes:
   - The rule name
   - The transaction context ID
   - The optional comment, if provided

2. For each action within a rule, generate a brief comment describing the action.

Example comment generation function:

```javascript
function generateRuleComment(rule) {
  let comment = `/**\n * Rule: ${rule.name}\n * Context: ${rule.transactionContextId}\n`;
  if (rule.comment) {
    comment += ` * ${rule.comment}\n`;
  }
  comment += ' */\n';
  return comment;
}

function generateActionComment(action) {
  return `// Action: ${action.type}\n`;
}
```

3. Integrate comment generation into the code generation process:

```javascript
function generateRuleCode(rule) {
  let code = generateRuleComment(rule);
  
  // Generate condition code
  code += `if (${generateConditionCode(rule.conditions)}) {\n`;
  
  // Generate action code with comments
  rule.actions.forEach(action => {
    code += generateActionComment(action);
    code += generateActionCode(action);
  });
  
  code += '}\n';
  return code;
}
```

Include comments explaining flow control logic:

```javascript
function generateFlowControlComment(flowControl) {
  let comment = '// Flow Control:';
  if (flowControl.breakOnSuccess) comment += ' Break on success.';
  if (flowControl.breakOnFailure) comment += ' Break on failure.';
  if (flowControl.gotoOnSuccess !== null) comment += ` Go to rule ${flowControl.gotoOnSuccess} on success.`;
  if (flowControl.gotoOnFailure !== null) comment += ` Go to rule ${flowControl.gotoOnFailure} on failure.`;
  return comment;
}
```


This process ensures that the generated code includes meaningful comments that provide context for each rule and its actions, improving readability and maintainability.

## 7. Performance Considerations

### 7.1 Minimize Object Creation
Reuse objects where possible to reduce garbage collection:
```javascript
const discountAction = { type: 'APPLY_DISCOUNT', parameters: {} };
// ... later in the code ...
discountAction.parameters.discountPercentage = 10;
triggeredActions.push(discountAction);
```

### 7.2 Optimize Condition Evaluation
Order conditions to evaluate cheaper checks first:
```javascript
if (context.isWeekend && expensiveFunction(context)) {
  // Action code
}
```

### 7.3 Use Short-Circuit Evaluation
Leverage JavaScript's short-circuit evaluation for compound conditions:
```javascript
if (cheapCheck() && expensiveCheck()) {
  // Action code
}
```
### 7.4 Flow Control Optimization
- Implement efficient lookup for goto targets to minimize performance impact of jumps in rule sequence.
- Consider pre-computing goto targets during code generation to avoid runtime lookups.
## 8. Security Measures

### 8.1 Input Validation
Generate code to validate inputs:
```javascript
if (typeof context.transactionAmount !== 'number' || isNaN(context.transactionAmount)) {
  throw new Error('Invalid transactionAmount');
}
```

### 8.2 Sandbox Evaluation
Ensure the generated code runs in a sandboxed environment:
```javascript
const vm = require('vm');
const sandbox = { context: userContext, triggeredActions: [] };
vm.runInNewContext(generatedCode, sandbox);
```

### 8.3 Prevent Code Injection
Use parameterization instead of string concatenation for dynamic values:
```javascript
// Good
const threshold = 1000;
if (context.transactionAmount > threshold) { /* ... */ }

// Avoid
const code = `if (context.transactionAmount > ${userInputThreshold}) { /* ... */ }`;
```

## 9. Extensibility and Maintenance

### 9.1 Template System
Use a template system for generating code structures:
```javascript
const conditionTemplate = `if ({{ condition }}) {
  {{ actionCode }}
}`;
```

### 9.2 Modular Design
Organize code generation into modular functions:
```javascript
function generateCondition(condition) { /* ... */ }
function generateAction(action) { /* ... */ }
function generateVariableOperation(operation) { /* ... */ }
```

### 9.3 Version Control
Include version information in generated code:
```javascript
// Generated for Campaign Version: 1.2.3
function evaluateCampaign_ABC123(context) {
  // ...
}
```

## 10. Code Generation Process

1. Parse campaign JSON
2. Create an AST representation of the rules
3. Traverse the AST, generating code for each node
4. Apply optimizations to the generated code
5. Wrap the generated code in the campaign evaluation function
6. Apply security measures (e.g., input validation)
7. Generate the final JavaScript code string
8. 8. Integrate flow control logic into the generated code.
9. Validate flow control configurations to prevent infinite loops or unreachable rules.

## 11. Testing and Validation

### 11.1 Unit Testing
Generate unit tests alongside the code:
```javascript
test('Rule RULE_001', () => {
  const context = { transactionAmount: 1500 };
  const result = evaluateCampaign_ABC123(context);
  expect(result).toContainEqual({ type: 'APPLY_DISCOUNT', parameters: { discountPercentage: 10 } });
});
```

### 11.2 Integration Testing
Test generated code with the rule evaluation engine to ensure proper integration.

### 11.3 Performance Testing
Benchmark generated code against performance requirements.

### 11.4 Flow Control Testing
Generate tests to verify correct behavior of flow control logic:

```javascript
test('Flow Control: Break on Success', () => {
  const context = { transactionAmount: 1500 };
  const result = evaluateCampaign_ABC123(context);
  expect(result).toHaveLength(1); // Only one action should be triggered before breaking
});

test('Flow Control: Goto on Failure', () => {
  const context = { transactionAmount: 500 };
  const result = evaluateCampaign_ABC123(context);
  expect(result).toContainEqual({ type: 'APPLY_ALTERNATIVE_DISCOUNT', parameters: { discountPercentage: 5 } });
});


## 12. Error Handling and Logging

### 12.1 Error Reporting
Generate code to report errors with context:
```javascript
catch (error) {
  console.error(`Error in Campaign ${campaignId}, Rule ${ruleId}:`, error, { context });
}
```

### 12.2 Logging
Include logging statements for debugging:
```javascript
console.log(`Evaluating rule ${ruleId} for transaction ${context.transactionId}`);
```

## 13. Documentation

### 13.1 Code Comments
Generate comprehensive comments explaining the logic:
```javascript
// Check if the transaction amount exceeds the threshold and the customer is in the GOLD tier
if (context.transactionAmount > THRESHOLD && context.customerTier === 'GOLD') {
  // Apply a 10% discount to reward high-value GOLD customers
  triggeredActions.push({ type: 'APPLY_DISCOUNT', parameters: { discountPercentage: 10 } });
}
```

### 13.2 Generated Code Documentation
Produce documentation detailing the structure and usage of the generated code.

