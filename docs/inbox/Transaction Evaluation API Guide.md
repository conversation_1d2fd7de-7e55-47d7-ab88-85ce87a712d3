---
title: Transaction Evaluation API Guide
classification: Confidential
created: 2024-08-27
updated: 2024-08-28
tags:
  - api
  - transaction-evaluation
  - guide
  - ruleforge
---

# Transaction Evaluation API Guide

## Document Specification

### Purpose
This document provides a comprehensive guide on using the Transaction Evaluation API in the RuleForge Campaign Rules Engine (CRE). It explains how to construct requests, interpret responses, and effectively use the API for evaluating transactions against campaign rules.

### Scope
This guide covers:
- Overview of the transaction evaluation process
- Constructing and sending transaction evaluation requests
- Interpreting transaction evaluation responses
- Handling common scenarios and edge cases
- Best practices for efficient transaction evaluation

This guide does not cover:
- Detailed internal workings of the transaction evaluation engine
- Creation or management of campaigns and entities
- Performance optimization of the Rules Engine itself

### Target Audience
- Developers integrating client systems with RuleForge CRE
- QA engineers testing transaction evaluation scenarios
- System architects designing rule-based systems
- Technical support personnel troubleshooting transaction evaluation issues

## Introduction to Transaction Evaluation

Transaction evaluation is the core process in RuleForge CRE where incoming transactions are assessed against defined campaign rules to determine which actions, if any, should be triggered. This process involves:

1. Receiving a transaction from an entity
2. Identifying applicable campaigns and rules
3. Evaluating the transaction against these rules
4. Determining which actions to trigger
5. Returning the list of triggered actions to the calling entity

## Transaction Evaluation API Endpoint

- **URL**: `/api/v1/evaluate`
- **Method**: POST
- **Authentication**: Bearer Token (JWT)

## Constructing the Request

### Request Headers

- `Content-Type: application/json`
- `Authorization: Bearer <your_access_token>`

### Request Body

The request body should follow the structure defined in the [[Transaction Request Schema]] document. Here's a basic structure:

```json
{
  "entityId": "string",
  "transactionContextId": "string",
  "transactionData": {
    "property1": "value1",
    "property2": "value2",
    ...
  }
}
```

## Sending the Request

Here's an example of how to send a transaction evaluation request using cURL:

```bash
curl -X POST https://api.ruleforge.com/v1/evaluate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "entityId": "RETAIL_POS",
    "transactionContextId": "PURCHASE",
    "transactionData": {
      "customerId": "CUST123",
      "totalAmount": 156.99,
      "productCount": 3,
      "customerType": "REGULAR"
    }
  }'
```

## Interpreting the Response

### Response Structure

The API will respond with a JSON object as defined in the [[Transaction Response Schema]] document. Here's a basic structure:

```json
{
  "actions": [
    {
      "type": "string",
      "parameters": {
        "param1": "value1",
        "param2": "value2",
        ...
      }
    }
  ],
  "evaluatedRules": [
    {
      "ruleId": "string",
      "triggered": boolean
    }
  ]
}
```

### Handling Actions

For each action in the response:

1. Identify the action type
2. Extract any parameters
3. Execute the action within your system
4. Log the action execution for auditing purposes

## Common Scenarios and Edge Cases

### No Actions Triggered

If no rules were triggered, you'll receive an empty actions array:

```json
{
  "actions": [],
  "evaluatedRules": [
    {
      "ruleId": "SUMMER_DISCOUNT",
      "triggered": false
    },
    {
      "ruleId": "LOYALTY_BONUS",
      "triggered": false
    }
  ]
}
```

### Multiple Actions Triggered

Multiple actions may be triggered for a single transaction. Process each action in the order they appear in the response.

### Invalid Transaction Context

If the transaction context is invalid (e.g., unknown entityId or transactionContextId), you'll receive an error response:

```json
{
  "error": {
    "code": "INVALID_TRANSACTION_CONTEXT",
    "message": "Unknown entity ID or transaction context ID provided",
    "details": {
      "entityId": "UNKNOWN_ENTITY",
      "transactionContextId": "UNKNOWN_CONTEXT"
    }
  }
}
```

## Best Practices

1. **Idempotency**: Ensure your system can handle potential duplicate evaluations gracefully.
2. **Error Handling**: Implement robust error handling for both API communication issues and action execution failures.
3. **Logging**: Log all transaction evaluation requests and responses for debugging and auditing.
4. **Timeouts**: Set appropriate timeouts for API calls to handle potential delays.
5. **Batching**: For high-volume scenarios, consider implementing a batching mechanism to evaluate multiple transactions in a single API call (if supported by your use case).
6. **Testing**: Thoroughly test with a variety of transaction scenarios to ensure correct evaluation and action execution.

## Performance Considerations

- The transaction evaluation process is designed to be fast, typically completing in under 100ms for most scenarios.
- Response times may vary based on the number and complexity of applicable rules.
- If you consistently experience slow response times, contact RuleForge support for assistance.

## Related Documents
- [[RuleForge Interface Overview]]
- [[Transaction Request Schema]]
- [[Transaction Response Schema]]
- [[Entity Integration Schema]]
- [[Rule Set Schema]]

## Version Control
Document Version: 2.0.0
Last Updated: [[2024-08-28]]
Author: [[RuleForge DevGenius]]
Next Review Date: [[2025-02-28]]

<!--
### Changelog:
* 2.0.0 (2024-08-28): Renamed document from "Rule Evaluation API Guide" to "Transaction Evaluation API Guide". Updated terminology throughout to reflect this change.
* 1.1.0 (2024-08-28): Updated to align with latest Transaction Evaluation Request and Response JSON structures.
* 1.0.1 (2024-08-27): Restructured document to align with updated template format.
* 1.0.0 (2024-08-27): Initial version of the Rule Evaluation API Guide.
-->

## Approvals
| Role/Department | Name | Date | Signature |
|-----------------|------|------|-----------|
| Lead Architect  |      |      |           |
| API Team Lead   |      |      |           |
| Documentation Manager |      |      |           |

---
© 2024 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Document Template v1.6. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->