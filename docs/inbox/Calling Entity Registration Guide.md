---
title: Calling Entity Registration Guide
classification: Confidential
created: 2024-08-27
updated: 2024-09-03
tags:
  - entity-registration
  - guide
  - ruleforge
---

# Calling Entity Registration Guide

## Document Specification

### Purpose
This document provides a comprehensive guide on how to register entities with the RuleForge Campaign Rules Engine (CRE). It outlines the process, requirements, and best practices for entity registration.

### Scope
This guide covers:
- Overview of entities in RuleForge CRE
- Step-by-step registration process
- Entity registration JSON structure
- Best practices for entity registration
- Updating and managing registered entities

This guide does not cover:
- Internal processing of entity registrations by the Rules Engine
- Detailed API specifications (covered in separate API documentation)
- Campaign creation and management

### Target Audience
- Developers integrating new systems with RuleForge CRE
- System architects designing entity structures
- Technical managers overseeing RuleForge CRE implementations

## Introduction to Entities

Entities in RuleForge CRE represent external systems or channels that interact with the Rules Engine. Each entity can have multiple transaction contexts and defines its own set of global actions and context-specific actions that can be triggered by rules.

## Entity Registration Process

### Prerequisites
- Obtain API credentials for RuleForge CRE
- Identify all transaction contexts for your entity
- Define global and context-specific actions

### Registration Steps
1. Prepare the entity registration JSON (see section 4)
2. Send a POST request to the entity registration endpoint
3. Handle the response and store the assigned entityId

### API Endpoint
- URL: `/api/v1/entities`
- Method: POST
- Headers:
  - Content-Type: application/json
  - Authorization: Bearer <your_api_token>

## Entity Registration JSON Structure

Refer to the [[Entity Integration Schema]] document for detailed JSON structure. Here's a brief overview:

```json
{
  "entityId": "string",
  "entityName": "string",
  "entityDescription": "string",
  "globalActions": [],
  "transactionContexts": []
}
```

Key components:
- `globalActions`: Actions available across all transaction contexts
- `transactionContexts`: Array of contexts, each with its own properties and actions
- `persistentKey`: Boolean flag for properties that can be used as keys for persistent variables

## Best Practices for Entity Registration

### Naming Conventions
- Use clear, descriptive names for entities, contexts, and actions
- Follow a consistent naming pattern (e.g., UPPER_SNAKE_CASE for IDs)

### Action Definitions
- Define actions at the most appropriate level (global or context-specific)
- Provide clear descriptions for all actions and their parameters

### Transaction Context Design
- Design contexts to be as generic as possible while capturing necessary details
- Consider future extensibility when defining context properties
- Carefully choose which properties should be marked as `persistentKey`

## Updating Registered Entities

### Update Process
1. Prepare the updated entity JSON
2. Send a PUT request to `/api/v1/entities/{entityId}`
3. Handle the response and update local records if necessary

### Versioning Considerations
- Consider versioning your entity definitions
- Be mindful of backwards compatibility when updating contexts or actions

## Managing Multiple Entities

### Entity Segregation
- Use separate entities for significantly different systems or channels
- Balance between granularity and manageability

### Cross-Entity Interactions
- Understand limitations of cross-entity rule evaluations
- Design your entity structure to support your business processes

## Troubleshooting

### Common Registration Issues
- Invalid JSON structure
- Duplicate entity or action IDs
- Missing required fields
- Inconsistent use of `persistentKey` flags

### Support Resources
- Refer to the API documentation for error codes and messages
- Contact RuleForge support for persistent issues

## Related Documents
- [[Entity Integration Schema]]
- [[RuleForge Interface Overview]]
- [[Rule Set Schema]]

## Version Control
Document Version: 1.1.1
Last Updated: [[2024-09-03]]
Author: [[Faraz Ali]]
Next Review Date: [[2025-02-28]]

<!--
### Changelog:
* 1.1.1 (2024-09-03): Removed reference to the Transaction Context JSON structure
* 1.1.0 (2024-08-28): Updated to reflect latest entity structure, including global actions and persistentKey concept.
* 1.0.1 (2024-08-27): Restructured document to align with updated template format.
* 1.0.0 (2024-08-27): Initial version of the Calling Entity Registration Guide.
-->

## Approvals
| Role/Department | Name | Date | Signature |
|-----------------|------|------|-----------|
| Lead Architect  |      |      |           |
| API Team Lead   |      |      |           |
| Documentation Manager |      |      |           |

---
© 2024 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Document Template v1.6. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->