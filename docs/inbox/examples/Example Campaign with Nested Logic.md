# Example Campaign with Nested Logic

This document provides an example of a marketing campaign that utilises nested logic in its rule conditions. The campaign demonstrates how complex business rules can be represented using the Campaign Rules Engine's JSON structure.

## Campaign: Summer Sale 2024

### Campaign JSON Structure

```json
{
  "campaignId": "SUMMER_SALE_2024",
  "name": "Summer Sale 2024",
  "startDate": "2024-06-01T00:00:00Z",
  "endDate": "2024-08-31T23:59:59Z",
  "persistentVariableDefinitions": [
    {
      "name": "totalPurchasesThisSummer",
      "type": "integer",
      "initialValue": 0,
      "scope": "subscriber"
    }
  ],
  "ruleDefinitions": [
    {
      "ruleId": "TIERED_DISCOUNT_RULE",
      "name": "Tiered Discount with Loyalty Bonus",
      "callingEntityId": "BILLING_SYSTEM",
      "transactionContextId": "PRODUCT_PURCHASE",
      "sequence": 1,
      "conditions": {
        "type": "COMPOUND",
        "logicalOperator": "OR",
        "conditions": [
          {
            "type": "COMPOUND",
            "logicalOperator": "AND",
            "conditions": [
              {
                "type": "COMPARISON",
                "parameters": {
                  "leftOperand": "{transactionAmount}",
                  "operator": ">=",
                  "rightOperand": 100
                }
              },
              {
                "type": "COMPARISON",
                "parameters": {
                  "leftOperand": "{customerTier}",
                  "operator": "==",
                  "rightOperand": "GOLD"
                }
              }
            ]
          },
          {
            "type": "COMPOUND",
            "logicalOperator": "AND",
            "conditions": [
              {
                "type": "COMPARISON",
                "parameters": {
                  "leftOperand": "{transactionAmount}",
                  "operator": ">=",
                  "rightOperand": 50
                }
              },
              {
                "type": "COMPARISON",
                "parameters": {
                  "leftOperand": "{totalPurchasesThisSummer}",
                  "operator": ">",
                  "rightOperand": 200
                }
              },
              {
                "type": "COMPOUND",
                "logicalOperator": "OR",
                "conditions": [
                  {
                    "type": "COMPARISON",
                    "parameters": {
                      "leftOperand": "{dayOfWeek}",
                      "operator": "IN",
                      "rightOperand": [6, 7]
                    }
                  },
                  {
                    "type": "COMPARISON",
                    "parameters": {
                      "leftOperand": "{isHoliday}",
                      "operator": "==",
                      "rightOperand": true
                    }
                  }
                ]
              }
            ]
          }
        ]
      },
      "actions": [
        {
          "type": "APPLY_DISCOUNT",
          "parameters": {
            "discountPercentage": 15
          }
        },
        {
          "type": "SEND_SMS",
          "parameters": {
            "message": "Enjoy your Summer Sale discount!"
          }
        }
      ],
      "variableOperations": [
        {
          "variableName": "totalPurchasesThisSummer",
          "operation": "ADD",
          "value": "{transactionAmount}"
        }
      ]
    }
  ]
}
```

### Explanation

This campaign, "Summer Sale 2024", runs from June 1st to August 31st, 2024. It includes a single rule with complex nested logic to determine when to apply a discount.

#### Persistent Variables

- `totalPurchasesThisSummer`: Tracks the total amount of purchases made by each subscriber during the campaign period.

#### Rule: Tiered Discount with Loyalty Bonus

The rule applies a 15% discount and sends an SMS if either of these conditions is met:

1. The transaction amount is $100 or more AND the customer is in the GOLD tier.
2. The transaction amount is $50 or more AND the customer has already spent over $200 this summer AND (it's a weekend OR it's a holiday).

In plain English:

```
IF (amount >= $100 AND customerTier == GOLD) OR
   (amount >= $50 AND totalPurchasesThisSummer > $200 AND (isWeekend OR isHoliday))
THEN
   Apply 15% discount
   Send SMS "Enjoy your Summer Sale discount!"
   Add transaction amount to totalPurchasesThisSummer
```

This rule demonstrates how nested logic can be used to create sophisticated conditions that combine transaction data, customer attributes, campaign-specific variables, and time-based factors.

### Key Points

1. **Nested Logic**: The rule uses nested compound conditions to create complex logical structures.
2. **Multiple Data Sources**: The conditions reference transaction data (`transactionAmount`), customer data (`customerTier`), persistent variables (`totalPurchasesThisSummer`), and global context (`dayOfWeek`, `isHoliday`).
3. **Persistent Variable Usage**: The rule both checks and updates the `totalPurchasesThisSummer` variable, demonstrating how persistent variables can influence and be influenced by rule execution.
4. **Multiple Actions**: The rule triggers both a discount application and an SMS notification.

This example showcases how the Campaign Rules Engine can be used to create nuanced, context-aware marketing campaigns that respond to a variety of factors in real-time.
