# Complex Campaign JSON - Multi-Tiered Loyalty Program

## Campaign Overview

This campaign implements a multi-tiered loyalty program for a Mobile Network Operator (MNO). It includes rules for different transaction types, customer tiers, and promotional activities.

## Campaign Structure

```json
{
  "campaignId": "LOYALTY_PROGRAM_2024",
  "name": "Multi-Tiered Loyalty Program 2024",
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-12-31T23:59:59Z",
  "persistentVariableDefinitions": [
    {
      "name": "loyaltyPoints",
      "type": "integer",
      "initialValue": 0,
      "scope": "subscriber"
    },
    {
      "name": "currentTier",
      "type": "string",
      "initialValue": "BRONZE",
      "scope": "subscriber"
    },
    {
      "name": "lastUpgradeDate",
      "type": "date",
      "initialValue": null,
      "scope": "subscriber"
    }
  ],
  "localVariableDefinitions": [
    {
      "name": "pointsEarned",
      "type": "integer",
      "initialValue": 0
    },
    {
      "name": "upgradeEligible",
      "type": "boolean",
      "initialValue": false
    }
  ],
  "ruleDefinitions": [
    // Rules will be defined here
  ]
}
```

## Rule Definitions

### 1. Airtime Purchase Points

```json
{
  "ruleId": "AIRTIME_POINTS",
  "name": "Award Points for Airtime Purchase",
  "callingEntityId": "BILLING_SYSTEM",
  "transactionContextId": "AIRTIME_PURCHASE",
  "sequence": 1,
  "conditions": [
    {
      "type": "COMPARISON",
      "parameters": {
        "leftOperand": "{transactionAmount}",
        "operator": ">=",
        "rightOperand": 5
      }
    }
  ],
  "actions": [
    {
      "type": "AWARD_POINTS",
      "parameters": {
        "points": "{pointsEarned}"
      }
    }
  ],
  "variableOperations": [
    {
      "variableName": "pointsEarned",
      "operation": "SET",
      "value": "{transactionAmount} / 5"
    },
    {
      "variableName": "loyaltyPoints",
      "operation": "ADD",
      "value": "{pointsEarned}"
    }
  ]
}
```

### 2. Data Bundle Purchase Bonus

```json
{
  "ruleId": "DATA_BUNDLE_BONUS",
  "name": "Bonus Data for Bundle Purchase",
  "callingEntityId": "BILLING_SYSTEM",
  "transactionContextId": "DATA_BUNDLE_PURCHASE",
  "sequence": 1,
  "conditions": [
    {
      "type": "COMPARISON",
      "parameters": {
        "leftOperand": "{bundleSize}",
        "operator": ">=",
        "rightOperand": 5
      }
    }
  ],
  "actions": [
    {
      "type": "AWARD_BONUS_DATA",
      "parameters": {
        "bonusPercentage": 10
      }
    },
    {
      "type": "SEND_SMS",
      "parameters": {
        "message": "Congratulations! You've received 10% bonus data on your purchase."
      }
    }
  ],
  "variableOperations": [
    {
      "variableName": "loyaltyPoints",
      "operation": "ADD",
      "value": "{bundleSize} * 2"
    }
  ]
}
```

### 3. Tier Upgrade Check

```json
{
  "ruleId": "TIER_UPGRADE_CHECK",
  "name": "Check and Perform Tier Upgrade",
  "callingEntityId": "LOYALTY_SYSTEM",
  "transactionContextId": "DAILY_CHECK",
  "sequence": 1,
  "conditions": [
    {
      "type": "COMPOUND",
      "logicalOperator": "AND",
      "conditions": [
        {
          "type": "COMPARISON",
          "parameters": {
            "leftOperand": "{loyaltyPoints}",
            "operator": ">=",
            "rightOperand": 1000
          }
        },
        {
          "type": "COMPARISON",
          "parameters": {
            "leftOperand": "{currentTier}",
            "operator": "!=",
            "rightOperand": "PLATINUM"
          }
        }
      ]
    }
  ],
  "actions": [
    {
      "type": "UPGRADE_TIER",
      "parameters": {
        "newTier": "{upgradeToTier}"
      }
    },
    {
      "type": "SEND_SMS",
      "parameters": {
        "message": "Congratulations! You've been upgraded to {upgradeToTier} tier."
      }
    }
  ],
  "variableOperations": [
    {
      "variableName": "upgradeToTier",
      "operation": "SET",
      "value": "FUNCTION:determineUpgradeTier({loyaltyPoints}, {currentTier})"
    },
    {
      "variableName": "currentTier",
      "operation": "SET",
      "value": "{upgradeToTier}"
    },
    {
      "variableName": "lastUpgradeDate",
      "operation": "SET",
      "value": "{currentDate}"
    }
  ]
}
```

### 4. Weekend Double Points Promotion

```json
{
  "ruleId": "WEEKEND_DOUBLE_POINTS",
  "name": "Double Points on Weekends",
  "callingEntityId": "BILLING_SYSTEM",
  "transactionContextId": "ALL_PURCHASES",
  "sequence": 2,
  "conditions": [
    {
      "type": "COMPOUND",
      "logicalOperator": "AND",
      "conditions": [
        {
          "type": "TIME_BASED",
          "parameters": {
            "condition": "IS_WEEKEND"
          }
        },
        {
          "type": "COMPARISON",
          "parameters": {
            "leftOperand": "{pointsEarned}",
            "operator": ">",
            "rightOperand": 0
          }
        }
      ]
    }
  ],
  "actions": [
    {
      "type": "AWARD_POINTS",
      "parameters": {
        "points": "{pointsEarned}"
      }
    },
    {
      "type": "SEND_SMS",
      "parameters": {
        "message": "It's the weekend! You've earned double points on your purchase."
      }
    }
  ],
  "variableOperations": [
    {
      "variableName": "loyaltyPoints",
      "operation": "ADD",
      "value": "{pointsEarned}"
    }
  ]
}
```

### 5. Bill Payment Discount

```json
{
  "ruleId": "BILL_PAYMENT_DISCOUNT",
  "name": "Discount for Early Bill Payment",
  "callingEntityId": "BILLING_SYSTEM",
  "transactionContextId": "BILL_PAYMENT",
  "sequence": 1,
  "conditions": [
    {
      "type": "COMPOUND",
      "logicalOperator": "AND",
      "conditions": [
        {
          "type": "COMPARISON",
          "parameters": {
            "leftOperand": "{daysToDueDate}",
            "operator": ">=",
            "rightOperand": 7
          }
        },
        {
          "type": "COMPARISON",
          "parameters": {
            "leftOperand": "{currentTier}",
            "operator": "IN",
            "rightOperand": ["GOLD", "PLATINUM"]
          }
        }
      ]
    }
  ],
  "actions": [
    {
      "type": "APPLY_DISCOUNT",
      "parameters": {
        "discountPercentage": 5
      }
    },
    {
      "type": "SEND_SMS",
      "parameters": {
        "message": "Thank you for your early payment! You've received a 5% discount."
      }
    }
  ],
  "variableOperations": [
    {
      "variableName": "loyaltyPoints",
      "operation": "ADD",
      "value": "{billAmount} / 10"
    }
  ]
}
```

## Explanation

This complex campaign JSON demonstrates several key features of our Campaign Rules Engine:

1. **Multiple Transaction Contexts**: Rules are defined for various contexts like airtime purchases, data bundle purchases, bill payments, and a daily check.

2. **Persistent Variables**: `loyaltyPoints`, `currentTier`, and `lastUpgradeDate` are used to maintain subscriber state across transactions.

3. **Local Variables**: `pointsEarned` and `upgradeEligible` are used for intermediate calculations within rule executions.

4. **Complex Conditions**: The rules use both simple and compound conditions, combining multiple factors like transaction amount, current tier, and time-based conditions.

5. **Diverse Actions**: The campaign includes actions for awarding points, upgrading tiers, applying discounts, and sending SMS notifications.

6. **Variable Operations**: The rules demonstrate various operations on both local and persistent variables, including calculations based on transaction data.

7. **Time-Based Rules**: The weekend double points promotion showcases how time-based conditions can be incorporated.

8. **Tier-Based Benefits**: Different rules apply based on the subscriber's current tier, demonstrating the multi-tiered nature of the loyalty program.

This campaign structure allows for a dynamic and engaging loyalty program that responds to various subscriber actions across different transaction types, while maintaining a coherent set of rules and benefits.

