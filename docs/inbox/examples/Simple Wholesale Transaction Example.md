# EMoov Retailers Bonus Campaign (Lomé Region)

## Problem Statement Analysis

### Campaign Goal

Increase airtime purchases and incentivise higher transaction volumes among EMoov retailers in the Lomé region by offering an additional trade bonus.

### Key Objectives

1. Encourage EMoov retailers to make more frequent airtime purchases from Reseller Agents.
2. Reward high-volume retailers with an additional 2% trade bonus.
3. Stimulate consistent purchasing behavior throughout the campaign period.

### Target Audience

EMoov retailers located in the Lomé region.

### Constraints and Requirements

- Campaign Duration: July 1 to December 31, 2024
- Campaign is limited to EMoov agents, in the Lomé region (zone 6001)
- Monthly transaction target: 100 airtime purchases
- Additional trade bonus: 2% on purchases after reaching the target
- Monthly reset of transaction count

### Calling Entity and Transaction Context

- Entity: CREDIVERSE
- Transaction Context: WHOLESALE_AIRTIME_PURCHASE

## Campaign Strategy Formulation

1. Monthly Transaction Tracking:
    - Implement a persistent variable to count monthly transactions for each EMoov retailer.
    - Reset this counter at the beginning of each month.
2. Bonus Activation:
    - Once a retailer reaches 100 transactions in a month, activate the additional 2% bonus for subsequent purchases.
3. Targeted Communication:
    - Send SMS notifications to retailers when they're close to reaching their monthly target.
    - Congratulate retailers upon reaching the target and inform them of the activated bonus.
4. Performance Monitoring:
    - Track the number of retailers reaching the monthly target.
    - Monitor the increase in transaction volume and total bonus payouts.

## Campaign Structure and Details

### Persistent Variables

1. `monthlyTransactionCount`: Tracks the number of eligible purchases made by each EMoov agent in the current month.

### Rules

1. Transaction Counter Rule
    - Increment `monthlyTransactionCount` for each transaction by EMoov agent.
2. Bonus Application Rule
    - If `monthlyTransactionCount` exceed 100, apply the additional 2% bonus to the next transaction.
3. Monthly Reset Rule
    - Reset `monthlyTransactionCount` to 0  at the beginning of each month.
4. Notification Rules
    - Send an SMS when the retailer reaches 90 transactions in a month.
    - Send a congratulatory SMS when the retailer reaches 100 transactions.

### Actions

- `OverrideTradeBonus`: To apply the additional 2% bonus.
- `SendSms`: To send notifications and congratulatory messages.

## JSON Campaign Definitions

### Crediverse Entity Registration JSON
```json
{
  "entityId": "CREDIVERSE",
  "entityName": "Crediverse EVD System",
  "entityDescription": "Manages airtime distribution between agents in the Crediverse network",
  "globalActions": [],
  "transactionContexts": [
    {
      "contextId": "WHOLESALE_AIRTIME_PURCHASE",
      "contextName": "Wholesale Airtime Credit Purchase",
      "contextDescription": "Represents a wholesale airtime stock purchase transaction between agents",
      "properties": [
        {
          "name": "buyerAgentMsisdn",
          "type": "string",
          "description": "The unique MSISDN for the agent buying the airtime credit",
          "persistentKey": true
        },
        {
          "name": "buyerAgentTierName",
          "type": "string",
          "description": "Tier name that the buying agent belongs to"
        },
        {
          "name": "buyerAgentGroups",
          "type": "array",
          "description": "Array of Group IDs the buyer agent belongs to",
          "items": {
            "type": "integer"
          }
        },
        {
          "name": "buyerAgentCgi",
          "type": "string",
          "description": "The Cell Global Identity where the buyer agent is at the time of transaction"
        },
        {
          "name": "buyerAgentZones",
          "type": "array",
          "description": "The list of unique zone IDs where the buyer agent is located at the time of transaction",
          "items": {
            "type": "integer"
          }
        },
        {
          "name": "buyerAgentLanguage",
          "type": "string",
          "description": "The language setting of the buyer agent"
        },
        {
          "name": "sellerAgentMsisdn",
          "type": "string",
          "description": "The unique MSISDN for the agent selling the airtime credit"
        },
        {
          "name": "sellerAgentTierName",
          "type": "string",
          "description": "Tier name that the selling agent belongs to"
        },
        {
          "name": "sellerAgentGroups",
          "type": "array",
          "description": "Array of Group IDs the seller agent belongs to",
          "items": {
            "type": "integer"
          }
        },
        {
          "name": "sellerAgentCgi",
          "type": "string",
          "description": "The Cell Global Identity where the seller agent is located at the time of transaction"
        },
        {
          "name": "sellerAgentZones",
          "type": "array",
          "description": "The list of unique zone IDs where the seller agent is located at the time of transaction",
          "items": {
            "type": "integer"
          }
        },
        {
          "name": "sellerAgentLanguage",
          "type": "string",
          "description": "The language setting of the seller agent"
        },
        {
          "name": "purchaseAmount",
          "type": "amount",
          "description": "The monetary value of airtime credit stock being purchased"
        },
        {
          "name": "currentTradeBonusAmount",
          "type": "amount",
          "description": "The current trade bonus applicable to the transaction"
        }
      ],
      "contextSpecificActions": [
        {
          "type": "SendSms",
          "name": "Send SMS",
          "description": "Send targeted messages to agents or subscribers",
          "parameters": [
            {
              "name": "recipientType",
              "type": "enum",
              "description": "The type of recipient the SMS message needs to be delivered to",
              "values": ["SELLER", "BUYER"]
            },
            {
              "name": "message",
              "type": "string",
              "description": "The SMS message which is to be delivered"
            }
          ]
        },
        {
          "type": "OverrideStandardUssdResponse",
          "name": "Override Standard USSD Response",
          "description": "Override default USSD responses within the transaction",
          "parameters": [
            {
              "name": "newResponse",
              "type": "string",
              "description": "Replacement message for the seller's USSD transaction response message"
            }
          ]
        },
        {
          "type": "OverrideStandardSmsResponse",
          "name": "Override Standard SMS Response",
          "description": "Override default SMS responses",
          "parameters": [
            {
              "name": "recipientType",
              "type": "enum",
              "description": "The type of the recipient whose default SMS message needs to be overwritten",
              "values": ["SELLER", "BUYER"]
            },
            {
              "name": "newResponse",
              "type": "string",
              "description": "The SMS message text that needs to replace the default SMS message text"
            }
          ]
        },
        {
          "type": "OverrideTradeBonus",
          "name": "Override Trade Bonus",
          "description": "Overrides the default trade bonus for agent stock purchases using an Adjustment Account within Crediverse",
          "parameters": [
            {
              "name": "newTradeBonusAmount",
              "type": "amount",
              "description": "The new trade bonus amount which is to be awarded"
            },
            {
              "name": "adjustmentAccountMsisdn",
              "type": "string",
              "description": "The adjustment account on Crediverse from which the trade bonus difference needs to be deducted"
            }
          ]
        }
      ]
    }
  ]
}
```


### Campaign JSON

```json
{
  "schemaVersion": "2.2.0",
  "campaignId": "EMOOV_LOME_RESELLER_PROMO_2024",
  "name": "EMoov Lomé Reseller Promotion 2024",
  "description": "Incentivize EMoov agents in Lomé to increase wholesale purchases from Resellers",
  "campaignVersion": 1,
  "status": "ACTIVE",
  "startDateTime": "2024-07-01T00:00:00Z",
  "endDateTime": "2024-12-31T23:59:59Z",
  "lastModifiedDateTime": "2024-06-15T10:30:00Z",
  "collectionMappings": [
    {
      "collectionName": "agents",
      "keyMappings": [
        {
          "entityId": "CREDIVERSE",
          "contextId": "WHOLESALE_AIRTIME_PURCHASE",
          "propertyName": "buyerAgentId"
        }
      ]
    }
  ],
  "persistentVariableDefinitions": [
    {
      "variableId": "monthlyTransactionCount",
      "name": "Monthly Agent Transaction Count",
      "description": "Number of eligible purchases made by each EMoov agent in the current month",
      "type": "integer",
      "defaultValue": 0,
      "collection": "agents"
    },
    {
      "variableId": "lastResetMonth",
      "name": "Last Reset Month",
      "description": "The month when the transaction count was last reset",
      "type": "integer",
      "defaultValue": 6,
      "collection": "agents"
    }
  ],
  "entities": [
    {
      "entityId": "CREDIVERSE",
      "entityName": "Crediverse EVD System",
      "transactionContexts": [
        {
          "contextId": "WHOLESALE_AIRTIME_PURCHASE",
          "contextName": "Wholesale Airtime Credit Purchase",
          "rules": [
            {
              "ruleId": "MONTHLY_RESET_RULE",
              "name": "Reset Transaction Count for New Month",
              "description": "Resets the monthly transaction count if it's a new month",
              "priority": 1,
              "condition": {
                "type": "LOGICAL",
                "operator": "AND",
                "parameters": {
                  "conditions": [
                    {
                      "type": "COMPARISON",
                      "operator": ">",
                      "parameters": {
                        "leftOperand": "{CURRENT_MONTH}",
                        "rightOperand": "{lastResetMonth}"
                      }
                    },
                    {
                      "type": "COMPARISON",
                      "operator": "==",
                      "parameters": {
                        "leftOperand": "{buyerAgentTierName}",
                        "rightOperand": "EMOOV"
                      }
                    },
                    {
                      "type": "COMPARISON",
                      "operator": "CONTAINS",
                      "parameters": {
                        "leftOperand": "{buyerAgentZones}",
                        "rightOperand": 6001
                      }
                    }
                  ]
                }
              },
              "actions": [],
              "variableOperations": [
                {
                  "variableId": "monthlyTransactionCount",
                  "operation": "SET",
                  "value": 1
                },
                {
                  "variableId": "lastResetMonth",
                  "operation": "SET",
                  "value": "{CURRENT_MONTH}"
                }
              ]
            },
            {
              "ruleId": "TRANSACTION_COUNTER_RULE",
              "name": "Increment Transaction Count",
              "description": "Increments the monthly transaction count for eligible EMoov agents in Lomé",
              "priority": 2,
              "condition": {
                "type": "LOGICAL",
                "operator": "AND",
                "parameters": {
                  "conditions": [
                    {
                      "type": "COMPARISON",
                      "operator": "==",
                      "parameters": {
                        "leftOperand": "{CURRENT_MONTH}",
                        "rightOperand": "{lastResetMonth}"
                      }
                    },
                    {
                      "type": "COMPARISON",
                      "operator": "==",
                      "parameters": {
                        "leftOperand": "{buyerAgentTierName}",
                        "rightOperand": "EMOOV"
                      }
                    },
                    {
                      "type": "COMPARISON",
                      "operator": "CONTAINS",
                      "parameters": {
                        "leftOperand": "{buyerAgentZones}",
                        "rightOperand": 6001
                      }
                    }
                  ]
                }
              },
              "actions": [],
              "variableOperations": [
                {
                  "variableId": "monthlyTransactionCount",
                  "operation": "ADD",
                  "value": 1
                }
              ]
            },
            {
              "ruleId": "BONUS_ACTIVATION_RULE",
              "name": "Apply Bonus for Eligible Transactions",
              "description": "Applies 2% trade bonus for transactions after the 100th purchase in a month",
              "priority": 3,
              "condition": {
                "type": "LOGICAL",
                "operator": "AND",
                "parameters": {
                  "conditions": [
                    {
                      "type": "COMPARISON",
                      "operator": "==",
                      "parameters": {
                        "leftOperand": "{buyerAgentTierName}",
                        "rightOperand": "EMOOV"
                      }
                    },
                    {
                      "type": "COMPARISON",
                      "operator": "CONTAINS",
                      "parameters": {
                        "leftOperand": "{buyerAgentZones}",
                        "rightOperand": 6001
                      }
                    },
                    {
                      "type": "COMPARISON",
                      "operator": ">",
                      "parameters": {
                        "leftOperand": "{monthlyTransactionCount}",
                        "rightOperand": 100
                      }
                    }
                  ]
                }
              },
              "actions": [
                {
                  "type": "OverrideTradeBonus",
                  "parameters": {
                    "newTradeBonusAmount": "{currentTradeBonusAmount} + ({purchaseAmount} * 0.02)",
                    "adjustmentAccountMSISDN": "SYSTEM_ADJUSTMENT_ACCOUNT"
                  }
                },
                {
                  "type": "SendSms",
                  "parameters": {
                    "recipientType": "BUYER",
                    "message": "Congratulations! You've unlocked a 2% bonus on this transaction. Keep purchasing to maintain your bonus until the end of the month!"
                  }
                }
              ],
              "variableOperations": []
            },
            {
              "ruleId": "APPROACHING_THRESHOLD_NOTIFICATION",
              "name": "Notify Agent Approaching Bonus Threshold",
              "description": "Sends a notification when an agent is close to reaching the bonus threshold",
              "priority": 4,
              "condition": {
                "type": "LOGICAL",
                "operator": "AND",
                "parameters": {
                  "conditions": [
                    {
                      "type": "COMPARISON",
                      "operator": "==",
                      "parameters": {
                        "leftOperand": "{buyerAgentTierName}",
                        "rightOperand": "EMOOV"
                      }
                    },
                    {
                      "type": "COMPARISON",
                      "operator": "CONTAINS",
                      "parameters": {
                        "leftOperand": "{buyerAgentZones}",
                        "rightOperand": 6001
                      }
                    },
                    {
                      "type": "COMPARISON",
                      "operator": "==",
                      "parameters": {
                        "leftOperand": "{monthlyTransactionCount}",
                        "rightOperand": 95
                      }
                    }
                  ]
                }
              },
              "actions": [
                {
                  "type": "SendSms",
                  "parameters": {
                    "recipientType": "BUYER",
                    "message": "You're only 5 purchases away from unlocking a 2% bonus on all subsequent transactions this month!"
                  }
                }
              ],
              "variableOperations": []
            }
          ]
        }
      ]
    }
  ]
}
```


## Performance Considerations

1. Variable Operations: The campaign frequently updates persistent variables. Ensure that these operations are optimized to handle high transaction volumes.
2. Rule Evaluation: The rules are designed with priorities to ensure efficient evaluation. The most frequently triggered rules are  (MONTHLY_RESET_RULE and TRANSACTION_COUNTER_RULE ).
3. SMS Notifications: The campaign sends SMS notifications at specific milestones. Monitor the SMS sending system to ensure it can handle the potential volume, especially at the beginning of each month when many retailers might reach their targets simultaneously.
4. Monthly Resets: Implement an efficient mechanism to reset the persistent variables at the start of each month. This could potentially be a high-load operation if done for all retailers simultaneously.

## Conclusion

The EMoov Retailers Bonus Campaign for the Lomé region is designed to incentivise higher transaction volumes among EMoov retailers. By offering an additional 2% trade bonus for retailers who complete 100 transactions in a month, the campaign encourages consistent purchasing behaviour.

Key strengths of the campaign:

1. Clear and achievable target for retailers
2. Immediate reward upon reaching the target
3. Monthly reset mechanism to maintain engagement throughout the campaign period
4. Targeted notifications to keep retailers informed and motivate