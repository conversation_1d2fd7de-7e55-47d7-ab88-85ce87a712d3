# Agent Sales Boost Campaign

## 1. Campaign Overview

### Objective

Boost agent sales performance by offering a short-term incentive structure.

### Key Goals

1. Increase overall sales volume across the agent network
2. Encourage agents to pursue larger individual sales
3. Motivate consistent sales performance over a short period

### Target Audience

The entire network of sales agents within the Crediverse system.

### Campaign Duration

30 days (e.g., September 1, 2024 - September 30, 2024)

## 2. Campaign Strategy

### Incentive Structure

- Tiered bonus system based on transaction amount
- Extended bonus eligibility for multiple transactions
- Bonus cap to manage costs

### Bonus Tiers

1. Tier 1: 2% bonus on next 5 transactions for sales ≥ 500 units
2. Tier 2: 3% bonus on next 7 transactions for sales ≥ 1000 units
3. Tier 3: 5% bonus on next 10 transactions for sales ≥ 2000 units

### Bonus Cap

Maximum bonus accumulation of 500 units of local currency per agent

### Communication

Utilize existing agent communication channels for campaign updates

## 3. Technical Implementation

### Persistent Variables

```json
"persistentVariableDefinitions": [
  {
    "name": "bonusEligibleTransactions",
    "type": "integer",
    "initialValue": 0,
    "scope": "agent"
  },
  {
    "name": "currentBonusTier",
    "type": "integer",
    "initialValue": 0,
    "scope": "agent"
  },
  {
    "name": "totalBonusAccumulated",
    "type": "amount",
    "initialValue": 0,
    "scope": "agent"
  }
]
```

### Rules

#### Rule 1: Qualify for Bonus Tier

```json
{
  "ruleId": "QUALIFY_BONUS_TIER",
  "name": "Qualify for Bonus Tier",
  "transactionContextId": "RETAIL_AIRTIME_RECHARGE,BUNDLE_SALES",
  "conditions": [
    {
      "type": "COMPARISON",
      "parameters": {
        "leftOperand": "{transactionAmount}",
        "operator": ">=",
        "rightOperand": 500
      }
    }
  ],
  "actions": [
    {
      "type": "ModifyAgentTradeBonus",
      "parameters": {
        "bonusType": "PERCENT",
        "bonusValue": 2
      }
    },
    {
      "type": "SendSMS",
      "parameters": {
        "recipientType": "AGENT",
        "message": "Congratulations! You've qualified for a 2% bonus on your next 5 transactions!"
      }
    }
  ],
  "variableOperations": [
    {
      "variableName": "bonusEligibleTransactions",
      "operation": "SET",
      "value": 5
    },
    {
      "variableName": "currentBonusTier",
      "operation": "SET",
      "value": 1
    }
  ]
}
```

#### Rule 2: Apply Bonus to Eligible Transaction

```json
{
  "ruleId": "APPLY_BONUS",
  "name": "Apply Bonus to Eligible Transaction",
  "transactionContextId": "RETAIL_AIRTIME_RECHARGE,BUNDLE_SALES",
  "conditions": [
    {
      "type": "COMPARISON",
      "parameters": {
        "leftOperand": "{bonusEligibleTransactions}",
        "operator": ">",
        "rightOperand": 0
      }
    }
  ],
  "actions": [
    {
      "type": "ModifyAgentTradeBonus",
      "parameters": {
        "bonusType": "PERCENT",
        "bonusValue": "{currentBonusTier * 2}"
      }
    }
  ],
  "variableOperations": [
    {
      "variableName": "bonusEligibleTransactions",
      "operation": "SUBTRACT",
      "value": 1
    },
    {
      "variableName": "totalBonusAccumulated",
      "operation": "ADD",
      "value": "{transactionAmount * currentBonusTier * 0.02}"
    }
  ]
}
```

#### Rule 3: Bonus Cap Check

```json
{
  "ruleId": "BONUS_CAP_CHECK",
  "name": "Bonus Cap Check",
  "transactionContextId": "RETAIL_AIRTIME_RECHARGE,BUNDLE_SALES",
  "conditions": [
    {
      "type": "COMPARISON",
      "parameters": {
        "leftOperand": "{totalBonusAccumulated}",
        "operator": ">=",
        "rightOperand": 500
      }
    }
  ],
  "actions": [
    {
      "type": "SendSMS",
      "parameters": {
        "recipientType": "AGENT",
        "message": "Congratulations! You've reached the maximum bonus of 500 units. Keep up the great work!"
      }
    }
  ],
  "variableOperations": [
    {
      "variableName": "bonusEligibleTransactions",
      "operation": "SET",
      "value": 0
    }
  ]
}
```

### Complete Campaign JSON

```json
{
  "campaignId": "AGENT_SALES_BOOST_2024",
  "name": "Agent Sales Boost Campaign 2024",
  "startDate": "2024-09-01T00:00:00Z",
  "endDate": "2024-09-30T23:59:59Z",
  "persistentVariableDefinitions": [
    // Include the persistent variables defined above
  ],
  "ruleDefinitions": [
    // Include the rules defined above
  ]
}
```

## 4. Implementation Considerations

1. **Calling Entities**: The primary calling entity will be the billing system, which will execute the defined actions during retail airtime recharge and bundle sales transactions.

2. **Transaction Contexts**: This campaign applies to both RETAIL_AIRTIME_RECHARGE and BUNDLE_SALES contexts. The Rules Engine will automatically evaluate the appropriate rules based on the context provided by the calling entity.

3. **Actions**: The defined actions (ModifyAgentTradeBonus and SendSMS) will be executed by the calling entity, not the Rules Engine.

4. **Persistent Variables**: The Rules Engine will manage the persistent variables across multiple transactions, maintaining the state of each agent's bonus eligibility and accumulation.

5. **Performance**: While the rules are designed to be lightweight, the calling entity should optimize action execution, particularly SMS sending, to prevent system overload.

6. **Daily Updates**: As the Rules Engine doesn't handle scheduling, the calling entity or a separate scheduling system will need to implement the daily performance update functionality, querying the persistent variables and sending SMS updates.

7. **Bonus Tiers**: The current implementation only includes the first bonus tier. To implement higher tiers, additional rules or more complex conditions will be needed.

## 5. Conclusion

This Agent Sales Boost Campaign leverages the Crediverse system's Campaign Rules Engine to create a dynamic, performance-based incentive structure for agents. By utilizing persistent variables and context-aware rules, the campaign can track and reward agent performance across multiple transactions and transaction types.

Key strengths of this campaign include:

- Automated bonus qualification and application
- Scalable structure that can be easily extended to include more bonus tiers
- Real-time performance tracking and communication with agents
- Cost control through bonus capping

For future iterations, consider:

- Implementing higher bonus tiers with more complex qualification criteria
- A/B testing different threshold levels and bonus percentages
- Introducing non-monetary rewards for sustained performance
- Analyzing sales patterns to optimize campaign timing and duration

By following this strategy and leveraging the capabilities of the Campaign Rules Engine, Crediverse can effectively boost agent performance and drive increased sales across its network.
