# Ramadan 2025 Strategic Growth Campaign

## Problem Statement Analysis

### Campaign Goal
To leverage the Ramadan period in 2025 to drive significant business growth, increase market share, and enhance long-term customer value in Islamabad through strategic evening discounts on airtime and bundle purchases.

### Key Objectives
1. Increase overall revenue by 15% during Ramadan 2025 compared to the previous year
2. Boost evening sales of airtime and bundles in Islamabad by 30%
3. Improve customer retention rate by 10% in the quarter following Ramadan
4. Increase market share in Islamabad by 3% during the campaign period
5. Enhance brand perception, aiming for a 20% increase in positive sentiment scores

### Target Audience
- Primary: Existing subscribers in Islamabad, with a focus on high-value customers
- Secondary: Potential new customers in Islamabad, especially those considering switching providers

### ROI Analysis
1. Revenue Increase: 
   - Projected 15% increase in overall revenue
   - Expected 30% boost in evening sales to offset the 10% discount
2. Customer Acquisition and Retention:
   - Estimated acquisition of 5000 new customers
   - Projected 10% improvement in customer retention, leading to increased lifetime value
3. Brand Value:
   - Anticipated 20% increase in brand sentiment scores
   - Expected increase in social media mentions and positive reviews
4. Projected Costs vs Returns:
   - Cost of discounts: Approximately 3% of total revenue
   - Marketing and operational costs: 2% of total revenue
   - Expected ROI: 300% (For every $1 spent, expect $3 in return)

### Constraints
- Time-based: Applicable from 8 PM to midnight during Ramadan 2025
- Location-based: Only for subscribers in Islamabad
- System Limitations: Campaign execution restricted to available Crediverse calling entity and transaction contexts

### Calling Entity and Transaction Contexts
- Calling Entity: Crediverse
- Transaction Contexts: 
  1. RETAIL_AIRTIME_RECHARGE (for airtime purchases)
  2. BUNDLE_SALES (for bundle purchases)

## Campaign Strategy Formulation

### 1. Tiered Discount Structure
- Standard Tier: 10% discount on all airtime and bundle purchases
- Loyalty Tier: 15% discount for customers with 6+ months of consistent usage
- New Customer Tier: 20% discount on first purchase during the campaign period

### 2. Market Penetration and Growth
- Leverage the tiered discount to attract competitor's customers
- Encourage existing customers to increase their usage during evening hours

### 3. Cross-selling and Upselling
- Promote bundle purchases to airtime-only customers during the discount period
- Offer upgraded bundles at discounted rates to encourage higher-value purchases

### 4. Customer Retention Strategy
- Use the Ramadan campaign to re-engage dormant customers
- Implement a "Ramadan Rewards" system for purchases made during the campaign period

### 5. Brand Positioning
- Launch a "Connected Ramadan" campaign, emphasizing how our services enhance community connections during the holy month
- Partner with local charities, donating a percentage of evening sales to support community iftar programs

## Campaign Structure and Details

### Rule 1: Standard Evening Discount for Airtime Recharge

#### Conditions:
1. Transaction date is within Ramadan 2025 period
2. Transaction time is between 8 PM and midnight
3. Subscriber location is in Islamabad
4. Transaction type is airtime recharge

#### Actions:
1. Apply 10% discount to the transaction amount
2. Send confirmation SMS to the subscriber

### Rule 2: Standard Evening Discount for Bundle Sales

#### Conditions:
1. Transaction date is within Ramadan 2025 period
2. Transaction time is between 8 PM and midnight
3. Subscriber location is in Islamabad
4. Transaction type is bundle sale

#### Actions:
1. Apply 10% discount to the transaction amount
2. Send confirmation SMS to the subscriber

### Rule 3: Loyalty Tier Discount for Airtime Recharge

#### Conditions:
1. All conditions from Rule 1
2. Subscriber has 6+ months of consistent usage

#### Actions:
1. Apply 15% discount to the transaction amount
2. Send special loyalty appreciation SMS

### Rule 4: Loyalty Tier Discount for Bundle Sales

#### Conditions:
1. All conditions from Rule 2
2. Subscriber has 6+ months of consistent usage

#### Actions:
1. Apply 15% discount to the transaction amount
2. Send special loyalty appreciation SMS

## JSON Campaign Definition

```json
{
  "campaignId": "RAMADAN_2025_STRATEGIC_GROWTH",
  "name": "Ramadan 2025 Strategic Growth Campaign",
  "startDate": "2025-03-01T00:00:00Z",
  "endDate": "2025-03-30T23:59:59Z",
  "persistentVariableDefinitions": [
    {
      "name": "loyalCustomer",
      "type": "boolean",
      "initialValue": false,
      "scope": "subscriber"
    }
  ],
  "ruleDefinitions": [
    {
      "ruleId": "STANDARD_EVENING_DISCOUNT_AIRTIME",
      "name": "Apply 10% discount for evening airtime purchases in Islamabad during Ramadan",
      "callingEntityId": "CREDIVERSE",
      "transactionContextId": "RETAIL_AIRTIME_RECHARGE",
      "sequence": 1,
      "conditions": [
        {
          "type": "COMPOUND",
          "logicalOperator": "AND",
          "conditions": [
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{transactionTimestamp}",
                "operator": ">=",
                "rightOperand": "2025-03-01T00:00:00Z"
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{transactionTimestamp}",
                "operator": "<=",
                "rightOperand": "2025-03-30T23:59:59Z"
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{transactionTimestamp}",
                "operator": "TIME_BETWEEN",
                "rightOperand": ["20:00:00", "23:59:59"]
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{subscriberLocation.zones}",
                "operator": "CONTAINS",
                "rightOperand": "ISLAMABAD"
              }
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "ApplySubscriberDiscount",
          "parameters": {
            "discountType": "PERCENT",
            "discountValue": 10,
            "applicableTo": "RETAIL"
          }
        },
        {
          "type": "SendSMS",
          "parameters": {
            "recipientType": "SUBSCRIBER",
            "message": "Ramadan Mubarak! You've received a 10% discount on your airtime purchase. Thank you for choosing our service."
          }
        }
      ]
    },
    {
      "ruleId": "STANDARD_EVENING_DISCOUNT_BUNDLE",
      "name": "Apply 10% discount for evening bundle purchases in Islamabad during Ramadan",
      "callingEntityId": "CREDIVERSE",
      "transactionContextId": "BUNDLE_SALES",
      "sequence": 1,
      "conditions": [
        {
          "type": "COMPOUND",
          "logicalOperator": "AND",
          "conditions": [
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{transactionTimestamp}",
                "operator": ">=",
                "rightOperand": "2025-03-01T00:00:00Z"
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{transactionTimestamp}",
                "operator": "<=",
                "rightOperand": "2025-03-30T23:59:59Z"
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{transactionTimestamp}",
                "operator": "TIME_BETWEEN",
                "rightOperand": ["20:00:00", "23:59:59"]
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{subscriberLocation.zones}",
                "operator": "CONTAINS",
                "rightOperand": "ISLAMABAD"
              }
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "ApplySubscriberDiscount",
          "parameters": {
            "discountType": "PERCENT",
            "discountValue": 10,
            "applicableTo": "BUNDLE"
          }
        },
        {
          "type": "SendSMS",
          "parameters": {
            "recipientType": "SUBSCRIBER",
            "message": "Ramadan Mubarak! You've received a 10% discount on your bundle purchase. Thank you for choosing our service."
          }
        }
      ]
    },
    {
      "ruleId": "LOYALTY_EVENING_DISCOUNT_AIRTIME",
      "name": "Apply 15% loyalty discount for evening airtime purchases in Islamabad during Ramadan",
      "callingEntityId": "CREDIVERSE",
      "transactionContextId": "RETAIL_AIRTIME_RECHARGE",
      "sequence": 2,
      "conditions": [
        {
          "type": "COMPOUND",
          "logicalOperator": "AND",
          "conditions": [
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{transactionTimestamp}",
                "operator": ">=",
                "rightOperand": "2025-03-01T00:00:00Z"
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{transactionTimestamp}",
                "operator": "<=",
                "rightOperand": "2025-03-30T23:59:59Z"
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{transactionTimestamp}",
                "operator": "TIME_BETWEEN",
                "rightOperand": ["20:00:00", "23:59:59"]
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{subscriberLocation.zones}",
                "operator": "CONTAINS",
                "rightOperand": "ISLAMABAD"
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{loyalCustomer}",
                "operator": "==",
                "rightOperand": true
              }
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "ApplySubscriberDiscount",
          "parameters": {
            "discountType": "PERCENT",
            "discountValue": 15,
            "applicableTo": "RETAIL"
          }
        },
        {
          "type": "SendSMS",
          "parameters": {
            "recipientType": "SUBSCRIBER",
            "message": "Ramadan Mubarak, valued customer! You've received a special 15% loyalty discount on your airtime purchase. Thank you for your continued trust in our service."
          }
        }
      ]
    },
    {
      "ruleId": "LOYALTY_EVENING_DISCOUNT_BUNDLE",
      "name": "Apply 15% loyalty discount for evening bundle purchases in Islamabad during Ramadan",
      "callingEntityId": "CREDIVERSE",
      "transactionContextId": "BUNDLE_SALES",
      "sequence": 2,
      "conditions": [
        {
          "type": "COMPOUND",
          "logicalOperator": "AND",
          "conditions": [
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{transactionTimestamp}",
                "operator": ">=",
                "rightOperand": "2025-03-01T00:00:00Z"
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{transactionTimestamp}",
                "operator": "<=",
                "rightOperand": "2025-03-30T23:59:59Z"
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{transactionTimestamp}",
                "operator": "TIME_BETWEEN",
                "rightOperand": ["20:00:00", "23:59:59"]
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{subscriberLocation.zones}",
                "operator": "CONTAINS",
                "rightOperand": "ISLAMABAD"
              }
            },
            {
              "type": "COMPARISON",
              "parameters": {
                "leftOperand": "{loyalCustomer}",
                "operator": "==",
                "rightOperand": true
              }
            }
          ]
        }
      ],
      "actions": [
        {
          "type": "ApplySubscriberDiscount",
          "parameters": {
            "discountType": "PERCENT",
            "discountValue": 15,
            "applicableTo": "BUNDLE"
          }
        },
        {
          "type": "SendSMS",
          "parameters": {
            "recipientType": "SUBSCRIBER",
            "message": "Ramadan Mubarak, valued customer! You've received a special 15% loyalty discount on your bundle purchase. Thank you for your continued trust in our service."
          }
        }
      ]
    }
  ]
}
```

## Performance Considerations

1. Database Optimization: Ensure that subscriber location and loyalty status data are properly indexed to handle high volumes of real-time queries.

2. Caching Strategy: Implement a caching mechanism for frequently accessed data, such as subscriber loyalty status, to reduce database load.

3. Rule Execution Efficiency: Optimize the rule execution engine to efficiently process the tiered discount structure, especially during peak evening hours.

4. SMS Gateway Capacity: Ensure the SMS gateway can handle the increased volume of messages, particularly during the evening discount period.

5. Monitoring and Alerting: Implement real-time monitoring and alerting systems to quickly identify and address any performance issues or anomalies during the campaign.

## Conclusion

The Ramadan 2025 Strategic Growth Campaign is designed to drive significant business growth while providing value to customers during the holy month. Key strengths of the proposed campaign include:

1. Tiered Discount Structure: Encourages loyalty and attracts new customers while maintaining profitability.
2. Focus on Evening Sales: Aligns with Ramadan behavioral patterns, potentially shifting network load to less congested hours.
3. Brand Enhancement: Positions the company as culturally aware and community-oriented.
4. Data-Driven Approach: Utilizes customer data for targeted discounts and personalized messaging.

Potential areas for future expansion or refinement:

1. Enhanced Personalization: Develop more granular customer segments for even more targeted offers.
2. Cross-Channel Integration: Extend the campaign to include app-based notifications and rewards.
3. Predictive Analytics: Implement AI-driven models to predict and incentivize customer behavior more effectively.
4. Community Engagement: Expand partnerships with local organizations to strengthen community ties.

This campaign strikes a balance between driving business growth and providing genuine value