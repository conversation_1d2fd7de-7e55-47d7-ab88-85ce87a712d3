---
title: Webhook Schema
classification: Confidential
created: 2025-04-10
updated: 2025-04-10
authors:
  - "[[<PERSON>]]"
version: 1.0.0
next-review: 2025-10-10
category: "[[Specifications]]"
tags:
  - specification
  - schema
  - json
  - webhooks
topics:
  - "[[Property Modification Framework]]"
  - "[[Integration]]"
  - "[[RuleForge]]"
  - "[[Webhooks]]"
---

# Webhook Schema

## Document Specification

### Purpose

This document defines the schema for Webhooks in the RuleForge Property Modification Framework (PMF). It specifies the format, content, and constraints of webhook definitions used for integration with external systems, such as SMS gateways, email services, and other APIs.

### Scope

This document covers:

- The structure for defining and registering webhooks
- Definitions of webhook components (endpoints, parameters, security)
- How webhooks are referenced in rules
- Data types and constraints for each field
- Usage guidelines for webhooks in rule execution

This document does not cover:

- The internal implementation of webhook execution
- The GUI interface for webhook management
- The API endpoints for webhook operations (covered in [[Webhook Management API]])

### Target Audience

- Developers implementing webhook functionality in RuleForge
- System architects designing integration points
- QA engineers validating webhook behavior
- Technical writers creating webhook-related documentation
- Integration specialists working with the RuleForge system

## Table of Contents

- [[#1. Webhook Structure]]
- [[#2. Component Definitions]]
- [[#3. Webhook Reference in Rules]]
- [[#4. Webhook Execution Model]]
- [[#5. Security Considerations]]
- [[#6. Examples]]

## 1. Webhook Structure

A Webhook is represented by a JSON object with the following structure:

```json
{
  "webhookId": "string",
  "name": "string",
  "description": "string",
  "url": "string",
  "method": "string",
  "headers": {
    "string": "string"
  },
  "bodyTemplate": "object",
  "timeout": "integer",
  "retryPolicy": {
    "maxRetries": "integer",
    "initialDelaySeconds": "integer",
    "backoffMultiplier": "number"
  },
  "securityLevel": "string",
  "parameters": [
    {
      "parameterId": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "required": "boolean"
    }
  ]
}
```

## 2. Component Definitions

### Webhook Metadata

- `webhookId`: Unique identifier for the webhook (string, max 50 characters, camelCase)
- `name`: Human-readable name for the webhook (string, max 100 characters)
- `description`: Description of the webhook's purpose (string, max 250 characters)

### Endpoint Configuration

- `url`: The HTTP endpoint URL to call (string, must be a valid URL, max 500 characters)
- `method`: The HTTP method to use (string, enum: "GET", "POST", "PUT", "DELETE", "PATCH")
- `headers`: Key-value pairs representing HTTP headers to include in the request (object)
  - Keys are header names (string)
  - Values are header values (string)
- `bodyTemplate`: Template for the request body as a JSON object (only JSON format is supported in this version)
  - Can include property references in the format `{propertyId}`
  - For GET/DELETE requests, this object is converted to query parameters
  - Content-Type header will default to "application/json" if not explicitly provided
- `timeout`: Maximum time to wait for a response in milliseconds (integer, default: 5000)

Note: Support for additional body formats (XML, form-urlencoded, plain text) may be added in future versions.

### Retry Configuration

- `retryPolicy`: Configuration for retrying failed webhook calls (object)
  - `maxRetries`: Maximum number of retry attempts (integer, default: 3)
  - `initialDelaySeconds`: Delay before the first retry attempt (integer, default: 30)
  - `backoffMultiplier`: Multiplier applied to delay for subsequent retries (number, default: 2.0)

### Security Settings

- `securityLevel`: Security level for the webhook (string, enum: "SYSTEM", "ORGANIZATION", "ENTITY")
  - `SYSTEM`: Only system administrators can manage the webhook
  - `ORGANIZATION`: Organization administrators can manage the webhook
  - `ENTITY`: Entity administrators can manage the webhook
  - Note: In the initial implementation, all webhooks will be treated as system-level

### Parameters

The `parameters` array defines the inputs required for the webhook call:

```json
{
  "parameters": [
    {
      "parameterId": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "required": "boolean"
    }
  ]
}
```

Fields:

- `parameterId`: Identifier of the parameter (string, max 50 characters, camelCase)
- `name`: Human-readable name of the parameter (string, max 100 characters)
- `description`: Description of the parameter's purpose (string, max 250 characters)
- `type`: Data type of the parameter (string, enum: "string", "number", "boolean", "date", "object", "array")
- `required`: Whether the parameter is mandatory (boolean)

Note: Direct references to lists (from the List Management API) are not supported in this initial implementation. To use list data in webhooks, first assign the relevant values to a local variable through a rule, then reference that variable in the webhook parameters.

## 3. Webhook Reference in Rules

Webhooks are referenced in rules using a `webhookCalls` array that can be included in both evaluation rules and outcome rules:

```json
{
  "webhookCalls": [
    {
      "webhookId": "string",
      "parameters": {
        "parameterId1": "value1",
        "parameterId2": "value2"
      }
    }
  ]
}
```

Fields:

- `webhookId`: Identifier of the webhook to call (string, must match a registered webhook)
- `parameters`: Key-value pairs representing the parameters to pass to the webhook (object)
  - Keys must match parameter IDs defined in the webhook
  - Values can be literals or property references in the format `{propertyId}`

## 4. Webhook Execution Model

### Execution Flow

1. When a rule's condition evaluates to true, any webhook calls defined in the rule are triggered
2. For each webhook call:
   - The system resolves parameter values, replacing property references with actual values
   - These parameter values are mapped to the webhook's body template
   - The webhook call is executed asynchronously
   - Results and errors are logged for monitoring

### Execution Timing

- **Evaluation Phase Webhooks**: Executed after all property modifications have been determined but before the response is sent back to the entity
- **Outcome Phase Webhooks**: Executed after processing the transaction outcome

### Parallel Execution

- All webhook calls triggered by a rule are executed in parallel
- There is no guaranteed order of execution
- The current design does not support dependencies between webhook calls
- Webhook results are not accessible to other webhooks or to the rule itself

### Error Handling

1. If a webhook call fails (non-2xx response, timeout, etc.):
   - The error is logged
   - After exhausting retries, the failure is recorded in analytics
2. Webhook failures do not affect the main transaction processing flow
   - Property modifications are still applied
   - Transaction outcomes are still processed
   - Responses are still returned to the entity

## 5. Security Considerations

### Webhook Management

- Webhook registration and management is restricted based on the `securityLevel`
- System administrators can manage all webhooks
- Organization administrators can only manage webhooks at the ORGANIZATION and ENTITY levels
- Entity administrators can only manage webhooks at the ENTITY level

### URL Validation

- Only HTTPS URLs are allowed by default
- URLs are validated against a system-wide domain whitelist
- The domain whitelist is configured by system administrators and is not available through the GUI in this initial version

### Credential Management

- Sensitive credentials should not be hardcoded in webhook definitions
- For this initial version, use `${SECRET:key_name}` syntax to reference environment variables configured on the server
- These references will be resolved by the Rules Engine Server at webhook execution time
- Example: `"Authorization": "Bearer ${SECRET:sms_api_key}"`
- Environment variables must be configured by system administrators during deployment

### Webhook Execution Permissions

- All webhooks are available system-wide in this initial implementation
- Webhook calls are logged for audit purposes
- Note: Rate limiting and permission restrictions will be implemented in future versions

## 6. Examples

### SMS Notification Webhook

```json
{
  "webhookId": "smsNotification",
  "name": "SMS Notification",
  "description": "Sends SMS notifications via the corporate SMS gateway",
  "url": "https://api.sms-gateway.com/send",
  "method": "POST",
  "headers": {
    "Authorization": "Bearer ${SECRET:sms_api_key}",
    "Content-Type": "application/json"
  },
  "bodyTemplate": {
    "to": "{recipient}",
    "message": "{message}",
    "sender": "RuleForge"
  },
  "timeout": 3000,
  "retryPolicy": {
    "maxRetries": 3,
    "initialDelaySeconds": 60,
    "backoffMultiplier": 2
  },
  "securityLevel": "SYSTEM",
  "parameters": [
    {
      "parameterId": "recipient",
      "name": "Recipient",
      "description": "The phone number to send the SMS to",
      "type": "string",
      "required": true
    },
    {
      "parameterId": "message",
      "name": "Message",
      "description": "The content of the SMS message",
      "type": "string",
      "required": true
    }
  ]
}
```

### Webhook Call in a Rule

```json
{
  "ruleId": "TARGET_REACHED_NOTIFICATION",
  "condition": {
    "conditionTypeId": "COMPARISON",
    "operator": ">=",
    "parameters": {
      "leftOperand": "{dailyBundleCount}",
      "rightOperand": 30
    }
  },
  "variableAssignments": [
    {
      "variableId": "notificationSent",
      "assignmentTypeId": "SET",
      "value": true
    }
  ],
  "webhookCalls": [
    {
      "webhookId": "smsNotification",
      "parameters": {
        "recipient": "{agentMsisdn}",
        "message": "Congratulations! You've reached your daily target of {targetValue} bundles."
      }
    }
  ]
}
```

## Related Documents

- [[Rule Set Schema]]
- [[Webhook Management API]]
- [[RuleForge Management API]]
- [[RuleForge Interface Overview]]
- [[Transaction Evaluation API]]

## Approvals

| Role/Department       | Name | Date | Signature |
| --------------------- | ---- | ---- | --------- |
| Lead Architect        |      |      |           |
| API Team Lead         |      |      |           |
| Security Officer      |      |      |           |
| Documentation Manager |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                 |
|---------|----------------|-----------------|--------------------------------------------------------|
| 1.0.0   | [[2025-04-10]] | [[Wayne Smith]] | Initial version of the Webhook Schema document |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->