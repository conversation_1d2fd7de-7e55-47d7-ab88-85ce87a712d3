---
title: List Management Schema
classification: Confidential
created: 2025-04-04
updated: 2025-04-04
authors:
  - "[[<PERSON>]]"
version: 1.0.0
next-review: 2025-10-04
category: "[[Specifications]]"
tags:
  - specification
  - schema
  - json
  - lists
topics:
  - "[[Property Modification Framework]]"
  - "[[Rule Logic]]"
  - "[[RuleForge]]"
  - "[[Lists]]"
---

# List Management Schema

## Document Specification

### Purpose

This document defines the schema for Lists in the RuleForge Property Modification Framework (PMF). It specifies the format, content, and constraints of lists used in rule conditions for membership testing and other list-based operations.

### Scope

This document covers:

- The structure for defining and managing lists
- How lists are referenced in rule conditions
- Data types and constraints for list elements
- Usage guidelines for lists in rule evaluation

This document does not cover:

- The internal implementation of list storage or retrieval
- The GUI interface for list management
- The API endpoints for list operations (covered in [[List Management API]])

### Target Audience

- Developers implementing rule creation and evaluation features
- System architects designing list management functionality
- QA engineers validating list behavior
- Technical writers creating list-related documentation

## Table of Contents

- [[#1. List Structure]]
- [[#2. Component Definitions]]
- [[#3. Usage in Rule Conditions]]
- [[#4. Constraints and Validation]]
- [[#5. Examples]]

## 1. List Structure

A List is represented by a JSON object with the following structure:

```json
{
  "listId": "string",
  "name": "string",
  "type": "string",
  "elementsCount": "integer"
}
```

When requesting the full list including elements, the following extended structure is used:

```json
{
  "listId": "string",
  "name": "string",
  "type": "string",
  "elementsCount": "integer",
  "elements": ["string1", "string2", "string3"]
}
```

## 2. Component Definitions

### List Metadata

- `listId`: Unique identifier for the list (string, max 50 characters, camelCase)
- `name`: Human-readable name for the list (string, max 100 characters)
- `type`: Data type of list elements (string, enum: "string", "number", "boolean")
  - Currently only "string" is fully supported
- `elementsCount`: Number of elements in the list (integer)

### List Elements

- `elements`: Array of values that make up the list
  - All elements must be of the same type as specified in the list's `type` field
  - Elements must be unique within a list (no duplicates)
  - Maximum number of elements per list: 100,000

## 3. Usage in Rule Conditions

Lists can be referenced in rule conditions using the standard `IN` and `NOT_IN` operators:

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": "IN",
  "parameters": {
    "leftOperand": "{propertyId}",
    "rightOperand": {
      "listId": "listId"
    }
  }
}
```

### List Reference Approach

When using the `IN` and `NOT_IN` operators, there are two ways to specify the right operand:

1. **Array Literal**: Direct specification of values in the rule
   ```json
   {
     "conditionTypeId": "COMPARISON",
     "operator": "IN",
     "parameters": {
       "leftOperand": "{customerType}",
       "rightOperand": ["GOLD", "PLATINUM", "DIAMOND"]
     }
   }
   ```

2. **List Reference**: Reference to a predefined list
   ```json
   {
     "conditionTypeId": "COMPARISON",
     "operator": "IN",
     "parameters": {
       "leftOperand": "{agentCGI}",
       "rightOperand": {
         "listId": "approvedAgents"
       }
     }
   }
   ```

Both approaches use the same operators (`IN` and `NOT_IN`), but the list reference approach provides advantages for large, frequently changing, or centrally managed sets of values.

### When to Use List References vs. Array Literals

| Approach | Best For | Example |
|----------|----------|---------|
| Array Literal | Small, static sets (<20 items) | `customerType IN ["GOLD", "PLATINUM", "DIAMOND"]` |
| List Reference | Large sets, frequently changing data, reusable sets | `agentCGI IN approvedAgents` |

## 4. Constraints and Validation

### List Identifiers

- `listId` must be unique across the system
- `listId` should follow the camelCase naming convention
- `listId` must contain only alphanumeric characters and must not begin with a number
- `name` does not need to be unique, but duplicate names are discouraged

### List Elements

- All elements in a list must be of the same data type
- String elements are case-sensitive by default in comparisons
- Number elements must be valid numeric values
- Boolean elements must be true or false
- The maximum size of an individual element is 1000 characters for strings
- Elements must be unique within a list (no duplicates)

## 5. Examples

### Basic List Definition

```json
{
  "listId": "approvedAgents",
  "name": "Approved Agents",
  "type": "string",
  "elementsCount": 5,
  "elements": ["agent1", "agent2", "agent3", "agent4", "agent5"]
}
```

### List Membership Test in a Rule Condition

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": "IN",
  "parameters": {
    "leftOperand": "{agentCGI}",
    "rightOperand": {
      "listId": "islamabadAgents"
    }
  }
}
```

### Complex Rule Using Lists

```json
{
  "conditionTypeId": "LOGICAL",
  "operator": "AND",
  "parameters": {
    "conditions": [
      {
        "conditionTypeId": "COMPARISON",
        "operator": "IN",
        "parameters": {
          "leftOperand": "{customerRegion}",
          "rightOperand": {
            "listId": "premiumRegions"
          }
        }
      },
      {
        "conditionTypeId": "COMPARISON",
        "operator": "NOT_IN",
        "parameters": {
          "leftOperand": "{productId}",
          "rightOperand": {
            "listId": "restrictedProducts"
          }
        }
      }
    ]
  }
}
```

## Usage Guidelines

### When to Use Lists

Lists are most effective in the following scenarios:

1. **Large Sets of Values**: When dealing with dozens to thousands of values
2. **Frequently Changing Values**: When the set of values changes often
3. **Reusable Sets**: When the same set of values is used across multiple rules
4. **Externally Maintained Data**: When values are managed by teams other than rule authors

### Performance Considerations

- Lists are optimized for fast membership testing
- Very large lists (>10,000 elements) may impact rule evaluation performance
- Consider pagination when retrieving the elements of very large lists

## Related Documents

- [[Condition Type Schema]]
- [[Rule Set Schema]]
- [[List Management API]]
- [[RuleForge Management API]]
- [[RuleForge Naming Convention Guide]]

## Approvals

| Role/Department       | Name | Date | Signature |
| --------------------- | ---- | ---- | --------- |
| Lead Architect        |      |      |           |
| API Team Lead         |      |      |           |
| Documentation Manager |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                 |
|---------|----------------|-----------------|--------------------------------------------------------|
| 1.0.0   | [[2025-04-04]] | [[Wayne Smith]] | Initial version of the List Management Schema document |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->