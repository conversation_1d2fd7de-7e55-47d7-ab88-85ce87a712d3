---
title: List Management API
classification: Confidential
created: 2025-04-04
updated: 2025-04-04
authors:
  - "[[<PERSON>]]"
version: 1.0.0
next-review: 2025-10-04
category: "[[Specifications]]"
tags:
  - specification
  - api
  - reference
topics:
  - "[[Property Modification Framework]]"
  - "[[API]]"
  - "[[RuleForge]]"
  - "[[Lists]]"
---

# List Management API

## Document Specification

### Purpose

This document provides the API specification for list management within the RuleForge Property Modification Framework (PMF). It focuses specifically on the endpoints needed for creating, retrieving, updating, and deleting lists that can be referenced in rule conditions.

### Scope

This specification covers:

- Authentication mechanisms for accessing list management endpoints
- List CRUD operation endpoints
- Data structures for list definitions
- File upload mechanism for bulk list creation
- Error handling and status codes related to list management

This document does not cover:

- The internal implementation of list storage
- The GUI implementation for list management
- How lists are evaluated during rule execution

### Target Audience

- Developers implementing list management functionality
- System architects designing list integration
- QA engineers testing list management features
- Integration specialists working with the RuleForge system

## Table of Contents

- [[#1. Introduction]]
- [[#2. API Endpoints]]
- [[#3. Error Handling]]
- [[#4. Usage with Rule Conditions]]
- [[#5. Implementation Best Practices]]

## 1. Introduction

### 1.1 API Overview

The List Management API provides endpoints that allow for the creation, retrieval, update, and deletion of lists within the RuleForge system. These lists can be referenced in rule conditions to check if a value is a member of a predefined set, enabling more flexible and maintainable rule logic.

### 1.2 API Versioning

The API uses semantic versioning (MAJOR.MINOR.PATCH) and includes the major version in the URL path, consistent with other RuleForge APIs:

Base URL: `https://api.ruleforge.net/v1`

### 1.3 Authentication

All List Management API endpoints use the same authentication method as other RuleForge APIs. All requests must include an `Authorization` header with a valid bearer token:

```
Authorization: Bearer your_token
```

## 2. API Endpoints

### 2.1 Get All Lists

- **Endpoint**: `/lists`
- **Method**: GET
- **Description**: Retrieves metadata for all available lists
- **Query Parameters**:
  - `page`: Page number for pagination (default: 1)
  - `pageSize`: Number of items per page (default: 20)
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "lists": [
        {
          "listId": "string",
          "name": "string",
          "type": "string",
          "elementsCount": 0
        }
      ],
      "totalCount": 0,
      "page": 1,
      "pageSize": 20
    }
    ```

### 2.2 Get List Details

- **Endpoint**: `/lists/{listId}`
- **Method**: GET
- **Description**: Retrieves details of a specific list, including its elements
- **Path Parameters**:
  - `listId`: Unique identifier of the list to retrieve
- **Query Parameters**:
  - `includeElements`: Whether to include the list elements in the response (default: false)
  - `page`: Page number for elements pagination when includeElements=true (default: 1)
  - `pageSize`: Number of elements per page when includeElements=true (default: 100)
- **Response**:
  - Status: 200 OK
  - Body when includeElements=false:
    ```json
    {
      "listId": "string",
      "name": "string",
      "type": "string",
      "elementsCount": 0
    }
    ```
  - Body when includeElements=true:
    ```json
    {
      "listId": "string",
      "name": "string",
      "type": "string",
      "elementsCount": 0,
      "elements": ["string1", "string2", "string3"],
      "page": 1,
      "pageSize": 100,
      "totalPages": 1
    }
    ```

### 2.3 Create List

- **Endpoint**: `/lists`
- **Method**: POST
- **Description**: Creates a new list from a CSV file or JSON payload
- **Request Body Options**:
  
  **Option 1: multipart/form-data (for file upload)**:
  - `name` (string): Name of the list
  - `type` (string): Data type of list elements (currently only "string" is supported)
  - `file` (binary): CSV file containing list elements
  
  **Option 2: application/json (for direct element specification)**:
  ```json
  {
    "name": "string",
    "type": "string",
    "elements": ["string1", "string2", "string3"]
  }
  ```
- **Response**:
  - Status: 201 Created (for small lists with immediate processing)
  - Body:
    ```json
    {
      "listId": "string",
      "name": "string",
      "type": "string",
      "elementsCount": 0,
      "message": "List successfully created"
    }
    ```
  
  OR
  
  - Status: 202 Accepted (for large file uploads requiring asynchronous processing)
  - Body:
    ```json
    {
      "listId": "string",
      "name": "string",
      "uploadId": "string",
      "status": "PROCESSING",
      "message": "File upload accepted, processing has begun"
    }
    ```

### 2.3.1 Check List Creation Status

- **Endpoint**: `/lists/status/{uploadId}`
- **Method**: GET
- **Description**: Checks the status of an asynchronous list creation operation
- **Path Parameters**:
  - `uploadId`: The ID returned from the list creation request
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "listId": "string",
      "name": "string",
      "uploadId": "string",
      "status": "string", // "PROCESSING", "COMPLETED", "FAILED"
      "progress": 75, // Percentage completed (0-100)
      "elementsProcessed": 7500,
      "totalElements": 10000,
      "message": "string",
      "error": {
        "code": "string",
        "message": "string",
        "details": {}
      } // Only present if status is "FAILED"
    }
    ```

### 2.4 Update List

- **Endpoint**: `/lists/{listId}`
- **Method**: PUT
- **Description**: Updates an existing list's metadata or elements
- **Path Parameters**:
  - `listId`: Unique identifier of the list to update
- **Request Body Options**:
  
  **Option 1: multipart/form-data (for file upload)**:
  - `name` (string, optional): New name for the list
  - `file` (binary, optional): New CSV file containing list elements
  
  **Option 2: application/json (for direct element specification)**:
  ```json
  {
    "name": "string", // Optional
    "elements": ["string1", "string2", "string3"] // Optional
  }
  ```
- **Response**:
  - Status: 200 OK (for small lists with immediate processing)
  - Body:
    ```json
    {
      "listId": "string",
      "name": "string",
      "type": "string",
      "elementsCount": 0,
      "message": "List successfully updated"
    }
    ```
  
  OR
  
  - Status: 202 Accepted (for large file uploads requiring asynchronous processing)
  - Body:
    ```json
    {
      "listId": "string",
      "name": "string",
      "uploadId": "string",
      "status": "PROCESSING",
      "message": "File upload accepted, processing has begun"
    }
    ```

### 2.5 Download List

- **Endpoint**: `/lists/{listId}/download`
- **Method**: GET
- **Description**: Downloads the contents of a list as a CSV file or JSON
- **Path Parameters**:
  - `listId`: Unique identifier of the list to download
- **Query Parameters**:
  - `format` (string, optional): Output format, either "csv" (default) or "json"
- **Response for CSV format**:
  - Status: 200 OK
  - Content-Type: text/csv
  - Content-Disposition: attachment; filename="{listId}.csv"
  - Body: CSV file content with header row
- **Response for JSON format**:
  - Status: 200 OK
  - Content-Type: application/json
  - Body: 
    ```json
    {
      "listId": "string",
      "name": "string",
      "type": "string",
      "elements": ["string1", "string2", "string3"]
    }
    ```

### 2.6 Delete List

- **Endpoint**: `/lists/{listId}`
- **Method**: DELETE
- **Description**: Deletes a list by its ID
- **Path Parameters**:
  - `listId`: Unique identifier of the list to delete
- **Response**:
  - Status: 204 No Content

### 2.7 List Element Operations

#### 2.7.1 Add Element

- **Endpoint**: `/lists/{listId}/elements`
- **Method**: POST
- **Description**: Adds a new element to an existing list
- **Path Parameters**:
  - `listId`: Unique identifier of the list to modify
- **Request Body**:
  ```json
  {
    "element": "string"
  }
  ```
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "listId": "string",
      "elementsCount": 0,
      "message": "Element successfully added"
    }
    ```

#### 2.7.2 Remove Element

- **Endpoint**: `/lists/{listId}/elements`
- **Method**: DELETE
- **Description**: Removes an element from an existing list
- **Path Parameters**:
  - `listId`: Unique identifier of the list to modify
- **Request Body**:
  ```json
  {
    "element": "string"
  }
  ```
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "listId": "string",
      "elementsCount": 0,
      "message": "Element successfully removed"
    }
    ```

## 3. Error Handling

The List Management API follows the standard error response format used by other RuleForge APIs:

### 400 Bad Request: Invalid Input Data

```json
{
  "error": {
    "code": "INVALID_LIST_DATA",
    "message": "List data contains invalid fields",
    "details": {
      "invalidFields": ["name"]
    }
  },
  "requestId": "string"
}
```

### 404 Not Found: List Not Found

```json
{
  "error": {
    "code": "LIST_NOT_FOUND",
    "message": "The specified list was not found",
    "details": {
      "listId": "unknown_list"
    }
  },
  "requestId": "string"
}
```

### 409 Conflict: Duplicate List Name

```json
{
  "error": {
    "code": "DUPLICATE_LIST_NAME",
    "message": "A list with this name already exists",
    "details": {
      "name": "Existing List Name"
    }
  },
  "requestId": "string"
}
```

### 413 Payload Too Large: File Size Exceeds Limit

```json
{
  "error": {
    "code": "FILE_TOO_LARGE",
    "message": "The uploaded file exceeds the maximum allowed size",
    "details": {
      "maxSizeBytes": 104857600, // 100MB
      "actualSizeBytes": 157286400
    }
  },
  "requestId": "string"
}
```

### 415 Unsupported Media Type: Invalid File Format

```json
{
  "error": {
    "code": "INVALID_FILE_FORMAT",
    "message": "The uploaded file format is not supported",
    "details": {
      "supportedFormats": ["csv"],
      "providedFormat": "xlsx"
    }
  },
  "requestId": "string"
}
```

### 422 Unprocessable Entity: Invalid CSV Format

```json
{
  "error": {
    "code": "INVALID_CSV_FORMAT",
    "message": "The CSV file contains formatting errors",
    "details": {
      "line": 42,
      "reason": "Invalid cell format"
    }
  },
  "requestId": "string"
}
```

## 4. Usage with Rule Conditions

Lists created through this API can be referenced in rule conditions using the standard membership operators (`IN` and `NOT_IN`):

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": "IN",
  "parameters": {
    "leftOperand": "{agentCGI}",
    "rightOperand": {
      "listId": "islamabadAgents"
    }
  }
}
```

This uses the same `IN` operator used for inline arrays, but with a list reference object as the right operand instead of an array literal. For full details on how to use lists in rule conditions, see the [[List Management Schema]] and [[Condition Type Schema]].

## 5. Implementation Best Practices

### 5.1 File Upload Specifications

- **Maximum file size**: 100MB
- **Supported file formats**: CSV
- **CSV format requirements**:
  - First row should be a header row (will be ignored)
  - Each subsequent row represents one list element
  - For multi-column CSVs, only the first column will be used for list elements
  - UTF-8 encoding is required
  - Both comma (,) and semicolon (;) delimiters are supported

### 5.2 Asynchronous Processing for Large Files

For large file uploads:

1. The initial upload request has a 60-second timeout
2. The server will continue processing the file even after the response is sent
3. Status endpoint should be polled to determine when processing is complete
4. Recommended polling interval: Start at 1 second, increasing to 5 seconds with exponential backoff

### 5.3 Performance Considerations

- Lists are optimized for fast membership testing
- The `/lists` endpoint supports pagination for efficient retrieval of list metadata
- When retrieving list elements via `/lists/{listId}?includeElements=true`, use pagination for lists with many elements
- List operations are cached for improved performance

### 5.4 Security Considerations

- Always validate list data on both client and server side
- Implement proper access controls to prevent unauthorized list modifications
- Sanitize list elements to prevent injection attacks when used in rules
- Use HTTPS for all API communications

## Related Documents

- [[List Management Schema]]
- [[Condition Type Schema]]
- [[RuleForge Management API]]
- [[RuleForge Interface Overview]]

## Approvals

| Role/Department    | Name | Date | Signature |
|--------------------|------|------|-----------|
| API Team Lead      |      |      |           |
| Integration Lead   |      |      |           |
| Security Officer   |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                              |
|---------|----------------|-----------------|------------------------------------------------------|
| 1.0.0   | [[2025-04-04]] | [[Wayne Smith]] | Initial version of the List Management API document |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->