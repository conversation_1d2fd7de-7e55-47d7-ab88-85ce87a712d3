---
title: Variable Assignment Schema
classification: Confidential
created: 2025-04-01
updated: 2025-04-03
authors:
  - "[[<PERSON>]]"
version: 1.1.0
next-review: 2025-06-28
category: "[[Specifications]]"
tags:
  - specification
  - schema
  - json
topics:
  - "[[Property Modification Framework]]"
  - "[[Rule Logic]]"
  - "[[RuleForge]]"
---

# Variable Assignment Schema

## Document Specification

### Purpose

This document defines the schema for Variable Assignments in the RuleForge Property Modification Framework (PMF). It specifies the format, content, and constraints of variable assignment operations used to modify properties and variables as part of rule execution.

### Scope

This document covers:

- The structure for defining variable assignments
- Definitions of assignment operations and their applicable types
- Data types and constraints for each field
- Usage guidelines for assignments in rule execution

This document does not cover:

- The internal implementation of assignment operations
- The process of creating or managing assignments in the system
- How assignments interact with entity-specific business rules

### Target Audience

- Developers implementing rule creation and execution features
- System architects designing property modification approaches
- QA engineers validating assignment behavior
- Technical writers creating assignment-related documentation

## Table of Contents

- [[#1. Variable Assignment Structure]]
- [[#2. Component Definitions]]
- [[#3. Usage Guidelines]]
- [[#4. Examples]]
- [[#5. Relationship with Rule Set Schema]]

## 1. Variable Assignment Structure

A Variable Assignment is represented by a JSON object with the following structure:

```json
{
  "assignmentTypeId": "string",
  "name": "string",
  "description": "string",
  "applicableTypes": ["string"]
}
```

## 2. Component Definitions

### Assignment Metadata

- `assignmentTypeId`: Unique identifier for the assignment operation (string, max 50 characters, UPPER_SNAKE_CASE)
- `name`: Human-readable name for the assignment operation (string, max 100 characters)
- `description`: Description of the assignment operation's purpose (string, max 250 characters)
- `applicableTypes`: Array of data types this assignment operation can be applied to (array of strings, enum: "string", "number", "boolean", "date", "enum")

## 3. Usage Guidelines

### Variable Assignments as State Updates

Variable Assignments in RuleForge are operations that update state based on rule outcomes. Unlike conditions (which are predicates that evaluate to true/false) and functions (which are pure transformations), variable assignments:

1. Modify the value of variables or properties
2. Implement the actual changes determined by rules
3. Apply to both local variables, persistent variables, and transaction properties

### Assignment Categories

The RuleForge system supports several categories of variable assignments:

#### Direct Assignments

These set variables to specific values:

- `SET`: Sets a variable to a specified value
  - Applicable to all types: string, number, boolean, date, enum

#### Numeric Operations

These perform mathematical operations on numeric variables:

- `ADD`: Adds a value to a variable
- `SUBTRACT`: Subtracts a value from a variable
- `MULTIPLY`: Multiplies a variable by a value
- `DIVIDE`: Divides a variable by a value
  - Applicable to: number

#### Percentage Operations

These modify numeric values by percentages:

- `INCREASE_BY_PERCENTAGE`: Increases a value by a percentage
- `DECREASE_BY_PERCENTAGE`: Decreases a value by a percentage
  - Applicable to: number

### Working with Functions in Assignments

Variable assignments can use function outputs as values. For example, an assignment might:
- Set a variable to the day of week: `SET(dayName, dayOfWeek(transactionDate))`
- Calculate a discount: `SET(discount, calculateDiscount(price, 10))`

Functions are registered separately and can provide values for assignments. See [[Function Schema]] for information on available functions.

## 4. Examples

### Direct Assignment

```json
{
  "assignmentTypeId": "SET",
  "name": "Set Value",
  "description": "Sets the variable to the specified value",
  "applicableTypes": ["string", "number", "boolean", "date", "enum"]
}
```

### Numeric Operation

```json
{
  "assignmentTypeId": "ADD",
  "name": "Add",
  "description": "Adds the specified value to the variable",
  "applicableTypes": ["number"]
}
```

### Percentage Operation

```json
{
  "assignmentTypeId": "DECREASE_BY_PERCENTAGE",
  "name": "Decrease by Percentage",
  "description": "Decreases the variable by the specified percentage",
  "applicableTypes": ["number"]
}
```

## 5. Relationship with Rule Set Schema

Variable Assignments provide the state update capabilities within the rules defined in the Rule Set Schema. They are referenced in the `variableAssignments` array of each rule:

```json
"variableAssignments": [
  {
    "variableId": "productPrice",
    "assignmentTypeId": "DECREASE_BY_PERCENTAGE",
    "value": 10
  },
  {
    "variableId": "discountApplied",
    "assignmentTypeId": "SET",
    "value": true
  }
]
```

In this example:
- The `assignmentTypeId` field references a Variable Assignment's `assignmentTypeId`
- The `variableId` specifies which variable or property to modify
- The `value` provides the value to use in the operation

### Assignment with Function Reference

```json
{
  "variableId": "dayOfPurchase",
  "assignmentTypeId": "SET",
  "value": {
    "functionId": "dayOfWeek",
    "args": ["{transactionDate}"]
  }
}
```

This architecture allows for a clean separation of concerns:
- **Condition Types**: Define predicates that evaluate to true/false
- **Functions**: Transform values through pure computation
- **Variable Assignments**: Update state based on rule outcomes

## Related Documents

- [[Rule Set Schema]]
- [[Condition Type Schema]]
- [[Function Schema]]
- [[RuleForge Management API]]
- [[Entity Integration Schema]]

## Approvals

| Role/Department       | Name | Date | Signature |
| --------------------- | ---- | ---- | --------- |
| Lead Architect        |      |      |           |
| API Team Lead         |      |      |           |
| Documentation Manager |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                 |
|---------|----------------|-----------------|--------------------------------------------------------|
| 1.1.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated field naming to align with RuleForge Naming Convention Guide: changed `code` to `assignmentTypeId` for assignment operation identifiers; updated references in examples to use consistent naming patterns |
| 1.0.0   | [[2025-04-01]] | [[Wayne Smith]] | Initial version of the Variable Assignment Schema document |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->