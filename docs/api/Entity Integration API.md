---
title: RuleForge Interface Overview
classification: Confidential
created: 2024-08-22
updated: 2025-04-07
authors:
  - "[[<PERSON><PERSON>]]"
  - "[[<PERSON>]]"
version: 3.7.0
next-review: 2025-08-22
category: "[[Specifications]]"
tags:
  - specification
  - overview
  - reference
topics:
  - "[[Property Modification Framework]]"
  - "[[System Architecture]]"
  - "[[Integration]]"
  - "[[Security]]"
---

# RuleForge Interface Overview

## Document Specification

### Purpose
This document provides a comprehensive overview of the interfaces within the RuleForge Property Modification Framework (PMF). Its purpose is to serve as a central reference for all team members involved in the development, integration, and maintenance of the system.

### Scope
This document covers:
- An overview of the RuleForge system architecture
- Authentication and security models
- Key terminology used in the system
- A summary table of all interfaces
- Detailed descriptions of each interface
- Version control and change management processes

This document does not cover:
- Detailed API specifications (covered in separate API documentation)
- Internal implementation details of individual components
- User interface design of the Rules Management GUI

### Target Audience
- Software Developers
- System Architects
- Integration Specialists
- QA Engineers
- Project Managers
- Security Engineers
- Data Analysts

## Introduction

The RuleForge system is a standalone, flexible rules engine designed to create, manage, and execute dynamic property modification rules in real-time across various business domains. It combines a powerful backend processing engine with a dedicated, user-friendly GUI, enabling complex, data-driven rules without reliance on legacy systems.

### Key Points
1. RuleForge is a standalone system with its own dedicated GUI.
2. It interacts with external systems (Entities) such as retail point-of-sale systems, e-commerce platforms, compliance systems, and operational tools.
3. The system uses a Rules Engine Server as its core, with Redis for rule set storage, persistent variable storage, and data caching, and Node.js for rule execution.
4. All external communications occur via RESTful APIs, using HTTPS and JWT for security.
5. The system supports various business domains through its domain-agnostic property modification approach.
6. MariaDB is used exclusively for analytics data storage and reporting.

## System Architecture Overview

![RuleForge System Architecture](https://placeholder.com/ruleforge-architecture-diagram)

The RuleForge system consists of several key components:

1. **Rules Engine Server**: The central processing component that evaluates transactions and applies rules
2. **Rules Management GUI**: Web-based interface for creating and managing rule sets
3. **Redis Database**: Provides storage for rule sets, persistent variables, lists, and caching
4. **Node.js Runtime**: Handles secure rule execution in a sandboxed environment
5. **MariaDB Database**: Stores analytics data for reporting and business intelligence
6. **Entity Systems**: External platforms that integrate with RuleForge

These components interact through well-defined interfaces described in this document.

## Authentication Model

RuleForge implements a multi-tiered authentication system to secure access to its interfaces:

### GUI User Authentication
- JWT-based authentication for human users accessing the Rules Management GUI
- Login credentials validated against Identity Provider
- Time-limited JWT tokens maintain authenticated state with appropriate timeout
- Users are assigned roles and permissions that control access to specific functionality

### Service-to-Service Authentication
- JWT-based authentication for automated services and CI/CD pipelines
- Client ID and Secret used to obtain time-limited JWT tokens
- Used primarily for automated rule deployment and system integration
- Tokens include scoped permissions to limit access to specific endpoints

### API Key Authentication
- Organization-level API keys for third-party integration
- Each organization receives a single API key that authorizes actions across all its entities
- Entity-specific operations require entityId parameters to identify the target entity
- API keys do not expire but can be revoked by system administrators
- Rate limiting is applied per API key to prevent abuse

All communications use HTTPS to ensure transport-level security, with appropriate rate limiting and logging for security monitoring.

## Key Terminology

- **Rules Engine Server**: The central component of the RuleForge system that processes rules and manages rule sets.
- **Entity**: External systems (e.g., retail POS, e-commerce platform, compliance system) that integrate with the Rules Engine.
- **Rules Management GUI**: The dedicated graphical user interface for creating and managing rule sets within RuleForge.
- **Transaction Context**: The set of data associated with a specific transaction, identified by a `contextId`, used for rule evaluation.
- **Persistent Variable**: A variable that maintains its state across multiple transactions or rule set executions.
- **Property Modification**: The process of dynamically changing property values based on rule conditions.
- **Rule Set**: A collection of related rules organized for a specific business purpose.
- **Evaluation Rules**: Rules that execute during transaction evaluation to modify properties before transaction completion.
- **Outcome Rules**: Rules that execute when processing transaction outcomes after transaction completion.
- **Condition**: A predicate that evaluates to true or false based on transaction data.
- **Function**: A pure transformation that computes values from inputs.
- **Variable Assignment**: An operation that updates state by modifying variables or properties.
- **List**: A named collection of values that can be referenced in rule conditions for membership testing.
- **Organization**: A business entity that owns and manages multiple system entities.
- **Analytics**: Metrics and insights derived from rule execution and transaction processing.

## Interface Summary Table

| No. | Interface                           | Provider              | Consumer                | Data Format | Description                                                                                                                                                                                                                                                                                                                          |
| --- | ----------------------------------- | --------------------- | ----------------------- | ----------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 1   | RuleForge Management API            | Rules Engine Server   | Rules Management GUI    | JSON        | RESTful API for rule set management. Enables creating, retrieving, updating, and deleting rule sets. Handles complex JSON structures for rule set definitions including evaluation rules and outcome rules. Uses HTTPS with JWT authentication. Includes endpoints for CRUD operations on rule sets and rule set listing with pagination. |
| 2   | Entity Integration API              | Rules Engine Server   | Entity System           | JSON        | RESTful API for registering and managing entities and their transaction contexts (identified by `contextId`). Uses organization-level API keys for authentication, with entityId parameters specifying the target entity. Provides endpoints like `POST /entities` for creation, `GET /entities/{entityId}` for retrieval, and `PUT /entities/{entityId}` for updates. |
| 3   | Transaction Evaluation API          | Rules Engine Server   | Entity System           | JSON        | API for real-time transaction evaluation and outcome processing. Includes `/evaluate` endpoint that executes evaluation rules and `/outcomes` endpoint that executes outcome rules. Entities send transaction context (identified by `contextId`) in JSON format and receive property modifications. Requires organization-level API key authentication with entityId parameter. |
| 4   | List Management API                 | Rules Engine Server   | Rules Management GUI    | JSON        | API for managing lists that can be referenced in rule conditions. Supports creating, retrieving, updating, and deleting lists. Includes endpoints for file upload, asynchronous processing, and element management. Uses the same authentication methods as other RuleForge APIs. |
| 5   | Rule Storage                        | Redis Database        | Rules Engine Server     | Redis Protocol | Interface for storing rule sets (with separate evaluation and outcome rule collections), persistent variables, and lists. Provides high-performance read/write operations, with atomicity guarantees for data integrity. Essential for maintaining rule definitions and cross-transaction state. |
| 6   | Rule Execution Engine               | Node.js Runtime       | Rules Engine Core       | JavaScript  | Internal interface for secure execution of generated JavaScript code representing rules. Implements sandboxing for security, context injection for rule evaluation, comprehensive error handling and logging, and collects analytics data for performance monitoring and rule effectiveness analysis. |
| 7   | Authentication Service              | Identity Provider     | Rules Engine Server     | JWT         | JWT-based authentication system securing all API access. Verifies the identity of both the Rules Management GUI and Entity Systems, ensuring secure and authorized interactions with the Rules Engine. |
| 8   | Analytics Storage                   | MariaDB Database      | Rules Engine Server     | SQL         | Persistent storage for analytics data, including rule execution metrics, transaction outcomes, and performance statistics. Supports complex queries and reporting. |
| 9   | Analytics Output                    | MariaDB Database      | Analytics Consumers     | SQL/JSON    | Interface for retrieving analytics data. Enables business intelligence through reports and dashboards. Supports both SQL queries and JSON export formats. |

## Detailed Interface Descriptions

### 1. RuleForge Management API

The RuleForge Management API is a RESTful interface that allows the Rules Management GUI to interact with the Rules Engine Server for creating, retrieving, updating, and deleting rule sets.

#### Key Endpoints:
- `POST /rulesets`: Create a new rule set
- `GET /rulesets/{ruleSetId}`: Retrieve a specific rule set
- `PUT /rulesets/{ruleSetId}`: Update an existing rule set
- `DELETE /rulesets/{ruleSetId}`: Delete a rule set
- `GET /rulesets`: List rule sets with pagination and filtering
- `GET /system/config-schema`: Retrieve system configuration including available condition types, functions, and variable assignments

#### Authentication:
This API supports two authentication methods:
- JWT-based authentication for GUI users
- JWT-based authentication for service-to-service interactions

#### Rule Collections:
The API handles rule sets containing two distinct rule collections:
- `evaluationRules`: Rules that execute during transaction evaluation
- `outcomeRules`: Rules that execute during outcome processing

For full API specification, refer to the [[RuleForge Management API]].

### 2. Entity Integration API

The Entity Integration API allows external systems to register with RuleForge and define their transaction contexts and properties.

#### Key Endpoints:
- `POST /entities`: Register a new entity
- `PUT /entities/{entityId}`: Update an entity's registration
- `GET /entities/{entityId}`: Retrieve an entity's details
- `GET /entities`: List all registered entities

#### Authentication:
This API uses organization-level API key authentication. Each organization has a single API key that authorises actions across all its entities.

For full API specification, refer to the [[Entity Integration API]].

### 3. Transaction Evaluation API

The Transaction Evaluation API enables entities to submit transactions for evaluation against applicable rule sets and receive property modifications to apply. It also provides a mechanism for reporting transaction outcomes.

#### Key Endpoints:
- `POST /evaluate`: Submit a transaction for evaluation (requires entityId and contextId) - executes evaluation rules
- `POST /outcomes`: Report the outcome of a transaction after property modifications (requires entityId and contextId) - executes outcome rules

#### Rule Collection Execution:
- The `/evaluate` endpoint executes only rules from the `evaluationRules` collection
- The `/outcomes` endpoint executes only rules from the `outcomeRules` collection

This separation ensures that rules are executed at the appropriate stage of the transaction lifecycle.

#### Authentication:
This API uses organization-level API key authentication. Each request must include the entityId to specify which entity's transaction is being evaluated.

#### Performance Considerations:
- Optimized for low-latency responses (typically <18ms)
- Supports high throughput (500+ transactions per second)
- Implements connection pooling and HTTP keep-alive for efficiency

For full API specification, refer to the [[Transaction Evaluation API]].

### 4. List Management API

The List Management API provides endpoints for creating, retrieving, updating, and deleting lists that can be referenced in rule conditions for membership testing.

#### Key Endpoints:
- `GET /lists`: Retrieve all lists with pagination
- `GET /lists/{listId}`: Retrieve a specific list and its elements
- `POST /lists`: Create a new list via JSON payload or file upload
- `PUT /lists/{listId}`: Update an existing list
- `DELETE /lists/{listId}`: Delete a list
- `GET /lists/{listId}/download`: Download a list as CSV or JSON

#### Authentication:
This API uses the same authentication methods as other RuleForge APIs.

For full API specification, refer to the [[List Management API]].

### 5. Rule Storage

The Rule Storage interface leverages Redis for storing rule sets, persistent variables, and lists with high performance and reliability.

#### Key Features:
- Optimized read/write operations for low-latency access
- Atomic operations for data integrity
- TTL (Time-To-Live) capabilities for expiring data
- Namespace isolation for security

#### Stored Data:
- Rule set definitions with separate evaluation and outcome rule collections
- Persistent variables
- List definitions and elements
- Cached transaction data

#### Implementation:
- Uses Redis data structures optimized for each data type
- Implements connection pooling for performance
- Supports publish/subscribe for real-time updates

### 6. Rule Execution Engine

The Rule Execution Engine provides a secure environment for executing the JavaScript code that implements rules.

#### Key Features:
- Sandboxed execution environment
- Resource limits to prevent abuse
- Comprehensive error handling and reporting
- Context injection for rule evaluation

#### Rule Collection Processing:
- Maintains separate execution contexts for evaluation rules and outcome rules
- Applies appropriate business logic for each rule type
- Enforces phase-specific constraints (e.g., property modifications only allowed in evaluation rules)

#### Implementation:
- Uses Node.js V8 isolates for secure execution
- Implements memory and CPU limits
- Provides standardized error reporting format

### 7. Authentication Service

The Authentication Service secures access to all RuleForge APIs through a combination of JWT and API key authentication mechanisms.

#### Key Features:
- User authentication for GUI access
- Service authentication for automated processes
- API key management for third-party integration
- Role-based access control

#### Implementation:
- Integrates with enterprise identity providers
- Issues and validates JWT tokens
- Manages API key lifecycle
- Enforces permission checks on all operations

### 8. Analytics Storage

The Analytics Storage interface utilizes MariaDB for reliable, structured storage of analytics data.

#### Key Features:
- Optimized schema for analytics queries
- High-performance read operations
- Comprehensive indexing for efficient queries
- Robust backup and recovery mechanisms

#### Stored Data:
- Rule execution metrics (separated by evaluation and outcome phases)
- Transaction processing statistics
- Property modification data
- Performance metrics
- Usage statistics

#### Implementation:
- Optimized schema for analytics workloads
- Efficient batch insertion mechanisms
- Advanced query optimization
- Data partitioning for performance

### 9. Analytics Output

The Analytics Output interface provides access to business intelligence data stored in MariaDB, enabling insights into rule execution, transaction processing, and system performance.

#### Key Features:
- Pre-aggregated metrics for common analytics needs
- Raw data access for custom analytics
- Historical trend analysis
- Support for both SQL queries and JSON export

#### Available Analytics:
- Rule execution metrics by phase (evaluation vs. outcome)
- Property modification statistics (types, values, impact)
- Transaction volume and processing times
- Entity-specific performance metrics
- Rule set effectiveness measurements

#### Implementation:
- Materialized views for efficient reporting
- Data warehouse schema for analytics optimization
- ETL processes for data transformation
- Read replicas for performance isolation

## Two-Phase Rule Execution Model

The RuleForge system implements a two-phase rule execution model that aligns with the transaction lifecycle:

### Phase 1: Evaluation Phase
- Triggered by the `/evaluate` endpoint
- Executes rules from the `evaluationRules` collection
- Focus on property modification before transaction completion
- Typical use cases: pricing adjustments, discounts, eligibility determination
- Real-time execution with low latency

### Phase 2: Outcome Phase
- Triggered by the `/outcomes` endpoint
- Executes rules from the `outcomeRules` collection
- Focus on post-transaction processing
- Typical use cases: loyalty point accrual, customer status updates, follow-up actions
- Asynchronous execution that doesn't block transaction completion

This separation provides clear boundaries between modification logic (applied during evaluation) and outcome processing logic (applied after knowing the transaction result).

## Version Control and Change Management

Interface changes in the RuleForge system are managed according to the [[Interface Change Management Process]]. Key points include:

1. **Semantic Versioning**: Interfaces are versioned using MAJOR.MINOR.PATCH format.
2. **Change Proposals**: All interface changes must be proposed via a Pull Request.
3. **Review Process**: Changes undergo thorough review by the development team and system architects.
4. **Documentation Updates**: This document and related API specifications are updated with each interface change.
5. **Deprecation Policy**: Major changes that break backwards compatibility include a deprecation period of at least 6 months.

## Security Considerations

### Data Protection
- All APIs enforce HTTPS for transport security
- Sensitive data is encrypted at rest in the database
- PII (Personally Identifiable Information) is handled according to data protection regulations

### Access Control
- Fine-grained permissions control access to API endpoints
- Role-based access control for GUI users
- Strict validation of all input data

### Monitoring and Auditing
- All authentication attempts are logged
- API access is monitored for unusual patterns
- Regular security audits are performed

## Analytics and Reporting

### Real-time Analytics
- Transaction volume and rule execution metrics by phase (evaluation vs. outcome)
- Property modification impact analysis
- Performance monitoring dashboards

### Historical Reporting
- Trend analysis for rule effectiveness
- Entity-specific performance reports
- Compliance and audit trails

### Data Access Methods
- Direct SQL access to analytics data for authorized analysts
- REST API for programmatic integration
- Pre-built dashboard templates for common use cases
- Export functionality for offline analysis

## Related Documents
- [[RuleForge Management API]]
- [[Entity Integration API]]
- [[Transaction Evaluation API]]
- [[List Management API]]
- [[List Management Schema]]
- [[Rule Set Schema]]
- [[Entity Integration Schema]]
- [[Function Schema]]
- [[Condition Type Schema]]
- [[Variable Assignment Schema]]
- [[Transaction Request Schema]]
- [[Transaction Response Schema]]
- [[Transaction Outcome Schema]]
- [[RuleForge System Architecture]]
- [[Interface Change Management Process]]
- [[Security Architecture]]
- [[Analytics Schema]]
- [[Data Warehouse Design]]

## Approvals
| Role/Department | Name | Date | Signature |
|-----------------|------|------|-----------|
| Lead Architect  |      |      |           |
| API Team Lead   |      |      |           |
| Documentation Manager |      |      |           |
| Security Officer |      |      |           |
| Data Analytics Lead |      |      |          |

## Changelog

| Version | Date           | Author          | Changes                                                                                                                                                                                                                                                                                                                                                                                                                                                   |
| ------- | -------------- | --------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 3.7.0   | [[2025-04-07]] | [[Wayne Smith]] | Updated Entity Integration API endpoints to follow RESTful principles: changed `/entities/registration` to `/entities` and `/entities/{entityId}/registration` to `/entities/{entityId}`; updated Interface Summary Table and Entity Integration API description to reflect the more resource-oriented URL structure |
| 3.6.0   | [[2025-04-07]] | [[Wayne Smith]] | Updated to align with Rule Set Schema v6.0.0: added evaluation rules and outcome rules to key terminology; updated interface descriptions to reflect the two-phase rule execution model; added new "Two-Phase Rule Execution Model" section; updated descriptions in Interface Summary Table; added rule collection references throughout the document |
| 3.5.0   | [[2025-04-04]] | [[Wayne Smith]] | Added List Management API to interface summary; updated key terminology to include Lists; corrected database usage descriptions (Redis for rule sets, persistent variables, and lists; MariaDB exclusively for analytics); renamed interfaces to reflect actual system implementation |
| 3.4.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated field naming to align with RuleForge Naming Convention Guide: ensured consistent use of standard identifier patterns (e.g., entityId, ruleSetId, contextId); standardized interface descriptions to follow naming conventions; aligned table terminology with established patterns |
| 3.3.0   | [[2025-04-05]] | [[Wayne Smith]] | Standardized field naming: ensured consistent use of 'contextId' rather than 'transactionContextId' throughout document for clarity and consistency across the RuleForge documentation                                                                                                                                                                                                                                                                    |
| 3.2.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated terminology from "campaign" to "rule set" throughout document; renamed "Campaign Rules Engine (CRE)" to "RuleForge Rules Engine"; standardized all references to related documents; updated interface descriptions and examples to reflect new terminology                                                                                                                                                                                        |
| 3.1.0   | [[2025-04-02]] | [[Wayne Smith]] | Added comprehensive Authentication Model section; expanded interface descriptions with authentication details; added Security Considerations section; updated Interface Summary Table with authentication clarifications; added Security Officer to approvals table; added MariaDB Database Storage and Analytics Output interfaces; expanded Analytics and Reporting section                                                                             |
| 3.0.0   | [[2025-04-02]] | [[Wayne Smith]] | Major revision to reposition as a domain-agnostic rules engine: renamed from "RuleForge CRE Interface Overview Document" to "RuleForge Interface Overview"; replaced campaign-centric terminology with domain-agnostic terms; updated interface names and descriptions; expanded key terminology to include property modification concepts; aligned with new schema naming; added more detailed interface descriptions; updated related documents section |
| 2.0.1   | [[2024-09-03]] | [[Faraz Ali]]   | Removed the reference to Transaction context JSON structure document                                                                                                                                                                                                                                                                                                                                                                                      |
| 2.0.0   | [[2024-08-28]] | [[Faraz Ali]]   | Major revision to align with latest system architecture and terminology. Updated interface descriptions and related documents.                                                                                                                                                                                                                                                                                                                            |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->