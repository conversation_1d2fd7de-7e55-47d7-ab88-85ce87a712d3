---
title: Condition Type Schema
classification: Confidential
created: 2024-08-27
updated: 2025-04-04
authors:
  - "[[<PERSON>]]"
version: 2.2.0
next-review: 2025-06-28
category: "[[Specifications]]"
tags:
  - specification
  - schema
  - json
topics:
  - "[[Property Modification Framework]]"
  - "[[Rule Logic]]"
  - "[[RuleForge]]"
---

# Condition Type Schema

## Document Specification

### Purpose

This document defines the schema for Condition Types in the RuleForge Property Modification Framework (PMF). It specifies the format, content, and constraints of condition type definitions used in rule creation and evaluation.

### Scope

This document covers:

- The structure for defining condition types
- Definitions of condition type components (operators, parameters)
- Data types and constraints for each field
- Usage guidelines for condition types

This document does not cover:

- The internal implementation of condition evaluation
- The process of creating or managing condition types in the system
- How condition types are used in the rule evaluation process

### Target Audience

- Developers implementing rule creation and evaluation features
- System architects designing condition structures
- QA engineers validating condition type definitions
- Technical writers creating condition-related documentation

## Table of Contents

- [[#1. Condition Type Structure]]
- [[#2. Component Definitions]]
- [[#3. Usage Guidelines]]
- [[#4. Examples]]

## 1. Condition Type Structure

A Condition Type is represented by a JSON object with the following structure:

```json
{
  "conditionTypeId": "string",
  "name": "string",
  "description": "string",
  "applicableTypes": ["string"],
  "parameters": [],
  "operators": []
}
```

## 2. Component Definitions

### Condition Type Metadata

- `conditionTypeId`: Unique identifier for the condition type (string, max 50 characters, UPPER_SNAKE_CASE)
- `name`: Human-readable name for the condition type (string, max 100 characters)
- `description`: Description of the condition type's purpose (string, max 250 characters)
- `applicableTypes`: Array of data types this condition type can be applied to (array of strings, enum: "string", "number", "boolean", "date", "enum")

### Parameters
 
The `parameters` array defines the inputs required for the condition:

```json
{
  "parameters": [
    {
      "parameterId": "string",
      "name": "string",
      "type": "string",
      "description": "string",
      "required": boolean
    }
  ]
}
```

Fields:

- `parameterId`: Identifier of the parameter (string, max 50 characters, camelCase)
- `name`: Human-readable name of the parameter (string, max 100 characters)
- `type`: Data type of the parameter (string, enum: "string", "number", "boolean", "date", "enum", "any")
- `description`: Description of the parameter's purpose (string, max 250 characters)
- `required`: Whether the parameter is mandatory (boolean)

### Operators

The `operators` array defines the valid operators for the condition type:

```json
{
  "operators": ["string"]
}
```

### Comparison Operators

The following operators are available for comparison conditions:

#### Equality Operators
- `==`: Equal to
- `!=`: Not equal to

#### Relational Operators
- `<`: Less than
- `<=`: Less than or equal to
- `>`: Greater than
- `>=`: Greater than or equal to

#### Membership Operators
- `IN`: Contained in a collection (either an array or a named list)
- `NOT_IN`: Not contained in a collection (either an array or a named list)

#### String Operators
- `CONTAINS`: String contains substring
- `STARTS_WITH`: String starts with substring
- `ENDS_WITH`: String ends with substring

#### Logical Operators 
- `AND`: Logical AND of multiple conditions
- `OR`: Logical OR of multiple conditions
- `NOT`: Logical negation of a condition

### Collection References

For membership operators (`IN` and `NOT_IN`), the `rightOperand` can be specified in two ways:

1. **Array Literal**: An inline array of values
   ```json
   ["value1", "value2", "value3"]
   ```

2. **List Reference**: A reference to a named list managed through the List Management API
   ```json
   {
     "listId": "listIdentifier"
   }
   ```

This unified approach allows for checking membership in both small inline collections and large managed lists using the same operators.

## 3. Usage Guidelines

### Condition Types as Predicates

Condition types define predicates that evaluate to true or false. They are the building blocks of rule conditions and determine when a rule should be triggered. Each condition type:

1. Defines a specific type of comparison or logical operation
2. Specifies required parameters for the operation
3. Defines allowed operators for the condition
4. Works with specific data types as indicated by `applicableTypes`

### Primitive Condition Types

The RuleForge system supports two fundamental condition types that serve as primitives for building any rule logic:

#### Comparison Condition

The comparison condition evaluates a simple relationship between two values:

- Type: `COMPARISON`
- Operators: `==`, `!=`, `<`, `<=`, `>`, `>=`, `IN`, `NOT_IN`, `CONTAINS`, `STARTS_WITH`, `ENDS_WITH`
- Parameters: `leftOperand`, `rightOperand`

This single primitive provides the foundation for all value comparisons.

#### Logical Condition

The logical condition combines multiple conditions:

- Type: `LOGICAL`
- Operators: `AND`, `OR`, `NOT`
- Parameter: `conditions` (array of condition objects)

This primitive enables the creation of complex, nested logical expressions by combining simpler conditions.

### Working with Functions

Conditions can use function outputs as parameters. For example, a condition might compare:
- The day of week from a transaction date: `dayOfWeek(transactionDate) == "FRIDAY"`
- A calculated value: `distance(customerLocation, storeLocation) < 10`

Functions are registered separately and can be used within conditions to transform values before comparison. See [[RuleForge Management API]] for information on available functions.

### Working with Collections

The `IN` and `NOT_IN` operators provide two ways to check if a value is a member of a collection:

1. **Small, Static Collections**: Use an array literal directly in the condition:
   ```json
   {
     "leftOperand": "{customerType}",
     "rightOperand": ["GOLD", "PLATINUM", "DIAMOND"]
   }
   ```

2. **Large or Dynamic Collections**: Reference a named list:
   ```json
   {
     "leftOperand": "{agentCGI}",
     "rightOperand": {
       "listId": "approvedAgents"
     }
   }
   ```

Use array literals for small collections (typically fewer than 10-20 items) that are rule-specific and change infrequently. Use list references for large collections (dozens to thousands of items) that may be shared across multiple rules or updated frequently. See [[List Management Schema]] for details on creating and managing lists.

## 4. Examples

### Comparison Condition

```json
{
  "conditionTypeId": "COMPARISON",
  "name": "Comparison",
  "description": "Compares two values using a specified operator",
  "applicableTypes": ["string", "number", "boolean", "date", "enum"],
  "parameters": [
    {
      "parameterId": "leftOperand",
      "name": "Left Operand",
      "type": "any",
      "description": "The left-hand side of the comparison",
      "required": true
    },
    {
      "parameterId": "rightOperand",
      "name": "Right Operand",
      "type": "any",
      "description": "The right-hand side of the comparison",
      "required": true
    }
  ],
  "operators": ["==", "!=", "<", "<=", ">", ">=", "IN", "NOT_IN", "CONTAINS", "STARTS_WITH", "ENDS_WITH"]
}
```

### Logical Condition

```json
{
  "conditionTypeId": "LOGICAL",
  "name": "Logical",
  "description": "Combines multiple conditions with a logical operator",
  "applicableTypes": ["boolean"],
  "parameters": [
    {
      "parameterId": "conditions",
      "name": "Conditions",
      "type": "array",
      "description": "The conditions to combine",
      "required": true
    }
  ],
  "operators": ["AND", "OR", "NOT"]
}
```

### Example: Array Membership Check

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": "IN",
  "parameters": {
    "leftOperand": "{customerType}",
    "rightOperand": ["GOLD", "PLATINUM", "DIAMOND"]
  }
}
```

This condition checks if the `customerType` property is one of "GOLD", "PLATINUM", or "DIAMOND".

### Example: List Membership Check

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": "IN",
  "parameters": {
    "leftOperand": "{agentCGI}",
    "rightOperand": {
      "listId": "approvedAgents"
    }
  }
}
```

This condition checks if the `agentCGI` property is in the list identified by "approvedAgents".

## 5. Relationship with Rule Set Schema

Condition Types provide the primitive building blocks for creating the `condition` objects within rules in the Rule Set Schema. By limiting the system to just two fundamental condition types (COMPARISON and LOGICAL), we enable maximum flexibility through composition.

### Example: Simple Price Check

A simple condition using the COMPARISON type:

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": ">",
  "parameters": {
    "leftOperand": "{productPrice}",
    "rightOperand": 100
  }
}
```

### Example: Date Range Check

A date range check constructed from primitive conditions:

```json
{
  "conditionTypeId": "LOGICAL",
  "operator": "AND",
  "parameters": {
    "conditions": [
      {
        "conditionTypeId": "COMPARISON",
        "operator": ">=",
        "parameters": {
          "leftOperand": "{transactionDate}",
          "rightOperand": "2025-06-01T00:00:00Z"
        }
      },
      {
        "conditionTypeId": "COMPARISON",
        "operator": "<=",
        "parameters": {
          "leftOperand": "{transactionDate}",
          "rightOperand": "2025-08-31T23:59:59Z"
        }
      }
    ]
  }
}
```

### Example: Complex Business Rule

Complex conditions combining multiple factors:

```json
{
  "conditionTypeId": "LOGICAL",
  "operator": "AND",
  "parameters": {
    "conditions": [
      {
        "conditionTypeId": "COMPARISON",
        "operator": ">",
        "parameters": {
          "leftOperand": "{totalPurchases}",
          "rightOperand": 1000
        }
      },
      {
        "conditionTypeId": "LOGICAL",
        "operator": "OR",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{customerSegment}",
                "rightOperand": "PREMIUM"
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "IN",
              "parameters": {
                "leftOperand": { "functionId": "dayOfWeek", "args": ["{transactionDate}"] },
                "rightOperand": {
                  "listId": "weekendDays"
                }
              }
            }
          ]
        }
      }
    ]
  }
}
```

This approach demonstrates how complex business rules can be constructed from just two primitive condition types, working in conjunction with functions for value transformation and lists for efficient membership testing.

## Related Documents

- [[Rule Set Schema]]
- [[Transaction Request Schema]]
- [[List Management Schema]]
- [[RuleForge Management API]]

## Approvals

| Role/Department       | Name | Date | Signature |
| --------------------- | ---- | ---- | --------- |
| Lead Architect        |      |      |           |
| API Team Lead         |      |      |           |
| Documentation Manager |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                 |
|---------|----------------|-----------------|--------------------------------------------------------|
| 2.2.0   | [[2025-04-04]] | [[Wayne Smith]] | Added list reference support for IN and NOT_IN operators; reorganized operators by type; improved documentation for collection references; unified membership testing for arrays and lists |
| 2.1.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated field naming to align with RuleForge Naming Convention Guide: renamed `code` to `conditionTypeId`, `name` to `parameterId` in parameters; added human-readable `name` fields where needed; ensured consistent use of condition type terminology |
| 2.0.0   | [[2025-04-01]] | [[Wayne Smith]] | Major revision to align with newer architecture: renamed from "Condition Type JSON Structure" to "Condition Type Schema"; updated structure to match the RuleForge Management API; added `code`, `name`, and `applicableTypes` fields; added section on condition types as predicates; enhanced examples; added explanation of relationship with Rule Set Schema; reorganized document using the Root Document Template |
| 1.1.0   | [[2024-08-28]] | [[RuleForge DevGenius]] | Updated examples, added usage guidelines, and aligned with latest system understanding |
| 1.0.0   | [[2024-08-27]] | [[RuleForge DevGenius]] | Initial version of the Condition Type structure document |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->