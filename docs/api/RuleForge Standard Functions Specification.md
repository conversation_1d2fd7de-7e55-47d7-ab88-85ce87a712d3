---
title: RuleForge Standard Functions Specification
classification: Confidential
created: 2025-04-03
updated: 2025-04-03
authors:
  - "[[<PERSON>]]"
version: 1.0.0
next-review: 2025-10-03
category: "[[Specifications]]"
tags:
  - specification
  - reference
  - functions
topics:
  - "[[Property Modification Framework]]"
  - "[[Rule Logic]]"
  - "[[RuleForge]]"
  - "[[Functions]]"
---

# RuleForge Standard Functions Specification

## Document Specification

### Purpose

This document defines the standard functions available within the RuleForge Property Modification Framework (PMF). It specifies both currently implemented functions and planned future functions that can be used in rule conditions and variable assignments, with a focus on their names, parameters, return types, and expected behaviors. This specification serves as both documentation for existing functionality and a roadmap for future development.

### Scope

This document covers:

- The comprehensive catalog of both current and planned standard functions for the RuleForge system
- Detailed specifications for each function, including parameters and return values
- Function categorization by domain (date/time, string, numeric, etc.)
- Usage examples for each function
- Implementation status of each function (current vs. planned)
- Roadmap for future function development
- Constraints and limitations of function usage

This document does not cover:

- The internal implementation of functions within the RuleForge engine
- Custom function development or extension mechanisms
- The process for deploying new standard functions
- Guidelines for creating user-defined functions

### Target Audience

- Developers implementing rule logic in RuleForge
- System architects designing rule structures that utilize functions
- Integration specialists creating rule sets with complex calculations
- QA engineers validating function behavior
- Technical writers creating function-related documentation

## Table of Contents

- [[#1. Function Overview]]
- [[#2. Date and Time Functions]]
- [[#3. String Functions]]
- [[#4. Numeric Functions]]
- [[#5. Logical Functions]]
- [[#6. Collection Functions]]
- [[#7. Conversion Functions]]
- [[#8. Function Usage in Rules]]
- [[#9. Future Additions]]

## 1. Function Overview

> **Important Note**: This specification document outlines both currently implemented functions and planned future functions for the RuleForge system. Functions marked with ✅ are currently implemented and available for use. Functions without this indicator are planned for future releases and documented here to provide a comprehensive roadmap for development and to gather feedback on proposed functionality.

### 1.1 Function Structure

Functions in RuleForge follow a consistent structure that includes:

- A unique name (camelCase)
- A set of parameters (zero or more)
- A single return value of a specific type
- Clear, deterministic behavior

### 1.2 Using Functions in Rules

Functions can be used in two primary contexts:

1. **In Condition Parameters**: To transform values before comparison
   ```json
   {
     "type": "COMPARISON",
     "operator": "==",
     "parameters": {
       "leftOperand": { "function": "dayOfWeek", "args": ["{transactionDate}"] },
       "rightOperand": "FRIDAY"
     }
   }
   ```

2. **In Variable Assignment Values**: To compute values for assignment
   ```json
   {
     "variableId": "formattedDate",
     "operation": "SET",
     "value": { "function": "formatDate", "args": ["{transactionDate}", "yyyy-MM-dd"] }
   }
   ```

### 1.3 Function Categories

RuleForge standard functions are organized into the following categories:

- **Date and Time Functions**: For manipulating and extracting information from dates and times
- **String Functions**: For string manipulation and transformation
- **Numeric Functions**: For mathematical calculations
- **Logical Functions**: For boolean operations and conditional logic
- **Collection Functions**: For working with arrays and collections
- **Conversion Functions**: For converting between different data types

## 2. Date and Time Functions

Date and time functions provide capabilities for extracting components from date values, formatting dates, calculating differences, and performing calendar-based operations.

### 2.1 Date Component Extraction

#### 2.1.1 dayOfWeek ✅

- **Description**: Returns the name of the day of the week for a given date
- **Parameters**:
  - `date` (date): The date to extract the day of week from
- **Return Type**: string
- **Return Values**: "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"
- **Example**:
  ```json
  { "function": "dayOfWeek", "args": ["2025-04-03T14:30:00Z"] } // Returns "THURSDAY"
  ```

#### 2.1.2 dayName

- **Description**: Returns the localized name of the day of the week for a given date
- **Parameters**:
  - `date` (date): The date to extract the day name from
  - `locale` (string, optional): The locale to use for formatting (default: "en-US")
- **Return Type**: string
- **Return Values**: Localized day name (e.g., "Monday", "Lundi", "Montag")
- **Example**:
  ```json
  { "function": "dayName", "args": ["2025-04-03T14:30:00Z"] } // Returns "Thursday"
  { "function": "dayName", "args": ["2025-04-03T14:30:00Z", "fr-FR"] } // Returns "Jeudi"
  ```

#### 2.1.3 monthName

- **Description**: Returns the name of the month for a given date
- **Parameters**:
  - `date` (date): The date to extract the month name from
  - `locale` (string, optional): The locale to use for formatting (default: "en-US")
- **Return Type**: string
- **Return Values**: Localized month name (e.g., "January", "Janvier", "Januar")
- **Example**:
  ```json
  { "function": "monthName", "args": ["2025-04-03T14:30:00Z"] } // Returns "April"
  { "function": "monthName", "args": ["2025-04-03T14:30:00Z", "es-ES"] } // Returns "Abril"
  ```

#### 2.1.4 year

- **Description**: Returns the year for a given date
- **Parameters**:
  - `date` (date): The date to extract the year from
- **Return Type**: number
- **Example**:
  ```json
  { "function": "year", "args": ["2025-04-03T14:30:00Z"] } // Returns 2025
  ```

#### 2.1.5 month

- **Description**: Returns the month (1-12) for a given date
- **Parameters**:
  - `date` (date): The date to extract the month from
- **Return Type**: number
- **Return Values**: 1 (January) through 12 (December)
- **Example**:
  ```json
  { "function": "month", "args": ["2025-04-03T14:30:00Z"] } // Returns 4
  ```

#### 2.1.6 day

- **Description**: Returns the day of the month (1-31) for a given date
- **Parameters**:
  - `date` (date): The date to extract the day from
- **Return Type**: number
- **Example**:
  ```json
  { "function": "day", "args": ["2025-04-03T14:30:00Z"] } // Returns 3
  ```

#### 2.1.7 hour

- **Description**: Returns the hour (0-23) for a given date
- **Parameters**:
  - `date` (date): The date to extract the hour from
- **Return Type**: number
- **Example**:
  ```json
  { "function": "hour", "args": ["2025-04-03T14:30:00Z"] } // Returns 14
  ```

#### 2.1.8 minute

- **Description**: Returns the minute (0-59) for a given date
- **Parameters**:
  - `date` (date): The date to extract the minute from
- **Return Type**: number
- **Example**:
  ```json
  { "function": "minute", "args": ["2025-04-03T14:30:00Z"] } // Returns 30
  ```

#### 2.1.9 second

- **Description**: Returns the second (0-59) for a given date
- **Parameters**:
  - `date` (date): The date to extract the second from
- **Return Type**: number
- **Example**:
  ```json
  { "function": "second", "args": ["2025-04-03T14:30:45Z"] } // Returns 45
  ```

#### 2.1.10 dayOfYear

- **Description**: Returns the day of the year (1-366) for a given date
- **Parameters**:
  - `date` (date): The date to extract the day of year from
- **Return Type**: number
- **Example**:
  ```json
  { "function": "dayOfYear", "args": ["2025-04-03T14:30:00Z"] } // Returns 93
  ```

#### 2.1.11 weekOfYear

- **Description**: Returns the week of the year (1-53) for a given date
- **Parameters**:
  - `date` (date): The date to extract the week of year from
- **Return Type**: number
- **Example**:
  ```json
  { "function": "weekOfYear", "args": ["2025-04-03T14:30:00Z"] } // Returns 14
  ```

#### 2.1.12 quarter

- **Description**: Returns the quarter (1-4) for a given date
- **Parameters**:
  - `date` (date): The date to extract the quarter from
- **Return Type**: number
- **Example**:
  ```json
  { "function": "quarter", "args": ["2025-04-03T14:30:00Z"] } // Returns 2
  ```

### 2.2 Date Formatting

#### 2.2.1 formatDate ✅

- **Description**: Formats a date according to the specified pattern
- **Parameters**:
  - `date` (date): The date to format
  - `pattern` (string): The format pattern (follows Java SimpleDateFormat syntax)
  - `locale` (string, optional): The locale to use for formatting (default: "en-US")
- **Return Type**: string
- **Example**:
  ```json
  { "function": "formatDate", "args": ["2025-04-03T14:30:00Z", "yyyy-MM-dd"] } // Returns "2025-04-03"
  { "function": "formatDate", "args": ["2025-04-03T14:30:00Z", "MMMM d, yyyy", "fr-FR"] } // Returns "Avril 3, 2025"
  ```

#### 2.2.2 parseDate

- **Description**: Parses a string into a date using the specified pattern
- **Parameters**:
  - `dateString` (string): The string to parse
  - `pattern` (string): The format pattern (follows Java SimpleDateFormat syntax)
  - `locale` (string, optional): The locale to use for parsing (default: "en-US")
- **Return Type**: date
- **Example**:
  ```json
  { "function": "parseDate", "args": ["2025-04-03", "yyyy-MM-dd"] } // Returns 2025-04-03T00:00:00Z
  { "function": "parseDate", "args": ["3 Avril 2025", "d MMMM yyyy", "fr-FR"] } // Returns 2025-04-03T00:00:00Z
  ```

### 2.3 Date Calculations

#### 2.3.1 addDays

- **Description**: Adds a specified number of days to a date
- **Parameters**:
  - `date` (date): The base date
  - `days` (number): The number of days to add (can be negative)
- **Return Type**: date
- **Example**:
  ```json
  { "function": "addDays", "args": ["2025-04-03T14:30:00Z", 7] } // Returns 2025-04-10T14:30:00Z
  { "function": "addDays", "args": ["2025-04-03T14:30:00Z", -3] } // Returns 2025-03-31T14:30:00Z
  ```

#### 2.3.2 addMonths

- **Description**: Adds a specified number of months to a date
- **Parameters**:
  - `date` (date): The base date
  - `months` (number): The number of months to add (can be negative)
- **Return Type**: date
- **Example**:
  ```json
  { "function": "addMonths", "args": ["2025-04-03T14:30:00Z", 2] } // Returns 2025-06-03T14:30:00Z
  { "function": "addMonths", "args": ["2025-04-03T14:30:00Z", -1] } // Returns 2025-03-03T14:30:00Z
  ```

#### 2.3.3 addYears

- **Description**: Adds a specified number of years to a date
- **Parameters**:
  - `date` (date): The base date
  - `years` (number): The number of years to add (can be negative)
- **Return Type**: date
- **Example**:
  ```json
  { "function": "addYears", "args": ["2025-04-03T14:30:00Z", 1] } // Returns 2026-04-03T14:30:00Z
  { "function": "addYears", "args": ["2025-04-03T14:30:00Z", -2] } // Returns 2023-04-03T14:30:00Z
  ```

#### 2.3.4 addHours

- **Description**: Adds a specified number of hours to a date
- **Parameters**:
  - `date` (date): The base date
  - `hours` (number): The number of hours to add (can be negative)
- **Return Type**: date
- **Example**:
  ```json
  { "function": "addHours", "args": ["2025-04-03T14:30:00Z", 3] } // Returns 2025-04-03T17:30:00Z
  { "function": "addHours", "args": ["2025-04-03T14:30:00Z", -6] } // Returns 2025-04-03T08:30:00Z
  ```

#### 2.3.5 addMinutes

- **Description**: Adds a specified number of minutes to a date
- **Parameters**:
  - `date` (date): The base date
  - `minutes` (number): The number of minutes to add (can be negative)
- **Return Type**: date
- **Example**:
  ```json
  { "function": "addMinutes", "args": ["2025-04-03T14:30:00Z", 45] } // Returns 2025-04-03T15:15:00Z
  { "function": "addMinutes", "args": ["2025-04-03T14:30:00Z", -30] } // Returns 2025-04-03T14:00:00Z
  ```

#### 2.3.6 dateDifference

- **Description**: Calculates the difference between two dates in the specified unit
- **Parameters**:
  - `date1` (date): The first date
  - `date2` (date): The second date
  - `unit` (string): The unit for the difference. One of: "years", "months", "days", "hours", "minutes", "seconds"
- **Return Type**: number
- **Example**:
  ```json
  { "function": "dateDifference", "args": ["2025-04-03T14:30:00Z", "2025-04-10T14:30:00Z", "days"] } // Returns 7
  { "function": "dateDifference", "args": ["2025-04-03T14:30:00Z", "2025-04-03T15:45:00Z", "minutes"] } // Returns 75
  ```

#### 2.3.7 now

- **Description**: Returns the current date and time
- **Parameters**: None
- **Return Type**: date
- **Example**:
  ```json
  { "function": "now", "args": [] } // Returns current date and time
  ```

#### 2.3.8 today

- **Description**: Returns the current date with time set to midnight (00:00:00)
- **Parameters**: None
- **Return Type**: date
- **Example**:
  ```json
  { "function": "today", "args": [] } // Returns current date at midnight
  ```

#### 2.3.9 startOfMonth

- **Description**: Returns the first day of the month for a given date, with time set to midnight
- **Parameters**:
  - `date` (date): The reference date
- **Return Type**: date
- **Example**:
  ```json
  { "function": "startOfMonth", "args": ["2025-04-15T14:30:00Z"] } // Returns 2025-04-01T00:00:00Z
  ```

#### 2.3.10 endOfMonth

- **Description**: Returns the last day of the month for a given date, with time set to 23:59:59
- **Parameters**:
  - `date` (date): The reference date
- **Return Type**: date
- **Example**:
  ```json
  { "function": "endOfMonth", "args": ["2025-04-15T14:30:00Z"] } // Returns 2025-04-30T23:59:59Z
  ```

#### 2.3.11 isFutureDate

- **Description**: Checks if a date is in the future compared to the current date and time
- **Parameters**:
  - `date` (date): The date to check
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "isFutureDate", "args": ["2026-04-03T14:30:00Z"] } // Returns true if current date is before this date
  ```

#### 2.3.12 isPastDate

- **Description**: Checks if a date is in the past compared to the current date and time
- **Parameters**:
  - `date` (date): The date to check
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "isPastDate", "args": ["2024-04-03T14:30:00Z"] } // Returns true if current date is after this date
  ```

#### 2.3.13 isWeekend

- **Description**: Checks if a date falls on a weekend (Saturday or Sunday)
- **Parameters**:
  - `date` (date): The date to check
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "isWeekend", "args": ["2025-04-05T14:30:00Z"] } // Returns true (April 5, 2025 is a Saturday)
  ```

#### 2.3.14 isWeekday

- **Description**: Checks if a date falls on a weekday (Monday through Friday)
- **Parameters**:
  - `date` (date): The date to check
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "isWeekday", "args": ["2025-04-03T14:30:00Z"] } // Returns true (April 3, 2025 is a Thursday)
  ```

#### 2.3.15 isLeapYear

- **Description**: Checks if the year of a given date is a leap year
- **Parameters**:
  - `date` (date): The date to check
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "isLeapYear", "args": ["2024-04-03T14:30:00Z"] } // Returns true (2024 is a leap year)
  { "function": "isLeapYear", "args": ["2025-04-03T14:30:00Z"] } // Returns false (2025 is not a leap year)
  ```

## 3. String Functions

String functions provide capabilities for manipulating and transforming text values.

### 3.1 String Manipulation

#### 3.1.1 concat

- **Description**: Concatenates multiple strings into a single string
- **Parameters**:
  - `string1` (string): First string
  - `string2` (string): Second string
  - `...` Additional strings (optional)
- **Return Type**: string
- **Example**:
  ```json
  { "function": "concat", "args": ["Hello, ", "world!"] } // Returns "Hello, world!"
  { "function": "concat", "args": ["Price: $", "19.99", " (", "Sale", ")"] } // Returns "Price: $19.99 (Sale)"
  ```

#### 3.1.2 substring

- **Description**: Extracts a portion of a string between the specified indices
- **Parameters**:
  - `string` (string): The input string
  - `startIndex` (number): The starting index (inclusive)
  - `endIndex` (number, optional): The ending index (exclusive). If omitted, extracts to the end of the string.
- **Return Type**: string
- **Example**:
  ```json
  { "function": "substring", "args": ["Hello, world!", 7, 12] } // Returns "world"
  { "function": "substring", "args": ["Hello, world!", 7] } // Returns "world!"
  ```

#### 3.1.3 replace

- **Description**: Replaces occurrences of a substring with another string
- **Parameters**:
  - `string` (string): The input string
  - `searchValue` (string): The substring to find
  - `replaceValue` (string): The string to replace with
- **Return Type**: string
- **Example**:
  ```json
  { "function": "replace", "args": ["Hello, world!", "world", "RuleForge"] } // Returns "Hello, RuleForge!"
  ```

#### 3.1.4 trim

- **Description**: Removes whitespace from both ends of a string
- **Parameters**:
  - `string` (string): The input string
- **Return Type**: string
- **Example**:
  ```json
  { "function": "trim", "args": ["  Hello, world!  "] } // Returns "Hello, world!"
  ```

#### 3.1.5 toLowerCase

- **Description**: Converts a string to lowercase
- **Parameters**:
  - `string` (string): The input string
- **Return Type**: string
- **Example**:
  ```json
  { "function": "toLowerCase", "args": ["Hello, World!"] } // Returns "hello, world!"
  ```

#### 3.1.6 toUpperCase

- **Description**: Converts a string to uppercase
- **Parameters**:
  - `string` (string): The input string
- **Return Type**: string
- **Example**:
  ```json
  { "function": "toUpperCase", "args": ["Hello, World!"] } // Returns "HELLO, WORLD!"
  ```

#### 3.1.7 length

- **Description**: Returns the length (number of characters) of a string
- **Parameters**:
  - `string` (string): The input string
- **Return Type**: number
- **Example**:
  ```json
  { "function": "length", "args": ["Hello, world!"] } // Returns 13
  ```

#### 3.1.8 startsWith

- **Description**: Checks if a string starts with the specified substring
- **Parameters**:
  - `string` (string): The input string
  - `prefix` (string): The substring to check for
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "startsWith", "args": ["Hello, world!", "Hello"] } // Returns true
  { "function": "startsWith", "args": ["Hello, world!", "world"] } // Returns false
  ```

#### 3.1.9 endsWith

- **Description**: Checks if a string ends with the specified substring
- **Parameters**:
  - `string` (string): The input string
  - `suffix` (string): The substring to check for
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "endsWith", "args": ["Hello, world!", "world!"] } // Returns true
  { "function": "endsWith", "args": ["Hello, world!", "Hello"] } // Returns false
  ```

#### 3.1.10 contains

- **Description**: Checks if a string contains the specified substring
- **Parameters**:
  - `string` (string): The input string
  - `substring` (string): The substring to check for
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "contains", "args": ["Hello, world!", "world"] } // Returns true
  { "function": "contains", "args": ["Hello, world!", "RuleForge"] } // Returns false
  ```

#### 3.1.11 indexOf

- **Description**: Returns the index of the first occurrence of a substring in a string
- **Parameters**:
  - `string` (string): The input string
  - `substring` (string): The substring to search for
- **Return Type**: number
- **Return Values**: 0-based index of the first occurrence, or -1 if not found
- **Example**:
  ```json
  { "function": "indexOf", "args": ["Hello, world!", "world"] } // Returns 7
  { "function": "indexOf", "args": ["Hello, world!", "RuleForge"] } // Returns -1
  ```

#### 3.1.12 lastIndexOf

- **Description**: Returns the index of the last occurrence of a substring in a string
- **Parameters**:
  - `string` (string): The input string
  - `substring` (string): The substring to search for
- **Return Type**: number
- **Return Values**: 0-based index of the last occurrence, or -1 if not found
- **Example**:
  ```json
  { "function": "lastIndexOf", "args": ["Hello, world! Hello again!", "Hello"] } // Returns 14
  ```

#### 3.1.13 split

- **Description**: Splits a string into an array of substrings based on a separator
- **Parameters**:
  - `string` (string): The input string
  - `separator` (string): The character or substring to split on
- **Return Type**: array of strings
- **Example**:
  ```json
  { "function": "split", "args": ["red,green,blue", ","] } // Returns ["red", "green", "blue"]
  ```

#### 3.1.14 join

- **Description**: Joins an array of strings into a single string with a specified separator
- **Parameters**:
  - `array` (array of strings): The array to join
  - `separator` (string): The separator to use between elements
- **Return Type**: string
- **Example**:
  ```json
  { "function": "join", "args": [["red", "green", "blue"], ", "] } // Returns "red, green, blue"
  ```

#### 3.1.15 padLeft

- **Description**: Pads the start of a string with a specified character to achieve the desired length
- **Parameters**:
  - `string` (string): The input string
  - `length` (number): The target length
  - `padChar` (string, optional): The character to pad with (default: space)
- **Return Type**: string
- **Example**:
  ```json
  { "function": "padLeft", "args": ["123", 5, "0"] } // Returns "00123"
  { "function": "padLeft", "args": ["abc", 5] } // Returns "  abc"
  ```

#### 3.1.16 padRight

- **Description**: Pads the end of a string with a specified character to achieve the desired length
- **Parameters**:
  - `string` (string): The input string
  - `length` (number): The target length
  - `padChar` (string, optional): The character to pad with (default: space)
- **Return Type**: string
- **Example**:
  ```json
  { "function": "padRight", "args": ["123", 5, "0"] } // Returns "12300"
  { "function": "padRight", "args": ["abc", 5] } // Returns "abc  "
  ```

### 3.2 String Formatting

#### 3.2.1 formatNumber

- **Description**: Formats a number according to specific formatting options
- **Parameters**:
  - `number` (number): The number to format
  - `format` (string, optional): The format string (e.g., "#,##0.00")
  - `locale` (string, optional): The locale to use for formatting (default: "en-US")
- **Return Type**: string
- **Example**:
  ```json
  { "function": "formatNumber", "args": [1234.56, "#,##0.00"] } // Returns "1,234.56"
  { "function": "formatNumber", "args": [1234.56, "#,##0.00", "de-DE"] } // Returns "1.234,56"
  ```

#### 3.2.2 formatCurrency

- **Description**: Formats a number as currency
- **Parameters**:
  - `number` (number): The number to format
  - `currencyCode` (string): The ISO 4217 currency code (e.g., "USD", "EUR")
  - `locale` (string, optional): The locale to use for formatting (default: "en-US")
- **Return Type**: string
- **Example**:
  ```json
  { "function": "formatCurrency", "args": [1234.56, "USD"] } // Returns "$1,234.56"
  { "function": "formatCurrency", "args": [1234.56, "EUR", "de-DE"] } // Returns "1.234,56 €"
  ```

#### 3.2.3 formatPercent

- **Description**: Formats a number as a percentage
- **Parameters**:
  - `number` (number): The number to format (e.g., 0.15 for 15%)
  - `fractionDigits` (number, optional): The number of decimal places (default: 2)
  - `locale` (string, optional): The locale to use for formatting (default: "en-US")
- **Return Type**: string
- **Example**:
  ```json
  { "function": "formatPercent", "args": [0.1567, 1] } // Returns "15.7%"
  { "function": "formatPercent", "args": [0.1567, 2, "fr-FR"] } // Returns "15,67 %"
  ```

## 4. Numeric Functions

Numeric functions provide mathematical operations and calculations.

### 4.1 Basic Math

#### 4.1.1 abs

- **Description**: Returns the absolute value of a number
- **Parameters**:
  - `number` (number): The input number
- **Return Type**: number
- **Example**:
  ```json
  { "function": "abs", "args": [-42] } // Returns 42
  ```

#### 4.1.2 min

- **Description**: Returns the smaller of two or more numbers
- **Parameters**:
  - `number1` (number): First number
  - `number2` (number): Second number
  - `...` Additional numbers (optional)
- **Return Type**: number
- **Example**:
  ```json
  { "function": "min", "args": [5, 10] } // Returns 5
  { "function": "min", "args": [5, 10, 3, 8] } // Returns 3
  ```

#### 4.1.3 max

- **Description**: Returns the larger of two or more numbers
- **Parameters**:
  - `number1` (number): First number
  - `number2` (number): Second number
  - `...` Additional numbers (optional)
- **Return Type**: number
- **Example**:
  ```json
  { "function": "max", "args": [5, 10] } // Returns 10
  { "function": "max", "args": [5, 10, 3, 8] } // Returns 10
  ```

#### 4.1.4 round

- **Description**: Rounds a number to the nearest integer or to a specified number of decimal places
- **Parameters**:
  - `number` (number): The number to round
  - `decimals` (number, optional): The number of decimal places (default: 0)
- **Return Type**: number
- **Example**:
  ```json
  { "function": "round", "args": [123.456] } // Returns 123
  { "function": "round", "args": [123.456, 2] } // Returns 123.46
  ```

#### 4.1.5 floor

- **Description**: Rounds a number down to the nearest integer or to a specified number of decimal places
- **Parameters**:
  - `number` (number): The number to round down
  - `decimals` (number, optional): The number of decimal places (default: 0)
- **Return Type**: number
- **Example**:
  ```json
  { "function": "floor", "args": [123.789] } // Returns 123
  { "function": "floor", "args": [123.789, 2] } // Returns 123.78
  ```

#### 4.1.6 ceiling

- **Description**: Rounds a number up to the nearest integer or to a specified number of decimal places
- **Parameters**:
  - `number` (number): The number to round up
  - `decimals` (number, optional): The number of decimal places (default: 0)
- **Return Type**: number
- **Example**:
  ```json
  { "function": "ceiling", "args": [123.456] } // Returns 124
  { "function": "ceiling", "args": [123.456, 2] } // Returns 123.46
  ```

#### 4.1.7 pow

- **Description**: Returns the base to the exponent power
- **Parameters**:
  - `base` (number): The base number
  - `exponent` (number): The exponent
- **Return Type**: number
- **Example**:
  ```json
  { "function": "pow", "args": [2, 3] } // Returns 8
  ```

#### 4.1.8 sqrt

- **Description**: Returns the square root of a number
- **Parameters**:
  - `number` (number): The number to find the square root of
- **Return Type**: number
- **Example**:
  ```json
  { "function": "sqrt", "args": [16] } // Returns 4
  ```

#### 4.1.9 random

- **Description**: Returns a random number between 0 (inclusive) and 1 (exclusive)
- **Parameters**: None
- **Return Type**: number
- **Example**:
  ```json
  { "function": "random", "args": [] } // Returns a random number between 0 and 1
  ```

#### 4.1.10 randomInt

- **Description**: Returns a random integer between min (inclusive) and max (inclusive)
- **Parameters**:
  - `min` (number): The minimum value (inclusive)
  - `max` (number): The maximum value (inclusive)
- **Return Type**: number
- **Example**:
  ```json
  { "function": "randomInt", "args": [1, 10] } // Returns a random integer between 1 and 10
  ```

### 4.2 Financial Calculations

#### 4.2.1 calculateDiscount

- **Description**: Calculates a discount amount based on a price and percentage
- **Parameters**:
  - `price` (number): The original price
  - `percentage` (number): The discount percentage
- **Return Type**: number
- **Example**:
  ```json
  { "function": "calculateDiscount", "args": [100, 20] } // Returns 20
  ```

#### 4.2.2 applyDiscount

- **Description**: Applies a discount percentage to a price
- **Parameters**:
  - `price` (number): The original price
  - `percentage` (number): The discount percentage
- **Return Type**: number
- **Example**:
  ```json
  { "function": "applyDiscount", "args": [100, 20] } // Returns 80
  ```

#### 4.2.3 calculateTax

- **Description**: Calculates the tax amount for a given price and tax rate
- **Parameters**:
  - `price` (number): The price before tax
  - `taxRate` (number): The tax rate as a percentage
- **Return Type**: number
- **Example**:
  ```json
  { "function": "calculateTax", "args": [100, 8.5] } // Returns 8.5
  ```

#### 4.2.4 applyTax

- **Description**: Applies a tax rate to a price
- **Parameters**:
  - `price` (number): The price before tax
  - `taxRate` (number): The tax rate as a percentage
- **Return Type**: number
- **Example**:
  ```json
  { "function": "applyTax", "args": [100, 8.5] } // Returns 108.5
  ```

## 5. Logical Functions

Logical functions provide boolean operations and conditional logic.

### 5.1 Boolean Operations

#### 5.1.1 and

- **Description**: Returns true if all input values are true
- **Parameters**:
  - `boolean1` (boolean): First boolean value
  - `boolean2` (boolean): Second boolean value
  - `...` Additional boolean values (optional)
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "and", "args": [true, true] } // Returns true
  { "function": "and", "args": [true, false, true] } // Returns false
  ```

#### 5.1.2 or

- **Description**: Returns true if any input value is true
- **Parameters**:
  - `boolean1` (boolean): First boolean value
  - `boolean2` (boolean): Second boolean value
  - `...` Additional boolean values (optional)
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "or", "args": [false, true] } // Returns true
  { "function": "or", "args": [false, false, false] } // Returns false
  ```

#### 5.1.3 not

- **Description**: Returns the logical negation of the input value
- **Parameters**:
  - `boolean` (boolean): The boolean value to negate
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "not", "args": [true] } // Returns false
  { "function": "not", "args": [false] } // Returns true
  ```

### 5.2 Conditional Functions

#### 5.2.1 ifThenElse

- **Description**: Returns one value if the condition is true, another value if false
- **Parameters**:
  - `condition` (boolean): The condition to evaluate
  - `trueValue` (any): The value to return if condition is true
  - `falseValue` (any): The value to return if condition is false
- **Return Type**: any (same type as trueValue and falseValue)
- **Example**:
  ```json
  { "function": "ifThenElse", "args": [true, "Yes", "No"] } // Returns "Yes"
  { "function": "ifThenElse", "args": [false, "Yes", "No"] } // Returns "No"
  ```

#### 5.2.2 isNull

- **Description**: Checks if a value is null
- **Parameters**:
  - `value` (any): The value to check
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "isNull", "args": [null] } // Returns true
  { "function": "isNull", "args": ["Hello"] } // Returns false
  ```

#### 5.2.3 isNotNull

- **Description**: Checks if a value is not null
- **Parameters**:
  - `value` (any): The value to check
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "isNotNull", "args": [null] } // Returns false
  { "function": "isNotNull", "args": ["Hello"] } // Returns true
  ```

#### 5.2.4 isEmpty

- **Description**: Checks if a string, array, or object is empty
- **Parameters**:
  - `value` (string|array|object): The value to check
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "isEmpty", "args": [""] } // Returns true
  { "function": "isEmpty", "args": ["Hello"] } // Returns false
  { "function": "isEmpty", "args": [[]] } // Returns true
  { "function": "isEmpty", "args": [[1, 2, 3]] } // Returns false
  { "function": "isEmpty", "args": [{}] } // Returns true
  ```

#### 5.2.5 isNotEmpty

- **Description**: Checks if a string, array, or object is not empty
- **Parameters**:
  - `value` (string|array|object): The value to check
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "isNotEmpty", "args": [""] } // Returns false
  { "function": "isNotEmpty", "args": ["Hello"] } // Returns true
  { "function": "isNotEmpty", "args": [[]] } // Returns false
  { "function": "isNotEmpty", "args": [[1, 2, 3]] } // Returns true
  ```

## 6. Collection Functions

Collection functions provide operations for working with arrays and collections of values.

### 6.1 Array Operations

#### 6.1.1 size

- **Description**: Returns the number of elements in an array
- **Parameters**:
  - `array` (array): The array to measure
- **Return Type**: number
- **Example**:
  ```json
  { "function": "size", "args": [[1, 2, 3, 4, 5]] } // Returns 5
  ```

#### 6.1.2 map

- **Description**: Transforms each element in an array using a specified function
- **Parameters**:
  - `array` (array): The input array
  - `functionName` (string): The name of the function to apply to each element
  - `...additionalArgs` (any, optional): Additional arguments to pass to the function
- **Return Type**: array
- **Example**:
  ```json
  { "function": "map", "args": [[1, 2, 3], "multiplyBy", 2] } // Returns [2, 4, 6] if multiplyBy(x, y) returns x*y
  ```

#### 6.1.3 filter

- **Description**: Returns elements from an array that meet a condition
- **Parameters**:
  - `array` (array): The input array
  - `functionName` (string): The name of the function to apply as a test
  - `...additionalArgs` (any, optional): Additional arguments to pass to the function
- **Return Type**: array
- **Example**:
  ```json
  { "function": "filter", "args": [[1, 2, 3, 4, 5], "greaterThan", 3] } // Returns [4, 5] if greaterThan(x, y) returns x > y
  ```

#### 6.1.4 find

- **Description**: Returns the first element in an array that meets a condition
- **Parameters**:
  - `array` (array): The input array
  - `functionName` (string): The name of the function to apply as a test
  - `...additionalArgs` (any, optional): Additional arguments to pass to the function
- **Return Type**: any
- **Example**:
  ```json
  { "function": "find", "args": [[1, 2, 3, 4, 5], "greaterThan", 3] } // Returns 4 if greaterThan(x, y) returns x > y
  ```

#### 6.1.5 some

- **Description**: Checks if at least one element in an array meets a condition
- **Parameters**:
  - `array` (array): The input array
  - `functionName` (string): The name of the function to apply as a test
  - `...additionalArgs` (any, optional): Additional arguments to pass to the function
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "some", "args": [[1, 2, 3], "greaterThan", 2] } // Returns true if greaterThan(x, y) returns x > y
  ```

#### 6.1.6 every

- **Description**: Checks if all elements in an array meet a condition
- **Parameters**:
  - `array` (array): The input array
  - `functionName` (string): The name of the function to apply as a test
  - `...additionalArgs` (any, optional): Additional arguments to pass to the function
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "every", "args": [[3, 4, 5], "greaterThan", 2] } // Returns true if greaterThan(x, y) returns x > y
  ```

#### 6.1.7 sum

- **Description**: Returns the sum of all elements in a numeric array
- **Parameters**:
  - `array` (array of numbers): The array to sum
- **Return Type**: number
- **Example**:
  ```json
  { "function": "sum", "args": [[1, 2, 3, 4, 5]] } // Returns 15
  ```

#### 6.1.8 average

- **Description**: Returns the average of all elements in a numeric array
- **Parameters**:
  - `array` (array of numbers): The array to average
- **Return Type**: number
- **Example**:
  ```json
  { "function": "average", "args": [[1, 2, 3, 4, 5]] } // Returns 3
  ```

#### 6.1.9 includes

- **Description**: Checks if an array includes a specific element
- **Parameters**:
  - `array` (array): The array to check
  - `element` (any): The element to search for
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "includes", "args": [[1, 2, 3, 4, 5], 3] } // Returns true
  { "function": "includes", "args": [["red", "green", "blue"], "yellow"] } // Returns false
  ```

#### 6.1.10 sort

- **Description**: Returns a sorted copy of an array
- **Parameters**:
  - `array` (array): The array to sort
  - `direction` (string, optional): The sort direction ("asc" or "desc", default: "asc")
- **Return Type**: array
- **Example**:
  ```json
  { "function": "sort", "args": [[3, 1, 4, 1, 5]] } // Returns [1, 1, 3, 4, 5]
  { "function": "sort", "args": [[3, 1, 4, 1, 5], "desc"] } // Returns [5, 4, 3, 1, 1]
  ```

## 7. Conversion Functions

Conversion functions provide capabilities for converting between different data types.

### 7.1 Type Conversions

#### 7.1.1 toString

- **Description**: Converts a value to a string
- **Parameters**:
  - `value` (any): The value to convert
- **Return Type**: string
- **Example**:
  ```json
  { "function": "toString", "args": [123] } // Returns "123"
  { "function": "toString", "args": [true] } // Returns "true"
  ```

#### 7.1.2 toNumber

- **Description**: Converts a value to a number
- **Parameters**:
  - `value` (any): The value to convert
- **Return Type**: number
- **Example**:
  ```json
  { "function": "toNumber", "args": ["123"] } // Returns 123
  { "function": "toNumber", "args": ["123.45"] } // Returns 123.45
  ```

#### 7.1.3 toInteger

- **Description**: Converts a value to an integer
- **Parameters**:
  - `value` (any): The value to convert
- **Return Type**: number
- **Example**:
  ```json
  { "function": "toInteger", "args": ["123.45"] } // Returns 123
  { "function": "toInteger", "args": [123.45] } // Returns 123
  ```

#### 7.1.4 toBoolean

- **Description**: Converts a value to a boolean
- **Parameters**:
  - `value` (any): The value to convert
- **Return Type**: boolean
- **Example**:
  ```json
  { "function": "toBoolean", "args": ["true"] } // Returns true
  { "function": "toBoolean", "args": [1] } // Returns true
  { "function": "toBoolean", "args": [0] } // Returns false
  { "function": "toBoolean", "args": [""] } // Returns false
  ```

#### 7.1.5 parseJson

- **Description**: Parses a JSON string into an object
- **Parameters**:
  - `jsonString` (string): The JSON string to parse
- **Return Type**: object
- **Example**:
  ```json
  { "function": "parseJson", "args": ["{\"name\":\"John\",\"age\":30}"] } // Returns {name: "John", age: 30}
  ```

#### 7.1.6 toDateFromTimestamp

- **Description**: Converts a timestamp (milliseconds since epoch) to a date
- **Parameters**:
  - `timestamp` (number): The timestamp in milliseconds
- **Return Type**: date
- **Example**:
  ```json
  { "function": "toDateFromTimestamp", "args": [1712169000000] } // Returns 2024-04-03T15:30:00Z
  ```

#### 7.1.7 toTimestamp

- **Description**: Converts a date to a timestamp (milliseconds since epoch)
- **Parameters**:
  - `date` (date): The date to convert
- **Return Type**: number
- **Example**:
  ```json
  { "function": "toTimestamp", "args": ["2024-04-03T15:30:00Z"] } // Returns 1712169000000
  ```

## 8. Function Usage in Rules

### 8.1 Using Functions in Conditions

Functions can be used within condition parameters to transform values before comparison:

```json
{
  "type": "COMPARISON",
  "operator": "IN",
  "parameters": {
    "leftOperand": { "function": "dayOfWeek", "args": ["{transactionDate}"] },
    "rightOperand": ["SATURDAY", "SUNDAY"]
  }
}
```

In this example, the `dayOfWeek` function extracts the day of the week from the transaction date, which is then compared to the array of weekend days.

### 8.2 Using Functions in Variable Assignments

Functions can also be used in variable assignments to compute values:

```json
{
  "variableId": "discountAmount",
  "operation": "SET",
  "value": { "function": "calculateDiscount", "args": ["{productPrice}", 15] }
}
```

In this example, the `calculateDiscount` function computes a 15% discount based on the product price, and this value is assigned to the `discountAmount` variable.

### 8.3 Function Composition

Functions can be composed by using one function's output as an input to another function:

```json
{
  "variableId": "formattedTotal",
  "operation": "SET",
  "value": { 
    "function": "formatCurrency", 
    "args": [
      { "function": "round", "args": ["{totalAmount}", 2] },
      "USD"
    ]
  }
}
```

In this example, the `round` function is applied to the total amount, and then the `formatCurrency` function formats the rounded value as USD currency.

## 9. Implementation Status and Roadmap

### 9.1 Currently Implemented Functions

As of version 1.0.0 of this specification, only the following functions are implemented and available for use in the RuleForge system:

1. `dayOfWeek` - Returns the day of the week for a given date
2. `formatDate` - Formats a date according to a specified pattern

These functions are marked with the ✅ indicator throughout this document.

### 9.2 Implementation Roadmap

The functions documented in this specification without the ✅ indicator represent our development roadmap and will be implemented according to the following priorities:

1. **Phase 1 (Target: Q3 2025)**:
   - Remaining date and time component extraction functions (year, month, day, etc.)
   - Basic string and numeric functions

2. **Phase 2 (Target: Q4 2025)**:
   - Additional date calculations
   - Logical functions
   - Simple collection functions

3. **Phase 3 (Target: Q1 2026)**:
   - Advanced collection operations
   - Conversion functions
   - Remaining functions from all categories

The roadmap is subject to change based on user feedback and business requirements.

### 9.3 Future Additions

The following functions are under consideration for future releases:

1. **Geographical Functions**: For distance calculations, region determination, etc.
2. **Advanced Statistical Functions**: For more complex statistical operations
3. **Regular Expression Functions**: For pattern matching and extraction
4. **Custom Function Framework**: To allow for client-specific function definitions
5. **ML/AI Integration Functions**: For integrating with machine learning models

## Related Documents

- [[Function Schema]]
- [[Rule Set Schema]]
- [[Condition Type Schema]]
- [[Variable Assignment Schema]]
- [[RuleForge Management API]]

## Approvals

| Role/Department       | Name | Date | Signature |
| --------------------- | ---- | ---- | --------- |
| Lead Architect        |      |      |           |
| API Team Lead         |      |      |           |
| Documentation Manager |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                 |
|---------|----------------|-----------------|--------------------------------------------------------|
| 1.0.0   | [[2025-04-03]] | [[Wayne Smith]] | Initial version of the RuleForge Standard Functions Specification |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->