---
title: Rule Set Schema
classification: Confidential
created: 2024-08-28
updated: 2025-04-10
authors:
  - "[[<PERSON>]]"
version: 7.0.0
next-review: 2025-06-28
category: "[[Specifications]]"
tags:
  - specification
  - schema
  - json
topics:
  - "[[Property Modification Framework]]"
  - "[[Rules Management]]"
  - "[[RuleForge]]"
  - "[[Webhooks]]"
---

# Rule Set Schema

## Document Specification

### Purpose

This document defines the schema for rule set definitions in the RuleForge Property Modification Framework (PMF). It specifies the format, content, and constraints of the rule set definition data used by the Rules Engine, with a focus on property modification capabilities and integration with external systems.

### Scope

This document covers:

- The top-level structure for rule set definitions
- Definitions of essential rule set components (metadata, variables, rules, conditions)
- Property modification approach using variable assignments
- External system integration using webhooks
- Data types and constraints for each field
- Naming conventions and size limits

This document does not cover:

- The internal processing of rule set definitions by the Rules Engine
- GUI representations of rule sets
- The process of updating or deleting rule sets (handled separately by the API)
- The detailed schema for webhook definitions (covered in [[Webhook Schema]])

### Target Audience

- Developers implementing rule logic in RuleForge
- System architects designing rule structures
- QA engineers validating rule definitions
- Technical writers creating rule-related documentation

## Table of Contents

- [[#1. Top-Level Structure]]
- [[#2. Component Definitions]]
- [[#3. Variable Assignments and Property Modification]]
- [[#4. Webhook Calls and External Integration]]
- [[#5. Constraints and Validation]]
- [[#6. Standard Properties in Rules]]
- [[#7. Examples]]

## 1. Top-Level Structure

A rule set definition is represented by a JSON object with the following top-level structure:

```json
{
  "schemaVersion": "string",
  "ruleSetId": "string",
  "name": "string",
  "description": "string",
  "entityId": "string",
  "contextId": "string",
  "version": "integer",
  "status": "string",
  "category": "string",
  "startDateTime": "string (ISO 8601 format)",
  "endDateTime": "string (ISO 8601 format)",
  "lastModifiedDateTime": "string (ISO 8601 format)",
  "collectionMappings": [],
  "persistentVariables": [],
  "localVariables": [],
  "evaluationRules": [],
  "outcomeRules": []
} 
```

> **Note:** Each rule set is associated with a single entity and transaction context. The `entityId` and `contextId` fields specify which entity and context the rule set applies to.

## 2. Component Definitions

### Schema Version

- `schemaVersion`: Version of the schema structure (string, semantic versioning format)
  - Example: "7.0.0"
  - This version represents changes to the structure itself, not the rule set content
  - MAJOR version for backwards-incompatible changes
  - MINOR version for backwards-compatible additions
  - PATCH version for backwards-compatible fixes

### Rule Set Metadata

- `ruleSetId`: Unique identifier for the rule set (string, max 50 characters, UPPER_SNAKE_CASE)
- `name`: Human-readable name for the rule set (string, max 100 characters)
- `description`: Description of the rule set's purpose (string, max 500 characters)
- `entityId`: Unique identifier for the entity this rule set applies to (string, server-generated UUID, e.g., `550e8400-e29b-41d4-a716-************`)
- `contextId`: Identifier of the transaction context this rule set applies to (string, max 50 characters, UPPER_SNAKE_CASE)
- `version`: Version of the rule set content (integer)
- `status`: Current status of the rule set (string, enum: "DRAFT", "ACTIVE", "PAUSED", "ARCHIVED")
- `category`: Business domain or purpose category (string, max 50 characters)
  - Examples: "PRICING", "COMPLIANCE", "PERSONALIZATION", "ELIGIBILITY", "OPERATIONS"
- `startDateTime`: Start date and time of the rule set (string, ISO 8601 format)
- `endDateTime`: End date and time of the rule set (string, ISO 8601 format)
- `lastModifiedDateTime`: Date and time of the last modification to the rule set (string, ISO 8601 format)

### Collection Mappings

The `collectionMappings` array defines how collections relate to specific properties across different entities and transaction contexts. Collections can be either inline (defined within the rule set) or imported from shared collections:

```json
{
  "collectionMappings": [
    {
      "collectionId": "string",
      "name": "string",
      "imported": "boolean",
      "keyMapping": {
        "propertyId": "string"
      }
    }
  ]
}
```

Fields:

- `collectionId`: Identifier of the collection (string, max 50 characters, camelCase)
- `name`: Human-readable name for the collection (string, max 100 characters)
- `imported`: Boolean flag indicating whether this collection is imported from shared collections (boolean, default: false)
  - `true`: Collection references a shared collection defined via the [[Shared Collections API]]
  - `false`: Collection is inline with persistent variables defined within this rule set
- `keyMapping`: Object defining which property to use as the key for the collection
  - `propertyId`: Identifier of the property to use as the key (string, max 50 characters, camelCase)

#### Collection Types

**Inline Collections:**
- Persistent variables are defined within the rule set's `persistentVariables` array
- Collection exists only within this rule set
- Provides complete control over variable definitions

**Imported Shared Collections:**
- References an existing shared collection created via the [[Shared Collections API]]
- Persistent variables are defined in the shared collection, not in the rule set
- Enables reuse of collections across multiple rule sets
- Changes to the shared collection affect all importing rule sets

> **Note:** When `imported: true`, the `collectionId` must match an existing shared collection ID. The persistent variables for imported collections are not defined in the rule set's `persistentVariables` array but are inherited from the shared collection.

### Persistent Variables

The `persistentVariables` array defines variables that maintain state across multiple transactions. This array is only used for **inline collections** (where `imported: false` in collection mappings):

```json
{
  "persistentVariables": [
    {
      "variableId": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "defaultValue": "any",
      "collectionId": "string"
    }
  ]
}
```

Fields:

- `variableId`: Unique identifier for the variable (string, max 50 characters, camelCase)
- `name`: Human-readable name for the variable (string, max 100 characters)
- `description`: Description of the variable's purpose (string, max 250 characters)
- `type`: Data type of the variable (string, enum: "number", "string", "boolean", "date", "enum")
- `defaultValue`: Initial value for the variable (type must match the specified `type`)
- `collectionId`: Identifier of the collection this variable is associated with (string, must match a collectionId in collectionMappings where `imported: true`)

> **Important:** For imported shared collections (`imported: true`), persistent variables are defined in the shared collection itself, not in this array. The rule set inherits the persistent variables from the shared collection automatically.

### Local Variables

The `localVariables` array defines variables that are local to a single rule execution:

```json
{
  "localVariables": [
    {
      "variableId": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "defaultValue": "any"
    }
  ]
}
```

Fields:

- `variableId`: Unique identifier for the variable (string, max 50 characters, camelCase)
- `name`: Human-readable name for the variable (string, max 100 characters)
- `description`: Description of the variable's purpose (string, max 250 characters)
- `type`: Data type of the variable (string, enum: "number", "string", "boolean", "date", "enum")
- `defaultValue`: Initial value for the variable (type must match the specified `type`)

### Rule Definitions

The rule set contains two separate rule collections:

1. **Evaluation Rules** (`evaluationRules`): Rules that execute during transaction evaluation to modify properties
2. **Outcome Rules** (`outcomeRules`): Rules that execute when processing transaction outcomes

Each collection contains rule objects with the following structure:

```json
{
  "ruleId": "string",
  "name": "string",
  "description": "string",
  "priority": "integer",
  "condition": {},
  "variableAssignments": [],
  "webhookCalls": []
}
```

Fields:

- `ruleId`: Unique identifier for the rule (string, max 50 characters, UPPER_SNAKE_CASE)
- `name`: Human-readable name for the rule (string, max 100 characters)
- `description`: Description of the rule's purpose (string, max 500 characters)
- `priority`: Integer indicating the priority of rule execution within its collection (lower numbers indicate higher priority)
- `condition`: Condition object defining when the rule should apply (see Condition Structure)
- `variableAssignments`: Array of assignments on variables and properties (see Variable Assignment Structure)
- `webhookCalls`: Array of webhook calls to trigger when the rule condition is true (see Webhook Call Structure)

> **Note:** Priorities are independent for each rule collection. Priority 1 in `evaluationRules` and priority 1 in `outcomeRules` represent separate execution sequences.

#### Condition Structure

Each condition is represented by an object with the following structure:

```json
{
  "conditionTypeId": "string",
  "operator": "string",
  "parameters": {}
}
```

- `conditionTypeId`: Type of condition (e.g., "COMPARISON", "LOGICAL")
- `operator`: The specific operation to perform (e.g., "==", ">", "AND", "OR")
- `parameters`: Object containing key-value pairs for the condition parameters
  - `leftOperand`: Literal, variable reference (e.g., `"{customerType}"`), or function call (e.g., `{ "functionId": "dayOfWeek", "args": ["{transactionDate}"] }`)
  - `rightOperand`: Literal, variable reference, or function call

Example of a comparison condition:

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": "==",
  "parameters": {
    "leftOperand": "{customerType}",
    "rightOperand": "GOLD"
  }
}
```

Example of a logical condition:

```json
{
  "conditionTypeId": "LOGICAL",
  "operator": "AND",
  "parameters": {
    "conditions": [
      {
        "conditionTypeId": "COMPARISON",
        "operator": ">",
        "parameters": {
          "leftOperand": "{productPrice}",
          "rightOperand": 50
        }
      },
      {
        "conditionTypeId": "COMPARISON",
        "operator": "==",
        "parameters": {
          "leftOperand": "{customerType}",
          "rightOperand": "GOLD"
        }
      }
    ]
  }
}
```

## 3. Variable Assignments and Property Modification

### Variable Assignment Structure

The `variableAssignments` array contains assignments that modify variables or transaction properties:

```json
{
  "variableAssignments": [
    {
      "variableId": "string",
      "assignmentTypeId": "string",
      "value": "any"
    }
  ]
}
```

Fields:

- `variableId`: Identifier of the variable or property to assign (string)
  - For local or persistent variables, use the variableId (e.g., "totalDiscount")
  - For transaction properties, use the propertyId (e.g., "productPrice")
  - Must reference a transaction property from the associated transaction context or a defined variable (`persistentVariables` or `localVariables`)
- `assignmentTypeId`: Type of assignment (string, enum: "SET", "ADD", "SUBTRACT", "MULTIPLY", "DIVIDE", "INCREASE_BY_PERCENTAGE", "DECREASE_BY_PERCENTAGE")
- `value`: Literal, variable reference (e.g., `"{productPrice}"`), or function call (e.g., `{ "functionId": "dayOfWeek", "args": ["{transactionDate}"] }`)
  - Type must match the operation's `applicableTypes` from `/system/variableAssignments`

> **Note:** Functions from `/system/functions` (e.g., `dayOfWeek`, `formatDate`) can be used in conditions and assignments to compute values, as defined in [[RuleForge Management API]].

### Property Modification

To modify transaction properties, include the property's identifier as the `variableId` in a variable assignment:

```json
{
  "variableId": "productPrice",
  "assignmentTypeId": "DECREASE_BY_PERCENTAGE",
  "value": 10
}
```

This will apply a 10% discount to the `productPrice` property.

### Available Assignments

The available assignments depend on the data type of the variable or property:

| Assignment | Description | Applicable Types | Example |
|------------|-------------|------------------|---------|
| SET | Sets the variable to the specified value | All | `{"variableId": "discountPercentage", "assignmentTypeId": "SET", "value": 10}` |
| ADD | Adds the value to the variable | number | `{"variableId": "totalPoints", "assignmentTypeId": "ADD", "value": 5}` |
| SUBTRACT | Subtracts the value from the variable | number | `{"variableId": "remainingAttempts", "assignmentTypeId": "SUBTRACT", "value": 1}` |
| MULTIPLY | Multiplies the variable by the value | number | `{"variableId": "bonusMultiplier", "assignmentTypeId": "MULTIPLY", "value": 2}` |
| DIVIDE | Divides the variable by the value | number | `{"variableId": "productPrice", "assignmentTypeId": "DIVIDE", "value": 2}` |
| INCREASE_BY_PERCENTAGE | Increases the variable by the percentage value | number | `{"variableId": "productPrice", "assignmentTypeId": "INCREASE_BY_PERCENTAGE", "value": 5}` |
| DECREASE_BY_PERCENTAGE | Decreases the variable by the percentage value | number | `{"variableId": "productPrice", "assignmentTypeId": "DECREASE_BY_PERCENTAGE", "value": 10}` |

### Variable References

Variable and property values can be referenced using curly braces {}. These can include:

1. **Entity Properties**: Properties defined in the entity's transaction context
   - Example: `"{productPrice}"`, `"{customerType}"`

2. **Local Variables**: Variables defined in the `localVariables` section of the rule set
   - Example: `"{calculatedDiscount}"`, `"{isWeekday}"`

3. **Persistent Variables**: Variables defined in the `persistentVariables` section of the rule set
   - Example: `"{dailyBundleCount}"`, `"{lastTransactionDay}"`

4. **Standard Properties**: Built-in properties available in all transactions
   - Example: `"{timestamp}"`, `"{transactionId}"`, `"{status}"`

Only simple identifiers are allowed inside curly braces {}.

- Allowed examples: `"{amount}"`, `"{customerType}"`, `"{totalPurchases}"`, `"{timestamp}"`
- Disallowed examples: `"{NOW - 30 DAYS}"`, `"{amount * 0.1}"`, `"{Math.max(score, 100)}"`, `"{modificationStatus.applied}"`

For a complete list of standard properties and their availability in different phases, see the [[Standard Transaction Properties Schema]].

## 4. Webhook Calls and External Integration

### Webhook Call Structure

The `webhookCalls` array contains references to registered webhooks that should be triggered when the rule condition is true:

```json
{
  "webhookCalls": [
    {
      "webhookId": "string",
      "parameters": {
        "parameterId1": "value1",
        "parameterId2": "value2"
      }
    }
  ]
}
```

Fields:

- `webhookId`: Identifier of the webhook to call (string, must match a registered webhook)
- `parameters`: Key-value pairs representing the parameters to pass to the webhook (object)
  - Keys must match parameter IDs defined in the webhook
  - Values can be literals or property references in the format `{propertyId}`

> **Note:** Webhook calls are executed asynchronously and do not affect the main transaction processing flow. For more details on webhooks, see the [[Webhook Schema]].

### Webhook Availability

Webhook calls can be included in both evaluation rules and outcome rules, with different execution timing:

1. **Evaluation Phase Webhooks**: Executed after all property modifications have been determined but before the response is sent back to the entity
2. **Outcome Phase Webhooks**: Executed after processing the transaction outcome

### Parameter Passing

Parameters passed to webhooks can include:

1. **Transaction Properties**: Values from the transaction context
   - Example: `"{productPrice}"`, `"{customerType}"`

2. **Variable Values**: Values from local or persistent variables
   - Example: `"{calculatedDiscount}"`, `"{dailyBundleCount}"`

3. **Standard Properties**: Values from standard properties
   - Example: `"{timestamp}"`, `"{transactionId}"`, `"{status}"`

4. **Literal Values**: Fixed values defined in the rule
   - Example: `"Discount Applied"`, `10`, `true`

Example of a webhook call with mixed parameter types:

```json
{
  "webhookId": "smsNotification",
  "parameters": {
    "recipient": "{agentMsisdn}",
    "message": "Congratulations! You've reached your daily target of {targetValue} bundles.",
    "sender": "RuleForge",
    "priority": "HIGH"
  }
}
```

## 5. Constraints and Validation

### Size Limits

- Maximum rule set JSON size: 1MB
- Maximum number of evaluation rules per rule set: 30
- Maximum number of outcome rules per rule set: 30
- Maximum number of conditions per rule: 10
- Maximum number of variable assignments per rule: 10
- Maximum number of webhook calls per rule: 5

### Naming Conventions

- Use camelCase for all property identifiers and variable identifiers
- Use UPPER_SNAKE_CASE for condition types, rule identifiers, context identifiers, and rule set identifiers
- Use UPPER_SNAKE_CASE for enumeration values
- All identifiers must:
  - Start with a letter
  - Contain only alphanumeric characters and underscores
  - Be case-sensitive

### Name Uniqueness

To prevent ambiguity in variable references, the following naming constraints apply:

1. Local variable identifiers must be unique within a rule set
2. Persistent variable identifiers must be unique within a rule set
3. Local variables, persistent variables, and transaction properties must have unique identifiers
   - A local variable cannot have the same identifier as a transaction property
   - A persistent variable cannot have the same identifier as a transaction property
   - A local variable cannot have the same identifier as a persistent variable

### Collection Mapping Validation

Additional validation rules apply to collection mappings:

1. **Collection ID Uniqueness**: Collection IDs must be unique within a rule set's `collectionMappings` array
2. **Imported Collection Validation**: 
   - When `imported: true`, the `collectionId` must reference an existing shared collection
3. **Inline Collection Validation**:
   - When `imported: false`, all persistent variables with the matching `collectionId` must be defined in the `persistentVariables` array
4. **Mixed Collection Types**: A rule set can contain both inline and imported collections
5. **Variable Reference Validation**: All variable references in rules must resolve to either:
   - A local variable defined in `localVariables`
   - A persistent variable from an inline collection defined in `persistentVariables`
   - A persistent variable from an imported shared collection
   - A standard transaction property

### Rule Uniqueness

Rule identifiers must be unique across both `evaluationRules` and `outcomeRules` collections within a rule set.

### Date Formats

All dates must be in ISO 8601 format: YYYY-MM-DDTHH:mm:ss.sssZ

## 6. Standard Properties in Rules

In addition to entity-defined properties and rule set variables, rules can reference standard properties that are automatically available in all transactions.

### Standard Property Availability

Standard properties are available based on the transaction phase:

1. **Evaluation Rules** (`evaluationRules` collection) can access:
   - Common properties: `transactionId`, `entityId`, `contextId`, `timestamp`

2. **Outcome Rules** (`outcomeRules` collection) can access:
   - All common properties
   - Outcome-specific properties: `status`, `modificationsApplied`, `modificationsRejected`

Attempting to reference a property that's not available in the current phase will result in a rule evaluation error.

### Example: Using Standard Properties in Rules

```json
// In an evaluation rule
{
  "conditionTypeId": "COMPARISON",
  "operator": "==",
  "parameters": {
    "leftOperand": { "functionId": "dayOfWeek", "args": ["{timestamp}"] },
    "rightOperand": "FRIDAY"
  }
}

// In an outcome rule
{
  "conditionTypeId": "COMPARISON",
  "operator": "==",
  "parameters": {
    "leftOperand": "{status}",
    "rightOperand": "COMPLETED"
  }
}
```

For more information on standard properties, refer to the [[Standard Transaction Properties Schema]].

## 7. Examples

### Example of Shared Collection Object

```json

{
  "schemaVersion": "1.0.0",
  "collectionId": "agents",
  "name": "Agents",
  "description": "Collection for agent-related persistent variables",
  "version": 1,
  "status": "LINKED",
  "createdDateTime": "2025-09-17T10:00:00Z",
  "lastModifiedDateTime": "2025-09-17T12:30:00Z",
  "persistentVariables": [
    {
      "variableId": "dailyBundleCount",
      "name": "Daily Bundle Count",
      "description": "The total number of bundles sold by an agent in a day",
      "type": "number",
      "defaultValue": 0
    },
    {
      "variableId": "totalSalesAmount",
      "name": "Total Sales Amount",
      "description": "Cumulative sales amount for the agent",
      "type": "number",
      "defaultValue": 0
    },
    {
      "variableId": "targetReached",
      "name": "Target Reached",
      "description": "Indicates if the agent has reached their daily sales target",
      "type": "boolean",
      "defaultValue": false
    }
  ]
}

```

### Complete Rule Set Example with Webhooks and Shared Collection

This example demonstrates a rule set that uses both imported shared collections and inline collections:

- **agents** collection: Imported from shared collections (contains `dailyBundleCount`, `totalSalesAmount`, etc.)
- **localMetrics** collection: Inline collection with variables defined in this rule set

```json
{
  "schemaVersion": "7.0.0",
  "ruleSetId": "AGENT_INCENTIVE_2025",
  "name": "Agent Bundle Sales Incentive 2025",
  "description": "Incentive program for agent bundle sales with notification capabilities",
  "entityId": "550e8400-e29b-41d4-a716-************",
  "contextId": "BUNDLE_SALE",
  "version": 1,
  "status": "ACTIVE",
  "category": "INCENTIVE",
  "startDateTime": "2025-04-25T00:00:00Z",
  "endDateTime": "2025-12-31T23:59:59Z",
  "lastModifiedDateTime": "2025-04-10T14:30:00Z",
  "collectionMappings": [
    {
      "collectionId": "agents",
      "name": "Agents", 
      "imported": true,
      "keyMapping": {
        "propertyId": "agentId"
      }
    },
    {
      "collectionId": "localMetrics",
      "name": "Local Metrics",
      "imported": false,
      "keyMapping": {
        "propertyId": "agentId"
      }
    }
  ],
  "persistentVariables": [
    {
      "variableId": "sessionBundleCount",
      "name": "Session Bundle Count",
      "description": "Number of bundles sold in the current session",
      "type": "number",
      "defaultValue": 0,
      "collectionId": "localMetrics"
    },
    {
      "variableId": "notificationSent",
      "name": "Notification Sent",
      "description": "Flag indicating if the achievement notification has been sent",
      "type": "boolean",
      "defaultValue": false,
      "collectionId": "localMetrics"
    }
  ],
  "localVariables": [
    {
      "variableId": "bundleDiscount",
      "name": "Bundle Discount",
      "description": "Calculated discount for the current bundle",
      "type": "number",
      "defaultValue": 0
    }
  ],
  "evaluationRules": [
    {
      "ruleId": "APPLY_BUNDLE_DISCOUNT",
      "name": "Apply Bundle Discount for Target Achievers",
      "description": "Apply a 2% discount on bundle sales for agents who have reached their daily target",
      "priority": 1,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{agents.targetReached}",
                "rightOperand": true
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{bundleType}",
                "rightOperand": "PREMIUM"
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "bundleDiscount",
          "assignmentTypeId": "SET",
          "value": 2
        },
        {
          "variableId": "bundlePrice",
          "assignmentTypeId": "DECREASE_BY_PERCENTAGE",
          "value": 2
        }
      ]
    }
  ],
  "outcomeRules": [
    {
      "ruleId": "INCREMENT_BUNDLE_COUNT",
      "name": "Increment Daily Bundle Count",
      "description": "Increment the agent's daily bundle count when a sale is completed",
      "priority": 1,
      "condition": {
        "conditionTypeId": "COMPARISON",
        "operator": "==",
        "parameters": {
          "leftOperand": "{status}",
          "rightOperand": "COMPLETED"
        }
      },
      "variableAssignments": [
        {
          "variableId": "agents.dailyBundleCount",
          "assignmentTypeId": "ADD",
          "value": 1
        }
      ]
    },
    {
      "ruleId": "CHECK_TARGET_ACHIEVEMENT",
      "name": "Check Daily Target Achievement",
      "description": "Check if the agent has reached their daily target of 30 bundles",
      "priority": 2,
      "condition": {
        "conditionTypeId": "LOGICAL",
        "operator": "AND",
        "parameters": {
          "conditions": [
            {
              "conditionTypeId": "COMPARISON",
              "operator": ">=",
              "parameters": {
                "leftOperand": "{agents.dailyBundleCount}",
                "rightOperand": 30
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{agents.targetReached}",
                "rightOperand": false
              }
            },
            {
              "conditionTypeId": "COMPARISON",
              "operator": "==",
              "parameters": {
                "leftOperand": "{notificationSent}",
                "rightOperand": false
              }
            }
          ]
        }
      },
      "variableAssignments": [
        {
          "variableId": "agents.targetReached",
          "assignmentTypeId": "SET",
          "value": true
        },
        {
          "variableId": "notificationSent",
          "assignmentTypeId": "SET",
          "value": true
        }
      ],
      "webhookCalls": [
        {
          "webhookId": "smsNotification",
          "parameters": {
            "recipient": "{agentMsisdn}",
            "message": "Congratulations! You've reached your daily target of 30 bundles. You now qualify for 2% additional discount on all bundle sales for the rest of the day."
          }
        }
      ]
    }
  ]
}
```

## Execution Model Explanation

The rule set schema now organizes rules into two distinct collections that align with the transaction processing workflow and supports external integration via webhooks:

1. **Evaluation Rules**: Execute during the initial transaction evaluation phase when the entity calls the `/evaluate` endpoint. These rules focus on:
   - Applying discounts
   - Adjusting prices
   - Setting promotional offers
   - Determining eligibility for special treatment
   - Triggering real-time notifications (via webhooks)

2. **Outcome Rules**: Execute when the transaction outcome is reported via the `/outcomes` endpoint. These rules focus on:
   - Loyalty point calculations
   - Post-purchase follow-ups
   - Customer status updates
   - Analytics data enrichment
   - Sending result-based notifications (via webhooks)

This separation provides clear boundaries between modification logic (applied before transaction completion) and outcome processing logic (applied after knowing the transaction result). The addition of webhook calls enables integration with external systems like SMS gateways, email services, and other APIs.

## Related Documents

- [[Entity Integration Schema]] 
- [[Transaction Request Schema]]
- [[Transaction Response Schema]]
- [[Transaction Outcome Schema]]
- [[RuleForge Management API]]
- [[Condition Type Schema]]
- [[Standard Transaction Properties Schema]]
- [[Webhook Schema]]
- [[Webhook Management API]]

## Approvals

| Role/Department       | Name | Date | Signature |
| --------------------- | ---- | ---- | --------- |
| Lead Architect        |      |      |           |
| API Team Lead         |      |      |           |
| Documentation Manager |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                                                                                                                                                                                                                                                                                                          |
| ------- | -------------- | --------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 7.1.0   | [[2025-09-17]] | [[Wahab Khurram]] | Added documentation and examples for shared collection integration: clarified the use of the `imported` property in `collectionMappings`, explained how rule sets reference shared collections via the [[Shared Collections API]], and detailed the inheritance of persistent variables from shared collections. Updated schema notes and examples to illustrate shared vs. inline collections usage. |
| 7.0.0   | [[2025-04-10]] | [[Wayne Smith]] | Major revision to add webhook integration: added `webhookCalls` array to both evaluation and outcome rules; added new section "Webhook Calls and External Integration"; updated examples to showcase webhook usage; added constraints for webhook calls; added Webhook Schema to related documents; added Webhooks topic tag |
| 6.1.0   | [[2025-04-09]] | [[Wayne Smith]] | Added documentation for standard properties in rules; clarified which standard properties are available in evaluation vs. outcome rules; expanded variable references section to include standard properties; added examples showing standard property usage |
| 6.0.0   | [[2025-04-07]] | [[Wayne Smith]] | Major revision to separate rule execution phases: split the rules array into `evaluationRules` and `outcomeRules` collections; added rule phase explanation; updated examples to demonstrate rule types; added constraints for rule identifiers across collections; adjusted size limits to account for the separation                      |
| 5.1.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated naming convention to align with RuleForge Naming Convention Guide: renamed array fields from `persistentVariableDefinitions`/`localVariableDefinitions` to `persistentVariables`/`localVariables`; changed `operation` to `assignmentTypeId` in variable assignments; replaced `function` with `functionId` in function references; updated all examples to reflect new conventions |
| 5.0.0   | [[2025-04-03]] | [[Wayne Smith]] | Restructured schema to limit rule sets to a single entity-context: added top-level entityId and contextId fields; simplified collection mappings; moved rules array to top level; added name uniqueness constraints; updated examples to reflect new structure; added section on single entity-context limitation |
| 4.0.0   | [[2025-04-02]] | [[Wayne Smith]] | Major revision to reposition as a domain-agnostic rules engine: renamed from "Campaign Definition JSON Structure" to "Rule Set Definition Schema"; replaced campaign-specific terminology with domain-agnostic terms; added "category" field for business domain classification; updated examples to demonstrate broader use cases; renamed campaignId to ruleSetId; renamed campaignVersion to version |
| 3.2.0   | [[2025-04-01]] | [[Wayne Smith]] | Added support for function calls in condition.parameters and variableAssignments.value, updated examples to demonstrate function usage, aligned with Campaign Management API Spec v1.1.0 |
| 3.1.0   | [[2025-04-01]] | [[Wayne Smith]] | Aligned with Campaign Management API Spec v1.1.0: renamed variableOperations to variableAssignments, updated entityId to UUID format, clarified variableId scoping for properties and variables, updated related docs to reference Campaign Management API Spec v1.1.0 |
| 3.0.0   | [[2025-03-28]] | [[Wayne Smith]] | Removed all action-related components, added comprehensive variable operations section for property modification, updated examples to demonstrate property modification approach, added detailed breakdown of available operations by data type, expanded condition examples, updated to align with Property Modification Framework |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->
