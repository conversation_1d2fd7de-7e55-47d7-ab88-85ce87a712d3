---
title: "Transaction Request Schema"
classification: Confidential
created: 2024-08-28
updated: 2025-04-09
authors:
  - "[[<PERSON>]]"
version: 2.5.0
next-review: 2025-06-28
category: "[[Specifications]]"
tags:
  - specification
  - schema
  - json
topics:
  - "[[Property Modification Framework]]"
  - "[[Transaction Evaluation]]"
  - "[[API]]"
  - "[[RuleForge]]"
---

# Transaction Request Schema

## Document Specification

### Purpose
This document defines the JSON structure for transaction evaluation requests in the RuleForge Rules Engine. It specifies the format, content, and constraints of the transaction data sent to the Rules Engine for evaluation and potential property modification.

### Scope
This document covers:
- The JSON structure for transaction evaluation requests
- Definitions of essential components (entity identification, transaction data)
- Data types and constraints for each field
- Examples of valid requests

This document does not cover:
- The internal processing of transaction evaluation
- The structure of the evaluation response
- Detailed business logic of how properties are modified

### Target Audience
- Developers integrating client systems with RuleForge
- QA engineers validating transaction evaluation requests
- Technical writers creating transaction-related documentation

## Table of Contents

- [[#1. JSON Structure]]
- [[#2. Component Definitions]]
- [[#2A. Standard Properties]]
- [[#3. Constraints and Validation]]
- [[#4. Examples]]

## 1. JSON Structure

A transaction evaluation request is represented by a JSON object with the following structure:

```json
{
  "entityId": "string",
  "contextId": "string",
  "transactionId": "string",
  "timestamp": "string",
  "transactionData": {
    "propertyId1": "value1",
    "propertyId2": "value2",
    ...
  }
}
```

## 2. Component Definitions

### Entity Identifier
- `entityId`: The unique identifier returned in the entity registration response (string, UUID format, e.g., `550e8400-e29b-41d4-a716-************`). See [[Entity Integration API#3.1 Register Entity]] for details.

### Transaction Context
- `contextId`: Identifier of the transaction context as defined in the entity registration (string, max 50 characters, UPPER_SNAKE_CASE)
  - Must match a contextId defined for the specified entity

### Transaction Identifier
- `transactionId`: Unique identifier for the specific transaction (string, max 50 characters)
  - Should be unique for each transaction to enable proper tracking and reporting

### Timestamp
- `timestamp`: Date and time when the transaction occurred (string, ISO 8601 format: YYYY-MM-DDTHH:mm:ss.sssZ)
  - Example: "2025-03-15T14:30:00Z"

### Transaction Data
- `transactionData`: An object containing key-value pairs representing the transaction properties
  - Keys must match the propertyId values defined in the entity's transaction context
  - Values must match the defined data types for each property
  - Must include all properties defined in the transaction context, as all are required for evaluation
  - Should include both mutable and immutable properties for proper evaluation

## 2A. Standard Properties

In addition to the explicitly defined properties in the transaction request, several standard properties are automatically available in the evaluation phase. These properties can be referenced in rule conditions and variable assignments using the same curly brace notation as entity-defined properties.

The following standard properties are available during transaction evaluation:

| Property ID | Description | Type | Format |
|-------------|-------------|------|--------|
| `transactionId` | Unique identifier for the transaction (matches the `transactionId` field in the request) | string | max 50 characters |
| `entityId` | Identifier of the entity sending the transaction (matches the `entityId` field in the request) | string | UUID format |
| `contextId` | Identifier of the transaction context (matches the `contextId` field in the request) | string | UPPER_SNAKE_CASE |
| `timestamp` | Date and time when the transaction occurred (matches the `timestamp` field in the request) | date | ISO 8601 format |

These standard properties can be referenced in evaluation rules using the format `{propertyId}`, for example:

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": "==",
  "parameters": {
    "leftOperand": { "functionId": "dayOfWeek", "args": ["{timestamp}"] },
    "rightOperand": "MONDAY"
  }
}
```

For a complete list of standard properties and their usage, see the [[Standard Transaction Properties Schema]].

## 3. Constraints and Validation

### Required Fields
All top-level fields (`entityId`, `contextId`, `transactionId`, `timestamp`, and `transactionData`) are required.

### Property Types

Transaction data properties must conform to the types defined in the entity registration:

| Property Type | JSON Representation | Example |
|---------------|---------------------|---------|
| string        | String              | `"Premium Customer"` |
| number        | Number              | `42.99` |
| boolean       | Boolean             | `true` |
| date          | String (ISO 8601)   | `"2025-03-15T14:30:00Z"` |
| enum          | String              | `"GOLD"` |
| object        | Object              | `{"street": "123 Main St", "city": "Springfield"}` |
| array         | Array               | `["item1", "item2"]` |

### Property Value Validation

- String properties must not be below the minimum length, exceed the maximum length, or fail to match the pattern defined in the entity registration
- Number properties must not be below the minimum or exceed the maximum values defined in the entity registration
- Enum properties must contain one of the allowed values defined in the entity registration
- Date properties must be in valid ISO 8601 format
- Object properties must include all required nested properties
- Array properties must contain elements of the specified type

## 4. Examples

### Basic Transaction Evaluation Request

```json
{
  "entityId": "550e8400-e29b-41d4-a716-************",
  "contextId": "PURCHASE",
  "transactionId": "TXN123456789",
  "timestamp": "2025-03-15T14:30:00Z",
  "transactionData": {
    "customerId": "CUST123456",
    "productId": "PROD789",
    "amount": 99.99,
    "loyaltyPoints": 500,
    "customerType": "REGULAR"
  }
}
```

### E-commerce Transaction with Property Modification Candidates

```json
{
  "entityId": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
  "contextId": "PURCHASE",
  "transactionId": "TXN987654321",
  "timestamp": "2025-03-20T09:45:00Z",
  "transactionData": {
    "customerId": "CUST789012",
    "productId": "PROD456",
    "productPrice": 149.99,
    "quantity": 1,
    "subtotal": 149.99,
    "discountPercentage": 0,
    "shippingCost": 8.99,
    "totalAmount": 158.98,
    "customerType": "VIP",
    "purchaseCount": 15,
    "paymentMethod": "CREDIT_CARD",
    "shippingAddress": {
      "street": "456 Oak Avenue",
      "city": "Metropolis",
      "postalCode": "54321",
      "country": "US"
    },
    "deviceType": "DESKTOP"
  }
}
```

In this example:
- The entity is an e-commerce platform
- The transaction context is a product purchase
- The request includes both mutable properties (like `productPrice`, `discountPercentage`, and `shippingCost`) that might be modified by rule sets and immutable properties (like `customerId`, `customerType`, and `deviceType`) that are used for targeting but won't be modified

## Usage Guidelines

### Including Properties for Evaluation

When building a transaction evaluation request:

1. Include all properties defined in the transaction context as per the entity registration
2. Provide accurate values for all properties to ensure proper rule set evaluation
3. Generate a unique `transactionId` for each transaction to enable tracking
4. Use the actual transaction timestamp rather than the request time

## Related Documents

- [[Entity Integration Schema]]
- [[Transaction Response Schema]]
- [[RuleForge Interface Overview]]
- [[Property Modification Guide]]
- [[Standard Transaction Properties Schema]]

## Approvals

| Role/Department    | Name | Date | Signature |
|--------------------|------|------|-----------|
| Lead Architect     |      |      |           |
| API Team Lead      |      |      |           |
| QA Team Lead       |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                |
|---------|----------------|-----------------|--------------------------------------------------------|
| 2.5.0   | [[2025-04-09]] | [[Wayne Smith]] | Added section on standard properties available during transaction evaluation |
| 2.4.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated field naming to align with RuleForge Naming Convention Guide: clarified that transactionData keys are propertyIds; updated JSON structure example to use propertyId notation; ensured consistent terminology for property identifiers throughout |
| 2.3.0   | [[2025-04-02]] | [[Wayne Smith]] | Revised 'Entity Identifier' phrasing; clarified 'Transaction Data' to require all properties; updated 'Property Value Validation' with full constraints; removed 'Transaction Data Optimization' (relocated to Entity Integration Schema); confirmed `entityId` UUID format in examples |
| 2.2.0   | [[2025-04-02]] | [[Wayne Smith]] | Standardized field naming: changed 'transactionContextId' to 'contextId' for consistency across the RuleForge documentation |
| 2.1.0   | [[2025-04-01]] | [[Wayne Smith]] | Updated terminology from "campaign" to "rule set" throughout document; renamed "RuleForge Campaign Rules Engine (CRE)" to "RuleForge Rules Engine"; updated document references; standardized entityId format to UUID throughout |
| 2.0.0   | [[2025-03-28]] | [[Wayne Smith]] | - Updated documentation to emphasize the Property Modification Framework approach<br>- Added guidance on including both mutable and immutable properties<br>- Enhanced property type descriptions<br>- Added more comprehensive examples showcasing mutable properties<br>- Added usage guidelines focused on property modification |
| 1.2.0   | [[2024-09-10]] | [[Wayne Smith]] | Added `transactionId` field to the top-level structure. Updated example and constraints.                                                                                                                                                      |
| 1.1.0   | [[2024-09-03]] | [[RuleForge DevGenius]] | Updated to include timestamp in the top-level structure.                                                                                                                                                                                    |
| 1.0.0   | [[2024-08-28]] | [[RuleForge DevGenius]] | Initial version of the Transaction Evaluation Request JSON Structure document.                                                                                                                                                               |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->