---
title: Function Schema
classification: Confidential
created: 2024-08-27
updated: 2025-04-03
authors:
  - "[[<PERSON>]]"
version: 2.1.0
next-review: 2025-06-28
category: "[[Specifications]]"
tags:
  - specification
  - schema
  - json
topics:
  - "[[Property Modification Framework]]"
  - "[[Rule Logic]]"
  - "[[RuleForge]]"
---

# Function Schema

## Document Specification

### Purpose

This document defines the schema for Functions in the RuleForge Property Modification Framework (PMF). It specifies the format, content, and constraints of function definitions used in rule conditions and variable assignments.

### Scope

This document covers:

- The structure for defining functions 
- Definitions of function components (parameters, return types)
- Data types and constraints for each field
- Usage guidelines for functions in rule evaluation

This document does not cover:

- The internal implementation of function execution
- The process of creating or managing functions in the system
- How to implement custom functions

### Target Audience

- Developers implementing rule creation and evaluation features
- System architects designing function integration
- QA engineers validating function behavior
- Technical writers creating function-related documentation

## Table of Contents

- [[#1. Function Structure]]
- [[#2. Component Definitions]]
- [[#3. Usage Guidelines]]
- [[#4. Examples]]
- [[#5. Relationship with Rule Set Schema]]

## 1. Function Structure

A Function is represented by a JSON object with the following structure:

```json
{
  "functionId": "string",
  "name": "string",
  "description": "string",
  "parameters": [],
  "returnType": "string",
  "returnValues": [],
  "examples": []
}
```

## 2. Component Definitions

### Function Metadata

- `functionId`: Unique identifier for the function (string, max 50 characters, camelCase)
- `name`: Human-readable name for the function (string, max 100 characters)
- `description`: Description of the function's purpose (string, max 250 characters)
- `returnType`: Data type of the function's return value (string, enum: "string", "number", "boolean", "date", "enum")
- `returnValues`: Optional array of possible return values (only for enum return types)

### Parameters

The `parameters` array defines the inputs required for the function:

```json
{
  "parameters": [
    {
      "parameterId": "string",
      "name": "string",
      "type": "string",
      "description": "string"
    }
  ]
}
```

Fields:

- `parameterId`: Identifier of the parameter (string, max 50 characters, camelCase)
- `name`: Human-readable name of the parameter (string, max 100 characters)
- `type`: Data type of the parameter (string, enum: "string", "number", "boolean", "date", "enum")
- `description`: Description of the parameter's purpose (string, max 250 characters)

### Examples

The `examples` array provides usage examples for the function using the same JSON syntax that would be used in rules:

```json
{
  "examples": [
    {
      "reference": {
        "functionId": "functionName",
        "args": [arg1, arg2]
      },
      "result": "any"
    }
  ]
}
```

Fields:

- `reference`: Example function reference using the actual JSON syntax used in rules
- `result`: Expected result of the example function call

## 3. Usage Guidelines

### Functions as Transformations

Functions in RuleForge are pure transformations that compute values from inputs. Unlike conditions (which are predicates that evaluate to true/false) and variable assignments (which update state), functions:

1. Accept inputs and return outputs without side effects
2. Transform data in a deterministic way
3. Can be composed with other functions
4. Can be used in both conditions and variable assignments

### Function Definition vs. Function References

It's important to understand the distinction between:

1. **Function Definition**: How functions are defined and registered in the system (as described in this schema)
2. **Function Reference**: How functions are referenced and used within rules

The Function Schema (this document) describes how functions are defined in the system registry. When actually using functions within rules, they are referenced using a specific syntax:

```json
{
  "functionId": "functionName",
  "args": [arg1, arg2, ...]
}
```

Where:
- `"functionId"` is a fixed key indicating a function reference
- `"functionName"` is the identifier of the function as registered in the system
- `"args"` is an array of arguments that match the parameters defined in the function

### Function Applications

Functions can be used in multiple contexts:

1. **In Condition Parameters**: To transform values before comparison
   ```json
   {
     "conditionTypeId": "COMPARISON",
     "operator": "==",
     "parameters": {
       "leftOperand": { "functionId": "dayOfWeek", "args": ["{transactionDate}"] },
       "rightOperand": "FRIDAY"
     }
   }
   ```

2. **In Variable Assignment Values**: To compute values for assignment
   ```json
   {
     "variableId": "formattedDate",
     "assignmentTypeId": "SET",
     "value": { "functionId": "formatDate", "args": ["{transactionDate}", "yyyy-MM-dd"] }
   }
   ```

3. **Inside Other Functions**: For function composition
   ```json
   {
     "functionId": "formatCurrency",
     "args": [
       { "functionId": "multiply", "args": ["{basePrice}", 0.9] },
       "USD"
     ]
   }
   ```

## 4. Examples

### Date Function

```json
{
  "functionId": "dayOfWeek",
  "name": "Day of Week",
  "description": "Returns the day of the week for a given date",
  "parameters": [
    {
      "parameterId": "date",
      "name": "Date",
      "type": "date",
      "description": "The date to get the day of week for"
    }
  ],
  "returnType": "string",
  "returnValues": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"],
  "examples": [
    {
      "reference": {
        "functionId": "dayOfWeek",
        "args": ["2025-03-28T00:00:00Z"]
      },
      "result": "FRIDAY"
    }
  ]
}
```

### String Function

```json
{
  "functionId": "formatDate",
  "name": "Format Date",
  "description": "Formats a date according to the specified pattern",
  "parameters": [
    {
      "parameterId": "date",
      "name": "Date",
      "type": "date",
      "description": "The date to format"
    },
    {
      "parameterId": "pattern",
      "name": "Pattern",
      "type": "string",
      "description": "The format pattern"
    }
  ],
  "returnType": "string",
  "examples": [
    {
      "reference": {
        "functionId": "formatDate",
        "args": ["2025-03-28T00:00:00Z", "yyyy-MM-dd"]
      },
      "result": "2025-03-28"
    }
  ]
}
```

### Mathematical Function

```json
{
  "functionId": "calculateDiscount",
  "name": "Calculate Discount",
  "description": "Calculates a discount amount based on a price and percentage",
  "parameters": [
    {
      "parameterId": "price",
      "name": "Price",
      "type": "number",
      "description": "The original price"
    },
    {
      "parameterId": "percentage",
      "name": "Percentage",
      "type": "number",
      "description": "The discount percentage"
    }
  ],
  "returnType": "number",
  "examples": [
    {
      "reference": {
        "functionId": "calculateDiscount",
        "args": [100, 10]
      },
      "result": 10
    }
  ]
}
```

## 5. Relationship with Rule Set Schema

Functions provide transformation capabilities that complement condition evaluation and variable assignment in the Rule Set Schema. They are referenced in:

### Function Usage in Conditions

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": "IN",
  "parameters": {
    "leftOperand": { "functionId": "dayOfWeek", "args": ["{transactionDate}"] },
    "rightOperand": ["SATURDAY", "SUNDAY"]
  }
}
```

### Function Usage in Variable Assignments

```json
{
  "variableId": "discountAmount",
  "assignmentTypeId": "SET",
  "value": { "functionId": "calculateDiscount", "args": ["{productPrice}", 15] }
}
```

This architecture allows for a clean separation of concerns:
- **Condition Types**: Define predicates that evaluate to true/false
- **Functions**: Transform values through pure computation
- **Variable Assignments**: Update state based on rule outcomes

## Related Documents

- [[Rule Set Schema]]
- [[Condition Type Schema]]
- [[RuleForge Management API]]
- [[Transaction Request Schema]]
- [[Transaction Response Schema]]

## Approvals

| Role/Department       | Name | Date | Signature |
| --------------------- | ---- | ---- | --------- |
| Lead Architect        |      |      |           |
| API Team Lead         |      |      |           |
| Documentation Manager |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                 |
|---------|----------------|-----------------|--------------------------------------------------------|
| 2.1.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated field naming to align with RuleForge Naming Convention Guide: changed `name` to `functionId` as primary identifier, `name` to `parameterId` in parameters, and added human-readable name fields; updated function reference syntax in examples from `function` to `functionId` |
| 2.0.0   | [[2025-04-02]] | [[Wayne Smith]] | Major revision to align with newer architecture: renamed from "Global Context JSON Structure" to "Function Schema"; shifted focus from global context to pure functions; removed properties section and focused on functions as transformations; added relationship with Rule Set Schema section; added detailed examples showing function usage in conditions and variable assignments; reorganized document using the Root Document Template |
| 1.2.0   | [[2024-08-28]] | [[RuleForge DevGenius]] | Updated document to clarify that the Rules Engine implements and manages the global context internally. Removed references to updating the global context through the API, as this is not possible for clients. |
| 1.0.0   | [[2024-08-27]] | [[RuleForge DevGenius]] | Initial version of the Global Context structure document |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->