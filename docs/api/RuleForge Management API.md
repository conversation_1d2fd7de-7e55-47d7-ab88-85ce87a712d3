---
title: "RuleForge Management API"
classification: Confidential
created: 2025-03-28
updated: 2025-04-10
authors:
  - "[[<PERSON>]]"
version: 2.4.0
next-review: 2025-09-28
category: "[[Specifications]]"
tags:
  - specification
  - api
  - reference
  - internal
topics:
  - "[[Property Modification Framework]]"
  - "[[API]]"
  - "[[RuleForge]]"
  - "[[Rules Management]]"
  - "[[GUI]]"
  - "[[Webhooks]]"
---

# RuleForge Management API

## Document Specification

### Purpose

This document provides the API specification for rules management within the RuleForge Property Modification Framework (PMF). It focuses specifically on rule-related endpoints needed by the GUI team for implementing the Rules Management Interface.

### Scope

This specification covers:

- Authentication mechanisms for GUI access
- Rule set management endpoints
- System configuration and information endpoints
- Webhook management endpoints
- Data structures for rule definitions
- Error handling and status codes related to rules management

This document does not cover:

- Entity registration endpoints (covered in [[Entity Integration API]])
- Transaction evaluation endpoints (covered in [[Transaction Evaluation API]])
- List management endpoints (covered in [[List Management API]])
- The internal implementation of the RuleForge engine
- Frontend implementation details

### Target Audience

- GUI developers implementing the Rules Management Interface
- System architects designing rule management workflows
- QA engineers testing rule management features
- Internal development teams

## 1. Introduction

### 1.1 API Overview

The RuleForge Management API provides endpoints that allow the RuleForge GUI to create, retrieve, update, and delete rule sets within RuleForge. The API also provides access to system configuration data needed for dynamic rule creation and management, as well as webhook management for integration with external systems. For list management capabilities, see the complementary [[List Management API]].

### 1.2 API Versioning

The API uses semantic versioning (MAJOR.MINOR.PATCH) and includes the major version in the URL path:

Base URL: `https://api.ruleforge.net/v1`

When breaking changes are introduced, a new major version will be released, and the previous version will be supported for a minimum of 6 months to allow for migration.

### 1.3 Authentication

The Management API uses JWT-based authentication for all access, including GUI users and service-to-service communication.

#### GUI Authentication

For GUI users, authentication is handled through JWT tokens:

1. Users log in through the GUI login page
2. Upon successful authentication, the user management microservice generates a JWT token
3. The JWT token is stored in the browser (localStorage or session storage)
4. All subsequent requests include this JWT token in the Authorization header as a Bearer token
   ```
   Authorization: Bearer <jwt_token>
   ```
5. Tokens expire after a short period (approximately 10 minutes)
6. The GUI automatically refreshes tokens as long as the application remains open in the browser

The RuleForge API validates tokens by:
- Verifying the signature using the public key
- Checking the token's expiry time
- Validating the permissions encoded in the token

This stateless authentication approach eliminates the need for server-side session storage and allows for better scalability.

#### Service Authentication

For service-to-service communication (e.g., automated CI/CD pipelines), the same JWT token authentication is used:

```
Authorization: Bearer <jwt_token>
```

Tokens are obtained through the authentication endpoints detailed in Section 2.

#### API Key Authentication for Integrations

API keys managed via `/api-keys` endpoints (Section 2.4) are used by third-party integrations. These requests must include:

```
Authorization: Bearer your_api_key
```

Each API key is associated with an organization, not with specific entities. An organization uses a single API key to manage all its entities, with the `entityId` parameter in API calls specifying which entity is being referenced.

This matches the authentication method in [[Entity Integration API]] and [[Transaction Evaluation API]].

## 2. Authentication

### 2.1 User Login

- **Endpoint**: `/auth/login`
- **Method**: POST
- **Description**: Authenticates a user and issues a JWT token
- **Request Body**:
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "token": "string",
      "expiresIn": "integer",
      "userId": "string",
      "displayName": "string",
      "roles": ["ADMIN", "RULES_MANAGER"],
      "permissions": ["CREATE_RULESET", "UPDATE_RULESET"]
    }
    ```

### 2.2 User Logout

- **Endpoint**: `/auth/logout`
- **Method**: POST
- **Description**: Invalidates the current JWT token
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "message": "Token successfully invalidated"
    }
    ```

### 2.3 Service Token

- **Endpoint**: `/auth/token`
- **Method**: POST
- **Description**: Obtains a JWT token for service-to-service authentication
- **Request Body**:
  ```json
  {
    "clientId": "string",
    "clientSecret": "string"
  }
  ```
- **Response**:
  ```json
  {
    "token": "string",
    "expiresIn": "integer"
  }
  ```

### 2.4 Token Refresh

- **Endpoint**: `/auth/refresh`
- **Method**: POST
- **Description**: Refreshes an existing valid JWT token before expiry
- **Request Header**: 
  ```
  Authorization: Bearer <current_jwt_token>
  ```
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "token": "string",
      "expiresIn": "integer"
    }
    ```

### 2.5 API Key Management

The Management API provides endpoints for managing API keys used by organizations integrating with RuleForge. Each key is associated with an organization, not a specific entity, and authorizes actions across all the organization's entities.

#### 2.5.1 List API Keys

- **Endpoint**: `/api-keys`
- **Method**: GET
- **Description**: Lists all API keys with associated organization details and permissions
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "apiKeys": [
        {
          "keyId": "key_id_1",
          "organizationId": "org_id_1",
          "description": "Production API key for Organization A",
          "createdAt": "2025-03-15T10:30:00Z",
          "lastUsed": "2025-04-01T15:45:22Z",
          "permissions": ["READ", "EVALUATE", "UPDATE"],
          "rateLimit": {
            "requestsPerMinute": 500
          }
        },
        {
          "keyId": "key_id_2",
          "organizationId": "org_id_2",
          "description": "Development API key for Organization B",
          "createdAt": "2025-03-20T09:15:00Z",
          "lastUsed": "2025-04-01T12:32:18Z",
          "permissions": ["READ", "EVALUATE"],
          "rateLimit": {
            "requestsPerMinute": 200
          }
        }
      ],
      "totalCount": 2
    }
    ```

#### 2.5.2 Create API Key

- **Endpoint**: `/api-keys`
- **Method**: POST
- **Description**: Creates a new API key for an organization
- **Request Body**:
  ```json
  {
    "organizationId": "string",
    "description": "string",
    "permissions": ["READ", "EVALUATE", "UPDATE"],
    "rateLimit": {
      "requestsPerMinute": 500
    }
  }
  ```
- **Response**:
  - Status: 201 Created
  - Body:
    ```json
    {
      "key": "api_key_value_here",
      "keyId": "unique_key_identifier",
      "organizationId": "org_id_associated_with_key",
      "description": "Key description provided at creation",
      "createdAt": "2025-04-01T14:30:00Z",
      "permissions": ["READ", "EVALUATE", "UPDATE"],
      "rateLimit": {
        "requestsPerMinute": 500
      }
    }
    ```

#### 2.5.3 Revoke API Key

- **Endpoint**: `/api-keys/{keyId}`
- **Method**: DELETE
- **Description**: Revokes an API key
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "keyId": "unique_key_identifier",
      "message": "API key successfully revoked",
      "revokedAt": "2025-04-02T09:45:00Z"
    }
    ```

## 3. Rule Set Management

> **Note**: Rule sets are associated with specific entities via the `entityId` field in their definition (see [[Rule Set Schema]]). An organization's API key authorizes rule set management across all its entities.

### 3.1 Create Rule Set

- **Endpoint**: `/rulesets`
- **Method**: POST
- **Description**: Creates a new rule set
- **Request Body**: Rule set definition JSON (see [[Rule Set Schema]])
  - Must include `entityId` and `contextId` fields to specify which entity and transaction context the rule set applies to
  - Contains separate collections for `evaluationRules` and `outcomeRules`
- **Response**:
  - Status: 201 Created
  - Body:
    ```json
    {
      "ruleSetId": "string",
      "message": "Rule set successfully created"
    }
    ```

### 3.2 Update Rule Set

- **Endpoint**: `/rulesets/{ruleSetId}`
- **Method**: PUT
- **Description**: Updates an existing rule set
- **Request Body**: Updated rule set definition JSON (see [[Rule Set Schema]])
  - Must maintain the same `entityId` and `contextId` as the original rule set
  - Contains separate collections for `evaluationRules` and `outcomeRules`
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "ruleSetId": "string",
      "message": "Rule set successfully updated"
    }
    ```

### 3.3 Get Rule Set Details

- **Endpoint**: `/rulesets/{ruleSetId}`
- **Method**: GET
- **Description**: Retrieves details of a specific rule set
- **Response**: Full rule set details (see [[Rule Set Schema]])

### 3.4 List Rule Sets

- **Endpoint**: `/rulesets`
- **Method**: GET
- **Description**: Retrieves a list of rule sets
- **Query Parameters**:
  - `page`: Page number for pagination (default: 1)
  - `pageSize`: Number of items per page (default: 20)
  - `status`: Filter by rule set status (optional: "ACTIVE", "DRAFT", "PAUSED", "ARCHIVED")
  - `entityId`: Filter by the entity ID the rule set applies to (optional, UUID format, e.g., `550e8400-e29b-41d4-a716-************`)
  - `contextId`: Filter by the transaction context ID the rule set applies to (optional)
  - `startDateFrom`: Filter by start date from (optional, ISO 8601 format)
  - `startDateTo`: Filter by start date to (optional, ISO 8601 format)
  - `endDateFrom`: Filter by end date from (optional, ISO 8601 format)
  - `endDateTo`: Filter by end date to (optional, ISO 8601 format)
  - `category`: Filter by business category (optional)
- **Response**:
  ```json
  {
    "ruleSets": [
      {
        "ruleSetId": "string",
        "name": "string",
        "entityId": "string",
        "contextId": "string",
        "status": "string",
        "startDateTime": "string",
        "endDateTime": "string",
        "category": "string"
      }
    ],
    "totalCount": "integer",
    "page": "integer",
    "pageSize": "integer"
  }
  ```

### 3.5 Delete Rule Set

- **Endpoint**: `/rulesets/{ruleSetId}`
- **Method**: DELETE
- **Description**: Deletes a rule set
- **Response**:
  - Status: 204 No Content

### 3.6 Rule Set Status Management

- **Endpoint**: `/rulesets/{ruleSetId}/status`
- **Method**: PUT
- **Description**: Updates a rule set's status
- **Request Body**:
  ```json
  {
    "status": "ACTIVE" | "DRAFT" | "PAUSED" | "ARCHIVED"
  }
  ```
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "ruleSetId": "string",
      "status": "string",
      "message": "Rule set status successfully updated"
    }
    ```

### 3.7 Rule Set Version History

- **Endpoint**: `/rulesets/{ruleSetId}/versions`
- **Method**: GET
- **Description**: Retrieves the complete version history for a specific rule set, including metadata about each version and the changes made
- **Path Parameters**:
  - `ruleSetId`: Unique identifier of the rule set
- **Query Parameters**:
  - `page`: Page number for pagination (default: 1)
  - `pageSize`: Number of items per page (default: 20)
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "ruleSetId": "PRICING_RULES_2025",
      "versions": [
        {
          "version": 3,
          "modifiedAt": "2025-04-01T15:30:00Z",
          "modifiedBy": "<EMAIL>",
          "changeDescription": "Updated discount percentage calculations",
          "status": "ACTIVE"
        },
        {
          "version": 2,
          "modifiedAt": "2025-03-25T09:45:00Z",
          "modifiedBy": "<EMAIL>",
          "changeDescription": "Added seasonal pricing rules",
          "status": "ARCHIVED"
        },
        {
          "version": 1,
          "modifiedAt": "2025-03-15T14:30:00Z",
          "modifiedBy": "<EMAIL>",
          "changeDescription": "Initial version",
          "status": "ARCHIVED"
        }
      ],
      "totalCount": 3,
      "page": 1,
      "pageSize": 20
    }
    ```

### 3.8 Rule Set Cloning

- **Endpoint**: `/rulesets/{ruleSetId}/clone`
- **Method**: POST
- **Description**: Creates a new rule set based on an existing one, copying all rules, conditions, and variable assignments
- **Path Parameters**:
  - `ruleSetId`: Unique identifier of the source rule set to clone
- **Request Body**:
  ```json
  {
    "newRuleSetId": "string",
    "newName": "string",
    "category": "string",
    "entityId": "string",
    "contextId": "string"
  }
  ```
- **Response**:
  - Status: 201 Created
  - Body: Complete rule set definition JSON as per [[Rule Set Schema]], containing all fields of the newly created rule set
    ```json
    {
      "schemaVersion": "7.0.0",
      "ruleSetId": "PRICING_RULES_2025_CLONE",
      "name": "Dynamic Pricing Rules 2025 - Clone",
      "description": "Adaptive pricing rules based on demand and customer segments",
      "entityId": "550e8400-e29b-41d4-a716-************",
      "contextId": "PURCHASE", 
      "version": 1,
      "status": "DRAFT",
      "category": "PRICING",
      "startDateTime": "2025-06-01T00:00:00Z",
      "endDateTime": "2025-12-31T23:59:59Z",
      "lastModifiedDateTime": "2025-04-05T14:30:00Z",
      "collectionMappings": [...],
      "persistentVariables": [...],
      "localVariables": [...],
      "evaluationRules": [...],
      "outcomeRules": [...]
    }
    ```

> **Note**: When cloning a rule set, you can optionally specify a different entity and context than the source rule set. If provided, the system will validate that variable references are valid in the new context.

## 4. System Configuration and Information

This section provides endpoints for retrieving system configuration data used by the GUI to construct rules. Key components are defined as follows:
- **Condition Types**: Predicates that evaluate to true or false, forming the logical backbone of rules (e.g., `EQUALS`, `GREATER_THAN`).
- **Variable Assignments**: State updates that assign or modify variables—transaction properties, local, or persistent—based on rule outcomes (e.g., `SET`, `ADD`).
- **Functions**: Pure transformations that compute values for conditions or assignments (e.g., `dayOfWeek`, `formatDate`).

These components are distinct in their computational roles—condition types decide, functions compute, and variable assignments update state—ensuring a clear grammar for rule logic. They are aggregated in the full configuration schema (Section 4.7).

### 4.1 Get Global Context

- **Endpoint**: `/system/globalContext`
- **Method**: GET
- **Description**: Retrieves the global context definition implemented and managed by the Rules Engine
- **Response**: Global context JSON (see [[Function Schema]])

### 4.2 Get Condition Types

- **Endpoint**: `/system/conditionTypes`
- **Method**: GET
- **Description**: Retrieves available condition types—predicates that evaluate rule conditions to true or false based on transaction properties or function outputs
- **Response**:
  ```json
  [
    {
      "conditionTypeId": "EQUALS",
      "name": "Equals",
      "description": "Tests if two values are equal",
      "applicableTypes": ["string", "number", "boolean", "date"]
    },
    {
      "conditionTypeId": "GREATER_THAN",
      "name": "Greater Than",
      "description": "Tests if a value exceeds another",
      "applicableTypes": ["number", "date"]
    },
    {
      "conditionTypeId": "CONTAINS",
      "name": "Contains",
      "description": "Tests if a string includes a substring",
      "applicableTypes": ["string"]
    }
  ]
  ```
- **Note**: Condition types form the evaluative syntax of rules, returning boolean outcomes (e.g., `productPrice > 50`, `dayOfWeek(timestamp) = 'FRIDAY'`). See [[Condition Type Schema]] for details.

### 4.3 Get Variable Assignments

- **Endpoint**: `/system/variableAssignments`
- **Method**: GET
- **Description**: Retrieves available state assignments that update variables (transaction properties, local, or persistent) based on rules
- **Response**:
  ```json
  {
    "variableAssignments": [
      {
        "assignmentTypeId": "SET",
        "name": "Set Value",
        "description": "Sets the variable to the specified value",
        "applicableTypes": ["string", "number", "boolean", "date"]
      },
      {
        "assignmentTypeId": "ADD",
        "name": "Add",
        "description": "Adds the specified value to the variable",
        "applicableTypes": ["number"]
      },
      {
        "assignmentTypeId": "SUBTRACT",
        "name": "Subtract",
        "description": "Subtracts the specified value from the variable",
        "applicableTypes": ["number"]
      },
      {
        "assignmentTypeId": "MULTIPLY",
        "name": "Multiply",
        "description": "Multiplies the variable by the specified value",
        "applicableTypes": ["number"]
      }
    ]
  }
  ```
- **Note**: Variable assignments modify variables (e.g., `SET(discount, 10)`), distinct from functions (compute) and conditions (evaluate).

### 4.4 Get System Functions

- **Endpoint**: `/system/functions`
- **Method**: GET
- **Description**: Retrieves available system functions—pure transformations that compute values from inputs for use in condition evaluations or variable assignments
- **Response**:
  ```json
  {
    "functions": [
      {
        "functionId": "dayOfWeek",
        "name": "Day of Week",
        "description": "Returns the day of the week for a given date",
        "parameters": [
          {
            "parameterId": "date",
            "name": "Date",
            "type": "date",
            "description": "The date to get the day of week for"
          }
        ],
        "returnType": "string",
        "returnValues": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"],
        "examples": [
          {
            "reference": {
              "functionId": "dayOfWeek",
              "args": ["2025-03-28T00:00:00Z"]
            },
            "result": "FRIDAY"
          }
        ]
      },
      {
        "functionId": "formatDate",
        "name": "Format Date",
        "description": "Formats a date according to the specified pattern",
        "parameters": [
          {
            "parameterId": "date",
            "name": "Date",
            "type": "date",
            "description": "The date to format"
          },
          {
            "parameterId": "pattern",
            "name": "Pattern",
            "type": "string",
            "description": "The format pattern (follows Java SimpleDateFormat syntax)"
          }
        ],
        "returnType": "string",
        "examples": [
          {
            "reference": {
              "functionId": "formatDate",
              "args": ["2025-03-28T00:00:00Z", "yyyy-MM-dd"]
            },
            "result": "2025-03-28"
          }
        ]
      }
    ]
  }
  ```
- **Note**: Functions are pure transformations that compute values from inputs without side effects. They can be used within condition parameters (e.g., `{"functionId": "dayOfWeek", "args": ["{transactionDate}"]}`) or as values in variable assignments (e.g., `{"variableId": "formattedDate", "assignmentTypeId": "SET", "value": {"functionId": "formatDate", "args": ["{transactionDate}", "yyyy-MM-dd"]}}`). For details on supported date formatting patterns in the `formatDate` function, refer to [Java SimpleDateFormat Patterns](https://docs.oracle.com/javase/8/docs/api/java/text/SimpleDateFormat.html).

### 4.5 Get Registered Entities

- **Endpoint**: `/system/entities`
- **Method**: GET
- **Description**: Retrieves a list of all registered entities with their transaction contexts and properties
- **Query Parameters**:
  - `page`: Page number for pagination (default: 1)
  - `pageSize`: Number of items per page (default: 20)
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "entities": [
        {
          "entityId": "550e8400-e29b-41d4-a716-************",
          "entityName": "E-commerce Platform",
          "entityDescription": "Online shopping platform",
          "transactionContexts": [
            {
              "contextId": "PURCHASE",
              "contextName": "Product Purchase",
              "contextDescription": "Customer purchasing products",
              "properties": [
                {
                  "propertyId": "customerId",
                  "name": "Customer ID",
                  "type": "string",
                  "description": "Unique identifier for the customer",
                  "mutable": false
                },
                {
                  "propertyId": "productPrice",
                  "name": "Product Price",
                  "type": "number",
                  "description": "Price of the product",
                  "mutable": true,
                  "constraints": {
                    "min": 0
                  }
                }
              ]
            }
          ]
        },
        {
          "entityId": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
          "entityName": "Loyalty Program",
          "entityDescription": "Customer rewards system",
          "transactionContexts": [
            {
              "contextId": "POINT_REDEMPTION",
              "contextName": "Point Redemption",
              "contextDescription": "Customer redeeming loyalty points",
              "properties": [
                {
                  "propertyId": "customerId",
                  "name": "Customer ID", 
                  "type": "string",
                  "description": "Unique identifier for the customer",
                  "mutable": false
                },
                {
                  "propertyId": "pointsToRedeem",
                  "name": "Points to Redeem",
                  "type": "number",
                  "description": "Number of points to redeem",
                  "mutable": true,
                  "constraints": {
                    "min": 100
                  }
                }
              ]
            }
          ]
        }
      ],
      "totalCount": 2,
      "page": 1,
      "pageSize": 20
    }
    ```

### 4.6 Get Standard Transaction Properties

- **Endpoint**: `/system/standard-properties`
- **Method**: GET
- **Description**: Retrieves all standard transaction properties that are automatically available in all transactions regardless of entity configuration
- **Response**:
  ```json
  {
    "standardProperties": [
      {
        "propertyId": "timestamp",
        "name": "Transaction Timestamp",
        "description": "Date and time when the transaction occurred",
        "type": "date",
        "availablePhases": ["evaluation", "outcome"]
      },
      {
        "propertyId": "transactionId",
        "name": "Transaction ID",
        "description": "Unique identifier for the transaction",
        "type": "string",
        "availablePhases": ["evaluation", "outcome"]
      },
      {
        "propertyId": "entityId",
        "name": "Entity ID",
        "description": "Identifier of the entity sending the transaction",
        "type": "string",
        "availablePhases": ["evaluation", "outcome"]
      },
      {
        "propertyId": "contextId",
        "name": "Context ID",
        "description": "Identifier of the transaction context",
        "type": "string",
        "availablePhases": ["evaluation", "outcome"]
      },
      {
        "propertyId": "status",
        "name": "Transaction Status",
        "description": "Indicates whether the transaction was completed, failed, or abandoned",
        "type": "enum",
        "values": ["COMPLETED", "FAILED", "ABANDONED"],
        "availablePhases": ["outcome"]
      },
      {
        "propertyId": "modificationsApplied",
        "name": "Applied Modifications",
        "description": "Array of property identifiers that were successfully modified",
        "type": "array",
        "itemType": "string",
        "availablePhases": ["outcome"]
      },
      {
        "propertyId": "modificationsRejected",
        "name": "Rejected Modifications",
        "description": "Array of property identifiers that couldn't be modified",
        "type": "array",
        "itemType": "string",
        "availablePhases": ["outcome"]
      }
    ]
  }
  ```
- **Note**: The `availablePhases` field indicates whether each property is available during the evaluation phase, outcome phase, or both. This helps developers understand which properties can be used in which rule collections.

### 4.7 Get Configuration Schema

- **Endpoint**: `/system/config-schema`
- **Method**: GET
- **Description**: Retrieves the full configuration schema for the RuleForge system, detailing condition types (predicates), variable assignments (state updates), functions (transformations), and standard properties
- **Response**:
  ```json
  {
    "conditionTypes": [
      {
        "conditionTypeId": "EQUALS",
        "name": "Equals",
        "description": "Tests if two values are equal",
        "applicableTypes": ["string", "number", "boolean", "date"]
      },
      {
        "conditionTypeId": "GREATER_THAN",
        "name": "Greater Than",
        "description": "Tests if a value exceeds another",
        "applicableTypes": ["number", "date"]
      }
    ],
    "variableAssignments": [
      {
        "assignmentTypeId": "SET",
        "name": "Set Value",
        "description": "Sets the variable to the specified value",
        "applicableTypes": ["string", "number", "boolean", "date"]
      },
      {
        "assignmentTypeId": "ADD",
        "name": "Add",
        "description": "Adds the specified value to the variable",
        "applicableTypes": ["number"]
      }
    ],
    "functions": [
      {
        "functionId": "dayOfWeek",
        "name": "Day of Week",
        "description": "Returns the day of the week for a given date",
        "parameters": [ … ],
        "returnType": "string",
        "returnValues": ["MONDAY", … ]
      },
      {
        "functionId": "formatDate",
        "name": "Format Date",
        "description": "Formats a date according to the specified pattern",
        "parameters": [ … ],
        "returnType": "string"
      }
    ],
    "standardProperties": [
      {
        "propertyId": "timestamp",
        "name": "Transaction Timestamp",
        "description": "Date and time when the transaction occurred",
        "type": "date",
        "availablePhases": ["evaluation", "outcome"]
      },
      // Additional standard properties here
    ],
    "systemVersion": "string"
  }
  ```
- **Note**: The `conditionTypes` field defines predicates for rule evaluation (Section 4.2), `variableAssignments` defines state updates for variables—transaction properties, local, or persistent (Section 4.3)—`functions` defines value transformations (Section 4.4), and `standardProperties` defines built-in properties available in all transactions (Section 4.6).

When using lists in conditions, the rightOperand can reference a list instead of providing an array directly:

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": "IN",
  "parameters": {
    "leftOperand": "{agentCGI}",
    "rightOperand": {
      "listId": "approvedAgents"
    }
  }
}
```

Lists provide an efficient way to check membership for large sets of values without embedding them directly in the rule. See the [[List Management Schema]] for details on list structure and usage.

## 5. Webhook Management

The Webhook Management API allows the GUI to manage registered webhooks that can be triggered by rules to integrate with external systems like SMS gateways, email services, and third-party APIs.

### 5.1 List Webhooks

- **Endpoint**: `/webhooks`
- **Method**: GET
- **Description**: Retrieves a list of registered webhooks
- **Query Parameters**:
  - `page`: Page number for pagination (default: 1)
  - `pageSize`: Number of items per page (default: 20)
  - `securityLevel`: Filter by security level (optional: "SYSTEM", "ORGANIZATION", "ENTITY")
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "webhooks": [
        {
          "webhookId": "smsNotification",
          "name": "SMS Notification",
          "description": "Sends SMS notifications via the corporate SMS gateway",
          "url": "https://api.sms-gateway.com/send",
          "method": "POST",
          "securityLevel": "SYSTEM",
          "createdAt": "2025-04-01T10:30:00Z",
          "updatedAt": "2025-04-05T14:45:00Z"
        },
        {
          "webhookId": "emailNotification",
          "name": "Email Notification",
          "description": "Sends transactional emails",
          "url": "https://api.email-service.com/send",
          "method": "POST",
          "securityLevel": "ORGANIZATION",
          "createdAt": "2025-04-02T09:15:00Z",
          "updatedAt": "2025-04-02T09:15:00Z"
        }
      ],
      "totalCount": 2,
      "page": 1,
      "pageSize": 20
    }
    ```

### 5.2 Get Webhook Details

- **Endpoint**: `/webhooks/{webhookId}`
- **Method**: GET
- **Description**: Retrieves details of a specific webhook
- **Path Parameters**:
  - `webhookId`: Unique identifier of the webhook
- **Response**:
  - Status: 200 OK
  - Body: Complete webhook definition (see [[Webhook Schema]])
    ```json
    {
      "webhookId": "smsNotification",
      "name": "SMS Notification",
      "description": "Sends SMS notifications via the corporate SMS gateway",
      "url": "https://api.sms-gateway.com/send",
      "method": "POST",
      "headers": {
        "Authorization": "Bearer ${SECRET:sms_api_key}",
        "Content-Type": "application/json"
      },
      "bodyTemplate": {
        "to": "{recipient}",
        "message": "{message}",
        "sender": "RuleForge"
      },
      "timeout": 3000,
      "retryPolicy": {
        "maxRetries": 3,
        "initialDelaySeconds": 60,
        "backoffMultiplier": 2
      },
      "securityLevel": "SYSTEM",
      "parameters": [
        {
          "parameterId": "recipient",
          "name": "Recipient",
          "description": "The phone number to send the SMS to",
          "type": "string",
          "required": true
        },
        {
          "parameterId": "message",
          "name": "Message",
          "description": "The content of the SMS message",
          "type": "string",
          "required": true
        }
      ],
      "createdAt": "2025-04-01T10:30:00Z",
      "updatedAt": "2025-04-05T14:45:00Z"
    }
    ```

### 5.3 Register Webhook

- **Endpoint**: `/webhooks`
- **Method**: POST
- **Description**: Registers a new webhook
- **Request Body**: Webhook definition JSON (see [[Webhook Schema]])
  ```json
  {
    "webhookId": "smsNotification",
    "name": "SMS Notification",
    "description": "Sends SMS notifications via the corporate SMS gateway",
    "url": "https://api.sms-gateway.com/send",
    "method": "POST",
    "headers": {
      "Authorization": "Bearer ${SECRET:sms_api_key}",
      "Content-Type": "application/json"
    },
    "bodyTemplate": {
      "to": "{recipient}",
      "message": "{message}",
      "sender": "RuleForge"
    },
    "timeout": 3000,
    "retryPolicy": {
      "maxRetries": 3,
      "initialDelaySeconds": 60,
      "backoffMultiplier": 2
    },
    "securityLevel": "SYSTEM",
    "parameters": [
      {
        "parameterId": "recipient",
        "name": "Recipient",
        "description": "The phone number to send the SMS to",
        "type": "string",
        "required": true
      },
      {
        "parameterId": "message",
        "name": "Message",
        "description": "The content of the SMS message",
        "type": "string",
        "required": true
      }
    ]
  }
  ```
- **Response**:
  - Status: 201 Created
  - Body:
    ```json
    {
      "webhookId": "smsNotification",
      "message": "Webhook successfully registered",
      "createdAt": "2025-04-10T11:30:00Z"
    }
    ```

### 5.4 Update Webhook

- **Endpoint**: `/webhooks/{webhookId}`
- **Method**: PUT
- **Description**: Updates an existing webhook
- **Path Parameters**:
  - `webhookId`: Unique identifier of the webhook to update
- **Request Body**: Updated webhook definition JSON (see [[Webhook Schema]])
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "webhookId": "smsNotification",
      "message": "Webhook successfully updated",
      "updatedAt": "2025-04-10T11:45:00Z"
    }
    ```

### 5.5 Delete Webhook

- **Endpoint**: `/webhooks/{webhookId}`
- **Method**: DELETE
- **Description**: Deletes a webhook
- **Path Parameters**:
  - `webhookId`: Unique identifier of the webhook to delete
- **Response**:
  - Status: 204 No Content

### 5.6 Test Webhook

- **Endpoint**: `/webhooks/{webhookId}/test`
- **Method**: POST
- **Description**: Tests a webhook with sample parameters
- **Path Parameters**:
  - `webhookId`: Unique identifier of the webhook to test
- **Request Body**:
  ```json
  {
    "parameters": {
      "recipient": "test-phone-number",
      "message": "This is a test message from RuleForge"
    }
  }
  ```
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "success": true,
      "statusCode": 200,
      "responseBody": {
        "messageId": "test-message-id",
        "status": "sent"
      },
      "executionTimeMs": 245
    }
    ```
  
  OR
  
  - Status: 200 OK (test executed, but webhook call failed)
  - Body:
    ```json
    {
      "success": false,
      "statusCode": 401,
      "error": {
        "code": "AUTHENTICATION_FAILED",
        "message": "Invalid API key"
      },
      "executionTimeMs": 156
    }
    ```

## 6. Error Handling

All API endpoints may return the following error responses:

- 400 Bad Request: Invalid input data
  ```json
  {
    "error": {
      "code": "INVALID_RULESET_DATA",
      "message": "Rule set data contains invalid fields",
      "details": {
        "invalidFields": ["name", "evaluationRules[0].condition"]
      }
    },
    "requestId": "string"
  }
  ```

- 401 Unauthorized: Not authenticated
  ```json
  {
    "error": {
      "code": "AUTHENTICATION_REQUIRED",
      "message": "Authentication is required",
      "details": {}
    },
    "requestId": "string"
  }
  ```

- 403 Forbidden: Insufficient permissions
  ```json
  {
    "error": {
      "code": "INSUFFICIENT_PERMISSIONS",
      "message": "You do not have permission to perform this action",
      "details": {
        "requiredPermission": "CREATE_RULESET"
      }
    },
    "requestId": "string"
  }
  ```

- 404 Not Found: Requested resource not found
- 409 Conflict: Resource conflict (e.g., duplicate rule set ID)
  ```json
  {
    "error": {
      "code": "DUPLICATE_RULESET_ID",
      "message": "A rule set with this ID already exists",
      "details": {
        "ruleSetId": "PRICING_RULES_2025"
      }
    },
    "requestId": "string"
  }
  ```

- 422 Unprocessable Entity: Validation error
  ```json
  {
    "error": {
      "code": "VALIDATION_ERROR",
      "message": "Rule set data validation failed",
      "details": {
        "validationErrors": [
          "Start date must be before end date",
          "Rules must have unique IDs within a rule set",
          "Variable name 'productPrice' conflicts with transaction property",
          "Rule ID must be unique across evaluation and outcome rules"
        ]
      }
    },
    "requestId": "string"
  }
  ```
  
- 429 Too Many Requests: Rate limit exceeded
  ```json
  {
    "error": {
      "code": "RATE_LIMIT_EXCEEDED",
      "message": "Rate limit has been exceeded",
      "details": {
        "retryAfter": 30
      }
    },
    "requestId": "string"
  }
  ```
  
- 500 Internal Server Error: Server-side error

### Webhook-Specific Errors

- 400 Bad Request: Invalid Webhook Configuration
  ```json
  {
    "error": {
      "code": "INVALID_WEBHOOK_CONFIG",
      "message": "Webhook configuration contains invalid fields",
      "details": {
        "invalidFields": ["url", "parameters[0].type"]
      }
    },
    "requestId": "string"
  }
  ```

- 403 Forbidden: URL Not Allowed
  ```json
  {
    "error": {
      "code": "URL_NOT_ALLOWED",
      "message": "The specified URL is not in the allowed domains list",
      "details": {
        "url": "https://suspicious-domain.com/endpoint",
        "allowedDomains": ["api.sms-gateway.com", "api.email-service.com"]
      }
    },
    "requestId": "string"
  }
  ```

- 409 Conflict: Duplicate Webhook ID
  ```json
  {
    "error": {
      "code": "DUPLICATE_WEBHOOK_ID",
      "message": "A webhook with this ID already exists",
      "details": {
        "webhookId": "smsNotification"
      }
    },
    "requestId": "string"
  }
  ```

## 7. Rate Limiting

- The API is rate limited to protect system resources
- Rate limits are applied per user/service
- Current rate limit: 100 requests per minute
- Rate limit headers are included in all responses:
  - `X-RateLimit-Limit`: The number of allowed requests in the current period
  - `X-RateLimit-Remaining`: The number of remaining requests in the current period
  - `X-RateLimit-Reset`: The time at which the current rate limit window resets in UTC epoch seconds

## 8. Versioning and Change Management

### 8.1 API Versioning

Major changes that break compatibility will result in a new API version (e.g., `/v2/` endpoints). Previous versions will be supported for at least 6 months after a new version is released.

### 8.2 Configuration Versioning

System configuration components (condition types, variable assignments, functions) are versioned independently of the API. The `/system/config-schema` endpoint includes a `systemVersion` field indicating the current configuration version.

### 8.3 Deprecated Features

Deprecated features will be marked with a `deprecated` flag in the configuration schema and documentation. They will continue to work for at least 6 months before being removed.

## Related Documents

- [[Rule Set Schema]]
- [[Function Schema]]
- [[Condition Type Schema]]
- [[Variable Assignment Schema]]
- [[Entity Integration API]]
- [[Transaction Evaluation API]]
- [[List Management API]]
- [[List Management Schema]]
- [[Standard Transaction Properties Schema]]
- [[Webhook Schema]]

## Approvals

| Role/Department    | Name | Date | Signature |
|--------------------|------|------|-----------|
| API Team Lead      |      |      |           |
| GUI Team Lead      |      |      |           |
| Product Manager    |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                 |
|---------|----------------|-----------------|--------------------------------------------------------|
| 2.4.0   | [[2025-04-10]] | [[Wayne Smith]] | Added Webhook Management section with endpoints for registering, retrieving, updating, deleting, and testing webhooks; added webhook-specific error codes; updated related documents to include Webhook Schema |
| 2.3.0   | [[2025-04-09]] | [[Wayne Smith]] | Added new endpoint `/system/standard-properties` to retrieve standard transaction properties; updated `/system/config-schema` to include standard properties |
| 2.2.0   | [[2025-04-07]] | [[Wayne Smith]] | Updated to align with Rule Set Schema v6.0.0: modified documentation to reference separate `evaluationRules` and `outcomeRules` collections; updated examples, request structures, and validation rules to reflect the separation of rule phases |
| 2.1.0   | [[2025-04-04]] | [[Wayne Smith]] | Added references to List Management API; updated examples to show list usage in conditions; updated field naming to align with RuleForge Naming Convention Guide: replaced `code` with `conditionTypeId`, `operation`/`code` with `assignmentTypeId`, `function` with `functionId`, and `name` with `parameterId` throughout |
| 2.0.0   | [[2025-04-03]] | [[Wayne Smith]] | Major update to align with Rule Set Schema v5.0.0: added entityId and contextId as required fields for rule sets; updated request/response structures; updated examples to reflect single entity-context model; added new error codes for name conflicts |
| 1.4.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated terminology from "campaign" to "rule set" throughout the document for consistency with other specifications; updated document references; revised endpoint descriptions |
| 1.3.0   | [[2025-04-02]] | [[Wayne Smith]] | Corrected authentication documentation to reflect JWT-based authentication for all access types, removing references to session-based authentication. Added token refresh endpoint. |
| 1.2.0   | [[2025-04-02]] | [[Wayne Smith]] | Reframed API to be more domain-agnostic: renamed endpoints from 'campaigns' to 'rulesets', adjusted terminology throughout, added 'category' field to rule sets to support domain classification |
| 1.1.0   | [[2025-04-01]] | [[Wayne Smith]] | Updated API key management to org-level (Section 2.4), added API key auth clarification (Section 1.3), clarified campaign-entity link (Section 3), defined conditionTypes as predicates, functions as transformations, and renamed variableOperations to variableAssignments for broader variable scope (Section 4), updated related docs to v1.1.0 |
| 1.0.0   | [[2025-03-28]] | [[Wayne Smith]] | Initial version of the Rules Management API Specification |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->
