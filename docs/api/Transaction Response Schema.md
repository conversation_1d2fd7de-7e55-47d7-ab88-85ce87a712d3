---
title: "Transaction Response Schema"
classification: Confidential
created: 2024-08-28
updated: 2025-04-03
authors:
  - "[[<PERSON>]]"
version: 3.1.0
next-review: 2025-06-28
category: "[[Specifications]]"
tags:
  - specification
  - schema
  - json
topics:
  - "[[Property Modification Framework]]"
  - "[[Transaction Evaluation]]"
  - "[[API]]"
  - "[[RuleForge]]"
---

# Transaction Response Schema

## Document Specification

### Purpose
This document defines the JSON structure for transaction evaluation responses in the RuleForge Rules Engine. It specifies the format, content, and constraints of the evaluation results returned by the Rules Engine after processing a transaction, focusing on property modifications.

### Scope
This document covers:
- The JSON structure for transaction evaluation responses
- Definitions of essential components (modified properties)
- Data types and constraints for each field
- Examples of valid responses

This document does not cover:
- The internal processing of transaction evaluation
- The structure of the evaluation request
- Detailed business logic of how property modifications are determined

### Target Audience
- Developers integrating client systems with RuleForge
- QA engineers validating transaction evaluation responses
- Technical writers creating transaction-related documentation

## Table of Contents

- [[#1. JSON Structure]]
- [[#2. Component Definitions]]
- [[#3. Constraints and Validation]]
- [[#4. Examples]]

## 1. JSON Structure

A transaction evaluation response is represented by a JSON object with the following structure:

```json
{
  "transactionId": "string",
  "modifiedProperties": {
    "property1": {
      "original": "any",
      "modified": "any",
      "ruleSetId": "string",
      "ruleId": "string"
    },
    "property2": {
      "original": "any",
      "modified": "any",
      "ruleSetId": "string",
      "ruleId": "string"
    }
  }
}
```

> **Note:** Previous versions of the transaction evaluation response structure included an `actions` array. This has been removed in favor of the Property Modification Framework, which focuses on modifying transaction properties rather than triggering actions.

## 2. Component Definitions

### Transaction Identifier
- `transactionId`: Unique identifier for the specific transaction, matching the ID provided in the request (string, max 50 characters)

### Modified Properties
The `modifiedProperties` object contains information about properties that were modified as a result of the transaction evaluation:

- Each key in the `modifiedProperties` object is the propertyId of a property that was modified
- Each value is an object containing:
  - `original`: The original value of the property from the transaction context
  - `modified`: The new value determined by rule set rules
  - `ruleSetId`: Unique identifier of the rule set that triggered the modification (string, UPPER_SNAKE_CASE)
  - `ruleId`: Unique identifier of the rule that triggered the modification (string, UPPER_SNAKE_CASE)

## 3. Constraints and Validation

- The `transactionId` must match the transaction ID provided in the corresponding evaluation request
- The `modifiedProperties` object contains only properties that have been modified; unmodified properties are not included
- The values in `original` and `modified` must conform to the data types defined for each property in the entity registration
- If no properties were modified during evaluation, the `modifiedProperties` object will be an empty object (`{}`)
- The `ruleSetId` must refer to a valid rule set in the system
- The `ruleId` must refer to a valid rule within the specified rule set

## 4. Examples

### Response with Modified Properties

```json
{
  "transactionId": "TXN123456789",
  "modifiedProperties": {
    "productPrice": {
      "original": 99.99,
      "modified": 89.99,
      "ruleSetId": "PRICING_RULES_2025",
      "ruleId": "SUMMER_DISCOUNT_10"
    },
    "discountPercentage": {
      "original": 0,
      "modified": 10,
      "ruleSetId": "PRICING_RULES_2025",
      "ruleId": "SUMMER_DISCOUNT_10"
    },
    "shippingCost": {
      "original": 5.99,
      "modified": 0,
      "ruleSetId": "LOYALTY_PROGRAM",
      "ruleId": "FREE_SHIPPING_VIP"
    }
  }
}
```

In this example:
- The transaction with ID "TXN123456789" had three properties modified
- The `productPrice` was reduced from 99.99 to 89.99 by the "SUMMER_DISCOUNT_10" rule
- The `discountPercentage` was increased from 0 to 10 by the same rule
- The `shippingCost` was reduced to 0 by the "FREE_SHIPPING_VIP" rule from a different rule set

### Response with No Modifications

```json
{
  "transactionId": "TXN987654321",
  "modifiedProperties": {}
}
```

In this example, no properties were modified during the evaluation of transaction "TXN987654321".

## Usage Guidelines

### Processing the Response

When receiving a transaction evaluation response:

1. Check if any properties were modified (i.e., if `modifiedProperties` is not empty)
2. Apply each modified property to your transaction
3. Recalculate any derived values that depend on the modified properties
4. For traceability, consider logging which rule sets and rules modified which properties

### Error Handling

If you receive a response with property modifications that violate your business logic:

1. Log the violation for analysis
2. Either reject the specific property modification or fall back to original values
3. Continue processing with other valid modifications

## Related Documents

- [[Entity Integration Schema]]
- [[Transaction Request Schema]]
- [[RuleForge Interface Overview]]
- [[Property Modification Guide]]

## Approvals

| Role/Department    | Name | Date | Signature |
|--------------------|------|------|-----------|
| Lead Architect     |      |      |           |
| API Team Lead      |      |      |           |
| QA Team Lead       |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                 |
|---------|----------------|-----------------|--------------------------------------------------------|
| 3.1.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated field naming to align with RuleForge Naming Convention Guide: renamed `modified_properties` to `modifiedProperties` following camelCase convention; clarified that keys in modifiedProperties are propertyIds |
| 3.0.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated terminology from "campaign" to "rule set" for consistency across documentation; renamed `campaignId` to `ruleSetId` in modified_properties objects; updated examples to reflect new terminology |
| 2.0.0   | [[2025-03-28]] | [[Wayne Smith]] | - Restructured response to focus on property modifications<br>- Added `modified_properties` object to contain modifications<br>- Each modified property now includes original and modified values<br>- Removed `actions` array to align with Property Modification Framework<br>- Added usage guidelines for processing modified properties<br>- Updated examples to showcase property modifications |
| 1.2.0   | [[2024-09-10]] | [[Wayne Smith]] | Added `transactionId` field to the top-level structure. Updated example and constraints. |
| 1.1.0   | [[2024-09-03]] | [[RuleForge DevGenius]] | Added campaignId and ruleId to all actions. Removed evaluated rules section. Updated example and constraints. |
| 1.0.0   | [[2024-08-28]] | [[RuleForge DevGenius]] | Initial version of the Transaction Evaluation Response JSON Structure document. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->