---
title: Standard Transaction Properties Schema
classification: Confidential
created: 2025-04-09
updated: 2025-04-09
authors:
  - "[[<PERSON>]]"
version: 1.0.0
next-review: 2025-10-09
category: "[[Specifications]]"
tags:
  - specification
  - schema
  - json
topics:
  - "[[Property Modification Framework]]"
  - "[[Transaction Evaluation]]"
  - "[[API]]"
  - "[[RuleForge]]"
---

# Standard Transaction Properties Schema

## Document Specification

### Purpose

This document defines the schema for standard transaction properties in the RuleForge Property Modification Framework (PMF). It specifies the standard properties that are automatically available in all transactions, regardless of the entity or context configuration, and can be referenced in rule conditions and variable assignments.

### Scope

This document covers:

- The comprehensive list of standard transaction properties
- Data types and descriptions for each standard property
- Availability of properties in different transaction phases (evaluation vs. outcome)
- Usage guidelines for referencing standard properties in rules

This document does not cover:

- Entity-specific properties defined in entity registration
- Internal implementation details of standard properties
- Custom properties that may be added by specific implementations

### Target Audience

- Developers implementing rule logic in RuleForge
- System architects designing rule structures
- GUI developers creating the rule authoring interface
- Integration specialists working with the RuleForge system

## Table of Contents

- [[#1. Standard Property Categories]]
- [[#2. Common Properties]]
- [[#3. Evaluation Phase Properties]]
- [[#4. Outcome Phase Properties]]
- [[#5. Usage in Rules]]

## 1. Standard Property Categories

Standard transaction properties in RuleForge are categorized based on when they are available during transaction processing:

1. **Common Properties**: Available in both evaluation and outcome phases
2. **Evaluation Phase Properties**: Only available during the synchronous evaluation phase
3. **Outcome Phase Properties**: Only available during the asynchronous outcome phase

Each property has a consistent schema definition to ensure proper usage in rules.

## 2. Common Properties

These properties are available in both the evaluation and outcome phases of transaction processing.

### 2.1 Transaction Identification

| Property ID | Name | Description | Type | Format |
|-------------|------|-------------|------|--------|
| `transactionId` | Transaction ID | Unique identifier for the transaction | string | max 50 characters |
| `entityId` | Entity ID | Identifier of the entity sending the transaction | string | UUID format |
| `contextId` | Context ID | Identifier of the transaction context | string | UPPER_SNAKE_CASE |

### 2.2 Timestamp Properties

| Property ID | Name | Description | Type | Format |
|-------------|------|-------------|------|--------|
| `timestamp` | Transaction Timestamp | Date and time when the transaction occurred | date | ISO 8601 format |

## 3. Evaluation Phase Properties

These properties are only available during the synchronous evaluation phase.

Currently, there are no evaluation-phase-specific standard properties. All evaluation phase properties are included in the Common Properties section.

## 4. Outcome Phase Properties

These properties are only available during the asynchronous outcome phase.

### 4.1 Transaction Outcome

| Property ID | Name | Description | Type | Format/Values |
|-------------|------|-------------|------|---------------|
| `status` | Transaction Status | Indicates whether the transaction was completed, failed, or abandoned | enum | "COMPLETED", "FAILED", "ABANDONED" |

### 4.2 Modification Status

| Property ID | Name | Description | Type | Format |
|-------------|------|-------------|------|--------|
| `modificationsApplied` | Applied Modifications | Array of property identifiers that were successfully modified | array of strings | Property IDs |
| `modificationsRejected` | Rejected Modifications | Array of property identifiers that couldn't be modified | array of strings | Property IDs |

Note: These are flattened top-level properties derived from the `modificationStatus` object in the transaction outcome notification to avoid nested property references.

## 5. Usage in Rules

### 5.1 Referencing Standard Properties

Standard properties are referenced in rules using curly braces, just like entity-defined properties:

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": "==",
  "parameters": {
    "leftOperand": "{status}",
    "rightOperand": "COMPLETED"
  }
}
```

### 5.2 Working with Array Properties

For array properties like `modificationsApplied`, the RuleForge specification supports checking if a value is contained in the array using the `IN` operator:

```json
{
  "conditionTypeId": "LOGICAL",
  "operator": "AND",
  "parameters": {
    "conditions": [
      {
        "conditionTypeId": "COMPARISON",
        "operator": "==",
        "parameters": {
          "leftOperand": "{status}",
          "rightOperand": "COMPLETED"
        }
      },
      {
        "conditionTypeId": "COMPARISON",
        "operator": "IN",
        "parameters": {
          "leftOperand": "propertyId",
          "rightOperand": "{modificationsApplied}"
        }
      }
    ]
  }
}
```

### 5.3 Phase Availability Considerations

When authoring rules, it's important to consider which phase the rule will execute in:

- Evaluation rules (`evaluationRules` collection) can only reference Common Properties and Evaluation Phase Properties
- Outcome rules (`outcomeRules` collection) can reference all properties, including Outcome Phase Properties

Attempting to reference a property that's not available in the current phase will result in a rule evaluation error.

## Related Documents

- [[Transaction Request Schema]]
- [[Transaction Response Schema]]
- [[Transaction Outcome Schema]]
- [[Rule Set Schema]]
- [[RuleForge Management API]]

## Approvals

| Role | Name | Date | Signature |
|-----------------|------|------|-----------|
| Lead Architect  |      |      |           |
| API Team Lead   |      |      |           |
| Documentation Manager |      |      |           |

## Changelog

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0.0   | [[2025-04-09]] | [[Wayne Smith]] | Initial version of the Standard Transaction Properties Schema. |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->
