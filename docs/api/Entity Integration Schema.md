---
title: Entity Integration Schema
classification: Confidential
created: 2024-08-27
updated: 2025-04-03
authors:
  - "[[<PERSON><PERSON>]]"
  - "[[<PERSON>]]"
version: 2.5.0
next-review: 2025-06-28
category: "[[Specifications]]"
tags:
  - specification
  - schema
  - json
topics:
  - "[[Property Modification Framework]]"
  - "[[Entity Management]]"
  - "[[API]]"
  - "[[RuleForge]]"
---

# Entity Integration Schema

## Document Specification

### Purpose

This document defines the JSON structure for entity registration in RuleForge. It specifies the format, content, and constraints of the entity registration data used by the Rules Engine, with a focus on property definitions for the Property Modification Framework (PMF).

### Scope

This document covers:

- The JSON structure for defining entities in the RuleForge Rules Engine
- Definitions of entity components (transaction contexts, properties)
- Data types and constraints for each field
- Mutability flags for properties that can be modified by rule sets
- Constraints on property modifications

This document does not cover:

- The internal implementation of entity registration
- The process of creating or managing entities in the system
- How entities are used in the rule evaluation process

### Target Audience

- Developers implementing entity registration
- System architects designing entity structures
- QA engineers validating entity registrations
- Technical writers creating entity-related documentation

## Table of Contents

- [[#1. Top-Level Structure]]
- [[#2. Component Definitions]]
- [[#3. Property Types and Constraints]]
- [[#4. Usage Guidelines]]
- [[#5. Examples]]

## 1. Top-Level Structure

An entity registration is represented by a JSON object with the following structure:

```json
{
  "name": "string",
  "description": "string",
  "transactionContexts": []
}
```

> **Note:** Previous versions of the entity registration structure included `globalActions` and context-specific `actions`. These have been removed in favor of the Property Modification Framework, which focuses on modifying transaction properties rather than triggering actions. The `entityId` is not included in the request; it is a server-generated UUID returned in the response. See [[Entity Integration API#3.1 Register Entity]] for details.

## 2. Component Definitions

### Entity Metadata

- `name`: Human-readable name for the entity (string, max 100 characters)
- `description`: Description of the entity's purpose and behavior (string, max 250 characters)

### Transaction Contexts

The `transactionContexts` array defines the different types of transactions the entity processes:

```json
{
  "transactionContexts": [
    {
      "contextId": "string",
      "name": "string",
      "description": "string",
      "properties": []
    }
  ]
}
```

Fields:

- `contextId`: Unique identifier for the transaction context (string, max 50 characters, UPPER_SNAKE_CASE)
- `name`: Human-readable name for the context (string, max 100 characters)
- `description`: Description of when this context applies (string, max 250 characters)
- `properties`: Array of property definitions for this context

### Properties

The `properties` array defines the data elements within a transaction context:

```json
{
  "properties": [
    {
      "propertyId": "string",
      "name": "string",
      "description": "string",
      "type": "string",
      "mutable": boolean,
      "constraints": {}
    }
  ]
}
```

Fields:

- `propertyId`: Identifier of the property (string, max 50 characters, camelCase)
- `name`: Human-readable name for the property (string, max 100 characters)
- `description`: Description of the property's purpose (string, max 250 characters)
- `type`: Data type of the property (string, enum: "string", "number", "boolean", "date", "enum", "array", "object")
- `mutable`: Whether the property can be modified by rule sets (boolean)
- `constraints`: Object containing constraints on valid modifications (optional)
- `values`: Required array of allowed values for enum properties (array of strings, only when `type` is "enum")
- `items`: Required when `type` is "array", specifies the type of array items
- `properties`: Required when `type` is "object", contains nested property definitions

## 3. Property Types and Constraints

### Numeric Properties

Properties of type "number" can have the following constraints:

```json
{
  "propertyId": "productPrice",
  "name": "Product Price",
  "description": "Price of the product",
  "type": "number",
  "mutable": true,
  "constraints": {
    "min": 0.01,
    "max": 9999.99
  }
}
```

### String Properties

Properties of type "string" can have the following constraints:

```json
{
  "propertyId": "promoCode",
  "name": "Promotional Code",
  "description": "Promotional code",
  "type": "string",
  "mutable": true,
  "constraints": {
    "minLength": 3,
    "maxLength": 10,
    "pattern": "^[A-Z0-9]{3,10}$"
  }
}
```

### Enum Properties

Properties of type "enum" require a values array and can have constraints:

```json
{
  "propertyId": "customerType",
  "name": "Customer Type",
  "description": "Type of customer",
  "type": "enum",
  "mutable": true,
  "values": ["NEW", "RETURNING", "LOYAL", "VIP"],
  "constraints": {
    "validValues": ["LOYAL", "VIP"]
  }
}
```

When `mutable` is `true`, the optional `constraints.validValues` field can define a subset of `values` that RuleForge rule sets are allowed to set. This differs from `values`—the full set of possible property values—when business logic restricts modifications to specific options. For example, `values` might list all customer states an entity tracks, while `validValues` limits rule set modifications to premium tiers (e.g., upgrading to `LOYAL` or `VIP` but not downgrading to `NEW`). If `validValues` is omitted, rule sets can use any value from `values`.

### Date Properties

Properties of type "date" can have the following constraints:

```json
{
  "propertyId": "expiryDate",
  "name": "Expiration Date",
  "description": "Expiration date",
  "type": "date",
  "mutable": true,
  "constraints": {
    "minDate": "2025-01-01T00:00:00Z",
    "maxDate": "2025-12-31T23:59:59Z"
  }
}
```

### Object Properties

Properties of type "object" contain nested properties:

```json
{
  "propertyId": "shippingAddress",
  "name": "Shipping Address",
  "description": "Customer's shipping address",
  "type": "object",
  "mutable": false,
  "properties": [
    {
      "propertyId": "street",
      "name": "Street",
      "description": "Street address",
      "type": "string",
      "mutable": true,
      "constraints": {
        "maxLength": 100
      }
    },
    {
      "propertyId": "city",
      "name": "City",
      "description": "City name",
      "type": "string",
      "mutable": true,
      "constraints": {
        "maxLength": 50
      }
    }
  ]
}
```

Note: While an object itself might not be mutable, individual properties within it can be marked as mutable.

### Array Properties

Properties of type "array" specify the type of their items:

```json
{
  "propertyId": "tags",
  "name": "Tags",
  "description": "Product tags",
  "type": "array",
  "mutable": false,
  "items": {
    "type": "string"
  }
}
```

## 4. Usage Guidelines

### Defining Transaction Contexts

When registering transaction contexts:
1. Include only properties essential for rule targeting or modification to optimize evaluation
2. Mark properties as mutable only if rule sets should modify them
3. Ensure sufficient properties are defined for effective rule targeting

### Mutability Considerations

When determining which properties should be mutable:

1. Consider business impact of allowing rule sets to modify the property
2. Set appropriate constraints to ensure modifications remain within valid ranges
3. Be selective - only mark properties as mutable if there's a genuine need for rule sets to modify them
4. Immutable properties can still be used for targeting in rule conditions
5. Objects and arrays themselves should typically not be mutable, but individual properties within objects can be

### Constraint Best Practices

1. Always define constraints for mutable properties
2. Use minimum and maximum values for numeric properties
3. Use pattern constraints for string properties that must follow specific formats
4. Keep constraints as specific as possible while allowing flexibility for rule sets

### Naming Conventions

To ensure consistency and readability across entity registrations, follow these naming conventions:

1. Context Identifiers:
   - Use UPPER_SNAKE_CASE for `contextId`
   - Examples: RETAIL_POS, PURCHASE_TRANSACTION

2. Property Identifiers:
   - Use camelCase for `propertyId`
   - Examples: customerId, totalAmount, phoneNumber

3. Enum Values:
   - Use UPPER_SNAKE_CASE
   - Examples: NEW_CUSTOMER, PAYMENT_COMPLETED

## 5. Examples

### Complete Entity Registration

```json
{
  "name": "E-commerce Platform",
  "description": "Online shopping platform",
  "transactionContexts": [
    {
      "contextId": "PURCHASE",
      "name": "Product Purchase",
      "description": "Customer purchasing products",
      "properties": [
        {
          "propertyId": "customerId",
          "name": "Customer ID",
          "description": "Unique identifier for the customer",
          "type": "string",
          "mutable": false
        },
        {
          "propertyId": "productPrice",
          "name": "Product Price",
          "description": "Price of the product",
          "type": "number",
          "mutable": true,
          "constraints": {
            "min": 0
          }
        },
        {
          "propertyId": "discountPercentage",
          "name": "Discount Percentage",
          "description": "Discount percentage",
          "type": "number",
          "mutable": true,
          "constraints": {
            "min": 0,
            "max": 50
          }
        },
        {
          "propertyId": "customerType",
          "name": "Customer Type",
          "description": "Type of customer",
          "type": "enum",
          "mutable": false,
          "values": ["NEW", "RETURNING", "LOYAL", "VIP"]
        },
        {
          "propertyId": "shippingAddress",
          "name": "Shipping Address",
          "description": "Customer's shipping address",
          "type": "object",
          "mutable": false,
          "properties": [
            {
              "propertyId": "street",
              "name": "Street",
              "description": "Street address",
              "type": "string",
              "mutable": false
            },
            {
              "propertyId": "city",
              "name": "City",
              "description": "City name",
              "type": "string",
              "mutable": false
            },
            {
              "propertyId": "postalCode",
              "name": "Postal Code",
              "description": "Postal code",
              "type": "string",
              "mutable": false
            },
            {
              "propertyId": "country",
              "name": "Country",
              "description": "Country",
              "type": "string",
              "mutable": false
            }
          ]
        }
      ]
    },
    {
      "contextId": "CART_ABANDONMENT",
      "name": "Cart Abandonment",
      "description": "Customer abandoned cart without completing purchase",
      "properties": [
        {
          "propertyId": "customerId",
          "name": "Customer ID",
          "description": "Unique identifier for the customer",
          "type": "string",
          "mutable": false
        },
        {
          "propertyId": "cartTotal",
          "name": "Cart Total",
          "description": "Total value of abandoned cart",
          "type": "number",
          "mutable": false
        },
        {
          "propertyId": "reminderDelay",
          "name": "Reminder Delay",
          "description": "Delay before sending reminder (hours)",
          "type": "number",
          "mutable": true,
          "constraints": {
            "min": 1,
            "max": 48
          }
        },
        {
          "propertyId": "offerType",
          "name": "Offer Type",
          "description": "Type of offer to send",
          "type": "enum",
          "mutable": true,
          "values": ["DISCOUNT", "FREE_SHIPPING", "BONUS_ITEM"],
          "constraints": {
            "validValues": ["DISCOUNT", "FREE_SHIPPING"]
          }
        }
      ]
    }
  ]
}
```

This is the request structure for POST /entities/registration. The server assigns a UUID entityId (e.g., 550e8400-e29b-41d4-a716-************) returned in the response, not included here.

## Related Documents

- [[Rule Set Schema]]
- [[Transaction Request Schema]]
- [[Transaction Response Schema]]
- [[Entity Integration API]]
- [[Property Modification Guide]]

## Approvals

| Role/Department       | Name | Date | Signature |
| --------------------- | ---- | ---- | --------- |
| Lead Architect        |      |      |           |
| API Team Lead         |      |      |           |
| Documentation Manager |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                 |
|---------|----------------|-----------------|--------------------------------------------------------|
| 2.5.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated property field naming to align with RuleForge Naming Convention Guide: changed `name` to `propertyId` for property identifiers, added dedicated `name` field as human-readable name; revised all examples to follow the convention; updated terminology to refer to `propertyId` consistently throughout |
| 2.4.0   | [[2025-04-03]] | [[Wayne Smith]] | Further clarification of `values` vs `validValues` in 'Enum Properties'; enhanced 'Defining Transaction Contexts' guidance in 'Usage Guidelines'; ensured consistent use of rule set terminology throughout |
| 2.3.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated terminology from "campaign" to "rule set" throughout document; replaced references to "Campaign Rules Engine (CRE)" with "RuleForge Rules Engine"; standardized all descriptions to use rule-centric terminology |
| 2.2.0   | [[2025-04-02]] | [[Wayne Smith]] | Relocated `entityId` notes from 'Entity Metadata' and 'Naming Conventions' to 'Top-Level Structure'; clarified `values` vs `validValues` in 'Enum Properties'; added 'Defining Transaction Contexts' to 'Usage Guidelines' from Transaction Request Schema |
| 2.1.0   | [[2025-04-01]] | [[Wayne Smith]] | Removed entityId from request structure (now server-generated per API spec v1.1.0), updated naming conventions, aligned example with API |
| 2.0.0   | [[2025-03-28]] | [[Wayne Smith]] | - Added `mutable` flag to property definitions to support Property Modification Framework<br>- Added `constraints` object for defining property modification constraints<br>- Updated property types to use "number" instead of multiple numeric types (integer, amount, float)<br>- Removed all action-related components (globalActions and contextSpecificActions)<br>- Added usage guidelines for defining mutable properties<br>- Updated examples to showcase property mutability and constraints |
| 1.3.0   | [[2024-09-17]] | [[Wayne Smith]] | - Added support for enum type in GlobalActions parameters<br>- Clarified naming conventions for different elements (PascalCase, camelCase, UPPER_SNAKE_CASE)<br>- Updated example to demonstrate enum usage in GlobalActions<br>- Added new section on Specific Naming Conventions |
| 1.2.0   | [[2024-09-14]] | [[Wayne Smith]] | - Standardized action type names to use PascalCase<br>- Added 'array' and 'object' to allowed property types<br>- Updated examples to showcase new types and naming convention<br>- Added section on Action Type Naming Convention<br>- Updated transaction contexts schema for consistent nested object properties<br>- Modified 'properties' structure in transaction contexts to use arrays of property objects<br>- Ensured consistency between top-level and nested object property definitions<br>- Updated example to reflect new schema structure |
| 1.1.0   | [[2024-09-13]] | [[Wayne Smith]] | - Updated action identifier from 'actionId' to 'type' throughout the document<br>- Aligned document structure with Root Document Template v1.0.1<br>- Added Table of Contents<br>- Expanded and clarified context-specific actions section<br>- Updated YAML front matter<br>- Added Document Specification and Approvals sections<br>- Added "enum" as a new property type with `values` field for specifying allowed values<br>- Removed `enumValues` for string properties to simplify the schema |
| 1.0.0   | [[2024-08-27]] | [[Faraz Ali]] | Initial version of the Entity Registration JSON Structure document |

---

© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->
