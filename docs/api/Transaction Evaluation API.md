---
title: Transaction Evaluation API
classification: Confidential
created: 2025-03-28
updated: 2025-06-05
authors:
  - "[[<PERSON>]]"
  - "[[<PERSON><PERSON><PERSON>]]"
version: 2.3.0
next-review: 2099-10-28
category: "[[Specifications]]"
tags:
  - specification
  - api
  - reference
topics:
  - "[[Property Modification Framework]]"
  - "[[API]]"
  - "[[RuleForge]]"
  - "[[Integration]]"
  - "[[Transaction Evaluation]]"
---

# Transaction Evaluation API

> **Confidential**: This document is provided to you as a partner integrating with the RuleForge platform. It contains information about our API that should not be shared with third parties.

## Document Specification

### Purpose

This document provides the API specification for transaction evaluation within the RuleForge Rules Engine. It focuses specifically on transaction evaluation endpoints needed by integration partners to submit transactions for evaluation and report their outcomes.

### Scope

This specification covers:

- Authentication using API keys
- Transaction evaluation endpoints
- Transaction outcome notification endpoints
- Data structures for transaction requests, responses, and outcomes
- Error handling and status codes

This document does not cover:

- Entity registration (covered in [[Entity Integration API]])
- Rule set management
- The internal implementation of the RuleForge Rules Engine

### Target Audience

- Developers implementing RuleForge integration
- System architects designing transaction evaluation workflows
- QA engineers testing transaction evaluation

## 1. Introduction

### 1.1 API Overview

The Transaction Evaluation API provides endpoints for submitting transactions to the RuleForge Rules Engine. It evaluates these transactions against active rule sets and returns the resulting mutable property modifications, enabling your system to apply these changes.

### 1.2 API Versioning

The API uses semantic versioning (MAJOR.MINOR.PATCH) and includes the major version in the URL path:

Base URL: `https://api.ruleforge.net/v1`

When breaking changes are introduced, a new major version will be released, and the previous version will be supported for a minimum of 6 months to allow for migration.

### 1.3 Authentication
All API requests must include an `Authorization` header with a valid API key:

```
Authorization: Bearer your_api_key
```

API keys are provided by the RuleForge system administrators. They do not expire unless revoked by an administrator.

## 2. API Key Management

API keys are managed by RuleForge administrators. Each organisation is provided with a unique API key that authorises actions across all its registered entities. Include this key in every API request.

### API Key Best Practices

1. **Security**: Store your API key securely in your configuration management system
2. **Do not share**: Each organisation should have one unique API key for all its entities.
3. **Rotation**: If you suspect your key has been compromised, contact RuleForge administrators to have it revoked and replaced
4. **Backend usage only**: Never expose API keys in client-side code or public repositories

## 3. Transaction Evaluation

> **Important**: Each API key is associated with an organization that may have multiple entities. Requests must include the `entityId` to specify which entity's transaction is being evaluated.

### 3.1 Evaluate Transaction

- **Endpoint**: `/evaluate`
- **Method**: POST
- **Description**: Evaluates a transaction against applicable rule sets' **evaluation rules** and returns property modifications
- **Request Body**: Transaction evaluation request JSON (see [[Transaction Request Schema]])
- **Response**: Transaction evaluation response JSON with modified properties (see [[Transaction Response Schema]])


#### Example Request

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "PURCHASE",
  "transactionId": "TXN123456789",
  "timestamp": "2025-03-15T14:30:00Z",
  "transactionData": {
    "customerId": "CUST123456",
    "productPrice": 99.99,
    "discountPercentage": 0,
    "shippingCost": 5.99,
    "customerType": "VIP",
    "paymentMethod": "CREDIT_CARD",
    "deviceType": "MOBILE"
  }
}
```
> **Note**: The entityId must match an entity registered under your organisation's API key (see [[Entity Integration API]]).

#### Example Response

```json
{
  "transactionId": "TXN123456789",
  "modifiedProperties": {
    "productPrice": {
      "original": 99.99,
      "modified": 89.99,
      "ruleSetId": "PRICING_RULES_2025",
      "ruleId": "SUMMER_DISCOUNT_10"
    },
    "discountPercentage": {
      "original": 0,
      "modified": 10,
      "ruleSetId": "PRICING_RULES_2025",
      "ruleId": "SUMMER_DISCOUNT_10"
    },
    "shippingCost": {
      "original": 5.99,
      "modified": 0,
      "ruleSetId": "LOYALTY_PROGRAM",
      "ruleId": "FREE_SHIPPING_VIP"
    }
  }
}
```

#### Response with No Modifications

```json
{
  "transactionId": "TXN987654321",
  "modifiedProperties": {}
}
```

### 3.2 Submit Transaction Outcome

- **Endpoint**: `/outcomes`
- **Method**: POST
- **Description**: Submits the outcome of a transaction after property modifications have been applied and triggers execution of applicable rule sets' **outcome rules**
- **Request Body**: Transaction outcome notification JSON (see [[Transaction Outcome Schema]])
- **Response**: 
  - Status: 202 Accepted
  - Body:
    ```json
    {
      "transactionId": "string",
      "message": "Outcome notification received"
    }
    ```

#### Example Request

```json
{
  "entityId": "550e8400-e29b-41d4-a716-446655440000",
  "contextId": "PURCHASE",
  "transactionId": "TXN123456789",
  "timestamp": "2025-03-15T14:35:00Z",
  "status": "COMPLETED",
  "transactionData": {
    "customerId": "CUST123456",
    "productPrice": 89.99,
    "discountPercentage": 10,
    "shippingCost": 0,
    "totalAmount": 89.99
  },
  "modificationStatus": {
    "modificationsApplied": ["productPrice", "discountPercentage", "shippingCost"],
    "modificationsRejected": []
  },
  "additionalData": {
    "conversionTime": 280,
    "deviceType": "MOBILE",
    "paymentMethod": "CREDIT_CARD"
  }
}
```

> **Note**: The entityId must match an entity registered under your organisation's API key (see [[Entity Integration API]]).

## 4. Rule Execution Model

The RuleForge system processes rules in two distinct phases that correspond to the transaction lifecycle:

1. **Evaluation Phase**: During calls to the `/evaluate` endpoint, only rules in the `evaluationRules` collection of applicable rule sets are executed. These rules focus on modifying transaction properties before the transaction is completed.

2. **Outcome Phase**: During calls to the `/outcomes` endpoint, only rules in the `outcomeRules` collection of applicable rule sets are executed. These rules focus on post-transaction processing, such as loyalty point assignment or follow-up actions.

This separation ensures that rules are executed at the appropriate point in the transaction lifecycle, with clear boundaries between modification logic and outcome processing logic.

## 5. Error Handling

The transaction evaluation endpoint may return the following specific error responses:

### 400 Bad Request: Invalid Transaction Context or Data

```json
{
  "error": {
    "code": "INVALID_TRANSACTION_DATA",
    "message": "Transaction data contains invalid property values",
    "details": {
      "invalidProperties": ["productPrice", "discountPercentage"]
    }
  },
  "requestId": "string"
}
```

### 404 Not Found: Transaction Context Not Found

```json
{
  "error": {
    "code": "CONTEXT_NOT_FOUND",
    "message": "Transaction context not found",
    "details": {
      "contextId": "UNKNOWN_CONTEXT"
    }
  },
  "requestId": "string"
}
```

### 422 Unprocessable Entity: Property Modification Constraint Violation

```json
{
  "error": {
    "code": "CONSTRAINT_VIOLATION",
    "message": "Property modification violates constraints",
    "details": {
      "properties": {
        "discountPercentage": {
          "constraint": "max",
          "value": 60,
          "limit": 50
        }
      }
    }
  },
  "requestId": "string"
}
```

Other standard HTTP status codes may also be returned:

- 401 Unauthorized: Missing API key
  ```json
  {
    "error": {
      "code": "MISSING_API_KEY",
      "message": "API key is required",
      "details": {}
    },
    "requestId": "string"
  }
  ```

- 403 Forbidden: Invalid API key or insufficient permissions
  ```json
  {
    "error": {
      "code": "INVALID_API_KEY",
      "message": "API key is invalid or has been revoked",
      "details": {}
    },
    "requestId": "string"
  }
  ```

- 429 Too Many Requests: Rate limit exceeded
  ```json
  {
    "error": {
      "code": "RATE_LIMIT_EXCEEDED",
      "message": "Rate limit has been exceeded",
      "details": {
        "retryAfter": 30
      }
    },
    "requestId": "string"
  }
  ```

- 500 Internal Server Error: Server-side error

## 6. Rate Limiting

- The API is rate limited to protect system resources
- Rate limits are applied per API key
- Current rate limit for transaction evaluation: 500 requests per minute
- Rate limit headers are included in all responses:
  - `X-RateLimit-Limit`: The number of allowed requests in the current period
  - `X-RateLimit-Remaining`: The number of remaining requests in the current period
  - `X-RateLimit-Reset`: The time at which the current rate limit window resets in UTC epoch seconds

## 7. Integration Best Practices

### 7.1 Transaction Evaluation Considerations

When submitting transactions for evaluation:

#### Transaction Data Requirements

1. Include all properties defined in your entity's transaction context registration
   - Properties must match the types defined in your entity registration
   - For properties without values, include them with `null` values rather than omitting them
   - The system uses both mutable and immutable properties for rule targeting

2. Use the actual transaction time for the `timestamp` field, not the API request time
   - This ensures accurate time-based targeting and reporting

3. Generate a unique `transactionId` for each transaction
   - This enables proper tracking, debugging, and outcome correlation
   - IDs should be unique within your system

4. Set appropriate timeouts for evaluation requests
   - Recommended timeout: 500ms
   - Balance between responsiveness and allowing sufficient processing time

5. Implement fallback logic for when the service is unavailable
   - Define clear default behavior for when the evaluation service cannot be reached
   - Consider using circuit breaker patterns for resilience

### 7.2 Transaction Outcome Reporting

When reporting transaction outcomes:

1. Send outcome notifications for all evaluated transactions
   - Include transactions both with and without property modifications
   - This provides complete data for analytics and reporting

2. Report outcomes asynchronously to avoid blocking your transaction flow
   - Use background processing when possible
   - Consider batching outcome notifications for very high-volume scenarios

3. Include all modified properties in the outcome data
   - The final values should reflect the state after all business logic was applied
   - Include derived values that were affected by property modifications

4. Clearly indicate which modifications were applied and which were rejected
   - Use the `modificationStatus` object to provide this information
   - Include reasons for rejections when available

5. Implement retry logic for failed outcome notifications
   - Use exponential backoff for retries
   - Set appropriate retry limits

### 7.3 Performance Optimization

For high-volume transaction evaluation:

1. Implement connection pooling for HTTP requests
   - Reuse connections to reduce connection establishment overhead
   - Configure appropriate pool sizes based on your traffic patterns

2. Use persistent HTTP connections
   - Enable keep-alive in your HTTP client
   - Configure appropriate keep-alive timeouts

3. Use a high-performance HTTP client library
   - Choose libraries with async/non-blocking capabilities
   - Consider libraries with built-in resilience features

4. Implement client-side caching where appropriate
   - Cache immutable reference data
   - Do not cache transaction-specific results

5. Implement circuit breakers for resilience
   - Prevent cascading failures during service disruptions
   - Configure appropriate thresholds and recovery periods

6. Monitor response times and adjust timeout values accordingly
   - Collect metrics on evaluation response times
   - Periodically review and adjust timeout configurations

### 7.4 Applying Property Modifications

When processing the response:

1. Apply all modified properties atomically to maintain consistency
   - Consider using a transaction or similar mechanism
   - Either apply all modifications or none

2. Update any derived values after applying all modifications
   - Recalculate totals, discounts, and other derived values
   - Ensure all dependent calculations reflect the modified values

3. Log the original and modified values for auditing purposes
   - Include the rule set and rule IDs that triggered each modification
   - This helps with debugging and analytics

4. Consider implementing additional validation checks on modified values
   - Apply any business rules not covered by property constraints
   - Document why modifications were rejected if they fail validation

## Related Documents

- [[Transaction Request Schema]]
- [[Transaction Response Schema]]
- [[Transaction Outcome Schema]]
- [[Entity Integration API]]
- [[Property Modification Guide]]
- [[Rule Set Schema]]

## Approvals

| Role/Department    | Name | Date | Signature |
|--------------------|------|------|-----------|
| API Team Lead      |      |      |           |
| Integration Lead   |      |      |           |
| Security Officer   |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                                                                                                                                                                                            |
| ------- | -------------- | --------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 2.3.0  | [[2025-06-05]] | [[Lourens Coetzer ]] | Changed the modification status structure |
| 2.2.0   | [[2025-04-07]] | [[Wayne Smith]] | Updated to align with Rule Set Schema v6.0.0: added Rule Execution Model section explaining the separate execution of evaluation rules and outcome rules; clarified that `/evaluate` runs evaluation rules and `/outcomes` runs outcome rules |
| 2.1.0   | [[2025-04-05]] | [[Wayne Smith]] | Standardized field naming: changed 'transactionContextId' to 'contextId' for consistency across the RuleForge documentation                                                                                                        |
| 2.0.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated terminology from "campaign" to "rule set" for consistency across documentation; updated examples to show `ruleSetId` instead of `campaignId`; renamed "Campaign Rules Engine (CRE)" to "RuleForge Rules Engine" throughout |
| 1.1.0   | [[2025-04-01]] | [[Wayne Smith]] | Updated API key to org-level, added entityId to /evaluate and /outcomes requests, switched X-API-Key to Authorization: Bearer, aligned with Entity Integration API Spec v1.1.0                                                     |
| 1.0.0   | [[2025-03-28]] | [[Wayne Smith]] | Initial version of the Transaction Evaluation API Specification                                                                                                                                                                    |


---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->
