---
title: "Transaction Outcome Notification Schema"
classification: Confidential
created: 2025-03-28
updated: 2025-06-05
authors:
  - "[[<PERSON>]]"
  - "[[<PERSON><PERSON><PERSON>]]"
version: 2.4.0
next-review: 2025-10-28
category: "[[Specifications]]"
tags:
  - specification
  - schema
  - json
topics:
  - "[[Property Modification Framework]]"
  - "[[Transaction Evaluation]]"
  - "[[API]]"
  - "[[RuleForge]]"
---

# Transaction Outcome Notification Schema

## Document Specification

### Purpose
This document defines the JSON structure for transaction outcome notifications in the RuleForge Rules Engine. It specifies the format, content, and constraints of the outcome data sent back to the Rules Engine after a transaction with modified properties has been processed by an entity.

### Scope
This document covers:
- The JSON structure for transaction outcome notifications
- Definitions of essential components (transaction identification, outcome status, applied modifications)
- Data types and constraints for each field
- Examples of valid outcome notifications

This document does not cover:
- The internal processing of outcome notifications by the Rules Engine
- The structure of the transaction evaluation request or response
- Detailed business logic of how outcomes affect analytics or reporting

### Target Audience
- Developers integrating client systems with RuleForge
- QA engineers validating transaction outcomes
- Technical writers creating transaction-related documentation

## Table of Contents

- [[#1. JSON Structure]]
- [[#2. Component Definitions]]
- [[#2A. Standard Properties]]
- [[#3. Constraints and Validation]]
- [[#4. Examples]]
- [[#5. Outcome Rule Execution]]

## 1. JSON Structure

A transaction outcome notification is represented by a JSON object with the following structure:

```json
{
  "entityId": "string",
  "contextId": "string",
  "transactionId": "string",
  "timestamp": "string",
  "status": "string",
  "transactionData": {
    "property1": "value1",
    "property2": "value2",
    ...
  },
  "modificationStatus": {
    "modificationsApplied": ["string"],
    "modificationsRejected": [
      {
        "propertyId": "string",
        "reason": "string"
      }
    ]
  },
  "additionalData": {
    "key1": "value1",
    "key2": "value2",
    ...
  }
}
```

> **Note:** The `entityId` is required in the request and must match an entity registered under your organisation's API key.

## 2. Component Definitions

### Entity and Transaction Identification
- `entityId`: Identifier of the entity sending the outcome notification (string, UUID format, e.g., `550e8400-e29b-41d4-a716-************`)
- `contextId`: Identifier of the transaction context (string, max 50 characters, UPPER_SNAKE_CASE)
- `transactionId`: Unique identifier for the specific transaction, matching the ID from the evaluation request (string, max 50 characters)
- `timestamp`: Date and time when the transaction was completed (string, ISO 8601 format)

### Outcome Status
- `status`: Final status of the transaction (string, enum: "COMPLETED", "FAILED", "ABANDONED")
  - `COMPLETED`: Transaction was successfully processed with or without modifications
  - `FAILED`: Transaction processing failed (e.g., payment declined, system error)
  - `ABANDONED`: Transaction was abandoned (e.g., user cancelled, timeout)

### Final Transaction Data
- `transactionData`: An object containing the final values of transaction properties after modifications were applied
  - Should include at minimum all properties that were modified during evaluation
  - Values should reflect the final state after all business logic was applied

### Modification Status
- `modificationStatus`: An object indicating which property modifications were applied or rejected
  - `modificationsApplied`: Array of property identifiers that were successfully modified
  - `modificationsRejected`: Array of objects for modifications that couldn't be modified, each containing:
    - `propertyId`: Identifier of the property that couldn't be modified
    - `reason`: Reason why the modification was rejected (string)

### Additional Data
- `additionalData`: Optional object containing any additional analytics data relevant to the transaction
  - Can include business-specific metrics or values
  - Useful for rule set effectiveness measurement

## 2A. Standard Properties

In addition to the explicitly defined properties in the transaction outcome notification, several standard properties are automatically available in the outcome phase. These properties can be referenced in rule conditions and variable assignments using the same curly brace notation as entity-defined properties.

The following standard properties are available during outcome processing:

| Property ID | Description | Type | Format |
|-------------|-------------|------|--------|
| `transactionId` | Unique identifier for the transaction (matches the `transactionId` field in the notification) | string | max 50 characters |
| `entityId` | Identifier of the entity sending the transaction (matches the `entityId` field in the notification) | string | UUID format |
| `contextId` | Identifier of the transaction context (matches the `contextId` field in the notification) | string | UPPER_SNAKE_CASE |
| `timestamp` | Date and time when the transaction occurred (matches the `timestamp` field in the notification) | date | ISO 8601 format |
| `status` | Transaction status (matches the `status` field in the notification) | enum | "COMPLETED", "FAILED", "ABANDONED" |
| `modificationsApplied` | Array of property identifiers that were successfully modified (derived from `modificationStatus.applied`) | array of strings | Property IDs |
| `modificationsRejected` | Array of property identifiers that couldn't be modified (derived from `modificationStatus.rejected`) | array of strings | Property IDs |

These standard properties can be referenced in outcome rules using the format `{propertyId}`, for example:

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": "==",
  "parameters": {
    "leftOperand": "{status}",
    "rightOperand": "COMPLETED"
  }
}
```

The `modificationsApplied` property can be used with the `IN` operator to check if a specific property was modified:

```json
{
  "conditionTypeId": "COMPARISON",
  "operator": "IN",
  "parameters": {
    "leftOperand": "productPrice",
    "rightOperand": "{modificationsApplied}"
  }
}
```

For a complete list of standard properties and their usage, see the [[Standard Transaction Properties Schema]].

## 3. Constraints and Validation

### Required Fields
- `transactionId`, `contextId`, `timestamp`, `status`, and `transactionData` are required
- `entityId` is required when not using API key authentication
- `modificationStatus` is required when the original evaluation response contained property modifications

### Field Constraints
- `transactionId` must match the ID provided in the original evaluation request
- `timestamp` must be in valid ISO 8601 format
- `status` must be one of the allowed values: "COMPLETED", "FAILED", or "ABANDONED"
- Property values in `transactionData` should conform to the data types defined in the entity registration

## 4. Examples

### Successful Transaction with Applied Modifications

```json
{
  "entityId": "550e8400-e29b-41d4-a716-************",
  "contextId": "PURCHASE",
  "transactionId": "TXN123456789",
  "timestamp": "2025-03-15T14:35:00Z",
  "status": "COMPLETED",
  "transactionData": {
    "customerId": "CUST123456",
    "productPrice": 89.99,
    "discountPercentage": 10,
    "shippingCost": 0,
    "totalAmount": 89.99
  },
  "modificationStatus": {
    "modificationsApplied": ["productPrice", "discountPercentage", "shippingCost"],
    "modificationsRejected": []
  },
  "additionalData": {
    "conversionTime": 280,
    "deviceType": "MOBILE",
    "paymentMethod": "CREDIT_CARD"
  }
}
```

### Transaction with Partially Applied Modifications

```json
{
  "entityId": "550e8400-e29b-41d4-a716-************",
  "contextId": "PURCHASE",
  "transactionId": "TXN987654321",
  "timestamp": "2025-03-16T09:45:30Z",
  "status": "COMPLETED",
  "transactionData": {
    "customerId": "CUST789012",
    "productPrice": 135.00,
    "discountPercentage": 10,
    "shippingCost": 12.99,
    "totalAmount": 147.99
  },
  "modificationStatus": {
    "modificationsApplied": ["discountPercentage"],
    "modificationsRejected": [
      {
        "propertyId": "shippingCost",
        "reason": "BUSINESS_RULE_VIOLATION"
      }
    ]
  },
  "additionalData": {
    "conversionTime": 320,
    "deviceType": "DESKTOP",
    "customerSegment": "NEW"
  }
}
```

### Failed Transaction

```json
{
  "entityId": "550e8400-e29b-41d4-a716-************",
  "contextId": "PURCHASE",
  "transactionId": "TXN456789012",
  "timestamp": "2025-03-17T16:20:15Z",
  "status": "FAILED",
  "transactionData": {
    "customerId": "CUST345678",
    "productPrice": 359.99,
    "discountPercentage": 15,
    "shippingCost": 0,
    "totalAmount": 305.99
  },
  "modificationStatus": {
    "modificationsApplied": ["productPrice", "discountPercentage", "shippingCost"],
    "modificationsRejected": []
  },
  "additionalData": {
    "failureReason": "PAYMENT_DECLINED",
    "failureCode": "INSUFFICIENT_FUNDS",
    "attemptCount": 1
  }
}
```

### Abandoned Transaction

```json
{
  "entityId": "550e8400-e29b-41d4-a716-************",
  "contextId": "PURCHASE",
  "transactionId": "TXN567890123",
  "timestamp": "2025-03-18T10:05:45Z",
  "status": "ABANDONED",
  "transactionData": {
    "customerId": "CUST456789",
    "productPrice": 199.99,
    "discountPercentage": 5,
    "shippingCost": 8.99,
    "totalAmount": 198.98
  },
  "modificationStatus": {
    "modificationsApplied": ["discountPercentage"],
    "modificationsRejected": []
  },
  "additionalData": {
    "abandonmentReason": "USER_CANCELLED",
    "timeSpentSeconds": 145,
    "pageReached": "PAYMENT"
  }
}
```

## 5. Outcome Rule Execution

When a transaction outcome notification is received by the RuleForge system through the `/outcomes` endpoint, the system processes the notification and executes the relevant **outcome rules**. Unlike evaluation rules (which run during the initial transaction evaluation phase), outcome rules are specifically designed to process transaction results after they have completed or failed.

### 5.1 Rule Execution Flow

1. The system receives a transaction outcome notification via the `/outcomes` endpoint
2. It validates the notification format and required fields
3. It identifies all active rule sets applicable to the entity and context
4. **It executes only the rules in the `outcomeRules` collection** of those rule sets
5. The rules are executed in order of priority (lower numbers indicate higher priority)
6. Any state changes from outcome rules (such as persistent variable updates) are stored

### 5.2 Common Outcome Rule Use Cases

Outcome rules are ideal for:

1. **Loyalty Point Calculation**: Awarding points based on completed transactions
2. **Customer Profile Updates**: Updating persistent variables like lifetime value or purchase count
3. **Follow-up Action Determination**: Setting flags or statuses for follow-up actions based on transaction outcomes
4. **Analytics Enrichment**: Adding derived metrics or categorization for reporting purposes
5. **Abandoned Cart Recovery**: Initiating recovery processes for abandoned transactions

### 5.3 Outcome Rule Constraints

When working with outcome rules:

1. Outcome rules have access to all transaction data, including the final state of properties
2. Outcome rules can access transaction status information (`COMPLETED`, `FAILED`, `ABANDONED`)
3. Outcome rules cannot modify transaction properties (as the transaction has already completed)
4. Outcome rules primarily update persistent variables and generate analytics data

## Usage Guidelines

### When to Send Outcome Notifications

Entities should send outcome notifications in the following scenarios:

1. After successfully completing a transaction with modified properties
2. When a transaction fails after property modifications have been applied
3. When a transaction is abandoned after property modifications have been applied
4. Anytime meaningful analytics about a transaction's outcome can be provided

### Performance Considerations

For optimal system performance:

1. Send outcome notifications asynchronously when possible
2. Implement a queuing mechanism for high-volume scenarios
3. Include only relevant properties in the transactionData
4. Set appropriate timeouts for outcome notification requests

### Data Privacy

When sending outcome notifications:

1. Do not include sensitive customer data (PII)
2. Follow data minimization principles
3. Ensure compliance with relevant data protection regulations

## Related Documents

- [[Transaction Request Schema]]
- [[Transaction Response Schema]]
- [[Transaction Evaluation API]]
- [[Entity Integration API]]
- [[Property Modification Guide]]
- [[Rule Set Schema]]
- [[Standard Transaction Properties Schema]]

## Approvals

| Role/Department    | Name | Date | Signature |
|--------------------|------|------|-----------|
| API Team Lead      |      |      |           |
| Integration Lead   |      |      |           |
| Security Officer   |      |      |           |

## Changelog

| Version | Date           | Author          | Changes                                                |
|---------|----------------|-----------------|--------------------------------------------------------|
| 2.5.0  | [[2025-06-05]] | [[Lourens Coetzer ]] | Changed the modification status structure |
| 2.4.0   | [[2025-04-09]] | [[Wayne Smith]] | Added section on standard properties available during outcome processing; clarified the flat structure of standard properties derived from the modificationStatus object |
| 2.3.0   | [[2025-04-07]] | [[Wayne Smith]] | Added new "Outcome Rule Execution" section explaining how transaction outcome notifications trigger outcome rules; clarified the execution flow, use cases, and constraints for outcome rules; added reference to Rule Set Schema in related documents |
| 2.2.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated field naming to align with RuleForge Naming Convention Guide: standardized field names to consistently use `contextId`; updated references to `propertyId` throughout; fixed field names in rejected modifications from `property` to `propertyId` |
| 2.1.0   | [[2025-04-03]] | [[Wayne Smith]] | Standardized field naming: changed 'transactionContextId' to 'contextId' for consistency across the RuleForge documentation |
| 2.0.0   | [[2025-04-03]] | [[Wayne Smith]] | Updated terminology from "campaign" to "rule set" for consistency across documentation; standardized entityId format to UUID; renamed document from "Transaction Outcome Notification JSON Structure" to "Transaction Outcome Notification Schema" |
| 1.0.0   | [[2025-03-28]] | [[Wayne Smith]] | Initial version of the Transaction Outcome Notification JSON Structure |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->
