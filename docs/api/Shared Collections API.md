---
title: Shared Collections API
classification: Confidential
created: 2025-09-17
updated: 2025-09-17
authors:
  - "[[<PERSON><PERSON><PERSON>]]"
version: 1.0.0
next-review: 2026-03-17
category: "[[Specifications]]"
tags:
  - specification
  - api
  - reference
  - collections
topics:
  - "[[Property Modification Framework]]"
  - "[[API]]"
  - "[[RuleForge]]"
  - "[[Shared Collections]]"
  - "[[Persistent Variables]]"
---

# Shared Collections API

## Document Specification

### Purpose

This document provides the API specification for shared collections management. It focuses specifically on the endpoints needed for creating, retrieving, updating, and deleting shared collections that contain persistent variables accessible across multiple rule sets.

### Scope

This specification covers:

- Authentication mechanisms for accessing shared collections endpoints
- Shared collection CRUD operation endpoints
- Data structures for shared collection definitions
- Status-based operation restrictions (LINKED, UNLINKED, LOCKED)
- Persistent variable management within collections
- Error handling and status codes related to shared collections

This document does not cover:

- The internal implementation of collection storage
- The GUI implementation for collection management
- How collections are evaluated during rule execution
- Rule set integration with collections (covered in [[Rule Set Schema]])

### Target Audience

- Developers implementing collection management functionality
- System architects designing collection integration
- QA engineers testing collection management features
- Integration specialists working with the RuleForge system

## Table of Contents

- [[#1. Introduction]]
- [[#2. API Endpoints]]
- [[#3. Data Structures]]
- [[#4. Status Management]]
- [[#5. Error Handling]]
- [[#6. Usage Examples]]

## 1. Introduction

### 1.1 API Overview

The Shared Collections API provides endpoints that allow for the creation, retrieval, update, and deletion of shared collections within the RuleForge system. These collections contain persistent variables that can be shared across multiple rule sets, enabling consistent state management and data reuse.

### 1.2 API Versioning

The API uses semantic versioning (MAJOR.MINOR.PATCH) and includes the major version in the URL path, consistent with other RuleForge APIs:

Base URL: `https://api.ruleforge.net/v1`

### 1.3 Authentication

All Shared Collections API endpoints use the same authentication method as other RuleForge APIs. All requests must include an `Authorization` header with a valid bearer token:

```
Authorization: Bearer your_token
```

### 1.4 Collection Status Model

Shared collections have three possible statuses that control their lifecycle:

- **UNLINKED**: Collection exists but is not referenced by any rule sets. Can be updated and deleted.
- **LINKED**: Collection is referenced by one or more rule sets and all of the rule sets status is 'draft'. Can be updated and deleted.
- **LOCKED**: Collection is referenced by one or more rule sets and any of the rule sets status is not 'draft'. Cannot be updated or deleted.

### 1.5 Schema and Version Management

Each shared collection includes two versioning fields:

#### Schema Version
- `schemaVersion`: Version of the schema structure (string, semantic versioning format)
  - Example: "1.0.0"
  - This version represents changes to the structure itself, not the collection content
  - MAJOR version for backwards-incompatible changes
  - MINOR version for backwards-compatible additions
  - PATCH version for backwards-compatible fixes

#### Collection Version
- `version`: Version of the collection content (integer)
  - Increments when collection content is modified (name, description, persistent variables)
  - Used for tracking changes and ensuring data consistency

## 2. API Endpoints

### 2.1 Get All Collections

- **Endpoint**: `/collections`
- **Method**: GET
- **Description**: Retrieves all available shared collections with full details including persistent variables
- **Query Parameters**:
  - `page`: Page number for pagination (default: 1)
  - `pageSize`: Number of items per page (default: 20, max: 100)
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "collections": [
        {
          "schemaVersion": "1.0.0",
          "collectionId": "agents",
          "name": "Agents",
          "description": "Collection for agent-related persistent variables",
          "version": 1,
          "status": "LINKED",
          "createdDateTime": "2025-09-17T10:00:00Z",
          "lastModifiedDateTime": "2025-09-17T12:30:00Z",
          "persistentVariables": [
            {
              "variableId": "dailyBundleCount",
              "name": "Daily Bundle Count",
              "description": "The total number of bundles sold by an agent in a day",
              "type": "number",
              "defaultValue": 0
            },
            {
              "variableId": "totalSalesAmount",
              "name": "Total Sales Amount",
              "description": "The total amount of sales made by an agent so far",
              "type": "number",
              "defaultValue": 0
            }
          ]
        }
      ],
      "pagination": {
        "page": 1,
        "pageSize": 20,
        "totalItems": 1,
        "totalPages": 1
      }
    }
    ```

### 2.2 Get Collection by ID

- **Endpoint**: `/collections/{collectionId}`
- **Method**: GET
- **Description**: Retrieves a specific shared collection with all its persistent variables
- **Path Parameters**:
  - `collectionId`: Unique identifier for the collection (string, camelCase)
- **Response**:
  - Status: 200 OK
  - Body:
    ```json
    {
      "schemaVersion": "1.0.0",
      "collectionId": "agents",
      "name": "Agents",
      "description": "Collection for agent-related persistent variables",
      "version": 1,
      "status": "LINKED",
      "createdDateTime": "2025-09-17T10:00:00Z",
      "lastModifiedDateTime": "2025-09-17T12:30:00Z",
      "persistentVariables": [
        {
          "variableId": "dailyBundleCount",
          "name": "Daily Bundle Count",
          "description": "The total number of bundles sold by an agent in a day",
          "type": "number",
          "defaultValue": 0
        },
        {
          "variableId": "totalSalesAmount",
          "name": "Total Sales Amount",
          "description": "The total amount of sales made by an agent so far",
          "type": "number",
          "defaultValue": 0
        }
      ]
    }
    ```
  - Status: 404 Not Found (if collection doesn't exist)

### 2.3 Create Collection

- **Endpoint**: `/collections`
- **Method**: POST
- **Description**: Creates a new shared collection
- **Request Body**:
  ```json
  {
    "schemaVersion": "1.0.0",
    "collectionId": "agents",
    "name": "Agents",
    "description": "Collection for agent-related persistent variables",
    "persistentVariables": [
      {
        "variableId": "dailyBundleCount",
        "name": "Daily Bundle Count",
        "description": "The total number of bundles sold by an agent in a day",
        "type": "number",
        "defaultValue": 0
      },
      {
        "variableId": "totalSalesAmount",
        "name": "Total Sales Amount",
        "description": "The total amount of sales made by an agent so far",
        "type": "number",
        "defaultValue": 0
      }
    ]
  }
  ```
- **Response**:
  - Status: 201 Created
  - Body:
    ```json
    {
      "schemaVersion": "1.0.0",
      "collectionId": "agents",
      "name": "Agents",
      "description": "Collection for agent-related persistent variables",
      "version": 1,
      "status": "UNLINKED",
      "createdDateTime": "2025-09-17T10:00:00Z",
      "lastModifiedDateTime": "2025-09-17T10:00:00Z",
      "persistentVariables": [
        {
          "variableId": "dailyBundleCount",
          "name": "Daily Bundle Count",
          "description": "The total number of bundles sold by an agent in a day",
          "type": "number",
          "defaultValue": 0
        },
        {
          "variableId": "totalSalesAmount",
          "name": "Total Sales Amount",
          "description": "The total amount of sales made by an agent so far",
          "type": "number",
          "defaultValue": 0
        }
      ]
    }
    ```
  - Status: 400 Bad Request (validation errors)
  - Status: 409 Conflict (collection ID already exists)

### 2.4 Update Collection

- **Endpoint**: `/collections/{collectionId}`
- **Method**: PUT
- **Description**: Updates an existing shared collection. Only allowed if status is not "LOCKED"
- **Path Parameters**:
  - `collectionId`: Unique identifier for the collection
- **Request Body**:
  ```json
  {
    "schemaVersion": "1.0.0",
    "name": "Updated Agents",
    "description": "Updated description for agent-related persistent variables",
    "persistentVariables": [
      {
        "variableId": "dailyBundleCount",
        "name": "Daily Bundle Count",
        "description": "The total number of bundles sold by an agent in a day",
        "type": "number",
        "defaultValue": 0
      },
      {
        "variableId": "totalSalesAmount",
        "name": "Total Sales Amount",
        "description": "The total amount of sales made by an agent so far",
        "type": "number",
        "defaultValue": 0
      },
      {
        "variableId": "monthlyTarget",
        "name": "Monthly Target",
        "description": "The monthly sales target for the agent",
        "type": "number",
        "defaultValue": 1000
      }
    ]
  }
  ```
- **Response**:
  - Status: 200 OK
  - Body: Updated collection object (same structure as GET response)
  - Status: 400 Bad Request (validation errors)
  - Status: 403 Forbidden (collection is LOCKED)
  - Status: 404 Not Found (collection doesn't exist)

### 2.5 Delete Collection

- **Endpoint**: `/collections/{collectionId}`
- **Method**: DELETE
- **Description**: Deletes a shared collection. Only allowed if status is not "LOCKED"
- **Path Parameters**:
  - `collectionId`: Unique identifier for the collection
- **Response**:
  - Status: 204 No Content (successful deletion)
  - Status: 403 Forbidden (collection is LOCKED)
  - Status: 404 Not Found (collection doesn't exist)

## 3. Data Structures

### 3.1 Collection Object

```json
{
  "schemaVersion": "string",          // Version of the schema structure (semantic versioning format, e.g., "1.0.0")
  "collectionId": "string",           // Unique identifier (camelCase, max 50 chars)
  "name": "string",                   // Human-readable name (max 100 chars)
  "description": "string",            // Description of purpose (max 500 chars)
  "version": "integer",               // Version of the collection content
  "status": "string",                 // LINKED | UNLINKED | LOCKED
  "createdDateTime": "string",        // ISO 8601 format
  "lastModifiedDateTime": "string",   // ISO 8601 format
  "persistentVariables": []           // Array of PersistentVariable objects
}
```

### 3.2 Persistent Variable Object

```json
{
  "variableId": "string",      // Unique identifier within collection (camelCase, max 50 chars)
  "name": "string",            // Human-readable name (max 100 chars)
  "description": "string",     // Description of purpose (max 250 chars)
  "type": "string",            // number | string | boolean | date | enum
  "defaultValue": "any"        // Default value matching the specified type
}
```

### 3.3 Collection Status Values

| Status | Description | Update Allowed | Delete Allowed |
|--------|-------------|----------------|----------------|
| UNLINKED | Not referenced by any rule sets | ✅ Yes | ✅ Yes |
| LINKED | Referenced by one or more rule sets and all of the rule sets status is 'draft' | ✅ Yes | ✅ Yes  |
| LOCKED | Referenced by one or more rule sets and any of the rule sets status is not 'draft' | ❌ No | ❌ No |

## 4. Status Management

### 4.1 Automatic Status Transitions

- **UNLINKED → LINKED**: Occurs when a rule set references the collection
- **LINKED → UNLINKED**: Occurs when all of the rule sets stops referencing the collection
- **LINKED → LOCKED**: Occurs if any referenced rule set transitions from 'draft' to an active or published state (e.g., 'active', 'published', 'archived'). This ensures collections cannot be modified while in use by finalized rule sets.

> **Note:** Status transitions are enforced by the system to maintain data integrity and prevent unauthorized modifications to collections in use by production rule sets.


## 5. Error Handling

### 5.1 HTTP Status Codes

| Code | Description | Usage |
|------|-------------|-------|
| 200 | OK | Successful GET, PUT, PATCH operations |
| 201 | Created | Successful POST operation |
| 204 | No Content | Successful DELETE operation |
| 400 | Bad Request | Validation errors, malformed JSON |
| 401 | Unauthorized | Missing or invalid authentication |
| 403 | Forbidden | Operation not allowed due to status restrictions |
| 404 | Not Found | Collection not found |
| 409 | Conflict | Collection ID already exists |
| 422 | Unprocessable Entity | Business logic violations |
| 500 | Internal Server Error | Server-side errors |

### 5.2 Error Response Format

```json
{
  "error": {
    "code": "COLLECTION_LOCKED",
    "message": "Cannot update collection because it is locked",
    "details": {
      "collectionId": "agents",
      "status": "LOCKED",
    }
  }
}
```

### 5.3 Common Error Scenarios

#### Collection Status Violations

```json
{
  "error": {
    "code": "OPERATION_NOT_ALLOWED",
    "message": "Cannot delete collection because it is linked to active rule sets",
    "details": {
      "collectionId": "agents",
      "status": "LINKED"
    }
  }
}
```

#### Validation Errors

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid collection data",
    "details": {
      "field": "persistentVariables[0].variableId",
      "issue": "Variable ID must be unique within collection"
    }
  }
}
```

## 6. Usage Examples

### 6.1 Creating a New Collection

```bash
curl -X POST https://api.ruleforge.net/v1/collections \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "collectionId": "customers",
    "name": "Customer Data",
    "description": "Persistent variables for customer behavior tracking",
    "persistentVariables": [
      {
        "variableId": "loyaltyPoints",
        "name": "Loyalty Points",
        "description": "Current loyalty points balance",
        "type": "number",
        "defaultValue": 0
      },
      {
        "variableId": "lastPurchaseDate",
        "name": "Last Purchase Date",
        "description": "Date of the customer'\''s last purchase",
        "type": "date",
        "defaultValue": null
      }
    ]
  }'
```

### 6.2 Updating Collection Variables

```bash
curl -X PUT https://api.ruleforge.net/v1/collections/customers \
  -H "Authorization: Bearer your_token" \
  -H "Content-Type: application/json" \
  -d '{
    "schemaVersion": "1.0.0",
    "version": "1",
    "name": "Customer Data",
    "description": "Enhanced persistent variables for customer behavior tracking",
    "persistentVariables": [
      {
        "variableId": "loyaltyPoints",
        "name": "Loyalty Points",
        "description": "Current loyalty points balance",
        "type": "number",
        "defaultValue": 0
      },
      {
        "variableId": "lastPurchaseDate",
        "name": "Last Purchase Date",
        "description": "Date of the customer'\''s last purchase",
        "type": "date",
        "defaultValue": null
      },
      {
        "variableId": "preferredCategory",
        "name": "Preferred Category",
        "description": "Customer'\''s most frequently purchased category",
        "type": "string",
        "defaultValue": ""
      }
    ]
  }'
```

### 6.3 Attempting to Delete a Linked Collection

```bash
curl -X DELETE https://api.ruleforge.net/v1/collections/agents \
  -H "Authorization: Bearer your_token"

# Response: 403 Forbidden
{
  "error": {
    "code": "OPERATION_NOT_ALLOWED",
    "message": "Cannot delete collection because it is linked to active rule sets",
    "details": {
      "collectionId": "agents",
      "status": "LINKED"
    }
  }
}
```

## Integration with Rule Sets

Shared collections can be imported and used within rule sets via the [[Rule Set Schema]]. When a rule set imports a shared collection:

### Collection Mapping Integration

In rule sets, collections are referenced in the `collectionMappings` array with the `imported` flag:

```json
{
  "collectionMappings": [
    {
      "collectionId": "agents",
      "name": "Agents",
      "imported": true,
      "keyMapping": {
        "propertyId": "agentId"
      }
    }
  ]
}
```

### Variable Access

When a shared collection is imported:
- Persistent variables from the shared collection are automatically available in rule conditions and assignments
- Variables are referenced by prefixing the shared collectionId `{agents.variableId}` syntax
- No need to define variables in the rule set's `persistentVariables` array


## Related Documents

- [[Rule Set Schema]]
- [[RuleForge Management API]]
- [[Variable Assignment Schema]]
- [[Standard Transaction Properties Schema]]
- [[RuleForge Interface Overview]]

## Changelog

| Version | Date       | Author              | Changes                                                                                                                                                                                                                                          |
| ------- | ---------- | ------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 1.0.0   | 2025-09-17 | [[Wahab Khurram]] | Initial version: Complete API specification for shared collections CRUD operations; defined collection status model (LINKED, UNLINKED, LOCKED); implemented status-based operation restrictions; added persistent variable management; included comprehensive error handling and usage examples |

---
© 2025 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems Root Document Template v1.1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->
