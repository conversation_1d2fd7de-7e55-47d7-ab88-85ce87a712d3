# Stock API Design Document

## Overview

This document outlines the design for a stock transfer API system that integrates with the RuleForge rules engine. The system enables dynamic stock transfers between entities through rule-based automation while maintaining security, scalability, and flexibility.

## Key Requirements (from conversation)

1. **Security**: No credentials stored in rule JSON - credentials managed at higher level
2. **Scalability**: Worker services for handling API operations
3. **Dynamic Registration**: APIs can be registered and used by GUI
4. **Account Ownership**: Rule set owns account credentials for transfers
5. **Generic API Structure**: Support A-to-B transfers with third-party authorization

## Architecture Overview

### Current State Analysis

The existing system has:
- `crediverseTransfer` function in astGenerator.js (lines 217-257)
- API call definitions in rule JSON with embedded credentials
- Token management via `getEcdsApiToken` function
- Synchronous API calls within rule evaluation

### Proposed Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   GUI/Frontend  │    │  Rules Engine   │    │  Stock API      │
│                 │    │                 │    │  Service        │
│ - API Registry  │◄──►│ - AST Generator │◄──►│ - Auth Manager  │
│ - Rule Composer │    │ - Evaluation    │    │ - Transfer Ops  │
│ - API Builder   │    │ - Message Queue │    │ - Provider Conn │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                ▼                       ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │  Message Queue  │    │  External APIs  │
                       │  (Redis/Bull)   │    │  (Crediverse,   │
                       │                 │    │   Other Stock   │
                       │ - Async Jobs    │    │   Providers)    │
                       │ - Retry Logic   │    │                 │
                       └─────────────────┘    └─────────────────┘
```

## Core Components

### 1. Stock API Service (`stockApiService.js`)

**Responsibilities:**
- Manage stock transfer operations
- Handle authentication with external providers
- Provide unified interface for different stock providers
- Manage API credentials securely

**Key Methods:**
```javascript
class StockApiService {
  async transferStock(fromAccount, toAccount, amount, stockType, credentials)
  async validateCredentials(credentials)
  async getAccountBalance(account, credentials)
  async registerProvider(providerConfig)
}
```

### 2. API Registration System

**Purpose:** Allow GUI to dynamically register and manage stock APIs

**Components:**
- `apiRegistrationService.js` - Core registration logic
- `apiRegistrationController.js` - REST endpoints
- Redis storage for API definitions

**API Definition Schema:**
```json
{
  "apiId": "stock-transfer-001",
  "name": "Stock Transfer API",
  "type": "stockTransfer",
  "provider": "crediverse",
  "endpoints": {
    "transfer": "/api/stock/transfer",
    "balance": "/api/stock/balance"
  },
  "authentication": {
    "type": "oauth2",
    "tokenEndpoint": "/oauth/token",
    "requiredFields": ["apiUsername", "apiSecret", "accountUsername", "accountPassword"]
  },
  "parameters": [
    {"name": "fromAccount", "type": "string", "required": true},
    {"name": "toAccount", "type": "string", "required": true},
    {"name": "amount", "type": "number", "required": true},
    {"name": "stockType", "type": "string", "required": false, "default": "default"}
  ]
}
```

### 3. Enhanced AST Generator

**Modifications to `createApiCallStatements`:**
- Support for stock transfer API types
- Credential resolution from secure storage
- Message queue integration for async operations

### 4. Message Queue System

**Purpose:** Convert synchronous API calls to asynchronous operations

**Components:**
- Job queue for API operations
- Worker processes for handling jobs
- Retry mechanisms and error handling

## Security Model

### Credential Management

1. **No Credentials in Rules**: Rule JSON contains only API references, not credentials
2. **Secure Storage**: Credentials stored in encrypted Redis hash
3. **Account Ownership**: Each ruleset owns specific account credentials
4. **Token Caching**: API tokens cached with expiration

### Credential Storage Schema
```javascript
// Redis key: "stockApi:credentials:{rulesetId}"
{
  "rulesetId": "ruleset-123",
  "accounts": {
    "primary": {
      "apiUsername": "ruleforge9",
      "apiSecret": "encrypted_secret",
      "accountUsername": "stock_account_001",
      "accountPassword": "encrypted_password",
      "provider": "crediverse"
    }
  },
  "createdAt": "2025-01-07T10:00:00Z",
  "updatedAt": "2025-01-07T10:00:00Z"
}
```

## Implementation Phases

### Phase 1: Core Stock API Service
- Create `StockApiService` class
- Implement basic transfer operations
- Add credential management
- Create provider abstraction layer

### Phase 2: AST Generator Integration
- Modify `createApiCallStatements` function
- Add stock API type support
- Implement credential resolution
- Add async operation support

### Phase 3: API Registration System
- Create registration endpoints
- Implement API definition storage
- Add validation and management features
- GUI integration points

### Phase 4: Message Queue Integration
- Implement async job processing
- Add retry mechanisms
- Create worker services
- Performance optimization

### Phase 5: Advanced Features
- Multiple provider support
- Advanced authentication methods
- Monitoring and analytics
- GUI API builder

## API Endpoints

### Stock API Management
```
POST   /api/stock/register     - Register new stock API
GET    /api/stock/apis         - List registered APIs
GET    /api/stock/apis/:id     - Get specific API
PUT    /api/stock/apis/:id     - Update API definition
DELETE /api/stock/apis/:id     - Delete API

POST   /api/stock/credentials  - Store credentials for ruleset
GET    /api/stock/credentials/:rulesetId - Get credentials (masked)
PUT    /api/stock/credentials/:rulesetId - Update credentials
DELETE /api/stock/credentials/:rulesetId - Delete credentials
```

### Stock Operations
```
POST   /api/stock/transfer     - Execute stock transfer
GET    /api/stock/balance      - Get account balance
GET    /api/stock/history      - Get transfer history
POST   /api/stock/validate     - Validate credentials
```

## Rule JSON Integration

### Current Format (with embedded credentials)
```json
{
  "apiCalls": [{
    "name": "transferStock",
    "type": "crediverseTransfer",
    "authentication": {
      "apiUsername": "ruleforge9",
      "apiSecret": "67c321580cd94076a6d1fa95f0833723",
      "crediverseUsername": "ruleforge",
      "crediversePassword": "97aaa688"
    },
    "parameters": [
      {"name": "amount", "value": "{stockAmount}"},
      {"name": "targetMSISDN", "value": "{targetAccount}"}
    ]
  }]
}
```

### Proposed Format (credential-free)
```json
{
  "apiCalls": [{
    "name": "transferStock",
    "type": "stockTransfer",
    "apiId": "stock-transfer-001",
    "accountRef": "primary",
    "parameters": [
      {"name": "fromAccount", "value": "{sourceAccount}"},
      {"name": "toAccount", "value": "{targetAccount}"},
      {"name": "amount", "value": "{stockAmount}"},
      {"name": "stockType", "value": "airtime"}
    ]
  }]
}
```

## Next Steps

1. **Create Stock API Service** - Implement core service class
2. **Modify AST Generator** - Update createApiCallStatements function
3. **Add Registration System** - Create API registration endpoints
4. **Implement Security** - Add credential management
5. **Add Message Queue** - Implement async processing
6. **Create Tests** - Comprehensive test suite
7. **Documentation** - API documentation and examples

## Benefits

1. **Security**: Credentials separated from rule definitions
2. **Scalability**: Async processing with worker services
3. **Flexibility**: Dynamic API registration and management
4. **Maintainability**: Clean separation of concerns
5. **Extensibility**: Support for multiple stock providers

## Testing

### Running Tests

```bash
# Run unit tests
npm test

# Run integration tests
./tests/test_stock_api.sh

# Register sample APIs and credentials
node scripts/registerStockApis.js all
```

### Test Coverage

The test suite includes:
- Unit tests for StockApiService
- Integration tests for API endpoints
- Validation error testing
- Encryption/decryption testing
- Mock external API responses

## Usage Examples

### 1. Register a Stock API

```bash
curl -X POST http://localhost:3000/api/v1/stock/register \
  -H "Content-Type: application/json" \
  -d '{
    "apiId": "my-stock-api",
    "name": "My Stock Transfer API",
    "type": "stockTransfer",
    "provider": "crediverse",
    "endpoints": {
      "transfer": "/api/account/transaction/transfer"
    },
    "authentication": {
      "type": "oauth2",
      "requiredFields": ["apiUsername", "apiSecret", "accountUsername", "accountPassword"]
    },
    "parameters": [
      {"name": "fromAccount", "type": "string", "required": true},
      {"name": "toAccount", "type": "string", "required": true},
      {"name": "amount", "type": "number", "required": true}
    ]
  }'
```

### 2. Store Credentials

```bash
curl -X POST http://localhost:3000/api/v1/stock/credentials \
  -H "Content-Type: application/json" \
  -d '{
    "rulesetId": "my-ruleset",
    "accountRef": "primary",
    "credentials": {
      "apiUsername": "ruleforge9",
      "apiSecret": "your-secret-key",
      "accountUsername": "your-account",
      "accountPassword": "your-password",
      "provider": "crediverse"
    }
  }'
```

### 3. Execute Transfer

```bash
curl -X POST http://localhost:3000/api/v1/stock/transfer \
  -H "Content-Type: application/json" \
  -d '{
    "fromAccount": "company-account",
    "toAccount": "+**********",
    "amount": 100,
    "stockType": "airtime",
    "apiId": "my-stock-api",
    "rulesetId": "my-ruleset",
    "accountRef": "primary"
  }'
```

## Configuration

Add to your environment variables or config:

```bash
# Encryption key for credentials (change in production)
STOCK_API_ENCRYPTION_KEY=your-32-character-encryption-key

# Redis configuration (existing)
REDIS_HOSTS=[{"host":"localhost","port":6379}]

# ECDS API configuration (existing)
ECDS_API_URL=http://ecds-api
ECDS_API_PORT=9084
```

## Migration from Legacy System

### Before (with embedded credentials)
```json
{
  "apiCalls": [{
    "name": "transferStock",
    "type": "crediverseTransfer",
    "authentication": {
      "apiUsername": "ruleforge9",
      "apiSecret": "secret",
      "crediverseUsername": "user",
      "crediversePassword": "pass"
    },
    "parameters": [
      {"name": "amount", "value": "{amount}"},
      {"name": "targetMSISDN", "value": "{target}"}
    ]
  }]
}
```

### After (credential-free)
```json
{
  "apiCalls": [{
    "name": "transferStock",
    "type": "stockTransfer",
    "apiId": "crediverse-stock-transfer",
    "rulesetId": "my-ruleset",
    "accountRef": "primary",
    "parameters": [
      {"name": "fromAccount", "value": "company-account"},
      {"name": "toAccount", "value": "{target}"},
      {"name": "amount", "value": "{amount}"},
      {"name": "stockType", "value": "airtime"}
    ]
  }]
}
```

## Troubleshooting

### Common Issues

1. **Credentials not found**: Ensure credentials are stored for the correct rulesetId and accountRef
2. **API definition not found**: Verify the apiId exists in registered APIs
3. **Transfer failures**: Check ECDS API connectivity and credentials
4. **Encryption errors**: Verify STOCK_API_ENCRYPTION_KEY is set correctly

### Debug Commands

```bash
# List registered APIs
curl http://localhost:3000/api/v1/stock/apis

# Check credentials (masked)
curl http://localhost:3000/api/v1/stock/credentials/my-ruleset

# Test API registration
node scripts/registerStockApis.js list
```
