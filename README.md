---
title: RuleForge CRE README
classification: Internal
created: 2024-08-22
updated: 2024-08-28
tags:
  - readme
  - project-documentation
  - campaign-rules-engine
  - telecom-marketing
  - sales-automation
  - redis
  - vuejs
---

# RuleForge Campaign Rules Engine (CRE)

RuleForge CRE is a cutting-edge, standalone system that revolutionizes marketing and sales automation across various industries. It enables the creation, management, and real-time execution of dynamic, data-driven campaigns across multiple contexts, transforming routine transactions into opportunities for enhanced engagement and business growth.

[Include relevant badges here: build status, test coverage, etc.]

## Document Specification

### Purpose
This README provides essential information about RuleForge CRE, including its purpose, key features, installation instructions, usage guidelines, and contribution process. It serves as the primary entry point for users and contributors to understand and engage with the project.

### Scope
This document covers the project overview, repository structure, key features, setup process, basic usage, development guidelines, and contribution procedures. It does not include detailed API documentation or extensive tutorials, which are provided in separate documents.

### Target Audience
- Telecom industry professionals interested in advanced marketing automation
- Developers looking to use or contribute to the RuleForge CRE project
- System administrators responsible for deployment and maintenance
- Marketing strategists and decision-makers in the telecom sector

## Document Content

### Table of Contents
- [[#Overview]]
- [[#Repository Structure]]
- [[#Key Features]]
- [[#Getting Started]]
- [[#Usage]]
- [[#Contributing]]
- [[#Testing]]
- [[#Deployment]]
- [[#Maintenance]]
- [[#License]]

### Overview

RuleForge CRE is a sophisticated, standalone system designed for creating, managing, and executing dynamic marketing campaigns in real-time. It addresses the challenge of delivering personalized, context-aware marketing by:

- Enabling non-technical users to implement complex campaign strategies through an intuitive GUI
- Integrating with minimally adapted legacy systems via Dynamic Transaction Enrichment (DTE) technology
- Orchestrating personalized actions during critical transaction moments
- Tracking and recalling activities over time for various entities (e.g., subscribers, agents, shops, regions) to enable contextual decision-making
- Turning existing infrastructure into personalization powerhouses

By combining a powerful backend processing engine with a dedicated, user-friendly GUI, RuleForge CRE empowers businesses to deliver the right offer or action at the right time, without reliance on legacy systems.

### Repository Structure

```
ruleforge-cre/
├── assets/
├── docs/
│   ├── AI agents/
│   ├── campaign-examples/
│   ├── design/
│   ├── interfaces/
│   ├── needs/
│   └── project/
├── src/
│   ├── backend/
│   │   ├── rules-engine/
│   │   └── api/
│   └── frontend/
│       └── gui/
├── tests/
│   ├── backend/
│   └── frontend/
├── scripts/
├── .gitignore
├── LICENSE
└── README.md
```

- `assets/`: Contains project-related images, logos, and diagrams.
- `docs/`: Comprehensive documentation including AI agent specifications, campaign examples, design documents, interface specifications, project needs, and overall project documentation.
- `src/`: Source code for both backend and frontend components.
- `tests/`: Unit and integration tests for all components.
- `scripts/`: Utility scripts for development, building, and deployment.

### Key Features

- Dynamic Transaction Enrichment (DTE) technology for seamless integration with legacy systems
- Multi-context support for highly targeted rule creation and evaluation
- User-friendly Vue.js-based GUI for intuitive campaign management by non-technical users
- Flexible rule configuration using JSON
- Efficient rule execution through JavaScript code generation
- Real-time rule evaluation and action triggering
- High-performance persistent variable management using Redis
- Customizable actions for tailored campaign responses
- Activity tracking and recall for various entity types, enabling contextual decision-making
- Scalable personalization across existing infrastructure

For more details on the system architecture and design, refer to the [[RuleForge CRE System Architecture]].

### Getting Started

1. Clone the repository:
```
<NAME_EMAIL>:Concurrent-Systems/ruleforge.git
```

2. Install dependencies:
   ```
   cd ruleforge-cre
   npm install
   ```

3. Set up environment variables:
   ```
   cp .env.example .env
   ```
   Edit `.env` with your specific configuration, including Redis connection details.

4. Ensure Redis is installed and running on your system.

5. Start the development server:
   ```
   npm run dev
   ```

For more detailed setup instructions, please refer to our [[Development Setup Guide]].

### Usage

To create and manage campaigns using RuleForge CRE:

1. Access the React based GUI at `http://localhost` (or your configured address).
2. Log in with your credentials.
3. Navigate to the Campaign Builder to create new campaigns or edit existing ones.
4. Use the Rule Editor to define complex rule sets for your campaigns.
6. Deploy your campaigns to make them live.

For detailed usage instructions, please see our [[User Guide]]. For examples of campaign structures, refer to the [[Complex Campaign JSON - Multi-Tiered Loyalty Program]] and other examples in the `docs/campaign-examples/` directory.

----

# RuleForge Frontend

## Environment Setup

The frontend application uses Vite and requires specific environment variables to connect with the RuleForge backend and User Management services.

### Environment Variables

This is the`.env` file in the frontend root directory with the following variables for the api connection:

```env
# API Endpoints
VITE_RULES_ENGINE_API_URL=http://localhost:3000/api/v1
VITE_USER_MANAGEMENT_API_URL=http://localhost:8080/api/v1
```

### Variable Descriptions

- `VITE_RULES_ENGINE_API_URL`: RuleForge backend API endpoint
- `VITE_USER_MANAGEMENT_API_URL`: User Management service API endpoint

### Variable Descriptions
Run using the docker-compose.yml file in the frontend directory and access the application at `http://localhost`



## Docker Configuration

When running the application using Docker Compose, you'll need to update the environment variables in the frontend service configuration:


# RuleForge User Management Service

## Overview

The User Management Service handles user authentication, authorization, and role management for the RuleForge platform.

### Role Types

1. **User Admin**
   - Full access to user management via RuleForge GUI
   - Complete access to all campaign actions
   - Can create, edit, and delete users
   - Can assign roles to users

2. **Campaign Admin**
   - Full access to all campaign actions
   - Can create, edit, delete, and manage campaigns
   - Cannot manage users

3. **API User**
   - Limited access for API integrations
   - Can view campaign listings
   - Can register entities
   - Read-only access to campaigns

## Default Admin Credentials

When the User Management Service starts for the first time, it creates a default admin user:

```
📧 Email: <EMAIL>
🔑 Password: Admin@123
```

**Important**: Change these credentials after first login for security purposes.

## Email Configuration

The service uses SMTP for sending emails (password reset, user creation, etc.). Configure the following environment variables:

```env
# SMTP Configuration
SMTP_HOST=smtp.mailtrap.io
SMTP_PORT=2525
SMTP_USER=your_smtp_username
SMTP_PASS=your_smtp_password
SMTP_FROM=<EMAIL>
SMTP_SECURE=false
COMPANY_NAME=Ruleforge
```

### Email Template Variables

- `COMPANY_NAME`: Used in email templates for branding
- `SMTP_FROM`: Sender email address for all system emails

### Email Notifications

The service sends emails for:
- New user creation (includes temporary password)
- Password reset requests

## Docker Configuration

Configure the User Management Service in your docker-compose.yml:

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/reset-password` - Password reset

### User Management
- `GET /api/v1/users` - List all users (User Admin only)
- `POST /api/v1/users` - Create new user (User Admin only)
- `PUT /api/v1/users/:id` - Update user (User Admin only)
- `DELETE /api/v1/users/:id` - Delete user (User Admin only)


## Security Notes

1. **Password Policy**
   - Minimum 6 characters

2. **JWT Tokens**
   - Default expiration: 15 minutes (900 seconds), can be changed in the docker-compose.yml file
   - Refresh token functionality available. Frontend will handle the refresh token automatically.

## Troubleshooting

### Common Issues

1. **Email Not Sending**
   - Verify SMTP credentials
   - Check email service connectivity
   - Verify port accessibility

2. **Database Connection**
   - Check database credentials
   - Verify database service is running
   - Check network connectivity

3. **User Creation Failed**
   - Verify email format
   - Check for duplicate email addresses
   - Ensure password meets requirements

----


#### Pull Request Guidelines

When submitting a pull request, please ensure:

1. Your PR includes a clear and concise description of the changes being made.
2. The description explains the reasoning behind the changes and their potential impact.
3. If applicable, reference any related issues or discussions.
4. Your changes adhere to our coding standards and pass all tests.
5. You've updated relevant documentation, including the [[RuleForge Interface Overview]] if your changes affect any interfaces.
6. For significant changes, consider updating the [[RuleForge CRE System Architecture]] document.

#### Code Review Process

1. All PRs will be reviewed by at least one core team member.
2. Follow the [[Interface Change Management Process]] for any changes affecting system interfaces.
3. Ensure your code adheres to the [[JavaScript Code Generation Specification]] when working on rule generation.
4. For changes to the API, consult the [[RuleForge Interface Overview]] and update it if necessary.

### Testing

To run tests:

```
npm run test
```

This will run both backend and frontend tests. To run them separately:

```
npm run test:backend
npm run test:frontend
```

[We use Jest for unit testing and Cypress for end-to-end testing. Please ensure all tests pass before submitting a pull request. TBR] 

### Deployment

Deployment is managed through our CI/CD pipeline. To trigger a deployment:

1. Merge your changes into the `main` branch.
2. Tag the commit with a version number (e.g., `v1.2.3`).
3. Push the tag to the repository.

The CI/CD pipeline will automatically build, test, and deploy the new version.

For manual deployment or more information, see our [[Deployment Guide]].

### Maintenance

The RuleForge CRE is actively maintained by the Core Engine Team:

- Regular updates are made to improve performance and add new features.
- Security patches are applied promptly.
- We follow semantic versioning for releases.

For the latest updates, please check the [[Changelog]].

### License

This project is proprietary and confidential to Concurrent Systems. Unauthorized copying, transferring or reproduction of the contents of this repository, via any medium is strictly prohibited.

## Related Documents
- [[RuleForge CRE System Architecture]]
- [[Development Setup Guide]]
- [[User Guide]]
- [[Contribution Guidelines]]
- [[Coding Standards]]
- [[Deployment Guide]]
- [[Changelog]]
- [[RuleForge Interface Overview]]
- [[JavaScript Code Generation Specification]]
- [[RuleForge Interface Overview]]
- [[Redis Implementation Guide for RuleForge CRE]]
- [[Vue.js GUI Architecture for RuleForge CRE]]

## Version Control
Document Version: 1.3
Last Updated: [[2024-08-28]]
Author: [[RuleForge DevGenius]]
Next Review Date: [[2024-11-30]]

<!--
### Changelog:
* 1.4 (2024-08-14): Fixed repository URL in Getting Started section <NAME_EMAIL>:Concurrent-Systems/ruleforge.git. 
* 1.3 (2024-08-30): Updated README to include information about Redis for persistent variable management and Vue.js for the GUI. Added related documents for Redis and Vue.js implementations.
* 1.2 (2024-08-27): Updated README to align with new template structure and incorporated new product information.
* 1.1 (2024-08-23): Updated product name from "Campaign Rules Engine" to "RuleForge CRE" throughout the document, revised project overview, updated installation instructions, added links to new documentation files, and corrected formatting.
* 1.0 (2024-08-22): Initial README creation.
-->

---
© 2024 Concurrent Systems. All Rights Reserved.

<!-- This document was created using the Concurrent Systems README Template v1.0. For the latest version of this template, visit: https://github.com/Concurrent-Systems/concurrent-handbook/tree/main/templates -->