# GitHub Actions Workflows

This document describes the workflows for building athe Rules Engine Docker images for both backend and frontend services.

## Workflow Overview

[Github link](https://github.com/Concurrent-Systems/ruleforge-cre/blob/main/.github/workflows/build-rules-engine.yml)

### 1. Build Rules Engine Docker Image (Backend and Frontend)

This workflow triggers on a push to the `main` branch and is responsible for building and pushing Docker images for both the backend and frontend of the Rules Engine. It consists of three main jobs:

#### Jobs

- **build-and-push-backend-docker-images**: Builds and pushes the backend Docker image for the Rules Engine.
- **build-and-push-gui-docker-images**: Builds and pushes the frontend Docker image for the Rules Engine GUI.
- **trigger-rules-forge-deployment-notification**: Notifies the deployment pipeline after successful image builds.

### Workflow Configuration

```yaml
name: Build Rules Engine Docker Image (Backend and Frontend)

on:
  push:
    branches: main
    paths:
      - "src/backend/rules-engine/**"
      - "src/frontend/rules-engine/**"
      - ".github/**"
```

### Job Descriptions

#### 1.1 Build and Push Backend Docker Images

```yaml
build-and-push-backend-docker-images:
  runs-on: rules-forge
  permissions:
    contents: 'read'
    id-token: 'write'
    packages: 'write'
  outputs:
    backendimage: ${{ steps.version.outputs.image }}
    backendversion: ${{ steps.version.outputs.version }}      
```

**Description**: This job builds the Docker image for the backend service and pushes it to the GitHub Container Registry.

**Steps**:
- **Job Start Time**: Captures the start time of the job.
- **Checkout Repository**: Checks out the repository code.
- **Login to GitHub Container Registry**: Authenticates with the registry using a token.
- **Configure Version Information**: Prepares the image ID and version based on the branch and run ID.
- **Build and Push Docker Image**: Builds the backend image and pushes it to the registry.
- **Output Image Details**: Outputs the image ID and version for downstream jobs.

#### 1.2 Build and Push GUI Docker Images

```yaml
build-and-push-gui-docker-images:
  runs-on: rules-forge
  permissions:
    contents: 'read'
    id-token: 'write'
    packages: 'write'
  outputs:
    frontendimage: ${{ steps.version.outputs.image }}
    frontendversion: ${{ steps.version.outputs.version }}      
```

**Description**: This job builds the Docker image for the frontend service and pushes it to the GitHub Container Registry.

**Steps**:
- **Job Start Time**: Captures the start time of the job.
- **Checkout Repository**: Checks out the repository code.
- **Login to GitHub Container Registry**: Authenticates with the registry using a token.
- **Configure Version Information**: Prepares the image ID and version based on the branch and run ID.
- **Build and Push Docker Image**: Builds the frontend image and pushes it to the registry.
- **Output Image Details**: Outputs the image ID and version for downstream jobs.

#### 1.3 Trigger Rules Forge Deployment Notification

```yaml
trigger-rules-forge-deployment-notification:
  runs-on: rules-forge
  needs: [ build-and-push-backend-docker-images, build-and-push-gui-docker-images ]
  if: ${{ success() }}
```

**Description**: This job triggers a notification to the GitOps repository after the successful completion of the backend and frontend image builds.

**Steps**:
- **Notify ITS GitOps Repository**: Sends a POST request to notify the deployment pipeline with the built image details and branch information.


## Conclusion

This document serves as a guide to understanding the workflows for building the Rules Engine.
