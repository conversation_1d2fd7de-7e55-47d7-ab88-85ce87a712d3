# This is a basic workflow to help you get started with Actions

name: Nightly Build RULE FORGE Base
on:
  workflow_call:
    inputs:
      BRANCH_NAME:
        required: true
        type: string
        default: "main"
      GITHUB_TAGNAME:
        required: true
        type: string
        default: "nightly_build"
      DOCKER_TAGNAME:
        required: true
        type: string
        default: "nightly_build_${{github.run_id}}"
    secrets:
       EMAIL_USER:
         required: true
       EMAIL_APP_PASSWORD:
         required: true
       GHCR_TOKEN:
          required: true
concurrency:
  group: nightly_build
env:
  BACKEND_IMAGE_NAME: rules-engine-service
  GUI_IMAGE_NAME: rules-engine-gui
  USER_MANAGMENT_IMAGE_NAME: rules-engine-user-managment
jobs:
  setup-variables:
    runs-on: ubuntu-latest
    outputs:
      run_job: ${{ steps.check_true_condition.outputs.run_job }}
      run_job1: ${{ steps.check_false_condition.outputs.run_job }}
    steps:
      - name: Job Start Time
        run: echo "NOW=$(date +%Y-%m-%d -d "yesterday")" >> $GITHUB_ENV
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ inputs.BRANCH_NAME }}
      - name: Check for new commits today
        id: check-new-commits
        uses: adriangl/check-new-commits-action@v1
        with:
          token: ${{secrets.GITHUB_TOKEN}}
          seconds: 86400 # One day in seconds
          branch: ${{ inputs.BRANCH_NAME }}
      - name: set lower case repo name
        run: |
          echo "REPO_LC=${GITHUB_REPOSITORY#*/}" >>${GITHUB_ENV}
          echo  REPO_LOWERCASE=$(echo  ${{ github.event.repository.name }}  | tr '[:upper:]' '[:lower:]') >>${GITHUB_ENV}
      - name: Read version.txt
        run: |-
          VER=$(cat ${{github.workspace}}/version.txt)
          echo "BuildVersion=$VER" >> $GITHUB_ENV
      - name: New Commits found for today
        id: check_true_condition
        if: ${{ steps.check-new-commits.outputs.has-new-commits == 'true' }}
        run: |-
          echo "run_job=yes" >> $GITHUB_OUTPUT
      - name: New Commits Not found
        id: check_false_condition
        if: ${{ steps.check-new-commits.outputs.has-new-commits != 'true' }}
        run: |-
          echo "run_job=no" >> $GITHUB_OUTPUT
      - name: Generate Changelog file for main
        if: inputs.BRANCH_NAME == 'main'
        run: |-
          rm -f ${{ github.workspace }}-${{inputs.BRANCH_NAME}}-CHANGELOG.md
          echo "Rule Forge-${{ inputs.BRANCH_NAME }} " > ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
          echo "Release Date | ${{env.NOW}}" >> ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
          echo "Build Number | ${{ github.run_id }}" >> ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md

      - name: Generate Changelog file
        if: inputs.BRANCH_NAME != 'main'
        run: |-
          rm -f ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
          echo "Rule Forge-${{ env.BuildVersion }} " > ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
          echo "Release Date | ${{env.NOW}}" >> ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
          echo "Build Number | ${{ github.run_id }}" >> ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md

      - name: check release notes file
        if: inputs.BRANCH_NAME != 'main'
        run: >-
          if [[ -f "${{github.workspace}}/release_notes/${{env.REPO_LOWERCASE}}_release_notes_${{ env.BuildVersion }}.md" ]]; 
          then 
              cat "${{github.workspace}}/release_notes/${{env.REPO_LOWERCASE}}_release_notes_${{ env.BuildVersion }}.md" >> ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
          fi
      - name: Check Advisory Notes file
        if: inputs.BRANCH_NAME != 'main'
        run: >-
          if [[ -f "${{github.workspace}}/release_notes/${{env.REPO_LOWERCASE}}_advisory_notes_${{ env.BuildVersion }}.md" ]]; 
          then
            echo "## Internal" >> ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
            echo "[Advisory Note](${{github.server_url}}/${{github.repository}}/tree/${{env.BRANCH_NAME}}/release_notes/${{env.REPO_LOWERCASE}}_advisory_notes_${{inputs.BRANCH_NAME}}.${{ env.BuildVersion }}.md) " >> ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
          fi
      - name: Update Changelog file
        run: |-
          echo "## DOCKER IMAGES" >>  ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
          cat ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
      - name: Upload Changelog
        uses: actions/upload-artifact@v4
        with:
          name: changelog-${{ inputs.BRANCH_NAME }}-${{github.run_id}}
          path: ${{ github.workspace }}/RuleForge-${{ inputs.BRANCH_NAME }}-CHANGELOG.md
          overwrite: true
      - name: check outptut
        run: |-
          ls -ltrh ${{github.workspace}}
          cat ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md

  build-and-push-backend-docker-images:
    runs-on: ubuntu-latest
    permissions:
      contents: 'read'
      id-token: 'write'
      packages: 'write'
    needs: [setup-variables] # helpful to keep the "order" of events
    if: needs.setup-variables.outputs.run_job =='yes' || needs.setup-variables.outputs.run_job1 =='yes'
    
    outputs:
      backendimage: ${{ steps.version.outputs.image }}
      backendversion: ${{ steps.version.outputs.version }}  

    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      - name: 'Job Start Time'
        run: |
          CS_START_TIME=$(date +"%Y-%m-%dT%H:%M:%S")
          echo "Start Time => $CS_START_TIME"        
      - name: checkout repo
        uses: 'actions/checkout@v3'
        with:
          ref: ${{ inputs.BRANCH_NAME }}

      - name: Set up Node.js 18
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Run unit tests
        run: |-
            echo "Running tests for rule-engine-service"
            cd ${GITHUB_WORKSPACE}/src/backend/rules-engine
            npm install
            npm test          
      - name: Log in to docker registry
        run: echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u $ --password-stdin
      - name: Download Changelog
        uses: actions/download-artifact@v4
        with:
          name: changelog-${{inputs.BRANCH_NAME}}-${{github.run_id}}
      - name: check output
        run: |
          ls -ltrh ${{github.workspace}}
          cat ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
      - name: Configure Version Information
        id: version-info
        run: |
          IMAGE_ID=ghcr.io/${{ github.repository_owner }}/$BACKEND_IMAGE_NAME
          IMAGE_ID=$(echo $IMAGE_ID | tr '[A-Z]' '[a-z]')
          VERSION=${{inputs.DOCKER_TAGNAME}}
          echo IMAGE_ID=$IMAGE_ID
          echo VERSION=$VERSION
          echo "IMAGE_ID=$IMAGE_ID" >> $GITHUB_ENV
          echo "VERSION=$VERSION" >> $GITHUB_ENV
      - name: Build and Push Docker Image
        run: |-
               build_and_push_image() {
                 SERVICE_NAME=$1
                 echo "Building $SERVICE_NAME"
                 cd ${GITHUB_WORKSPACE}/src/$SERVICE_NAME
                 docker build -t ${{ env.IMAGE_ID }}:${{ env.VERSION }} .
                 docker tag ${{ env.IMAGE_ID }}:${{ env.VERSION }} ${{ env.IMAGE_ID }}:latest
                 docker push ${{ env.IMAGE_ID }}:${{ env.VERSION }}
                 docker push ${{ env.IMAGE_ID }}:latest
                 #docker tag ${{ env.IMAGE_ID }}:${{ env.VERSION }} ${{ env.IMAGE_ID }}:${{github.run_number}}
                 #docker push ${{ env.IMAGE_ID }}:${{github.run_number}}
                 echo "- [${{env.IMAGE_ID}}:${{env.VERSION}}](https://${{env.IMAGE_ID}}:${{env.VERSION}})" >> ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
                 cat ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md

               }
               # Build and push backend image
               build_and_push_image "backend/rules-engine"
               cat ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md

      - name: Output Image Details
        id: version
        run: |
          echo "image=${{ env.IMAGE_ID }}" >>$GITHUB_OUTPUT
          echo "version=${{ env.VERSION }}" >>$GITHUB_OUTPUT
      - name: Upload Changelog
        uses: actions/upload-artifact@v4
        with:
          name: changelog-${{ inputs.BRANCH_NAME }}-${{github.run_id}}
          path: ${{ github.workspace }}/RuleForge-${{ inputs.BRANCH_NAME }}-CHANGELOG.md
          overwrite: true
      - name: check outptut
        run: |-
          ls -ltrh ${{github.workspace}}
          cat ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md

  build-and-push-gui-docker-images:
    runs-on: ubuntu-latest
    permissions:
      contents: 'read'
      id-token: 'write'
      packages: 'write'
    needs: [setup-variables, build-and-push-backend-docker-images] # helpful to keep the "order" of events
    if: needs.setup-variables.outputs.run_job =='yes' || needs.setup-variables.outputs.run_job1 =='yes'

    outputs:
      frontendimage: ${{ steps.ruleforge-gui.outputs.image }}
      frontendversion: ${{ steps.ruleforge-gui.outputs.version }} 


    # Steps represent a sequence of tasks that will be executed as part of the job
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - name: 'Job Start Time'
        run: |
          CS_START_TIME=$(date +"%Y-%m-%dT%H:%M:%S")
          echo "Start Time => $CS_START_TIME"        
      - name: checkout repo
        uses: 'actions/checkout@v3'
        with:
          ref: ${{ inputs.BRANCH_NAME }}

      - name: Read Product Version
        run: |
          cd ${GITHUB_WORKSPACE}/
          echo "PRODUCT_VERSION=$(cat version.txt)" >> $GITHUB_ENV

      - name: Replace Placeholders in .env
        run: |
          cd ${GITHUB_WORKSPACE}/src/frontend/rules-engine
          sed -i "s/{productVersion}/${PRODUCT_VERSION}/g" .env
          sed -i "s/{buildNumber}/${GITHUB_RUN_ID}/g" .env

      - name: Display Updated .env
        run: cat ${GITHUB_WORKSPACE}/src/frontend/rules-engine/.env
      - name: Log in to docker registry
        run: echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u $ --password-stdin
      - name: Download Changelog
        uses: actions/download-artifact@v4
        with:
          name: changelog-${{inputs.BRANCH_NAME}}-${{github.run_id}}
      - name: check output
        run: |
          ls -ltrh ${{github.workspace}}
          cat ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
      - name: Configure Version Information
        id: version-info
        run: |
          IMAGE_ID=ghcr.io/${{ github.repository_owner }}/$GUI_IMAGE_NAME
          IMAGE_ID=$(echo $IMAGE_ID | tr '[A-Z]' '[a-z]')
          VERSION=${{inputs.DOCKER_TAGNAME}}
          echo IMAGE_ID=$IMAGE_ID
          echo VERSION=$VERSION
          # Share variables between steps (which each run in a separate process)
          echo "IMAGE_ID=$IMAGE_ID" >> $GITHUB_ENV
          echo "VERSION=$VERSION" >> $GITHUB_ENV
      - name: Build and Push Docker Image
        run: |-
               build_and_push_image() {
                 SERVICE_NAME=$1
                 echo "Building $SERVICE_NAME"
                 cd ${GITHUB_WORKSPACE}/src/$SERVICE_NAME
                 docker build -t ${{ env.IMAGE_ID }}:${{ env.VERSION }} .
                 docker tag ${{ env.IMAGE_ID }}:${{ env.VERSION }} ${{ env.IMAGE_ID }}:latest
                 echo "- [${{env.IMAGE_ID}}:${{env.VERSION}}](https://${{env.IMAGE_ID}}:${{env.VERSION}})" >> ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
                 docker push ${{ env.IMAGE_ID }}:${{ env.VERSION }}
                 docker push ${{ env.IMAGE_ID }}:latest
               }
               # Build and push frontend image
               build_and_push_image "frontend/rules-engine/"
      - name: Output Image Details
        id: ruleforge-gui
        run: |
          echo "image=${{ env.IMAGE_ID }}" >>$GITHUB_OUTPUT
          echo "version=${{ env.VERSION }}" >>$GITHUB_OUTPUT
      - name: Upload Changelog
        uses: actions/upload-artifact@v4
        with:
          name: changelog-${{ inputs.BRANCH_NAME }}-${{github.run_id}}
          path: ${{ github.workspace }}/RuleForge-${{ inputs.BRANCH_NAME }}-CHANGELOG.md
          overwrite: true
      - name: check outptut
        run: |-
          ls -ltrh ${{github.workspace}}
          cat ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
  build-and-push-user-managment-docker-images:
    runs-on: ubuntu-latest
    permissions:
         contents: 'read'
         id-token: 'write'
         packages: 'write'
    needs: [setup-variables, build-and-push-backend-docker-images, build-and-push-gui-docker-images] # helpful to keep the "order" of events
    if: needs.setup-variables.outputs.run_job =='yes' || needs.setup-variables.outputs.run_job1 =='yes'

    outputs:
      usermgtimage: ${{ steps.user-version.outputs.image }}
      usermgtversion: ${{ steps.user-version.outputs.version }}      
    steps:
      - name: 'Job Start Time'
        run: |
          CS_START_TIME=$(date +"%Y-%m-%dT%H:%M:%S")
          echo "Start Time => $CS_START_TIME"        
      - name: checkout repo
        uses: 'actions/checkout@v3'
        with:
          ref: ${{ inputs.BRANCH_NAME }}
      - name: Set up Node.js 18
        uses: actions/setup-node@v3
        with:
          node-version: '18'        
      - name: Download Changelog
        uses: actions/download-artifact@v4
        with:
           name: changelog-${{inputs.BRANCH_NAME}}-${{github.run_id}}
      - name: check output
        run: |
           ls -ltrh ${{github.workspace}}
           cat ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
      - name: Log in to docker registry
        run: echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u $ --password-stdin
   
      - name: Configure Version Information
        id: version-info
        run: |
           IMAGE_ID=ghcr.io/${{ github.repository_owner }}/$USER_MANAGMENT_IMAGE_NAME
           IMAGE_ID=$(echo $IMAGE_ID | tr '[A-Z]' '[a-z]')
           VERSION=${{inputs.DOCKER_TAGNAME}}
           echo IMAGE_ID=$IMAGE_ID
           echo VERSION=$VERSION
           # Share variables between steps (which each run in a separate process)
           echo "IMAGE_ID=$IMAGE_ID" >> $GITHUB_ENV
           echo "VERSION=$VERSION" >> $GITHUB_ENV
      - name: Build and Push Docker Image
        run: |-
             build_and_push_image() {
               SERVICE_NAME=$1
               echo "Building $SERVICE_NAME"
               cd ${GITHUB_WORKSPACE}/src/$SERVICE_NAME
               docker build -t ${{ env.IMAGE_ID }}:${{ env.VERSION }} .
               docker tag ${{ env.IMAGE_ID }}:${{ env.VERSION }} ${{ env.IMAGE_ID }}:latest
               echo "- [${{env.IMAGE_ID}}:${{env.VERSION}}](https://${{env.IMAGE_ID}}:${{env.VERSION}})" >> ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
               docker push ${{ env.IMAGE_ID }}:${{ env.VERSION }}
               docker push ${{ env.IMAGE_ID }}:latest
             }
             # Build and push backend image
             build_and_push_image "backend/user-management"
      - name: Output Image Details
        id: user-version
        run: |
              echo "image=${{ env.IMAGE_ID }}" >>$GITHUB_OUTPUT
              echo "version=${{ env.VERSION }}" >>$GITHUB_OUTPUT
  
      - name: Upload Changelog
        uses: actions/upload-artifact@v4
        with:
          name: changelog-${{ inputs.BRANCH_NAME }}-${{github.run_id}}
          path: ${{ github.workspace }}/RuleForge-${{ inputs.BRANCH_NAME }}-CHANGELOG.md
          overwrite: true
      - name: check outptut
        run: |-
          ls -ltrh ${{github.workspace}}
          cat ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
  create-release-page-main:
    runs-on: ubuntu-latest
    permissions: "write-all"
    needs: [
        build-and-push-backend-docker-images,
        build-and-push-gui-docker-images,
        setup-variables,
        build-and-push-user-managment-docker-images,        
      ]
    if: |
      inputs.BRANCH_NAME == 'main' &&  always()  && 
      needs.build-and-push-backend-docker-images.result == 'success' &&
      needs.build-and-push-gui-docker-images.result == 'success' &&
      needs.build-and-push-user-managment-docker-images.result == 'success' &&
      needs.setup-variables.result == 'success' 

    steps:
      - name: Get Current date
        id: date
        run: |-
          echo "NOW=$(date +%Y-%m-%d -d "yesterday")" >> $GITHUB_ENV
      - name: Download Changelog
        uses: actions/download-artifact@v4
        with:
          name: changelog-${{inputs.BRANCH_NAME}}-${{github.run_id}}
      - name: check output
        run: |
          ls -ltrh ${{github.workspace}}
          cat ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md

      - name: Check if nightly-build tag exist
        uses: mukunku/tag-exists-action@v1.2.0
        id: checkTag
        with:
          tag: ${{ inputs.GITHUB_TAGNAME }}
      - run: echo ${{ steps.checkTag.outputs.exists }}
      - name: Delete git tag
        uses: actions/github-script@v3
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            try {
                await github.git.deleteRef({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  ref: "tags/${{inputs.GITHUB_TAGNAME}}"
                })
            } catch (e) {
              console.log("The ${{inputs.GITHUB_TAGNAME}} tag doesn't exist yet: " + e)
              }
      - name: delete release by tag name
        uses: cb80/delrel@latest
        with:
          tag: ${{inputs.GITHUB_TAGNAME}}
          token: ${{ secrets.GITHUB_TOKEN }}
      - name: Create Release
        id: create_release
        uses: ncipollo/release-action@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN}} # This token is provided by Actions, you do not need to create your own token
        with:
          tag: ${{inputs.GITHUB_TAGNAME}}
          commit: ${{inputs.BRANCH_NAME}}
          name: RuleForge Nightly Release ${{env.NOW}}
          draft: false
          replacesArtifacts: true
          removeArtifacts: false
          allowUpdates: true
          bodyFile: ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
          #discussionCategory: 'Announcements'
          generateReleaseNotes: true
          prerelease: true
      - name: Cleanup
        run: |
          rm -f ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md

  create-release-page:
    runs-on: ubuntu-latest
    permissions: "write-all"
    needs: [
        build-and-push-backend-docker-images,
        build-and-push-gui-docker-images,
        setup-variables,
        build-and-push-user-managment-docker-images,        
      ]
    if: |
      inputs.BRANCH_NAME != 'main' &&  always()  && 
      needs.build-and-push-backend-docker-images.result == 'success' &&
      needs.build-and-push-gui-docker-images.result == 'success' &&
      needs.build-and-push-user-managment-docker-images.result == 'success' &&
      needs.setup-variables.result == 'success'

    steps:
      - name: Get Current date
        id: date
        run: |-
          echo "NOW=$(date +%Y-%m-%d -d "yesterday")" >> $GITHUB_ENV
      - name: Download Changelog
        uses: actions/download-artifact@v4
        with:
          name: changelog-${{inputs.BRANCH_NAME}}-${{github.run_id}}
      - name: check output
        run: |
          ls -ltrh ${{github.workspace}}
          cat ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
      - name: Check if nightly-build tag exist
        uses: mukunku/tag-exists-action@v1.2.0
        id: checkTag
        with:
          tag: ${{ inputs.GITHUB_TAGNAME }}
      - run: echo ${{ steps.checkTag.outputs.exists }}
      - name: Delete git tag
        uses: actions/github-script@v3
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            try {
                await github.git.deleteRef({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  ref: "tags/${{inputs.GITHUB_TAGNAME}}"
                })
            } catch (e) {
              console.log("The ${{inputs.GITHUB_TAGNAME}} tag doesn't exist yet: " + e)
              }
      - name: delete release by tag name
        uses: cb80/delrel@latest
        with:
          tag: ${{inputs.GITHUB_TAGNAME}}
          token: ${{ secrets.GITHUB_TOKEN }}
      - name: Create Release
        id: create_release
        uses: ncipollo/release-action@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN}} # This token is provided by Actions, you do not need to create your own token
        with:
          tag: ${{inputs.GITHUB_TAGNAME}}
          name: RuleForge-${{inputs.BRANCH_NAME}} Nightly Release ${{env.NOW}}
          commit: ${{inputs.BRANCH_NAME}}
          draft: false
          replacesArtifacts: true
          removeArtifacts: false
          allowUpdates: true
          bodyFile: ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
          discussionCategory: "general"
          #generateReleaseNotes: true
          #prerelease: true
      - name: Cleanup
        run: |
          rm -f ${{ github.workspace }}/RuleForge-${{inputs.BRANCH_NAME}}-CHANGELOG.md
  trigger-rules-forge-deployment-notification:
     runs-on: ubuntu-latest
     needs: [ build-and-push-backend-docker-images, build-and-push-gui-docker-images, build-and-push-user-managment-docker-images ]
     if: > 
        ${{ needs.build-and-push-backend-docker-images.result == 'success' &&
            needs.build-and-push-gui-docker-images.result == 'success' &&
            needs.build-and-push-user-managment-docker-images.result == 'success'
        }}

     steps:
        - name: Notify ITS GitOps Repository
          run: |
            response=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
              -H "Accept: application/vnd.github.v3+json" \
              -H "Authorization: token ${{ secrets.GHCR_TOKEN }}" \
              -H "Content-Type: application/json" \
              https://api.github.com/repos/Concurrent-Systems/gitops/dispatches \
              -d '{
                    "event_type": "trigger_ruleforge_deployment",
                    "client_payload": {
                      "rules_forge_backend_docker_tag": "${{needs.build-and-push-backend-docker-images.outputs.backendversion}}",
                      "rules_forge_backend_docker_image": "${{needs.build-and-push-backend-docker-images.outputs.backendimage}}",
                      "rules_forge_gui_docker_tag": "${{needs.build-and-push-gui-docker-images.outputs.frontendversion}}",
                      "rules_forge_gui_docker_image": "${{needs.build-and-push-gui-docker-images.outputs.frontendimage}}",
                      "rules_forge_ums_docker_tag": "${{needs.build-and-push-user-managment-docker-images.outputs.usermgtversion}}",
                      "rules_forge_ums_docker_image": "${{needs.build-and-push-user-managment-docker-images.outputs.usermgtimage}}",

                      "branch": "main"
                    }
                  }')
            http_code=$(echo "$response" | tail -n1)
            response_body=$(echo "$response" | head -n -1)

            if [ "$http_code" -ne 204 ]; then
            echo "Error: Notification failed with status code $http_code"
            echo "Response body: $response_body"
            exit 1
            fi
                 
  emailNotification:
      runs-on: ubuntu-latest
      needs: [
          build-and-push-backend-docker-images,
          build-and-push-gui-docker-images,
          build-and-push-user-managment-docker-images,
          setup-variables,
          create-release-page,
          create-release-page-main,
          trigger-rules-forge-deployment-notification
        ]
      if: always() # Ensures this job runs regardless of previous failures or skips
      steps:
        - name: Check if a new build was triggered
          id: check_build_trigger
          run: |
            if [[ "${{ needs.setup-variables.outputs.run_job }}" == "yes" ]]; then
              echo "new_build=true" >> $GITHUB_ENV
            else
              echo "new_build=false" >> $GITHUB_ENV
            fi
  
        - name: Check for Failures (Ignoring Trigger Job)
          id: check_failures
          run: |
            if [[ 
              "${{ needs.build-and-push-backend-docker-images.result }}" == "failure" || 
              "${{ needs.build-and-push-gui-docker-images.result }}" == "failure" || 
              "${{ needs.setup-variables.result }}" == "failure" || 
              "${{ needs.build-and-push-user-managment-docker-images.result }}" == "failure" ||
              "${{ needs.create-release-page.result }}" == "failure" || 
              "${{ needs.create-release-page-main.result }}" == "failure"
            ]]; then
              echo "status=failure" >> $GITHUB_ENV
            else
              echo "status=success" >> $GITHUB_ENV
            fi
  
            if [[ "${{ needs.trigger-rules-forge-deployment-notification.result }}" == "failure" ]]; then
              echo "trigger_failed=true" >> $GITHUB_ENV
            else
              echo "trigger_failed=false" >> $GITHUB_ENV
            fi
  
        - name: Send Email on Success (With Trigger Failure Warning)
          if: ${{ env.new_build == 'true' && (env.status == 'success' || env.trigger_failed == 'true') }}
          uses: dawidd6/action-send-mail@v3
          with:
            server_address: smtp.gmail.com
            server_port: 465
            username: ${{secrets.EMAIL_USER}}
            password: ${{secrets.EMAIL_APP_PASSWORD}}
            subject: ✅ RuleForge Nightly Build Successful${{ env.trigger_failed == 'true' && ' ⚠️ (Deployment Trigger Failed)' || '' }}
            body: |
              RuleForge Nightly build completed successfully.
  
              Release Link:
              https://github.com/Concurrent-Systems/ruleforge/releases/tag/${{ inputs.GITHUB_TAGNAME }}
  
              Build Logs:
              https://github.com/Concurrent-Systems/ruleforge/actions/runs/${{ github.run_id }}
  
              ${{ env.trigger_failed == 'true' && '⚠️ WARNING: Deployment trigger job failed! Please check the logs.' || '' }}
  
            to: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
            from: RuleForge CI Pipeline <${{secrets.EMAIL_USER}}>
  
        - name: Send Email on Failure (Ignoring Trigger Job)
          if: env.status == 'failure'
          uses: dawidd6/action-send-mail@v3
          with:
            server_address: smtp.gmail.com
            server_port: 465
            username: ${{secrets.EMAIL_USER}}
            password: ${{secrets.EMAIL_APP_PASSWORD}}
            subject: ❌ RuleForge Nightly Build Failed
            body: |
              RuleForge Nightly build failed.
  
              Build Logs:
              https://github.com/Concurrent-Systems/ruleforge/actions/runs/${{ github.run_id }}
  
            to: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
            from: RuleForge CI Pipeline <${{secrets.EMAIL_USER}}>
        - name: Send Email When No New Changes Exist
          if: ${{ env.new_build == 'false' }}
          uses: dawidd6/action-send-mail@v3
          with:
              server_address: smtp.gmail.com
              server_port: 465
              username: ${{secrets.EMAIL_USER}}
              password: ${{secrets.EMAIL_APP_PASSWORD}}
              subject: ℹ️ RuleForge Build Skipped - No New Changes
              body: |
                No new code changes were found in the Last 24 hours, so no new build was released.
    
                You can still use the latest available build.
  
                Last Successful Release:
                https://github.com/Concurrent-Systems/ruleforge/releases/tag/${{ inputs.GITHUB_TAGNAME }}
  
                Build Logs:
                https://github.com/Concurrent-Systems/ruleforge/actions/runs/${{ github.run_id }}

              to: <EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>
              from: RuleForge CI Pipeline <${{secrets.EMAIL_USER}}>
     