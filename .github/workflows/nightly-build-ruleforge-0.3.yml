name: Nightly Build RuleForge 0.3

# Controls when the workflow will run
on:
 # push:
 #    branches:
 #     - triggerDeployment
  schedule:
    - cron: '00 01 * * *'
  workflow_dispatch:
      inputs:
        environment:
          description: 'Manual run...'
          required: true
          default: 'manual-run'
          type: choice
          options:
            - 'manual-run'
concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

env:
  branchName: '0.3'
jobs:
  set-variables:
    runs-on: ubuntu-latest
    outputs:
      currentBranch: ${{ steps.set-value.outputs.currentBranch }}
    steps:
      - name: Output branch Details
        id: set-value
        run: |
          echo "currentBranch=${{ env.branchName }}" >>$GITHUB_OUTPUT
  Ruleforge_Nightly_0_3:
    needs: set-variables
    uses: Concurrent-Systems/ruleforge/.github/workflows/nightly-build-ruleforge-base.yml@main
    with:
      DOCKER_TAGNAME: nightly_build_${{needs.set-variables.outputs.currentBranch}}_${{github.run_id}}
      GITHUB_TAGNAME: nightly_build_${{needs.set-variables.outputs.currentBranch}}
      BRANCH_NAME: ${{needs.set-variables.outputs.currentBranch}}
    secrets:
        EMAIL_USER: ${{ secrets.EMAIL_USER }}
        EMAIL_APP_PASSWORD: ${{ secrets.EMAIL_APP_PASSWORD }}
        GHCR_TOKEN: ${{ secrets.GHCR_TOKEN }}   