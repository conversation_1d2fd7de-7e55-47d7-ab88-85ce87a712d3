name: Nightly Build RuleForge BR<PERSON>CH_NAME

# Controls when the workflow will run
on:
  # push:
  #  branches:
  #   - nightlyProcess
  schedule:
    - cron: '00 00 * * *'
  workflow_dispatch:
      inputs:
        environment:
          description: 'Manual run...'
          required: true
          default: 'manual-run'
          type: choice
          options:
            - 'manual-run'
concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

env:
  branchName: 'BRANCH_NAME'
jobs:
  set-variables:
    runs-on: ubuntu-latest
    outputs:
      currentBranch: ${{ steps.set-value.outputs.currentBranch }}
    steps:
      - name: Output branch Details
        id: set-value
        run: |
          echo "currentBranch=${{ env.branchName }}" >>$GITHUB_OUTPUT
  Ruleforge_Nightly_BRANCH_NAME:
    needs: set-variables
    uses: Concurrent-Systems/ruleforge/.github/workflows/nightly-build-ruleforge-base.yml@main
    with:
      DOCKER_TAGNAME: nightly_build_${{github.run_id}}
      GITHUB_TAGNAME: nightly_build_${{needs.set-variables.outputs.currentBranch}}
      BRANCH_NAME: ${{needs.set-variables.outputs.currentBranch}}
    secrets:
        EMAIL_USER: ${{ secrets.EMAIL_USER }}
        EMAIL_APP_PASSWORD: ${{ secrets.EMAIL_APP_PASSWORD }}   