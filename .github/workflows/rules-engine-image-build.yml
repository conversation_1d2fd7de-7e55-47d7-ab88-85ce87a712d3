# name: Docker Build and Push

# on:
#   push:
#     branches: [main]
#     paths:
#       - "src/backend/rules-engine/**"
#       - ".github/**"

# jobs:
#   build-and-push:
#     runs-on: ubuntu-latest
#     permissions:
#       contents: read
#       packages: write
#     steps:
#       - uses: actions/checkout@v3

#       - name: Login to GitHub Container Registry
#         uses: docker/login-action@v2
#         with:
#           registry: ghcr.io
#           username: ${{ github.actor }}
#           password: ${{ secrets.GITHUB_TOKEN }}

#       - name: Prepare image metadata
#         id: prep
#         run: |
#           # Convert to lowercase
#           REPO_OWNER=$(echo "${{ github.repository_owner }}" | tr '[:upper:]' '[:lower:]')
#           REPO_NAME=$(echo "${{ github.event.repository.name }}" | tr '[:upper:]' '[:lower:]')
#           IMAGE_NAME="${REPO_OWNER}/${REPO_NAME}/rules-engine-service"

#           # Generate tags
#           VERSION=${GITHUB_REF##*/}
#           TAGS="ghcr.io/${IMAGE_NAME}:latest,ghcr.io/${IMAGE_NAME}:${VERSION},ghcr.io/${IMAGE_NAME}:${{ github.run_number }}"

#           # Set outputs
#           echo "tags=${TAGS}" >> $GITHUB_OUTPUT
#           echo "image_name=${IMAGE_NAME}" >> $GITHUB_OUTPUT

#       - name: Build and push Docker image
#         uses: docker/build-push-action@v4
#         with:
#           context: src/backend/rules-engine
#           push: true
#           tags: ${{ steps.prep.outputs.tags }}

#       - name: Debug - List pushed images
#         run: |
#           echo "Attempting to list pushed images..."
#           docker pull ghcr.io/${{ steps.prep.outputs.image_name }}:latest
#           docker image ls

#       - name: Verify image push
#         run: |
#           # Wait a bit for the image to be available
#           sleep 30
#           # Check if the image is available
#           if docker pull ghcr.io/${{ steps.prep.outputs.image_name }}:latest > /dev/null; then
#             echo "Image successfully pushed and available in the registry"
#           else
#             echo "Failed to verify image in the registry"
#             exit 1
#           fi
