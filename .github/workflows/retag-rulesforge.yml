name: Create RuleForge GA Release

# Controls when the workflow will run
on:
  workflow_dispatch:
    inputs:
      oldTag:
        description: 'Docker image old tag'
        required: true
      newTag:
        description: 'Desired Docker image new tag'
        required: true
      buildNumber:
        description: 'Nightly buildNumber'
        required: true
      branchName:
        description: 'Branch Name'
        required: true 
      commitSha:
        description: 'The commit SHA to tag'
        required: true        
jobs:
  printInputs:
    runs-on: ubuntu-latest
    steps:
      - uses: 'actions/checkout@v3'
        with:
          ref: ${{inputs.branchName}}
      - name: 'Job Start Time'
        run: |
          CS_START_TIME=$(date +"%Y-%m-%dT%H:%M:%S")
          echo "Start Time => $CS_START_TIME"
          echo "oldTag: ${{ inputs.oldTag }}"
          echo "newTag: ${{ inputs.newTag }}"
          echo "buildNumber: ${{ inputs.buildNumber }}"
          echo "RELEASE_DATE=$(date +%Y-%m-%d)" >> $GITHUB_ENV
      - name: set lower case repo name
        run: |
          echo "REPO_LC=${GITHUB_REPOSITORY#*/}" >>${GITHUB_ENV}
          echo  REPO_LOWERCASE=$(echo  ${{ github.event.repository.name }}  | tr '[:upper:]' '[:lower:]') >>${GITHUB_ENV}
      - name: Generate Changelog file
        run: |-
          rm -f ${{ github.workspace }}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
          echo "RuleForge-${{ inputs.newTag }} " > ${{ github.workspace }}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
          echo "Release Date | ${{env.RELEASE_DATE}}" >> ${{ github.workspace }}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
          echo "Build Number | ${{ inputs.buildNumber }}" >> ${{ github.workspace }}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
      - name: get repository name
        run: echo "REPOSITORY_NAME=${GITHUB_REPOSITORY#*/}" >> $GITHUB_ENV
      - name: check release notes file
        run: >-
          if [[ -f "${{github.workspace}}/release_notes/${{env.REPO_LOWERCASE}}_release_notes_${{inputs.newTag}}.md" ]]; 
          then 
              cat "${{github.workspace}}/release_notes/${{env.REPO_LOWERCASE}}_release_notes_${{inputs.newTag}}.md" >> ${{github.workspace}}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
          fi
      - name: Check Advisory Notes file
        run: >-
          if [[ -f "${{github.workspace}}/release_notes/${{env.REPO_LOWERCASE}}_advisory_notes_${{inputs.newTag}}.md" ]]; 
          then
            echo "## Internal" >> ${{github.workspace}}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
            echo "[Advisory Note](${{github.server_url}}/${{github.repository}}/tree/${{inputs.BRANCH_NAME}}/release_notes/${{env.REPO_LOWERCASE}}_advisory_notes_${{inputs.newTag}}.md) " >> ${{ github.workspace }}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
          fi

      - name: Create test report links
        run: >-
          echo "## Test Report" >> ${{github.workspace}}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md;
          arr=($(find . -type f -name "${{env.REPO_LOWERCASE}}_test_report_${{ inputs.newTag }}*.pdf"));
          for i in "${arr[@]}" ; do   echo "$i" ; fname=`basename $i .pdf` ; id=$(echo "$fname" | cut -d'_' -f5); echo $id ; echo "[Test Report $id]($i)" >> ${{github.workspace}}-${{ inputs.newTag }}-CHANGELOG.md ; done
      - name: Docker image links
        run: |-
          echo "## DOCKER IMAGES" >> ${{github.workspace}}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
          cat ${{github.workspace}}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
      - name: Upload Changelog
        uses: actions/upload-artifact@v4
        with:
              name: changelog-${{ inputs.newTag }}-${{github.run_id}}  
              path: ${{ github.workspace }}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
              overwrite: true
        
      - name: check outptut
        run: |-
           ls -ltrh ${{github.workspace}}
           cat ${{ github.workspace }}/RuleForge-retag-${{inputs.newTag}}-CHANGELOG.md
  retag-docker-images:
    needs:
      - printInputs
    runs-on: ubuntu-latest
    strategy:
      max-parallel: 1
      matrix:
        IMAGE_NAME:
          [
            rules-engine-service,
            rules-engine-gui,
            rules-engine-user-managment 
          ]
    steps:
      - name: Download Changelog
        uses: actions/download-artifact@v4
        with:
          name: changelog-${{inputs.newTag}}-${{github.run_id}}  
      - name: check output
        run: | 
           ls -ltrh ${{github.workspace}}
           cat ${{ github.workspace }}/RuleForge-retag-${{inputs.newTag}}-CHANGELOG.md 
      - name: login ghcr
        run: echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u $ --password-stdin
      - name: downcase REPO
        run: |
          echo "REPO_LOWERCASE=${GITHUB_REPOSITORY,,}" >>${GITHUB_ENV}

      - name: owner lower case
        run: |
          echo "OWNER_LC=${OWNER,,}" >> ${GITHUB_ENV}
        env:
          OWNER: '${{ github.repository_owner }}'
      - name: run retagging
        run: |-
          docker pull ghcr.io/${{env.OWNER_LC}}/${{matrix.IMAGE_NAME}}:${{inputs.oldTag}} 
          docker tag ghcr.io/${{env.OWNER_LC}}/${{matrix.IMAGE_NAME}}:${{inputs.oldTag}}  ghcr.io/${{env.OWNER_LC}}/${{matrix.IMAGE_NAME}}:${{inputs.newTag}} 
          docker push ghcr.io/${{env.OWNER_LC}}/${{matrix.IMAGE_NAME}}:${{inputs.newTag}}
          echo "- [ghcr.io/${{env.OWNER_LC}}/${{matrix.IMAGE_NAME}}:${{inputs.newTag}}](https://ghcr.io/${{env.OWNER_LC}}/${{matrix.IMAGE_NAME}}:${{inputs.newTag}})" >> ${{github.workspace}}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
          cat ${{github.workspace}}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
      - name: Upload Changelog
        uses: actions/upload-artifact@v4
        with:
            name: changelog-${{ inputs.newTag }}-${{github.run_id}}  
            path: ${{ github.workspace }}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
            overwrite: true
        
      - name: check outptut
        run: |-
             ls -ltrh ${{github.workspace}}
             cat ${{ github.workspace }}/RuleForge-retag-${{inputs.newTag}}-CHANGELOG.md          
  create-release-page:
    runs-on: ubuntu-latest
    permissions: 'write-all'
    needs: [retag-docker-images, printInputs]
    if: |
      always() && needs.printInputs.result == 'success' && needs.retag-docker-images.result == 'success'

    steps:
      - name: Get Current date
        id: date
        run: |-
          echo "NOW=$(date +%Y-%m-%d)" >> $GITHUB_ENV
      - name: Download Changelog
        uses: actions/download-artifact@v4
        with:
            name: changelog-${{inputs.newTag}}-${{github.run_id}}  
      - name: check output
        run: | 
             ls -ltrh ${{github.workspace}}
             cat ${{ github.workspace }}/RuleForge-retag-${{inputs.newTag}}-CHANGELOG.md  
      - name: Create tag
        uses: actions/github-script@v5
        with:
              script: |
                const response = await github.rest.git.createRef({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  ref: 'refs/tags/${{inputs.newTag}}', // Replace with your tag name
                  sha: '${{inputs.commitSha}}'
                });
    
                console.log(response);
  
      - name: Create Release
        id: create_release
        uses: ncipollo/release-action@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # This token is provided by Actions, you do not need to create your own token
        with:
          commit: ${{inputs.commitSha}}
          tag: ${{ inputs.newTag }}
          name: RuleForge-${{ inputs.newTag }}
          draft: false
          replacesArtifacts: true
          removeArtifacts: false
          allowUpdates: true
          bodyFile: ${{github.workspace}}/RuleForge-retag-${{ inputs.newTag }}-CHANGELOG.md
          discussionCategory: 'general'
          #generateReleaseNotes: true
          #prerelease: true
