name: Build Rules Engine Docker Image (Backend and Frontend)

on:
    push:
      branches: 
        - main

    pull_request:
      branches:
        - main
      types: [opened, reopened, synchronize]

concurrency:
  group: pr-${{ github.event.pull_request.number }}
  cancel-in-progress: true
  # The above concurrency group ensures that only one job runs at a time for the same pull request.
  # If a new commit is pushed to the same pull request, the previous job will be canceled.
  # This helps to avoid unnecessary builds and saves resources.
  # The `cancel-in-progress` option ensures that if a new commit is pushed to the same pull request,
jobs:
  detect-docs-only:
    runs-on: ubuntu-latest
    outputs:
      docs_only: ${{ steps.changed-files.outputs.only_changed }}
    steps:
      - uses: actions/checkout@v4

      - name: Detect if only docs or markdown files changed
        id: changed-files
        uses: tj-actions/changed-files@v35
        with:
          files: |
            docs/**
            **/*.md
  build-and-push-backend-docker-images:
    needs: detect-docs-only
    if: needs.detect-docs-only.outputs.docs_only == 'false'
    runs-on: ubuntu-latest
    permissions:
         contents: 'read'
         id-token: 'write'
         packages: 'write'
         issues: 'read'
         checks: 'write'
         pull-requests: 'write'
    outputs:
      backendimage: ${{ steps.version.outputs.image }}
      backendversion: ${{ steps.version.outputs.version }}      
    steps:
      - name: 'Job Start Time'
        run: |
          CS_START_TIME=$(date +"%Y-%m-%dT%H:%M:%S")
          echo "Start Time => $CS_START_TIME"        
      - name: checkout repo
        uses: 'actions/checkout@v3'
      - name: Set up Node.js 18
        uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Run unit tests
        run: |-
            echo "Running tests for rule-engine-service"
            cd ${GITHUB_WORKSPACE}/src/backend/rules-engine
            npm install
            npm test
      - name: Login to GitHub Container Registry
        run: echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u $ --password-stdin

      - name: Configure Version Information
        id: version-info
        run: |-
          IMAGE_ID=ghcr.io/${{ github.repository_owner }}/rules-engine-service
          IMAGE_ID=$(echo $IMAGE_ID | tr '[A-Z]' '[a-z]')
          VERSION=$(echo "${{ github.ref }}" | sed -e 's,.*/\(.*\),\1,')
          VERSION=$VERSION-build-${{github.run_id}}
          echo IMAGE_ID=$IMAGE_ID
          echo VERSION=$VERSION
          # Share variables between steps (which each run in a separate process)
          echo "IMAGE_ID=$IMAGE_ID" >> $GITHUB_ENV
          echo "VERSION=$VERSION" >> $GITHUB_ENV

      - name: Build and Push Docker Image
        run: |-
             build_and_push_image() {
               SERVICE_NAME=$1
               echo "Building $SERVICE_NAME"
               cd ${GITHUB_WORKSPACE}/src/$SERVICE_NAME
               docker build -t ${{ env.IMAGE_ID }}:${{ env.VERSION }} .
               docker tag ${{ env.IMAGE_ID }}:${{ env.VERSION }} ${{ env.IMAGE_ID }}:latest
               docker push ${{ env.IMAGE_ID }}:${{ env.VERSION }}
               docker push ${{ env.IMAGE_ID }}:latest
               docker tag ${{ env.IMAGE_ID }}:${{ env.VERSION }} ${{ env.IMAGE_ID }}:${{github.run_number}}
               docker push ${{ env.IMAGE_ID }}:${{github.run_number}}
             }
             # Build and push backend image
             build_and_push_image "backend/rules-engine"
      - name: Output Image Details
        id: version
        run: |
         echo "image=${{ env.IMAGE_ID }}" >>$GITHUB_OUTPUT
         echo "version=${{ env.VERSION }}" >>$GITHUB_OUTPUT

      - name: Debug Test Results Path
        run: ls -R ${{ github.workspace }}/src/backend/rules-engine/test-results/
      - name: Upload Jest Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: jest-test-results-${{github.run_id}}
          path: ${{ github.workspace }}/src/backend/rules-engine/test-results/
          overwrite: true
      - name: Publish Jest Test Report on PR
        if: github.event_name == 'pull_request'
        uses: EnricoMi/publish-unit-test-result-action@v2
        with:
          files: "${{ github.workspace }}/src/backend/rules-engine/test-results/jest-report.xml"   
  build-and-push-gui-docker-images:
    needs: detect-docs-only
    if: needs.detect-docs-only.outputs.docs_only == 'false'
    runs-on: ubuntu-latest
    permissions:
           contents: 'read'
           id-token: 'write'
           packages: 'write'
    outputs:
      frontendimage: ${{ steps.version.outputs.image }}
      frontendversion: ${{ steps.version.outputs.version }}      
    steps:
      - name: 'Job Start Time'
        run: |
          CS_START_TIME=$(date +"%Y-%m-%dT%H:%M:%S")
          echo "Start Time => $CS_START_TIME"        
      - name: checkout repo
        uses: 'actions/checkout@v3'
      - name: Validate Product Version Format
        run: |
            cd ${GITHUB_WORKSPACE}/
            PRODUCT_VERSION=$(cat version.txt | tr -d '[:space:]') 
            echo "Read version: $PRODUCT_VERSION"
            
            if [[ ! "$PRODUCT_VERSION" =~ ^[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
              echo "❌ Invalid version format in version.txt. Expected format: X.Y.Z (e.g., 0.1.1, 1.2.3, 10.5.7)"
              exit 1
            fi

            echo "✅ Version format is valid: $PRODUCT_VERSION"
            echo "PRODUCT_VERSION=$PRODUCT_VERSION" >> $GITHUB_ENV

      - name: Replace Placeholders in .env
        run: |
          cd ${GITHUB_WORKSPACE}/src/frontend/rules-engine
          sed -i "s/{productVersion}/${PRODUCT_VERSION}/g" .env
          sed -i "s/{buildNumber}/${GITHUB_RUN_ID}/g" .env

      - name: Display Updated .env
        run: cat ${GITHUB_WORKSPACE}/src/frontend/rules-engine/.env
      - name: Login to GitHub Container Registry
        run: echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u $ --password-stdin

      - name: Configure Version Information
        id: version-info-gui
        run: |-
          IMAGE_ID=ghcr.io/${{ github.repository_owner }}/rules-engine-gui
          IMAGE_ID=$(echo $IMAGE_ID | tr '[A-Z]' '[a-z]')
          VERSION=$(echo "${{ github.ref }}" | sed -e 's,.*/\(.*\),\1,')
          VERSION=$VERSION-build-${{github.run_id}}
          echo IMAGE_ID=$IMAGE_ID
          echo VERSION=$VERSION
          # Share variables between steps (which each run in a separate process)
          echo "IMAGE_ID=$IMAGE_ID" >> $GITHUB_ENV
          echo "VERSION=$VERSION" >> $GITHUB_ENV

      - name: Build and Push Docker Image
        run: |-
             build_and_push_image() {
               SERVICE_NAME=$1
               echo "Building $SERVICE_NAME"
               cd ${GITHUB_WORKSPACE}/src/$SERVICE_NAME
               docker build -t ${{ env.IMAGE_ID }}:${{ env.VERSION }} .
               docker tag ${{ env.IMAGE_ID }}:${{ env.VERSION }} ${{ env.IMAGE_ID }}:latest
               docker push ${{ env.IMAGE_ID }}:${{ env.VERSION }}
               docker push ${{ env.IMAGE_ID }}:latest
             }
             # Build and push frontend image
             build_and_push_image "frontend/rules-engine/"
      - name: Output Image Details
        id: version
        run: |
         echo "image=${{ env.IMAGE_ID }}" >>$GITHUB_OUTPUT
         echo "version=${{ env.VERSION }}" >>$GITHUB_OUTPUT
  build-and-push-user-managment-docker-images:
   needs: detect-docs-only
   if: needs.detect-docs-only.outputs.docs_only == 'false'
   runs-on: ubuntu-latest
   permissions:
        contents: 'read'
        id-token: 'write'
        packages: 'write'
   outputs:
     usermgtimage: ${{ steps.user-version.outputs.image }}
     usermgtversion: ${{ steps.user-version.outputs.version }}      
   steps:
     - name: 'Job Start Time'
       run: |
         CS_START_TIME=$(date +"%Y-%m-%dT%H:%M:%S")
         echo "Start Time => $CS_START_TIME"        
     - name: checkout repo
       uses: 'actions/checkout@v3'
     - name: Set up Node.js 18
       uses: actions/setup-node@v3
       with:
         node-version: '18'        
     - name: Login to GitHub Container Registry
       run: echo "${{ secrets.GITHUB_TOKEN }}" | docker login ghcr.io -u $ --password-stdin
     - name: Configure Version Information
       id: version-info
       run: |-
         IMAGE_ID=ghcr.io/${{ github.repository_owner }}/rules-engine-user-managment
         IMAGE_ID=$(echo $IMAGE_ID | tr '[A-Z]' '[a-z]')
         VERSION=$(echo "${{ github.ref }}" | sed -e 's,.*/\(.*\),\1,')
         VERSION=$VERSION-build-${{github.run_id}}
         echo IMAGE_ID=$IMAGE_ID
         echo VERSION=$VERSION
         # Share variables between steps (which each run in a separate process)
         echo "IMAGE_ID=$IMAGE_ID" >> $GITHUB_ENV
         echo "VERSION=$VERSION" >> $GITHUB_ENV
     - name: Build and Push Docker Image
       run: |-
            build_and_push_image() {
              SERVICE_NAME=$1
              echo "Building $SERVICE_NAME"
              cd ${GITHUB_WORKSPACE}/src/$SERVICE_NAME
              docker build -t ${{ env.IMAGE_ID }}:${{ env.VERSION }} .
              docker tag ${{ env.IMAGE_ID }}:${{ env.VERSION }} ${{ env.IMAGE_ID }}:latest
              docker push ${{ env.IMAGE_ID }}:${{ env.VERSION }}
              docker push ${{ env.IMAGE_ID }}:latest
            }
            # Build and push backend image
            build_and_push_image "backend/user-management"
     - name: Output Image Details
       id: user-version
       run: |
             echo "image=${{ env.IMAGE_ID }}" >>$GITHUB_OUTPUT
             echo "version=${{ env.VERSION }}" >>$GITHUB_OUTPUT
  mark-docs-only-complete:
   needs: detect-docs-only
   if: needs.detect-docs-only.outputs.docs_only == 'true'
   runs-on: ubuntu-latest
   steps:
     - name: Skipped Build - Docs Only PR
       run: echo "Only docs changed, skipping build/test jobs."      
  # trigger-rules-forge-deployment-notification:
  #  runs-on: ubuntu-latest
  #  needs: [ build-and-push-backend-docker-images, build-and-push-gui-docker-images ]
  #  if: ${{ success() }}
  #  steps:
  #     - name: Notify ITS GitOps Repository
  #       run: |
  #         response=$(curl -s -o /dev/null -w "%{http_code}" -X POST \
  #           -H "Accept: application/vnd.github.v3+json" \
  #           -H "Authorization: token ${{ secrets.GHCR_TOKEN }}" \
  #           -H "Content-Type: application/json" \
  #           https://api.github.com/repos/Concurrent-Systems/gitops/dispatches \
  #           -d '{
  #                 "event_type": "trigger_ruleforge_deployment",
  #                 "client_payload": {
  #                   "rules_forge_backend_docker_tag": "${{needs.build-and-push-backend-docker-images.outputs.backendversion}}",
  #                   "rules_forge_backend_docker_image": "${{needs.build-and-push-backend-docker-images.outputs.backendimage}}",
  #                   "rules_forge_gui_docker_tag": "${{needs.build-and-push-gui-docker-images.outputs.frontendversion}}",
  #                   "rules_forge_gui_docker_image": "${{needs.build-and-push-gui-docker-images.outputs.frontendimage}}",

  #                   "branch": "main"
  #                 }
  #               }')
  #         http_code=$(echo "$response" | tail -n1)
  #         response_body=$(echo "$response" | head -n -1)

  #         if [ "$http_code" -ne 204 ]; then
  #         echo "Error: Notification failed with status code $http_code"
  #         echo "Response body: $response_body"
  #         exit 1
  #         fi

  
