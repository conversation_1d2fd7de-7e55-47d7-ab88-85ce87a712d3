name: PR Checklist Validation
on:
  pull_request:
    types: [opened, edited, synchronize]

jobs:
  checklist-validation:
    runs-on: ubuntu-latest
    steps:
      - name: Check for unchecked boxes
        run: |
          if echo "${{ github.event.pull_request.body }}" | grep -q "\- \[ \]"; then
            echo "❌ PR has unchecked boxes"
            exit 1
          fi
          echo "✅ All boxes checked"
